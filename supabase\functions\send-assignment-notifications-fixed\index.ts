import { serve } from 'https://deno.land/std@0.223.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.45.4';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

    if (!supabaseUrl || !supabaseKey) {
      return new Response(
        JSON.stringify({ error: 'Supabase configuration is missing' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    const supabaseClient = createClient(supabaseUrl, supabaseKey);

    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization header required' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Get current user from auth
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid authorization' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Get request body
    const { learner_ids, path_id } = await req.json();

    if (!learner_ids || !Array.isArray(learner_ids) || learner_ids.length === 0) {
      return new Response(
        JSON.stringify({ error: 'learner_ids array is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    if (!path_id) {
      return new Response(
        JSON.stringify({ error: 'path_id is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Get learning path information
    const { data: pathData, error: pathError } = await supabaseClient
      .from('learning_paths')
      .select('id, title, description, estimated_duration, difficulty_level')
      .eq('id', path_id)
      .single();

    if (pathError || !pathData) {
      return new Response(
        JSON.stringify({ error: 'Learning path not found' }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Get assigner information
    const { data: assignerData, error: assignerError } = await supabaseClient
      .from('users')
      .select('full_name, email')
      .eq('id', user.id)
      .single();

    if (assignerError || !assignerData) {
      return new Response(
        JSON.stringify({ error: 'Assigner not found' }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Get learner information - direct query to avoid relationship ambiguity
    const { data: learners, error: learnersError } = await supabaseClient
      .from('users')
      .select('id, full_name, email, tenant_id')
      .in('id', learner_ids);

    if (learnersError) {
      throw learnersError;
    }

    if (!learners || learners.length === 0) {
      return new Response(
        JSON.stringify({ error: 'No valid learners found' }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Verify all learners belong to the same tenant as the assigner
    const { data: assignerProfile } = await supabaseClient
      .from('users')
      .select('tenant_id')
      .eq('id', user.id)
      .single();

    const validLearners = learners.filter(learner => 
      learner.tenant_id === assignerProfile?.tenant_id
    );

    if (validLearners.length === 0) {
      return new Response(
        JSON.stringify({ error: 'No valid learners found in your tenant' }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Create notifications for each valid learner
    const notifications = validLearners.map(learner => ({
      user_id: learner.id,
      type: 'assignment',
      title: 'New Learning Path Assigned',
      content: `You have been assigned the learning path: ${pathData.title}`,
      data: {
        path_id: pathData.id,
        path_title: pathData.title,
        assigned_by: assignerData.full_name,
        estimated_duration: pathData.estimated_duration,
        difficulty_level: pathData.difficulty_level
      },
      created_at: new Date().toISOString(),
      read: false
    }));

    const { error: notificationError } = await supabaseClient
      .from('notifications')
      .insert(notifications);

    if (notificationError) {
      throw notificationError;
    }

    // Send email notifications
    for (const learner of validLearners) {
      const emailContent = {
        to: learner.email,
        subject: `New Learning Path Assigned: ${pathData.title}`,
        html: generateAssignmentEmailHTML(learner.full_name, pathData, assignerData.full_name),
        text: generateAssignmentEmailText(learner.full_name, pathData, assignerData.full_name)
      };

      // In a real implementation, you would send the email using an email service
      console.log(`Sending assignment notification email to ${learner.email}`);
      console.log(`Subject: ${emailContent.subject}`);
      console.log(`Content: ${emailContent.text}`);
    }

    return new Response(
      JSON.stringify({ 
        success: true,
        notifications_sent: validLearners.length,
        learners: validLearners.map(l => ({ id: l.id, name: l.full_name, email: l.email })),
        path: {
          id: pathData.id,
          title: pathData.title
        }
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Error in send-assignment-notifications:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});

function generateAssignmentEmailHTML(learnerName: string, pathData: any, assignerName: string): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>New Learning Path Assigned</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #1976d2;">New Learning Path Assigned</h1>
        
        <p>Hi ${learnerName},</p>
        
        <p>You have been assigned a new learning path by ${assignerName}.</p>
        
        <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>${pathData.title}</h3>
          <p><strong>Description:</strong> ${pathData.description || 'No description provided'}</p>
          <p><strong>Difficulty:</strong> ${pathData.difficulty_level}</p>
          <p><strong>Estimated Duration:</strong> ${pathData.estimated_duration} weeks</p>
        </div>
        
        <p>Please log in to your learning platform to start this path.</p>
        
        <p>If you have any questions, please contact your administrator.</p>
        
        <p>Happy learning!</p>
        
        <p>Best regards,<br>Your Learning Team</p>
      </div>
    </body>
    </html>
  `;
}

function generateAssignmentEmailText(learnerName: string, pathData: any, assignerName: string): string {
  return `
New Learning Path Assigned

Hi ${learnerName},

You have been assigned a new learning path by ${assignerName}.

Learning Path Details:
Title: ${pathData.title}
Description: ${pathData.description || 'No description provided'}
Difficulty: ${pathData.difficulty_level}
Estimated Duration: ${pathData.estimated_duration} weeks

Please log in to your learning platform to start this path.

If you have any questions, please contact your administrator.

Happy learning!

Best regards,
Your Learning Team
  `.trim();
}
