'use client'

import React, { useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  <PERSON><PERSON>graphy,
  Button,
  Alert,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  Chip,
  Divider,
  Grid
} from '@mui/material'
import {
  CloudUpload as UploadIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Security as SecurityIcon
} from '@mui/icons-material'
import { StorageService, FileUploadResult, UploadProgress } from '@/lib/services/storageService'

interface TestResult {
  test: string
  status: 'pending' | 'success' | 'error'
  message: string
  duration?: number
}

export default function StorageTestComponent() {
  const [isRunning, setIsRunning] = useState(false)
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [uploadProgress, setUploadProgress] = useState<UploadProgress>({ loaded: 0, total: 0, percentage: 0 })
  const [uploadedFiles, setUploadedFiles] = useState<FileUploadResult[]>([])

  const addTestResult = (result: TestResult) => {
    setTestResults(prev => [...prev, result])
  }

  const updateTestResult = (index: number, updates: Partial<TestResult>) => {
    setTestResults(prev => prev.map((result, i) => 
      i === index ? { ...result, ...updates } : result
    ))
  }

  const createTestFile = (name: string, type: string, size: number): File => {
    const content = new Array(size).fill('a').join('')
    const blob = new Blob([content], { type })
    return new File([blob], name, { type, lastModified: Date.now() })
  }

  const runStorageTests = async () => {
    setIsRunning(true)
    setTestResults([])
    setUploadedFiles([])

    try {
      // Test 1: File Validation
      addTestResult({ test: 'File Validation', status: 'pending', message: 'Testing file size and type validation...' })
      const startTime1 = Date.now()
      
      try {
        // Test oversized file (should fail)
        const oversizedFile = createTestFile('large-file.mp4', 'video/mp4', 200 * 1024 * 1024) // 200MB
        await StorageService.uploadFile(oversizedFile)
        updateTestResult(0, { 
          status: 'error', 
          message: 'File validation failed - oversized file was accepted',
          duration: Date.now() - startTime1
        })
      } catch (error) {
        updateTestResult(0, { 
          status: 'success', 
          message: 'File validation working - oversized file rejected',
          duration: Date.now() - startTime1
        })
      }

      // Test 2: Valid File Upload
      addTestResult({ test: 'Valid File Upload', status: 'pending', message: 'Uploading valid test file...' })
      const startTime2 = Date.now()
      
      try {
        const testFile = createTestFile('test-video.mp4', 'video/mp4', 1024 * 1024) // 1MB
        const uploadResult = await StorageService.uploadFile(testFile, (progress) => {
          setUploadProgress(progress)
        })
        
        setUploadedFiles(prev => [...prev, uploadResult])
        updateTestResult(1, { 
          status: 'success', 
          message: `File uploaded successfully to: ${uploadResult.path}`,
          duration: Date.now() - startTime2
        })
      } catch (error) {
        updateTestResult(1, { 
          status: 'error', 
          message: `Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          duration: Date.now() - startTime2
        })
      }

      // Test 3: Tenant Isolation
      addTestResult({ test: 'Tenant Isolation', status: 'pending', message: 'Verifying tenant path structure...' })
      const startTime3 = Date.now()
      
      if (uploadedFiles.length > 0) {
        const filePath = uploadedFiles[0].path
        const tenantMatch = filePath.match(/^tenant_(\d+)\/content\//)
        
        if (tenantMatch) {
          updateTestResult(2, { 
            status: 'success', 
            message: `Tenant isolation verified - file stored in tenant_${tenantMatch[1]} bucket`,
            duration: Date.now() - startTime3
          })
        } else {
          updateTestResult(2, { 
            status: 'error', 
            message: 'Tenant isolation failed - incorrect path structure',
            duration: Date.now() - startTime3
          })
        }
      } else {
        updateTestResult(2, { 
          status: 'error', 
          message: 'Cannot test tenant isolation - no files uploaded',
          duration: Date.now() - startTime3
        })
      }

      // Test 4: File Access
      addTestResult({ test: 'File Access', status: 'pending', message: 'Testing file URL generation...' })
      const startTime4 = Date.now()
      
      if (uploadedFiles.length > 0) {
        try {
          const publicUrl = StorageService.getPublicUrl(uploadedFiles[0].path)
          const signedUrl = await StorageService.getSignedUrl(uploadedFiles[0].path, 300)
          
          updateTestResult(3, { 
            status: 'success', 
            message: `URLs generated successfully - Public: ${publicUrl.length > 0 ? 'Yes' : 'No'}, Signed: ${signedUrl.length > 0 ? 'Yes' : 'No'}`,
            duration: Date.now() - startTime4
          })
        } catch (error) {
          updateTestResult(3, { 
            status: 'error', 
            message: `URL generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
            duration: Date.now() - startTime4
          })
        }
      } else {
        updateTestResult(3, { 
          status: 'error', 
          message: 'Cannot test file access - no files uploaded',
          duration: Date.now() - startTime4
        })
      }

      // Test 5: Multiple File Types
      addTestResult({ test: 'Multiple File Types', status: 'pending', message: 'Testing different file types...' })
      const startTime5 = Date.now()
      
      try {
        const fileTypes = [
          { name: 'test-doc.pdf', type: 'application/pdf' },
          { name: 'test-presentation.pptx', type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation' },
          { name: 'test-image.jpg', type: 'image/jpeg' }
        ]
        
        const uploadPromises = fileTypes.map(({ name, type }) => {
          const file = createTestFile(name, type, 512 * 1024) // 512KB
          return StorageService.uploadFile(file)
        })
        
        const results = await Promise.all(uploadPromises)
        setUploadedFiles(prev => [...prev, ...results])
        
        updateTestResult(4, { 
          status: 'success', 
          message: `Successfully uploaded ${results.length} different file types`,
          duration: Date.now() - startTime5
        })
      } catch (error) {
        updateTestResult(4, { 
          status: 'error', 
          message: `Multi-file upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          duration: Date.now() - startTime5
        })
      }

      // Test 6: File Deletion
      addTestResult({ test: 'File Deletion', status: 'pending', message: 'Testing file deletion...' })
      const startTime6 = Date.now()
      
      if (uploadedFiles.length > 0) {
        try {
          const fileToDelete = uploadedFiles[uploadedFiles.length - 1]
          await StorageService.deleteFile(fileToDelete.path)
          
          setUploadedFiles(prev => prev.filter(f => f.path !== fileToDelete.path))
          
          updateTestResult(5, { 
            status: 'success', 
            message: 'File deletion successful',
            duration: Date.now() - startTime6
          })
        } catch (error) {
          updateTestResult(5, { 
            status: 'error', 
            message: `File deletion failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
            duration: Date.now() - startTime6
          })
        }
      } else {
        updateTestResult(5, { 
          status: 'error', 
          message: 'Cannot test file deletion - no files available',
          duration: Date.now() - startTime6
        })
      }

    } catch (error) {
      console.error('Test suite error:', error)
    } finally {
      setIsRunning(false)
      setUploadProgress({ loaded: 0, total: 0, percentage: 0 })
    }
  }

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <SuccessIcon color="success" />
      case 'error':
        return <ErrorIcon color="error" />
      default:
        return <LinearProgress sx={{ width: 20, height: 20 }} />
    }
  }

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return 'success'
      case 'error':
        return 'error'
      default:
        return 'default'
    }
  }

  return (
    <Box>
      <Card>
        <CardContent>
          <Typography variant="h5" gutterBottom>
            Storage Service Test Suite
          </Typography>
          
          <Alert severity="info" sx={{ mb: 3 }}>
            This test suite validates the Supabase Storage integration including file uploads, 
            tenant isolation, security policies, and file management operations.
          </Alert>

          <Button
            variant="contained"
            startIcon={<UploadIcon />}
            onClick={runStorageTests}
            disabled={isRunning}
            size="large"
            sx={{ mb: 3 }}
          >
            {isRunning ? 'Running Tests...' : 'Run Storage Tests'}
          </Button>

          {uploadProgress.percentage > 0 && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" gutterBottom>
                Upload Progress: {uploadProgress.percentage}%
              </Typography>
              <LinearProgress variant="determinate" value={uploadProgress.percentage} />
            </Box>
          )}

          {testResults.length > 0 && (
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Test Results
                </Typography>
                
                <List>
                  {testResults.map((result, index) => (
                    <React.Fragment key={index}>
                      <ListItem>
                        <Box display="flex" alignItems="center" width="100%">
                          <Box mr={2}>
                            {getStatusIcon(result.status)}
                          </Box>
                          <Box flexGrow={1}>
                            <Typography variant="subtitle1">
                              {result.test}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {result.message}
                            </Typography>
                            {result.duration && (
                              <Typography variant="caption" color="text.secondary">
                                Completed in {result.duration}ms
                              </Typography>
                            )}
                          </Box>
                          <Chip
                            label={result.status}
                            color={getStatusColor(result.status)}
                            size="small"
                          />
                        </Box>
                      </ListItem>
                      {index < testResults.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          )}

          {uploadedFiles.length > 0 && (
            <Card variant="outlined" sx={{ mt: 2 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Uploaded Test Files ({uploadedFiles.length})
                </Typography>
                
                <Grid container spacing={2}>
                  {uploadedFiles.map((file, index) => (
                    <Grid item xs={12} sm={6} md={4} key={index}>
                      <Card variant="outlined">
                        <CardContent>
                          <Typography variant="subtitle2" noWrap>
                            {file.metadata.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {file.metadata.type}
                          </Typography>
                          <Typography variant="caption" display="block">
                            {(file.metadata.size / 1024).toFixed(1)} KB
                          </Typography>
                          <Box display="flex" alignItems="center" mt={1}>
                            <SecurityIcon fontSize="small" color="success" />
                            <Typography variant="caption" color="success.main" ml={0.5}>
                              Secure
                            </Typography>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </Box>
  )
}
