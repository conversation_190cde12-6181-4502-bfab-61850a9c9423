'use client'

import { useState } from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  IconButton,
  Chip,
  Switch,
  FormControlLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Avatar,
  useTheme,
  Divider,
} from '@mui/material'
import {
  Schedule as ScheduleIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  Email as EmailIcon,
  Download as DownloadIcon,
  Notifications as NotificationIcon,
  AccessTime as TimeIcon,
  CalendarToday as CalendarIcon,
  MoreVert as MoreVertIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'

interface ScheduledReport {
  id: number
  name: string
  description: string
  template: string
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  nextRun: string
  lastRun: string | null
  isActive: boolean
  recipients: string[]
  format: 'pdf' | 'excel' | 'csv'
  deliveryMethod: 'email' | 'download' | 'both'
  createdBy: string
  runCount: number
}

export default function ScheduledReports() {
  const theme = useTheme()
  const [schedules, setSchedules] = useState<ScheduledReport[]>([
    {
      id: 1,
      name: 'Weekly Progress Report',
      description: 'Comprehensive weekly progress report for all active learning paths',
      template: 'Learner Progress Report',
      frequency: 'weekly',
      nextRun: '2024-01-22T09:00:00Z',
      lastRun: '2024-01-15T09:00:00Z',
      isActive: true,
      recipients: ['<EMAIL>', '<EMAIL>'],
      format: 'pdf',
      deliveryMethod: 'email',
      createdBy: 'John Doe',
      runCount: 12,
    },
    {
      id: 2,
      name: 'Monthly Engagement Analytics',
      description: 'Monthly deep dive into learner engagement patterns and trends',
      template: 'Engagement Analytics Dashboard',
      frequency: 'monthly',
      nextRun: '2024-02-01T08:00:00Z',
      lastRun: '2024-01-01T08:00:00Z',
      isActive: true,
      recipients: ['<EMAIL>'],
      format: 'excel',
      deliveryMethod: 'both',
      createdBy: 'Jane Smith',
      runCount: 3,
    },
    {
      id: 3,
      name: 'Daily Activity Summary',
      description: 'Quick daily summary of platform activity and key metrics',
      template: 'Daily Activity Report',
      frequency: 'daily',
      nextRun: '2024-01-16T07:00:00Z',
      lastRun: '2024-01-15T07:00:00Z',
      isActive: false,
      recipients: ['<EMAIL>'],
      format: 'csv',
      deliveryMethod: 'email',
      createdBy: 'Mike Johnson',
      runCount: 45,
    },
    {
      id: 4,
      name: 'Quarterly Competency Review',
      description: 'Quarterly analysis of competency development and skill gaps',
      template: 'Competency Gap Analysis',
      frequency: 'quarterly',
      nextRun: '2024-04-01T10:00:00Z',
      lastRun: '2024-01-01T10:00:00Z',
      isActive: true,
      recipients: ['<EMAIL>', 'l&<EMAIL>'],
      format: 'pdf',
      deliveryMethod: 'email',
      createdBy: 'Sarah Wilson',
      runCount: 1,
    },
  ])

  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [selectedSchedule, setSelectedSchedule] = useState<ScheduledReport | null>(null)
  const [newSchedule, setNewSchedule] = useState({
    name: '',
    description: '',
    template: '',
    frequency: 'weekly' as const,
    nextRun: new Date(),
    recipients: '',
    format: 'pdf' as const,
    deliveryMethod: 'email' as const,
  })

  const toggleScheduleStatus = (id: number) => {
    setSchedules(prev => prev.map(schedule => 
      schedule.id === id 
        ? { ...schedule, isActive: !schedule.isActive }
        : schedule
    ))
  }

  const runScheduleNow = (id: number) => {
    // In a real app, this would trigger the report generation
    console.log('Running schedule:', id)
  }

  const deleteSchedule = (id: number) => {
    setSchedules(prev => prev.filter(schedule => schedule.id !== id))
  }

  const editSchedule = (schedule: ScheduledReport) => {
    setSelectedSchedule(schedule)
    setEditDialogOpen(true)
  }

  const getFrequencyColor = (frequency: string) => {
    switch (frequency) {
      case 'daily':
        return 'success'
      case 'weekly':
        return 'primary'
      case 'monthly':
        return 'warning'
      case 'quarterly':
        return 'secondary'
      default:
        return 'default'
    }
  }

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'pdf':
        return '📄'
      case 'excel':
        return '📊'
      case 'csv':
        return '📋'
      default:
        return '📄'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const getNextRunStatus = (nextRun: string, isActive: boolean) => {
    if (!isActive) return 'Paused'
    
    const now = new Date()
    const runDate = new Date(nextRun)
    const diffHours = (runDate.getTime() - now.getTime()) / (1000 * 60 * 60)
    
    if (diffHours < 0) return 'Overdue'
    if (diffHours < 24) return 'Due Soon'
    return 'Scheduled'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Due Soon':
        return 'warning'
      case 'Overdue':
        return 'error'
      case 'Paused':
        return 'default'
      default:
        return 'success'
    }
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box>
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h6" fontWeight="bold">
            Scheduled Reports ({schedules.length})
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setCreateDialogOpen(true)}
          >
            Schedule New Report
          </Button>
        </Box>

        {/* Summary Cards */}
        <Grid container spacing={2} mb={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <ScheduleIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                <Typography variant="h4" color="primary" fontWeight="bold">
                  {schedules.filter(s => s.isActive).length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Active Schedules
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <TimeIcon color="warning" sx={{ fontSize: 40, mb: 1 }} />
                <Typography variant="h4" color="warning.main" fontWeight="bold">
                  {schedules.filter(s => getNextRunStatus(s.nextRun, s.isActive) === 'Due Soon').length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Due Soon
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <EmailIcon color="info" sx={{ fontSize: 40, mb: 1 }} />
                <Typography variant="h4" color="info.main" fontWeight="bold">
                  {schedules.reduce((sum, s) => sum + s.runCount, 0)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Runs
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <NotificationIcon color="secondary" sx={{ fontSize: 40, mb: 1 }} />
                <Typography variant="h4" color="secondary.main" fontWeight="bold">
                  {schedules.reduce((sum, s) => sum + s.recipients.length, 0)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Recipients
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Scheduled Reports List */}
        <Grid container spacing={3}>
          {schedules.map((schedule, index) => (
            <Grid item xs={12} md={6} lg={4} key={schedule.id}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    opacity: schedule.isActive ? 1 : 0.7,
                  }}
                >
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                      <Box display="flex" alignItems="center">
                        <Avatar
                          sx={{
                            bgcolor: schedule.isActive ? theme.palette.primary.main : theme.palette.grey[400],
                            mr: 1,
                            width: 32,
                            height: 32,
                          }}
                        >
                          <ScheduleIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="h6" fontWeight="bold" sx={{ fontSize: '1rem' }}>
                            {schedule.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {schedule.template}
                          </Typography>
                        </Box>
                      </Box>
                      <IconButton size="small">
                        <MoreVertIcon />
                      </IconButton>
                    </Box>

                    <Typography variant="body2" color="text.secondary" mb={2}>
                      {schedule.description}
                    </Typography>

                    <Box display="flex" flexWrap="wrap" gap={1} mb={2}>
                      <Chip
                        label={schedule.frequency}
                        size="small"
                        color={getFrequencyColor(schedule.frequency) as any}
                        variant="outlined"
                      />
                      <Chip
                        label={getNextRunStatus(schedule.nextRun, schedule.isActive)}
                        size="small"
                        color={getStatusColor(getNextRunStatus(schedule.nextRun, schedule.isActive)) as any}
                        variant="filled"
                      />
                    </Box>

                    <Box mb={2}>
                      <Typography variant="caption" color="text.secondary" gutterBottom>
                        Next Run: {formatDate(schedule.nextRun)}
                      </Typography>
                      <br />
                      <Typography variant="caption" color="text.secondary">
                        Recipients: {schedule.recipients.length} • Format: {getFormatIcon(schedule.format)} {schedule.format.toUpperCase()}
                      </Typography>
                    </Box>

                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography variant="caption" color="text.secondary">
                        Runs: {schedule.runCount}
                      </Typography>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={schedule.isActive}
                            onChange={() => toggleScheduleStatus(schedule.id)}
                            size="small"
                          />
                        }
                        label=""
                        sx={{ m: 0 }}
                      />
                    </Box>
                  </CardContent>

                  <CardActions>
                    <Button
                      size="small"
                      startIcon={<PlayIcon />}
                      onClick={() => runScheduleNow(schedule.id)}
                      disabled={!schedule.isActive}
                    >
                      Run Now
                    </Button>
                    <Button
                      size="small"
                      startIcon={<EditIcon />}
                      onClick={() => editSchedule(schedule)}
                    >
                      Edit
                    </Button>
                    <IconButton
                      size="small"
                      onClick={() => deleteSchedule(schedule.id)}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </CardActions>
                </Card>
              </motion.div>
            </Grid>
          ))}
        </Grid>

        {/* Create Schedule Dialog */}
        <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>Schedule New Report</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Report Name"
                  value={newSchedule.name}
                  onChange={(e) => setNewSchedule(prev => ({ ...prev, name: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  multiline
                  rows={3}
                  value={newSchedule.description}
                  onChange={(e) => setNewSchedule(prev => ({ ...prev, description: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Template</InputLabel>
                  <Select
                    value={newSchedule.template}
                    onChange={(e) => setNewSchedule(prev => ({ ...prev, template: e.target.value }))}
                  >
                    <MenuItem value="Learner Progress Report">Learner Progress Report</MenuItem>
                    <MenuItem value="Engagement Analytics Dashboard">Engagement Analytics Dashboard</MenuItem>
                    <MenuItem value="Competency Gap Analysis">Competency Gap Analysis</MenuItem>
                    <MenuItem value="Monthly Training Summary">Monthly Training Summary</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Frequency</InputLabel>
                  <Select
                    value={newSchedule.frequency}
                    onChange={(e) => setNewSchedule(prev => ({ ...prev, frequency: e.target.value as any }))}
                  >
                    <MenuItem value="daily">Daily</MenuItem>
                    <MenuItem value="weekly">Weekly</MenuItem>
                    <MenuItem value="monthly">Monthly</MenuItem>
                    <MenuItem value="quarterly">Quarterly</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <DateTimePicker
                  label="First Run Date & Time"
                  value={newSchedule.nextRun}
                  onChange={(date) => setNewSchedule(prev => ({ ...prev, nextRun: date || new Date() }))}
                  slotProps={{ textField: { fullWidth: true } }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Format</InputLabel>
                  <Select
                    value={newSchedule.format}
                    onChange={(e) => setNewSchedule(prev => ({ ...prev, format: e.target.value as any }))}
                  >
                    <MenuItem value="pdf">PDF</MenuItem>
                    <MenuItem value="excel">Excel</MenuItem>
                    <MenuItem value="csv">CSV</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Recipients (comma-separated emails)"
                  value={newSchedule.recipients}
                  onChange={(e) => setNewSchedule(prev => ({ ...prev, recipients: e.target.value }))}
                  placeholder="<EMAIL>, <EMAIL>"
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
            <Button variant="contained" onClick={() => setCreateDialogOpen(false)}>
              Create Schedule
            </Button>
          </DialogActions>
        </Dialog>

        {/* Edit Schedule Dialog */}
        <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>Edit Scheduled Report</DialogTitle>
          <DialogContent>
            {selectedSchedule && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="body1">
                  Edit functionality would be implemented here for: {selectedSchedule.name}
                </Typography>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
            <Button variant="contained" onClick={() => setEditDialogOpen(false)}>
              Save Changes
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </LocalizationProvider>
  )
}
