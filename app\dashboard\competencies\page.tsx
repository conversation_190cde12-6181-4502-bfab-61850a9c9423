'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  <PERSON>ltip,
  <PERSON>ert,
  Skeleton,
  useTheme,
} from '@mui/material'
import {
  Add as AddIcon,
  Psychology as CompetenciesIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  School as SchoolIcon,
  EmojiEvents as BadgeIcon,
  Refresh as RefreshIcon,
  FileDownload as ExportIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'
import { useQuery } from '@tanstack/react-query'
import toast from 'react-hot-toast'

import { supabase } from '@/lib/supabase'
import { useAuthStore } from '@/lib/store'
import CompetencyDashboard from '@/components/competencies/CompetencyDashboard'
import CompetencyEditor from '@/components/competencies/CompetencyEditor'
import SkillGapDetector from '@/components/competencies/SkillGapDetector'
import CompetencyTrends from '@/components/competencies/CompetencyTrends'

interface CompetencyStats {
  totalCompetencies: number
  activeCompetencies: number
  mappedPaths: number
  learnersWithGaps: number
  averageCompletionRate: number
  trendingCompetencies: Array<{
    id: number
    name: string
    growth: number
  }>
}

export default function CompetenciesPage() {
  const theme = useTheme()
  const { user } = useAuthStore()
  const [editorOpen, setEditorOpen] = useState(false)
  const [selectedCompetency, setSelectedCompetency] = useState<any>(null)
  const [activeTab, setActiveTab] = useState('dashboard')

  // Fetch competency statistics
  const { data: stats, isLoading: statsLoading, refetch: refetchStats } = useQuery({
    queryKey: ['competency-stats', user?.tenant_id],
    queryFn: async (): Promise<CompetencyStats> => {
      if (!user?.tenant_id) throw new Error('No tenant ID')

      // Get total competencies
      const { count: totalCompetencies } = await supabase
        .from('competencies')
        .select('*', { count: 'exact', head: true })
        .eq('tenant_id', user.tenant_id)

      // Get active competencies
      const { count: activeCompetencies } = await supabase
        .from('competencies')
        .select('*', { count: 'exact', head: true })
        .eq('tenant_id', user.tenant_id)
        .eq('status', 'active')

      // Get mapped paths count
      const { count: mappedPaths } = await supabase
        .from('path_competencies')
        .select(`
          competency_id,
          competencies!inner(tenant_id)
        `, { count: 'exact', head: true })
        .eq('competencies.tenant_id', user.tenant_id)

      // Get learners with skill gaps
      const { count: learnersWithGaps } = await supabase
        .from('skill_gaps')
        .select(`
          learner_id,
          users!inner(tenant_id)
        `, { count: 'exact', head: true })
        .eq('users.tenant_id', user.tenant_id)
        .eq('status', 'open')

      // Calculate average completion rate (mock for now)
      const averageCompletionRate = 78.5

      // Get trending competencies (mock for now)
      const trendingCompetencies = [
        { id: 1, name: 'AI/Machine Learning', growth: 45.2 },
        { id: 2, name: 'Data Analysis', growth: 32.1 },
        { id: 3, name: 'Cloud Computing', growth: 28.7 },
      ]

      return {
        totalCompetencies: totalCompetencies || 0,
        activeCompetencies: activeCompetencies || 0,
        mappedPaths: mappedPaths || 0,
        learnersWithGaps: learnersWithGaps || 0,
        averageCompletionRate,
        trendingCompetencies,
      }
    },
    enabled: !!user?.tenant_id,
    refetchInterval: 30000, // Refresh every 30 seconds
  })

  const handleCreateCompetency = () => {
    setSelectedCompetency(null)
    setEditorOpen(true)
  }

  const handleEditCompetency = (competency: any) => {
    setSelectedCompetency(competency)
    setEditorOpen(true)
  }

  const handleCloseEditor = () => {
    setEditorOpen(false)
    setSelectedCompetency(null)
    refetchStats()
  }

  const handleExportData = async () => {
    try {
      // Export competencies data as CSV
      const { data: competencies } = await supabase
        .from('competencies')
        .select('*')
        .eq('tenant_id', user?.tenant_id)

      if (competencies) {
        const csvContent = [
          ['Name', 'Description', 'Category', 'Status', 'Created At'].join(','),
          ...competencies.map(comp => [
            comp.name,
            comp.description || '',
            comp.category || '',
            comp.status,
            new Date(comp.created_at).toLocaleDateString()
          ].join(','))
        ].join('\n')

        const blob = new Blob([csvContent], { type: 'text/csv' })
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `competencies-${new Date().toISOString().split('T')[0]}.csv`
        a.click()
        window.URL.revokeObjectURL(url)

        toast.success('Competencies data exported successfully')
      }
    } catch (error) {
      console.error('Export error:', error)
      toast.error('Failed to export data')
    }
  }

  const StatCard = ({ title, value, icon, trend, color = 'primary' }: any) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box>
              <Typography color="textSecondary" gutterBottom variant="body2">
                {title}
              </Typography>
              <Typography variant="h4" component="div" color={`${color}.main`}>
                {statsLoading ? <Skeleton width={60} /> : value}
              </Typography>
              {trend && (
                <Box display="flex" alignItems="center" mt={1}>
                  <TrendingUpIcon 
                    sx={{ 
                      fontSize: 16, 
                      color: trend > 0 ? 'success.main' : 'error.main',
                      mr: 0.5 
                    }} 
                  />
                  <Typography 
                    variant="body2" 
                    color={trend > 0 ? 'success.main' : 'error.main'}
                  >
                    {trend > 0 ? '+' : ''}{trend}%
                  </Typography>
                </Box>
              )}
            </Box>
            <Box
              sx={{
                backgroundColor: `${color}.light`,
                borderRadius: 2,
                p: 1.5,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              {icon}
            </Box>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  )

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Competencies Management
          </Typography>
          <Typography variant="body1" color="textSecondary">
            Define, map, and track skills and competencies across your organization
          </Typography>
        </Box>
        <Box display="flex" gap={1}>
          <Tooltip title="Refresh Data">
            <IconButton onClick={() => refetchStats()}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Export Data">
            <IconButton onClick={handleExportData}>
              <ExportIcon />
            </IconButton>
          </Tooltip>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateCompetency}
            sx={{ ml: 1 }}
          >
            Create Competency
          </Button>
        </Box>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Competencies"
            value={stats?.totalCompetencies || 0}
            icon={<CompetenciesIcon sx={{ color: 'primary.main' }} />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Active Competencies"
            value={stats?.activeCompetencies || 0}
            icon={<SchoolIcon sx={{ color: 'success.main' }} />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Mapped Learning Paths"
            value={stats?.mappedPaths || 0}
            icon={<AssessmentIcon sx={{ color: 'info.main' }} />}
            color="info"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Learners with Gaps"
            value={stats?.learnersWithGaps || 0}
            icon={<BadgeIcon sx={{ color: 'warning.main' }} />}
            color="warning"
          />
        </Grid>
      </Grid>

      {/* Trending Competencies Alert */}
      {stats?.trendingCompetencies && stats.trendingCompetencies.length > 0 && (
        <Alert 
          severity="info" 
          sx={{ mb: 3 }}
          action={
            <Button color="inherit" size="small">
              View Details
            </Button>
          }
        >
          <Typography variant="subtitle2" gutterBottom>
            Trending Competencies
          </Typography>
          <Box display="flex" gap={1} flexWrap="wrap">
            {stats.trendingCompetencies.map((comp) => (
              <Chip
                key={comp.id}
                label={`${comp.name} (+${comp.growth}%)`}
                size="small"
                color="info"
                variant="outlined"
              />
            ))}
          </Box>
        </Alert>
      )}

      {/* Main Content */}
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <CompetencyDashboard onEditCompetency={handleEditCompetency} />
        </Grid>
        
        <Grid item xs={12} md={6}>
          <SkillGapDetector />
        </Grid>
        
        <Grid item xs={12} md={6}>
          <CompetencyTrends />
        </Grid>
      </Grid>

      {/* Competency Editor Modal */}
      <CompetencyEditor
        open={editorOpen}
        competency={selectedCompetency}
        onClose={handleCloseEditor}
      />
    </Box>
  )
}
