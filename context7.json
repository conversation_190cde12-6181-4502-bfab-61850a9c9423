{"settings": {"title": "Supabase Auth", "project": "/supabase/auth", "description": "A JWT based API for managing users and issuing JWT tokens", "docsRepoUrl": "https://github.com/supabase/auth", "folders": [], "excludeFolders": ["archive", "archived", "old", "docs/old", "deprecated", "legacy", "previous", "outdated", "superseded", "i18n/zh*", "i18n/es*", "i18n/fr*", "i18n/de*", "i18n/ja*", "i18n/ko*", "i18n/ru*", "i18n/pt*", "i18n/it*", "i18n/ar*", "i18n/hi*", "i18n/tr*", "i18n/nl*", "i18n/pl*", "i18n/sv*", "i18n/vi*", "i18n/th*", "zh-cn", "zh-tw", "zh-hk", "zh-mo", "zh-sg"], "excludeFiles": ["CHANGELOG.md", "changelog.md", "CHANGELOG.mdx", "changelog.mdx", "LICENSE.md", "license.md", "CODE_OF_CONDUCT.md", "code_of_conduct.md"], "branch": "master", "stars": 1930, "trustScore": 9.5, "type": "repo"}}