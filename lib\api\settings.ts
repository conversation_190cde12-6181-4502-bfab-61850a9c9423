import {
  TenantSettings,
  GeneralSettings,
  BrandingSettings,
  SecuritySettings,
  Role,
  Permission,
  FeatureFlag,
  AISettings,
  AutomationRule,
  APIKey,
  Webhook,
  NotificationSettings,
  AnalyticsSettings,
  ComplianceSettings,
  SettingsAuditLog,
  FeatureFlagConfig,
  SettingsResponse,
  SettingsUpdateRequest
} from '@/lib/types/settings'

// Helper function to make API calls to Next.js API routes
const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const response = await fetch(`/api/settings${endpoint}`, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  })

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Unknown error' }))
    throw new Error(errorData.message || `HTTP ${response.status}`)
  }

  return response.json()
}

// Settings API
export const settingsApi = {
  // General Settings
  async getGeneralSettings(): Promise<GeneralSettings> {
    try {
      const response = await apiCall('/general')
      return response.data
    } catch (error) {
      console.error('Failed to get general settings:', error)
      throw error
    }
  },

  async updateGeneralSettings(settings: Partial<GeneralSettings>): Promise<SettingsResponse<GeneralSettings>> {
    try {
      const response = await apiCall('/general', {
        method: 'PUT',
        body: JSON.stringify(settings),
      })
      return response
    } catch (error) {
      console.error('Failed to update general settings:', error)
      throw error
    }
  },

  // Branding Settings
  async getBrandingSettings(): Promise<BrandingSettings> {
    try {
      const response = await apiCall('/branding')
      return response.data
    } catch (error) {
      console.error('Failed to get branding settings:', error)
      throw error
    }
  },

  async updateBrandingSettings(settings: Partial<BrandingSettings>): Promise<SettingsResponse<BrandingSettings>> {
    try {
      const response = await apiCall('/branding', {
        method: 'PUT',
        body: JSON.stringify(settings),
      })
      return response
    } catch (error) {
      console.error('Failed to update branding settings:', error)
      throw error
    }
  },

  // Security Settings
  async getSecuritySettings(): Promise<SecuritySettings> {
    try {
      const response = await apiCall('/security')
      return response.data
    } catch (error) {
      console.error('Failed to get security settings:', error)
      throw error
    }
  },

  async updateSecuritySettings(settings: Partial<SecuritySettings>): Promise<SettingsResponse<SecuritySettings>> {
    try {
      const response = await apiCall('/security', {
        method: 'PUT',
        body: JSON.stringify(settings),
      })
      return response
    } catch (error) {
      console.error('Failed to update security settings:', error)
      throw error
    }
  },

  // Role Management
  async getRoles(): Promise<Role[]> {
    try {
      const response = await apiCall('/roles')
      return response.data
    } catch (error) {
      console.error('Failed to get roles:', error)
      throw error
    }
  },

  async getPermissions(): Promise<Permission[]> {
    try {
      const response = await apiCall('/permissions')
      return response.data
    } catch (error) {
      console.error('Failed to get permissions:', error)
      throw error
    }
  },

  async createRole(roleData: Omit<Role, 'id' | 'created_at' | 'updated_at' | 'user_count'>): Promise<Role> {
    try {
      const response = await apiCall('/roles', {
        method: 'POST',
        body: JSON.stringify(roleData),
      })
      return response.data
    } catch (error) {
      console.error('Failed to create role:', error)
      throw error
    }
  },

  // Feature Flags
  async getFeatureFlags(): Promise<FeatureFlag[]> {
    try {
      const response = await apiCall('/feature-flags')
      return response.data
    } catch (error) {
      console.error('Failed to get feature flags:', error)
      throw error
    }
  },

  // TODO: Implement these functions with API routes
  async updateFeatureFlag(key: string, enabled: boolean, config?: Record<string, any>): Promise<SettingsResponse<FeatureFlag>> {
    throw new Error('Not implemented - will be added in future update')
  },

  async logSettingsChange(settingKey: string, oldValue: any, newValue: any, adminId: string): Promise<void> {
    // TODO: Implement with API route
    console.log('Settings change logged:', { settingKey, oldValue, newValue, adminId })
  },

  async getAuditLogs(limit = 50, offset = 0): Promise<SettingsAuditLog[]> {
    throw new Error('Not implemented - will be added in future update')
  },

  async uploadLogo(file: File): Promise<string> {
    throw new Error('Not implemented - will be added in future update')
  }
}
