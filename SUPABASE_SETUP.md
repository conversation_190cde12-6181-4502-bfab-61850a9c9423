# Supabase Setup Guide for ZenithLearn AI

This guide will help you set up Supabase for the ZenithLearn AI project.

## 1. Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign up/sign in
2. Click "New Project"
3. Choose your organization
4. Fill in project details:
   - **Name**: ZenithLearn AI
   - **Database Password**: Choose a strong password
   - **Region**: Choose the closest region to your users
5. Click "Create new project"

## 2. Get Your Project Credentials

Once your project is created:

1. Go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (something like `https://your-project.supabase.co`)
   - **anon public** key
   - **service_role** key (keep this secret!)

## 3. Update Environment Variables

Update your `.env.local` file with the real values:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
```

## 4. Run Database Migrations

### Option A: Using Supabase CLI (Recommended)

1. Install Supabase CLI:
   ```bash
   npm install -g supabase
   ```

2. Login to Supabase:
   ```bash
   supabase login
   ```

3. Link your project:
   ```bash
   supabase link --project-ref your-project-ref
   ```

4. Push the migrations:
   ```bash
   supabase db push
   ```

### Option B: Manual SQL Execution

1. Go to your Supabase dashboard
2. Navigate to **SQL Editor**
3. Run the SQL files in order:
   - `supabase/migrations/001_initial_schema.sql`
   - `supabase/migrations/002_rls_policies.sql`
   - `supabase/migrations/003_seed_data.sql`

## 5. Configure Authentication

1. Go to **Authentication** → **Settings**
2. Configure your site URL:
   - **Site URL**: `http://localhost:3000` (for development)
   - **Redirect URLs**: `http://localhost:3000/auth/callback`

3. Enable email authentication or configure OAuth providers as needed

## 6. Set up Storage

1. Go to **Storage**
2. Create the following buckets:
   - `avatars` (public)
   - `content` (public)
   - `uploads` (private)

3. Set up storage policies for each bucket

## 7. Configure Row Level Security (RLS)

The migration files include RLS policies, but you can verify them in:
- **Authentication** → **Policies**

## 8. Test the Connection

1. Restart your development server:
   ```bash
   npm run dev
   ```

2. Visit `http://localhost:3000`
3. The app should now connect to Supabase without errors

## 9. Create Your First Admin User

1. Go to **Authentication** → **Users**
2. Click "Add user"
3. Create an admin user with:
   - Email: your email
   - Password: secure password
   - Confirm: checked

4. The user will be automatically assigned to the default tenant with admin role

## 10. Optional: Set up Edge Functions

For AI features, you may want to set up Edge Functions:

1. Create functions directory:
   ```bash
   mkdir supabase/functions
   ```

2. Create AI-related functions:
   - `generate_learning_path`
   - `ai_chat`
   - `tag_content`

## Troubleshooting

### Common Issues

1. **Invalid URL Error**
   - Make sure your `NEXT_PUBLIC_SUPABASE_URL` is correct
   - Ensure it starts with `https://`

2. **Authentication Errors**
   - Check that your anon key is correct
   - Verify RLS policies are set up correctly

3. **Database Connection Issues**
   - Ensure your project is not paused
   - Check that migrations ran successfully

4. **CORS Errors**
   - Add your domain to the allowed origins in Supabase settings

5. **Migration Error: "cannot determine type of empty array"**
   - This occurs with `ARRAY[]` in PostgreSQL
   - **Solution**: Use the fixed migration file `003_seed_data.sql` (already fixed)
   - **Alternative**: Use `003_seed_data_simple.sql` for a minimal setup
   - **Manual fix**: Cast empty arrays like `ARRAY[]::TEXT[]`

### Getting Help

- Check the [Supabase Documentation](https://supabase.com/docs)
- Visit the [Supabase Discord](https://discord.supabase.com)
- Review the project's GitHub issues

## Security Checklist

Before going to production:

- [ ] Enable RLS on all tables
- [ ] Review and test all RLS policies
- [ ] Set up proper storage policies
- [ ] Configure SMTP for email authentication
- [ ] Set up database backups
- [ ] Enable audit logging
- [ ] Configure rate limiting
- [ ] Set up monitoring and alerts

## Production Deployment

When deploying to production:

1. Update environment variables in your hosting platform
2. Update site URL and redirect URLs in Supabase
3. Configure custom domain if needed
4. Set up SSL certificates
5. Enable database backups
6. Configure monitoring

---

Once Supabase is set up, you can start building the core features of ZenithLearn AI!
