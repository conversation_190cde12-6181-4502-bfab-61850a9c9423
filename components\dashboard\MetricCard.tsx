'use client'

import {
  Card,
  CardContent,
  Typography,
  Box,
  Skeleton,
  useTheme,
} from '@mui/material'
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
} from '@mui/icons-material'

interface MetricCardProps {
  title: string
  value: string | number
  change?: number
  icon: React.ReactNode
  color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info'
  isLoading?: boolean
}

export default function MetricCard({
  title,
  value,
  change,
  icon,
  color = 'primary',
  isLoading = false,
}: MetricCardProps) {
  const theme = useTheme()

  const getChangeColor = (change: number) => {
    if (change > 0) return theme.palette.success.main
    if (change < 0) return theme.palette.error.main
    return theme.palette.text.secondary
  }

  const getChangeIcon = (change: number) => {
    if (change > 0) return <TrendingUpIcon fontSize="small" />
    if (change < 0) return <TrendingDownIcon fontSize="small" />
    return null
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="flex-start">
            <Box flex={1}>
              <Skeleton variant="text" width="60%" height={20} />
              <Skeleton variant="text" width="40%" height={32} sx={{ mt: 1 }} />
              <Skeleton variant="text" width="30%" height={16} sx={{ mt: 1 }} />
            </Box>
            <Skeleton variant="circular" width={40} height={40} />
          </Box>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card
      sx={{
        height: '100%',
        transition: theme.transitions.create(['transform', 'box-shadow'], {
          duration: theme.transitions.duration.short,
        }),
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: theme.shadows[4],
        },
      }}
    >
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start">
          <Box flex={1}>
            <Typography
              variant="body2"
              color="text.secondary"
              gutterBottom
              fontWeight="medium"
            >
              {title}
            </Typography>
            
            <Typography
              variant="h4"
              fontWeight="bold"
              color="text.primary"
              sx={{ mb: 1 }}
            >
              {typeof value === 'number' ? value.toLocaleString() : value}
            </Typography>

            {change !== undefined && (
              <Box
                display="flex"
                alignItems="center"
                gap={0.5}
                sx={{ color: getChangeColor(change) }}
              >
                {getChangeIcon(change)}
                <Typography
                  variant="body2"
                  fontWeight="medium"
                  sx={{ color: getChangeColor(change) }}
                >
                  {change > 0 ? '+' : ''}{change.toFixed(1)}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  vs last month
                </Typography>
              </Box>
            )}
          </Box>

          <Box
            sx={{
              width: 48,
              height: 48,
              borderRadius: 2,
              backgroundColor: `${color}.main`,
              color: `${color}.contrastText`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              ml: 2,
            }}
          >
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  )
}
