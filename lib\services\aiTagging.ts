interface TagContentRequest {
  title: string
  description?: string
  content?: string
  type: 'video' | 'pdf' | 'ppt' | 'quiz' | 'text' | 'interactive'
}

interface TagContentResponse {
  tags: string[]
  category: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedDuration: number
  skills: string[]
  prerequisites: string[]
}

export class AITaggingService {
  private static readonly OPENAI_API_KEY = process.env.NEXT_PUBLIC_OPENAI_API_KEY

  static async generateTags(request: TagContentRequest): Promise<TagContentResponse> {
    try {
      // For development, return mock data if no API key
      if (!this.OPENAI_API_KEY) {
        return this.generateMockTags(request)
      }

      const prompt = this.createPrompt(request)
      
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.OPENAI_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4',
          messages: [
            {
              role: 'system',
              content: 'You are an expert educational content analyst. Provide accurate, helpful metadata for learning content.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 500,
          temperature: 0.3,
        }),
      })

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status}`)
      }

      const data = await response.json()
      const aiResponse = data.choices[0]?.message?.content

      if (!aiResponse) {
        throw new Error('No response from OpenAI')
      }

      const parsedResponse = JSON.parse(aiResponse)
      return this.sanitizeResponse(parsedResponse)

    } catch (error) {
      console.error('AI tagging error:', error)
      // Fallback to mock data on error
      return this.generateMockTags(request)
    }
  }

  private static createPrompt(request: TagContentRequest): string {
    const contentToAnalyze = [
      `Title: ${request.title}`,
      request.description ? `Description: ${request.description}` : '',
      request.content ? `Content: ${request.content.substring(0, 1000)}` : '',
      `Content Type: ${request.type}`
    ].filter(Boolean).join('\n\n')

    return `
Analyze the following educational content and provide structured metadata:

${contentToAnalyze}

Please provide a JSON response with the following structure:
{
  "tags": ["tag1", "tag2", "tag3"], // 3-8 relevant tags
  "category": "category_name", // Main category (e.g., "Programming", "Business", "Design")
  "difficulty": "beginner|intermediate|advanced", // Difficulty level
  "estimatedDuration": 30, // Estimated duration in minutes
  "skills": ["skill1", "skill2"], // 2-5 skills learners will gain
  "prerequisites": ["prereq1", "prereq2"] // 0-3 prerequisites (can be empty array)
}

Guidelines:
- Tags should be specific and relevant to the content
- Category should be broad but descriptive
- Difficulty should match the content complexity
- Duration should be realistic for the content type and complexity
- Skills should be actionable outcomes
- Prerequisites should only include essential prior knowledge

Respond only with valid JSON.`
  }

  private static generateMockTags(request: TagContentRequest): TagContentResponse {
    const { title, type } = request
    
    // Generate mock tags based on title keywords
    const titleWords = title.toLowerCase().split(' ')
    const commonTags = ['learning', 'education', 'training']
    const typeTags = {
      video: ['video', 'visual', 'multimedia'],
      pdf: ['document', 'reading', 'reference'],
      ppt: ['presentation', 'slides', 'visual'],
      quiz: ['assessment', 'evaluation', 'test'],
      text: ['reading', 'text', 'content'],
      interactive: ['interactive', 'hands-on', 'practice']
    }

    const tags = [
      ...titleWords.slice(0, 3),
      ...typeTags[type] || [],
      ...commonTags
    ].slice(0, 6)

    // Determine category based on title keywords
    const categories = {
      'programming': ['code', 'programming', 'development', 'software', 'javascript', 'python', 'react'],
      'business': ['business', 'management', 'leadership', 'strategy', 'marketing'],
      'design': ['design', 'ui', 'ux', 'graphics', 'visual'],
      'data': ['data', 'analytics', 'statistics', 'analysis'],
      'general': []
    }

    let category = 'General'
    for (const [cat, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => title.toLowerCase().includes(keyword))) {
        category = cat.charAt(0).toUpperCase() + cat.slice(1)
        break
      }
    }

    // Estimate difficulty based on title complexity
    const advancedKeywords = ['advanced', 'expert', 'master', 'deep', 'complex']
    const intermediateKeywords = ['intermediate', 'practical', 'applied', 'implementation']
    
    let difficulty: 'beginner' | 'intermediate' | 'advanced' = 'beginner'
    if (advancedKeywords.some(keyword => title.toLowerCase().includes(keyword))) {
      difficulty = 'advanced'
    } else if (intermediateKeywords.some(keyword => title.toLowerCase().includes(keyword))) {
      difficulty = 'intermediate'
    }

    // Estimate duration based on type and complexity
    const baseDurations = {
      video: 20,
      pdf: 15,
      ppt: 25,
      quiz: 10,
      text: 12,
      interactive: 30
    }

    const estimatedDuration = baseDurations[type] * (difficulty === 'advanced' ? 1.5 : difficulty === 'intermediate' ? 1.2 : 1)

    return {
      tags: tags.filter(Boolean),
      category,
      difficulty,
      estimatedDuration: Math.round(estimatedDuration),
      skills: [
        `${title.split(' ')[0]} Knowledge`,
        'Problem Solving',
        difficulty === 'advanced' ? 'Expert Application' : 'Practical Skills'
      ].filter(Boolean),
      prerequisites: difficulty === 'beginner' ? [] : ['Basic Knowledge', 'Foundational Concepts'].slice(0, difficulty === 'advanced' ? 2 : 1)
    }
  }

  private static sanitizeResponse(response: any): TagContentResponse {
    return {
      tags: Array.isArray(response.tags) 
        ? response.tags.slice(0, 8).filter((tag: any) => typeof tag === 'string' && tag.length > 0)
        : ['learning'],
      category: typeof response.category === 'string' 
        ? response.category 
        : 'General',
      difficulty: ['beginner', 'intermediate', 'advanced'].includes(response.difficulty)
        ? response.difficulty
        : 'beginner',
      estimatedDuration: typeof response.estimatedDuration === 'number' && response.estimatedDuration > 0
        ? Math.min(response.estimatedDuration, 480) // Max 8 hours
        : 30,
      skills: Array.isArray(response.skills)
        ? response.skills.slice(0, 5).filter((skill: any) => typeof skill === 'string' && skill.length > 0)
        : ['Knowledge'],
      prerequisites: Array.isArray(response.prerequisites)
        ? response.prerequisites.slice(0, 3).filter((prereq: any) => typeof prereq === 'string' && prereq.length > 0)
        : []
    }
  }

  // Utility method to extract content from different file types
  static async extractContentFromFile(file: File): Promise<string> {
    try {
      if (file.type.startsWith('text/')) {
        return await file.text()
      }
      
      // For other file types, return basic metadata
      return `File: ${file.name}, Size: ${file.size} bytes, Type: ${file.type}`
    } catch (error) {
      console.error('Error extracting content from file:', error)
      return `File: ${file.name}`
    }
  }

  // Utility method to extract metadata from YouTube URLs
  static extractYouTubeMetadata(url: string): { videoId: string | null; title: string } {
    const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/)
    const videoId = match ? match[1] : null
    
    return {
      videoId,
      title: videoId ? `YouTube Video: ${videoId}` : 'YouTube Video'
    }
  }
}
