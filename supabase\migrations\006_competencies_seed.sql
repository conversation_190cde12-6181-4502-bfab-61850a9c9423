-- Seed data for Competencies system
-- This provides sample data for development and testing

-- Insert sample competency categories
INSERT INTO competency_categories (tenant_id, name, description, color, icon) VALUES
(1, 'Technical Skills', 'Programming, software development, and technical competencies', '#1976d2', 'code'),
(1, 'Leadership', 'Management and leadership capabilities', '#2e7d32', 'group'),
(1, 'Communication', 'Verbal, written, and interpersonal communication skills', '#ed6c02', 'chat'),
(1, 'Data & Analytics', 'Data analysis, statistics, and business intelligence', '#9c27b0', 'analytics'),
(1, 'Project Management', 'Planning, execution, and delivery of projects', '#d32f2f', 'assignment'),
(1, 'Soft Skills', 'Personal effectiveness and interpersonal skills', '#795548', 'psychology'),
(1, 'Compliance', 'Regulatory and compliance knowledge', '#607d8b', 'gavel'),
(1, 'Sales & Marketing', 'Customer acquisition and marketing strategies', '#ff5722', 'trending_up');

-- Insert sample competencies
INSERT INTO competencies (tenant_id, name, description, category, tags, status, is_public, created_by, metadata) VALUES
(1, 'JavaScript Programming', 'Proficiency in JavaScript programming language including ES6+ features, async/await, and modern frameworks', 'Technical Skills', ARRAY['javascript', 'programming', 'frontend', 'es6'], 'active', true, (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), '{"level_definitions": {"1": "Basic syntax and variables", "2": "Functions and objects", "3": "Advanced concepts and frameworks", "4": "Expert-level optimization", "5": "Teaching and mentoring others"}}'),

(1, 'React Development', 'Building modern web applications using React.js, including hooks, state management, and component architecture', 'Technical Skills', ARRAY['react', 'frontend', 'javascript', 'components'], 'active', true, (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), '{"level_definitions": {"1": "Basic components", "2": "State and props", "3": "Hooks and context", "4": "Performance optimization", "5": "Architecture design"}}'),

(1, 'Data Analysis', 'Analyzing data to extract insights, create reports, and support decision-making processes', 'Data & Analytics', ARRAY['data', 'analytics', 'sql', 'excel', 'statistics'], 'active', true, (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), '{"level_definitions": {"1": "Basic data interpretation", "2": "Statistical analysis", "3": "Advanced analytics", "4": "Predictive modeling", "5": "Strategic insights"}}'),

(1, 'Team Leadership', 'Leading and managing teams effectively, including motivation, delegation, and performance management', 'Leadership', ARRAY['leadership', 'management', 'team', 'motivation'], 'active', true, (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), '{"level_definitions": {"1": "Self-awareness", "2": "Leading individuals", "3": "Team leadership", "4": "Organizational leadership", "5": "Strategic leadership"}}'),

(1, 'Project Management', 'Planning, executing, and delivering projects on time and within budget using various methodologies', 'Project Management', ARRAY['project', 'planning', 'agile', 'scrum', 'delivery'], 'active', true, (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), '{"level_definitions": {"1": "Basic project concepts", "2": "Project planning", "3": "Execution and monitoring", "4": "Advanced methodologies", "5": "Portfolio management"}}'),

(1, 'Public Speaking', 'Effective presentation and public speaking skills for various audiences and contexts', 'Communication', ARRAY['presentation', 'speaking', 'communication', 'confidence'], 'active', true, (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), '{"level_definitions": {"1": "Basic presentation", "2": "Structured delivery", "3": "Engaging presentations", "4": "Persuasive speaking", "5": "Thought leadership"}}'),

(1, 'Python Programming', 'Programming in Python for various applications including data science, web development, and automation', 'Technical Skills', ARRAY['python', 'programming', 'data-science', 'automation'], 'active', true, (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), '{"level_definitions": {"1": "Basic syntax", "2": "Object-oriented programming", "3": "Libraries and frameworks", "4": "Advanced patterns", "5": "Architecture and optimization"}}'),

(1, 'Customer Service', 'Providing excellent customer service and support across various channels and situations', 'Soft Skills', ARRAY['customer', 'service', 'support', 'communication'], 'active', true, (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), '{"level_definitions": {"1": "Basic service skills", "2": "Problem resolution", "3": "Proactive service", "4": "Service excellence", "5": "Service strategy"}}'),

(1, 'Machine Learning', 'Understanding and applying machine learning algorithms and techniques for data-driven solutions', 'Data & Analytics', ARRAY['ml', 'ai', 'algorithms', 'data-science', 'python'], 'active', true, (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), '{"level_definitions": {"1": "ML concepts", "2": "Basic algorithms", "3": "Model development", "4": "Advanced techniques", "5": "ML architecture"}}'),

(1, 'Agile Methodology', 'Understanding and implementing Agile development practices and principles', 'Project Management', ARRAY['agile', 'scrum', 'kanban', 'methodology'], 'active', true, (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), '{"level_definitions": {"1": "Agile principles", "2": "Scrum basics", "3": "Advanced practices", "4": "Scaling agile", "5": "Agile transformation"}}'),

(1, 'Digital Marketing', 'Creating and executing digital marketing strategies across various online channels', 'Sales & Marketing', ARRAY['marketing', 'digital', 'seo', 'social-media', 'analytics'], 'active', true, (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), '{"level_definitions": {"1": "Digital basics", "2": "Channel management", "3": "Campaign optimization", "4": "Strategic marketing", "5": "Marketing leadership"}}'),

(1, 'Financial Analysis', 'Analyzing financial data and creating reports to support business decision-making', 'Data & Analytics', ARRAY['finance', 'analysis', 'excel', 'modeling', 'reporting'], 'active', true, (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), '{"level_definitions": {"1": "Basic financial concepts", "2": "Financial statements", "3": "Advanced analysis", "4": "Financial modeling", "5": "Strategic finance"}}');

-- Insert sample competency badges
INSERT INTO competency_badges (tenant_id, competency_id, name, description, criteria_json, points_value, rarity) VALUES
(1, 1, 'JavaScript Novice', 'Completed basic JavaScript training', '{"requirements": ["complete_basic_course", "pass_quiz_80"]}', 100, 'common'),
(1, 1, 'JavaScript Expert', 'Mastered advanced JavaScript concepts', '{"requirements": ["complete_advanced_course", "build_project", "mentor_others"]}', 500, 'epic'),
(1, 2, 'React Developer', 'Built first React application', '{"requirements": ["complete_react_course", "build_app"]}', 200, 'rare'),
(1, 3, 'Data Analyst', 'Completed comprehensive data analysis training', '{"requirements": ["complete_course", "analyze_dataset", "present_findings"]}', 300, 'rare'),
(1, 4, 'Team Leader', 'Successfully led a team project', '{"requirements": ["lead_project", "team_feedback_positive", "achieve_goals"]}', 400, 'epic'),
(1, 5, 'Project Manager', 'Delivered project on time and budget', '{"requirements": ["manage_project", "on_time_delivery", "stakeholder_satisfaction"]}', 350, 'rare');

-- Insert sample competency goals
INSERT INTO competency_goals (competency_id, goal_type, reward_type, reward_value, criteria_json, is_active) VALUES
(1, 'mastery', 'points', 100, '{"level_required": 3, "assessment_score": 85}', true),
(2, 'completion', 'badge', 1, '{"courses_completed": 2, "projects_built": 1}', true),
(3, 'time_based', 'points', 50, '{"days_active": 30, "exercises_completed": 10}', true),
(4, 'mastery', 'badge', 1, '{"level_required": 4, "peer_reviews": 3}', true),
(5, 'completion', 'points', 200, '{"certification_earned": true, "project_delivered": true}', true);

-- Map competencies to existing learning paths (if any exist)
-- This assumes some learning paths already exist from previous seeds
INSERT INTO path_competencies (path_id, competency_id, level_gained) 
SELECT lp.id, c.id, 2
FROM learning_paths lp, competencies c
WHERE lp.tenant_id = 1 AND c.tenant_id = 1
AND (
  (lp.title ILIKE '%javascript%' AND c.name = 'JavaScript Programming') OR
  (lp.title ILIKE '%react%' AND c.name = 'React Development') OR
  (lp.title ILIKE '%python%' AND c.name = 'Python Programming') OR
  (lp.title ILIKE '%data%' AND c.name = 'Data Analysis') OR
  (lp.title ILIKE '%leadership%' AND c.name = 'Team Leadership') OR
  (lp.title ILIKE '%project%' AND c.name = 'Project Management')
)
ON CONFLICT (path_id, competency_id) DO NOTHING;

-- Insert sample user competencies for existing users
INSERT INTO user_competencies (user_id, competency_id, current_level, target_level, last_assessed_at)
SELECT u.id, c.id, 
  CASE 
    WHEN random() < 0.3 THEN 1
    WHEN random() < 0.6 THEN 2
    WHEN random() < 0.8 THEN 3
    ELSE 4
  END as current_level,
  CASE 
    WHEN random() < 0.5 THEN 4
    ELSE 5
  END as target_level,
  NOW() - (random() * interval '30 days')
FROM users u, competencies c
WHERE u.tenant_id = 1 AND c.tenant_id = 1
AND random() < 0.4 -- Only assign to 40% of users randomly
ON CONFLICT (user_id, competency_id) DO NOTHING;

-- Insert sample skill gaps
INSERT INTO skill_gaps (learner_id, competency_id, gap_score, confidence_score, suggested_path_id, status)
SELECT 
  u.id,
  c.id,
  0.3 + (random() * 0.6), -- Gap score between 0.3 and 0.9
  0.7 + (random() * 0.3), -- Confidence score between 0.7 and 1.0
  lp.id,
  CASE 
    WHEN random() < 0.8 THEN 'open'
    WHEN random() < 0.9 THEN 'addressed'
    ELSE 'dismissed'
  END
FROM users u
CROSS JOIN competencies c
LEFT JOIN learning_paths lp ON lp.tenant_id = c.tenant_id
WHERE u.tenant_id = 1 AND c.tenant_id = 1
AND random() < 0.1 -- Only create gaps for 10% of user-competency combinations
AND NOT EXISTS (
  SELECT 1 FROM user_competencies uc 
  WHERE uc.user_id = u.id AND uc.competency_id = c.id AND uc.current_level >= 3
)
ON CONFLICT (learner_id, competency_id) DO NOTHING;

-- Insert sample mapping suggestions
INSERT INTO mapping_suggestions (competency_id, path_id, confidence_score, reasoning, status)
SELECT 
  c.id,
  lp.id,
  0.6 + (random() * 0.4), -- Confidence between 0.6 and 1.0
  CASE 
    WHEN c.name ILIKE '%javascript%' THEN 'Strong correlation between JavaScript competency and web development path'
    WHEN c.name ILIKE '%data%' THEN 'Data analysis skills align well with analytics learning path'
    WHEN c.name ILIKE '%leadership%' THEN 'Leadership competency matches management training objectives'
    ELSE 'AI-detected correlation between competency and learning path content'
  END,
  CASE 
    WHEN random() < 0.6 THEN 'pending'
    WHEN random() < 0.8 THEN 'accepted'
    ELSE 'rejected'
  END
FROM competencies c
CROSS JOIN learning_paths lp
WHERE c.tenant_id = 1 AND lp.tenant_id = 1
AND random() < 0.2 -- Only suggest mappings for 20% of combinations
AND NOT EXISTS (
  SELECT 1 FROM path_competencies pc 
  WHERE pc.competency_id = c.id AND pc.path_id = lp.id
)
ON CONFLICT (competency_id, path_id) DO NOTHING;

-- Insert sample competency audit logs
INSERT INTO competency_audit (competency_id, action, admin_id, changes, created_at)
SELECT 
  c.id,
  CASE 
    WHEN random() < 0.4 THEN 'created'
    WHEN random() < 0.7 THEN 'updated'
    WHEN random() < 0.9 THEN 'mapped'
    ELSE 'unmapped'
  END,
  u.id,
  '{"field": "description", "old_value": "Previous description", "new_value": "Updated description"}',
  NOW() - (random() * interval '60 days')
FROM competencies c
CROSS JOIN users u
WHERE c.tenant_id = 1 AND u.tenant_id = 1
AND EXISTS (SELECT 1 FROM roles r WHERE r.id = u.role_id AND r.name IN ('admin', 'super_admin'))
AND random() < 0.3; -- Create audit logs for 30% of combinations

-- Insert sample learner points
INSERT INTO learner_points (learner_id, competency_id, points, reason, awarded_by)
SELECT 
  u.id,
  c.id,
  (random() * 500)::integer,
  CASE 
    WHEN random() < 0.3 THEN 'Completed assessment'
    WHEN random() < 0.6 THEN 'Finished learning path'
    WHEN random() < 0.8 THEN 'Peer recognition'
    ELSE 'Project completion'
  END,
  admin.id
FROM users u
CROSS JOIN competencies c
CROSS JOIN users admin
WHERE u.tenant_id = 1 AND c.tenant_id = 1
AND EXISTS (SELECT 1 FROM roles r WHERE r.id = admin.role_id AND r.name IN ('admin', 'super_admin'))
AND random() < 0.15 -- Award points to 15% of user-competency combinations
LIMIT 100; -- Limit to prevent too much data

-- Update competency statistics (this would normally be done by triggers or scheduled jobs)
UPDATE competencies SET 
  updated_at = NOW()
WHERE tenant_id = 1;
