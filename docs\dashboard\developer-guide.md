# Developer Guide - ZenithLearn AI Learner Dashboard

## 🏗 Architecture Overview

The ZenithLearn AI Learner Dashboard follows a modern, scalable architecture built with Next.js 14, React 18, and TypeScript. The system emphasizes performance, maintainability, and developer experience.

### System Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Layer     │    │   Database      │
│   (Next.js)     │◄──►│   (Next.js API) │◄──►│   (Supabase)    │
│                 │    │                 │    │                 │
│ • React 18      │    │ • REST APIs     │    │ • PostgreSQL    │
│ • TypeScript    │    │ • Validation    │    │ • RLS Policies  │
│ • Material-UI   │    │ • Auth Middleware│    │ • Real-time     │
│ • Framer Motion │    │ • Error Handling│    │ • Backups       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Design Patterns
- **Component Composition** - Reusable, composable React components
- **Custom Hooks** - Shared logic and state management
- **Provider Pattern** - Context-based state distribution
- **Repository Pattern** - Data access abstraction
- **Observer Pattern** - Real-time updates via Supabase

## 🔧 Development Setup

### Prerequisites
```bash
# Required software
Node.js 18+
npm or yarn
Git
VS Code (recommended)

# Recommended VS Code extensions
- ES7+ React/Redux/React-Native snippets
- TypeScript Importer
- Prettier - Code formatter
- ESLint
- Tailwind CSS IntelliSense
```

### Local Development Environment
```bash
# Clone repository
git clone https://github.com/your-org/zenithlearn-ai-learner.git
cd zenithlearn-ai-learner

# Install dependencies
npm install

# Setup environment
cp .env.example .env.local
# Configure your environment variables

# Start Supabase locally (optional)
npx supabase start

# Start development server
npm run dev

# Open browser
open http://localhost:3000/learner/dashboard
```

### Development Scripts
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "db:generate": "supabase gen types typescript --local > types/supabase.ts",
    "db:reset": "supabase db reset",
    "db:seed": "supabase db seed"
  }
}
```

## 📁 Project Structure

### Directory Organization
```
app/
├── components/
│   ├── dashboard/           # Dashboard-specific components
│   │   ├── PersonalizedWelcome.tsx
│   │   ├── EnhancedProgressOverview.tsx
│   │   └── creative/        # Innovative features
│   │       ├── GamifiedJourney.tsx
│   │       ├── AIInsights.tsx
│   │       └── MoodTracker.tsx
│   ├── ui/                  # Reusable UI components
│   └── layout/              # Layout components
├── hooks/                   # Custom React hooks
│   ├── useDashboardData.ts
│   ├── useGamification.ts
│   └── useMoodTracker.ts
├── lib/                     # Utility libraries
│   ├── supabase.ts
│   ├── utils.ts
│   └── validations.ts
├── types/                   # TypeScript type definitions
│   ├── dashboard.ts
│   ├── user.ts
│   └── supabase.ts
├── api/                     # API route handlers
│   └── dashboard/
│       ├── insights/
│       ├── mood/
│       └── challenges/
└── learner/
    └── dashboard/
        └── page.tsx         # Main dashboard page
```

### Component Architecture
```typescript
// Component structure pattern
interface ComponentProps {
  // Data props
  data?: ComponentData;
  user?: User | null;
  
  // Event handlers
  onUpdate?: (data: any) => void;
  onError?: (error: Error) => void;
  
  // Styling
  sx?: SxProps<Theme>;
  className?: string;
}

const Component: React.FC<ComponentProps> = ({
  data,
  user,
  onUpdate,
  onError,
  sx,
  className
}) => {
  // Component implementation
};
```

## 🎣 Custom Hooks

### Data Fetching Hooks
```typescript
// useDashboardData.ts
export function useDashboardData() {
  const { user } = useAuth();
  
  return useQuery({
    queryKey: ['dashboard', user?.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('dashboard_view')
        .select('*')
        .eq('user_id', user?.id)
        .single();
      
      if (error) throw error;
      return data;
    },
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 30 * 1000, // 30 seconds
  });
}

// useGamification.ts
export function useGamification() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  const { data: gamificationData } = useQuery({
    queryKey: ['gamification', user?.id],
    queryFn: fetchGamificationData,
    enabled: !!user,
  });
  
  const updateGamification = useMutation({
    mutationFn: updateGamificationData,
    onSuccess: () => {
      queryClient.invalidateQueries(['gamification', user?.id]);
    },
  });
  
  return {
    gamificationData,
    updateGamification: updateGamification.mutate,
    isLoading: updateGamification.isLoading,
  };
}
```

### Real-time Hooks
```typescript
// useRealtimeSubscription.ts
export function useRealtimeSubscription<T>(
  table: string,
  filter?: string,
  callback?: (payload: T) => void
) {
  const { user } = useAuth();
  
  useEffect(() => {
    if (!user) return;
    
    const subscription = supabase
      .channel(`${table}_changes`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table,
        filter: filter || `user_id=eq.${user.id}`,
      }, (payload) => {
        callback?.(payload.new as T);
      })
      .subscribe();
    
    return () => subscription.unsubscribe();
  }, [user, table, filter, callback]);
}
```

## 🎨 Styling & Theming

### Material-UI Theme Configuration
```typescript
// theme/index.ts
import { createTheme } from '@mui/material/styles';

export const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
      light: '#42a5f5',
      dark: '#1565c0',
    },
    secondary: {
      main: '#9c27b0',
      light: '#ba68c8',
      dark: '#7b1fa2',
    },
    background: {
      default: '#f5f5f5',
      paper: '#ffffff',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 700,
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.6,
    },
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 600,
        },
      },
    },
  },
});
```

### Responsive Design Patterns
```typescript
// Responsive breakpoints
const theme = useTheme();
const isMobile = useMediaQuery(theme.breakpoints.down('md'));
const isTablet = useMediaQuery(theme.breakpoints.between('md', 'lg'));

// Responsive component
const ResponsiveGrid = () => (
  <Grid container spacing={isMobile ? 2 : 3}>
    <Grid item xs={12} md={6} lg={4}>
      <Component />
    </Grid>
  </Grid>
);
```

## 🔄 State Management

### TanStack Query Configuration
```typescript
// lib/queryClient.ts
import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: (failureCount, error) => {
        if (error.status === 404) return false;
        return failureCount < 3;
      },
    },
    mutations: {
      retry: 1,
    },
  },
});
```

### Zustand Store Pattern
```typescript
// stores/dashboardStore.ts
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

interface DashboardState {
  // State
  customization: DashboardCustomization;
  notifications: Notification[];
  
  // Actions
  updateCustomization: (customization: Partial<DashboardCustomization>) => void;
  addNotification: (notification: Notification) => void;
  removeNotification: (id: string) => void;
}

export const useDashboardStore = create<DashboardState>()(
  devtools(
    persist(
      (set, get) => ({
        customization: defaultCustomization,
        notifications: [],
        
        updateCustomization: (updates) =>
          set((state) => ({
            customization: { ...state.customization, ...updates },
          })),
        
        addNotification: (notification) =>
          set((state) => ({
            notifications: [...state.notifications, notification],
          })),
        
        removeNotification: (id) =>
          set((state) => ({
            notifications: state.notifications.filter((n) => n.id !== id),
          })),
      }),
      {
        name: 'dashboard-storage',
        partialize: (state) => ({ customization: state.customization }),
      }
    )
  )
);
```

## 🧪 Testing Strategy

### Component Testing
```typescript
// __tests__/components/PersonalizedWelcome.test.tsx
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider } from '@mui/material/styles';
import { PersonalizedWelcome } from '../PersonalizedWelcome';
import { theme } from '../../theme';

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false } },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={theme}>
        {children}
      </ThemeProvider>
    </QueryClientProvider>
  );
};

describe('PersonalizedWelcome', () => {
  it('displays user name correctly', () => {
    const mockUser = { name: 'John Doe', id: '123' };
    const mockData = { enrolledCourses: 5, averageProgress: 75 };
    
    render(
      <PersonalizedWelcome user={mockUser} dashboardData={mockData} />,
      { wrapper: createWrapper() }
    );
    
    expect(screen.getByText(/Welcome back, John!/)).toBeInTheDocument();
    expect(screen.getByText('5')).toBeInTheDocument(); // Active courses
    expect(screen.getByText('75%')).toBeInTheDocument(); // Average progress
  });
});
```

### API Testing
```typescript
// __tests__/api/dashboard/insights.test.ts
import { createMocks } from 'node-mocks-http';
import handler from '../../../pages/api/dashboard/insights';

describe('/api/dashboard/insights', () => {
  it('returns insights for authenticated user', async () => {
    const { req, res } = createMocks({
      method: 'GET',
      headers: {
        authorization: 'Bearer valid_token',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    const data = JSON.parse(res._getData());
    expect(data.success).toBe(true);
    expect(data.data.insights).toBeInstanceOf(Array);
  });
});
```

## 🚀 Performance Optimization

### Code Splitting
```typescript
// Lazy load heavy components
import { lazy, Suspense } from 'react';

const GamifiedJourney = lazy(() => import('./creative/GamifiedJourney'));
const VoiceAssistant = lazy(() => import('./creative/VoiceAssistant'));

// Usage with fallback
<Suspense fallback={<ComponentSkeleton />}>
  <GamifiedJourney {...props} />
</Suspense>
```

### Memoization Patterns
```typescript
// Expensive calculations
const expensiveValue = useMemo(() => {
  return complexCalculation(data);
}, [data]);

// Component memoization
const MemoizedComponent = memo(({ data, onUpdate }) => {
  // Component implementation
}, (prevProps, nextProps) => {
  return prevProps.data.id === nextProps.data.id;
});

// Callback memoization
const handleUpdate = useCallback((newData) => {
  onUpdate(newData);
}, [onUpdate]);
```

### Image Optimization
```typescript
// Next.js Image component
import Image from 'next/image';

<Image
  src="/dashboard-hero.jpg"
  alt="Dashboard overview"
  width={800}
  height={400}
  priority
  placeholder="blur"
  blurDataURL="data:image/jpeg;base64,..."
/>
```

## 🔐 Security Best Practices

### Authentication Patterns
```typescript
// Protected route wrapper
export function withAuth<P extends object>(
  Component: React.ComponentType<P>
) {
  return function AuthenticatedComponent(props: P) {
    const { user, isLoading } = useAuth();
    
    if (isLoading) return <LoadingSpinner />;
    if (!user) return <LoginPrompt />;
    
    return <Component {...props} />;
  };
}

// Usage
export default withAuth(DashboardPage);
```

### Data Validation
```typescript
// Zod schema validation
import { z } from 'zod';

const moodEntrySchema = z.object({
  mood: z.enum(['excited', 'happy', 'neutral', 'tired', 'stressed']),
  energyLevel: z.number().min(1).max(10),
  notes: z.string().optional(),
});

// API validation
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const validatedData = moodEntrySchema.parse(body);
    
    // Process validated data
  } catch (error) {
    return Response.json({ error: 'Invalid data' }, { status: 400 });
  }
}
```

## 📊 Monitoring & Analytics

### Error Tracking
```typescript
// Error boundary
class DashboardErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }
  
  static getDerivedStateFromError(error) {
    return { hasError: true };
  }
  
  componentDidCatch(error, errorInfo) {
    console.error('Dashboard error:', error, errorInfo);
    // Send to error tracking service
  }
  
  render() {
    if (this.state.hasError) {
      return <ErrorFallback onRetry={() => this.setState({ hasError: false })} />;
    }
    
    return this.props.children;
  }
}
```

### Performance Monitoring
```typescript
// Performance tracking
export function trackPerformance(name: string, fn: () => Promise<any>) {
  return async (...args: any[]) => {
    const start = performance.now();
    try {
      const result = await fn(...args);
      const duration = performance.now() - start;
      
      // Log performance metrics
      console.log(`${name} took ${duration}ms`);
      
      return result;
    } catch (error) {
      const duration = performance.now() - start;
      console.error(`${name} failed after ${duration}ms:`, error);
      throw error;
    }
  };
}
```

---

**Next Steps**: Explore specific implementation details in the [Component Documentation](./components/README.md) or check the [API Reference](./api/README.md) for backend integration patterns.
