-- Fix the handle_new_user trigger to use the correct tenant_id
-- This script will diagnose and fix the user creation issue

-- Step 1: Check current database state
SELECT 'TENANTS' as table_name, id, name FROM tenants ORDER BY id;

-- Step 2: Check roles for all tenants
SELECT 'ROLES' as table_name, id, name, tenant_id, is_default FROM roles ORDER BY tenant_id, id;

-- Step 3: Check if there's a default role for tenant_id = 3
SELECT 'DEFAULT_ROLE_CHECK' as check_type,
       CASE
           WHEN EXISTS (SELECT 1 FROM roles WHERE tenant_id = 3 AND is_default = true)
           THEN 'Default role exists for tenant_id = 3'
           ELSE 'NO DEFAULT ROLE for tenant_id = 3'
       END as status;

-- Step 4: If no default role exists for tenant_id = 3, create one
INSERT INTO roles (name, tenant_id, is_default, permissions, created_at, updated_at)
SELECT 'Learner', 3, true, '["read_content", "submit_assignments"]'::jsonb, NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM roles WHERE tenant_id = 3 AND is_default = true);

-- Step 5: Update the handle_new_user function to use tenant_id = 3
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert user into users table with default role for Demo Organization (tenant_id = 3)
    INSERT INTO users (id, tenant_id, role_id, email, full_name)
    VALUES (
        NEW.id,
        3, -- Demo Organization tenant ID
        (SELECT id FROM roles WHERE tenant_id = 3 AND is_default = true LIMIT 1),
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1))
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 6: Recreate the trigger (drop first if exists)
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Step 7: Verify the setup
SELECT 'VERIFICATION' as check_type;
SELECT 'Tenants:' as info, COUNT(*) as count FROM tenants;
SELECT 'Roles for tenant 3:' as info, COUNT(*) as count FROM roles WHERE tenant_id = 3;
SELECT 'Default role for tenant 3:' as info, COUNT(*) as count FROM roles WHERE tenant_id = 3 AND is_default = true;

-- Step 8: Show the trigger
SELECT trigger_name, event_manipulation, action_statement
FROM information_schema.triggers
WHERE event_object_table = 'users' AND trigger_name = 'on_auth_user_created';
