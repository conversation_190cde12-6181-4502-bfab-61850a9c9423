-- Fix the handle_new_user trigger to use the correct tenant_id
-- This script will update the trigger to use tenant_id = 3 (Demo Organization)

-- First, let's check what tenants exist
SELECT id, name FROM tenants ORDER BY id;

-- Check what roles exist for tenant_id = 3
SELECT id, name, tenant_id, is_default FROM roles WHERE tenant_id = 3 ORDER BY id;

-- Update the handle_new_user function to use tenant_id = 3
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert user into users table with default role for Demo Organization (tenant_id = 3)
    INSERT INTO users (id, tenant_id, role_id, email, full_name)
    VALUES (
        NEW.id,
        3, -- Demo Organization tenant ID
        (SELECT id FROM roles WHERE tenant_id = 3 AND is_default = true LIMIT 1),
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1))
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate the trigger (drop first if exists)
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Verify the trigger was created
SELECT trigger_name, event_manipulation, action_statement 
FROM information_schema.triggers 
WHERE event_object_table = 'users' AND trigger_name = 'on_auth_user_created';
