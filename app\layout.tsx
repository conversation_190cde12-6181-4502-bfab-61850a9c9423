import * as React from 'react';
import { Metadata } from 'next';
import ThemeRegistry from '@/components/providers/ThemeRegistry';
import { Providers } from '@/components/providers/Providers';
import "./globals.css";

export const metadata: Metadata = {
  title: 'ZenithLearn AI',
  description: 'AI-powered Learning Management System',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <meta name="emotion-insertion-point" content="" />
      </head>
      <body>
        <ThemeRegistry>
          <Providers>
            {children}
          </Providers>
        </ThemeRegistry>
      </body>
    </html>
  );
}
