'use client'

import { useState, useCallback } from 'react'
import {
  Box,
  Card,
  CardContent,
  CardMedia,
  Typography,
  Chip,
  IconButton,
  Checkbox,
  Menu,
  MenuItem,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Tooltip,
  LinearProgress,
  Alert
} from '@mui/material'
import {
  MoreVert as MoreVertIcon,
  Download as DownloadIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  PictureAsPdf as PdfIcon,
  VideoLibrary as VideoIcon,
  Image as ImageIcon,
  AudioFile as AudioIcon,
  Description as DocumentIcon,
  School as ScormIcon,
  Quiz as QuizIcon
} from '@mui/icons-material'
import { motion, AnimatePresence } from 'framer-motion'
import { formatDistanceToNow } from 'date-fns'

import type { ContentItem } from '@/lib/types/content'

interface ContentGridProps {
  content: ContentItem[]
  viewMode: 'grid' | 'list'
  selectedItems: number[]
  onItemSelect: (item: ContentItem) => void
  onSelectionChange: (selectedIds: number[]) => void
  onBulkAction: (action: string) => void
  loading?: boolean
}

const ContentGrid = ({
  content,
  viewMode,
  selectedItems,
  onItemSelect,
  onSelectionChange,
  onBulkAction,
  loading = false
}: ContentGridProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [selectedItem, setSelectedItem] = useState<ContentItem | null>(null)

  const handleMenuOpen = useCallback((event: React.MouseEvent<HTMLElement>, item: ContentItem) => {
    event.stopPropagation()
    setAnchorEl(event.currentTarget)
    setSelectedItem(item)
  }, [])

  const handleMenuClose = useCallback(() => {
    setAnchorEl(null)
    setSelectedItem(null)
  }, [])

  const handleItemClick = useCallback((item: ContentItem) => {
    onItemSelect(item)
  }, [onItemSelect])

  const handleCheckboxChange = useCallback((itemId: number, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedItems, itemId])
    } else {
      onSelectionChange(selectedItems.filter(id => id !== itemId))
    }
  }, [selectedItems, onSelectionChange])

  const handleSelectAll = useCallback(() => {
    if (selectedItems.length === content.length) {
      onSelectionChange([])
    } else {
      onSelectionChange(content.map(item => item.id))
    }
  }, [content, selectedItems, onSelectionChange])

  const getContentIcon = (type: string) => {
    switch (type) {
      case 'pdf': return <PdfIcon />
      case 'video': return <VideoIcon />
      case 'image': return <ImageIcon />
      case 'audio': return <AudioIcon />
      case 'document': return <DocumentIcon />
      case 'scorm': return <ScormIcon />
      case 'quiz': return <QuizIcon />
      default: return <DocumentIcon />
    }
  }

  const getContentTypeColor = (type: string) => {
    switch (type) {
      case 'pdf': return 'error'
      case 'video': return 'primary'
      case 'image': return 'success'
      case 'audio': return 'warning'
      case 'document': return 'info'
      case 'scorm': return 'secondary'
      case 'quiz': return 'primary'
      default: return 'default'
    }
  }

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown size'
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  const formatDuration = (seconds?: number) => {
    if (!seconds) return null
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    return `${minutes}m`
  }

  if (loading) {
    return (
      <Box p={3}>
        <LinearProgress />
        <Typography variant="body2" color="text.secondary" mt={2}>
          Loading content...
        </Typography>
      </Box>
    )
  }

  if (content.length === 0) {
    return (
      <Box p={3}>
        <Alert severity="info">
          No content found. Upload your first content item to get started.
        </Alert>
      </Box>
    )
  }

  return (
    <Box>
      {/* Bulk Actions Bar */}
      {selectedItems.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
        >
          <Box
            sx={{
              p: 2,
              mb: 2,
              bgcolor: 'primary.light',
              borderRadius: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}
          >
            <Typography variant="body2" color="primary.contrastText">
              {selectedItems.length} item(s) selected
            </Typography>
            <Box>
              <IconButton
                size="small"
                onClick={() => onBulkAction('download')}
                sx={{ color: 'primary.contrastText' }}
              >
                <DownloadIcon />
              </IconButton>
              <IconButton
                size="small"
                onClick={() => onBulkAction('delete')}
                sx={{ color: 'primary.contrastText' }}
              >
                <DeleteIcon />
              </IconButton>
            </Box>
          </Box>
        </motion.div>
      )}

      {/* Content Display */}
      {viewMode === 'grid' ? (
        <Grid container spacing={2}>
          {content.map((item) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={item.id}>
              <motion.div
                layout
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.2 }}
              >
                <Card
                  sx={{
                    height: '100%',
                    cursor: 'pointer',
                    transition: 'all 0.2s',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: 4
                    },
                    border: selectedItems.includes(item.id) ? 2 : 0,
                    borderColor: 'primary.main'
                  }}
                  onClick={() => handleItemClick(item)}
                >
                  {/* Thumbnail/Icon */}
                  <Box sx={{ position: 'relative', height: 160, bgcolor: 'grey.100' }}>
                    {item.thumbnail_url ? (
                      <CardMedia
                        component="img"
                        height="160"
                        image={item.thumbnail_url}
                        alt={item.title}
                        sx={{ objectFit: 'cover' }}
                      />
                    ) : (
                      <Box
                        sx={{
                          height: '100%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: 'grey.500'
                        }}
                      >
                        {getContentIcon(item.type)}
                      </Box>
                    )}
                    
                    {/* Selection Checkbox */}
                    <Checkbox
                      checked={selectedItems.includes(item.id)}
                      onChange={(e) => handleCheckboxChange(item.id, e.target.checked)}
                      onClick={(e) => e.stopPropagation()}
                      sx={{
                        position: 'absolute',
                        top: 8,
                        left: 8,
                        bgcolor: 'rgba(255, 255, 255, 0.8)',
                        '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.9)' }
                      }}
                    />

                    {/* Actions Menu */}
                    <IconButton
                      size="small"
                      onClick={(e) => handleMenuOpen(e, item)}
                      sx={{
                        position: 'absolute',
                        top: 8,
                        right: 8,
                        bgcolor: 'rgba(255, 255, 255, 0.8)',
                        '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.9)' }
                      }}
                    >
                      <MoreVertIcon />
                    </IconButton>

                    {/* Content Type Badge */}
                    <Chip
                      label={item.type.toUpperCase()}
                      size="small"
                      color={getContentTypeColor(item.type) as any}
                      sx={{
                        position: 'absolute',
                        bottom: 8,
                        left: 8
                      }}
                    />
                  </Box>

                  <CardContent>
                    <Typography variant="h6" noWrap gutterBottom>
                      {item.title}
                    </Typography>
                    
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        mb: 1
                      }}
                    >
                      {item.description || 'No description available'}
                    </Typography>

                    {/* Tags */}
                    <Box sx={{ mb: 1 }}>
                      {item.tags.slice(0, 2).map((tag) => (
                        <Chip
                          key={tag}
                          label={tag}
                          size="small"
                          variant="outlined"
                          sx={{ mr: 0.5, mb: 0.5 }}
                        />
                      ))}
                      {item.tags.length > 2 && (
                        <Chip
                          label={`+${item.tags.length - 2}`}
                          size="small"
                          variant="outlined"
                          sx={{ mr: 0.5, mb: 0.5 }}
                        />
                      )}
                    </Box>

                    {/* Metadata */}
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="caption" color="text.secondary">
                        {formatFileSize(item.file_size)}
                      </Typography>
                      {item.duration && (
                        <Typography variant="caption" color="text.secondary">
                          {formatDuration(item.duration)}
                        </Typography>
                      )}
                    </Box>

                    <Typography variant="caption" color="text.secondary">
                      {formatDistanceToNow(new Date(item.created_at), { addSuffix: true })}
                    </Typography>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          ))}
        </Grid>
      ) : (
        <List>
          {content.map((item) => (
            <motion.div
              key={item.id}
              layout
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
            >
              <ListItem
                sx={{
                  border: 1,
                  borderColor: 'divider',
                  borderRadius: 1,
                  mb: 1,
                  cursor: 'pointer',
                  '&:hover': { bgcolor: 'action.hover' },
                  bgcolor: selectedItems.includes(item.id) ? 'action.selected' : 'transparent'
                }}
                onClick={() => handleItemClick(item)}
              >
                <Checkbox
                  checked={selectedItems.includes(item.id)}
                  onChange={(e) => handleCheckboxChange(item.id, e.target.checked)}
                  onClick={(e) => e.stopPropagation()}
                />
                
                <ListItemAvatar>
                  <Avatar sx={{ bgcolor: `${getContentTypeColor(item.type)}.main` }}>
                    {getContentIcon(item.type)}
                  </Avatar>
                </ListItemAvatar>

                <ListItemText
                  primary={item.title}
                  secondary={
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        {item.description}
                      </Typography>
                      <Box sx={{ mt: 1 }}>
                        {item.tags.slice(0, 3).map((tag) => (
                          <Chip
                            key={tag}
                            label={tag}
                            size="small"
                            variant="outlined"
                            sx={{ mr: 0.5 }}
                          />
                        ))}
                      </Box>
                    </Box>
                  }
                />

                <Box sx={{ textAlign: 'right', minWidth: 120 }}>
                  <Typography variant="body2" color="text.secondary">
                    {formatFileSize(item.file_size)}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {formatDistanceToNow(new Date(item.created_at), { addSuffix: true })}
                  </Typography>
                </Box>

                <IconButton
                  onClick={(e) => handleMenuOpen(e, item)}
                  sx={{ ml: 1 }}
                >
                  <MoreVertIcon />
                </IconButton>
              </ListItem>
            </motion.div>
          ))}
        </List>
      )}

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => { handleItemClick(selectedItem!); handleMenuClose() }}>
          <ViewIcon sx={{ mr: 1 }} />
          Preview
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <DownloadIcon sx={{ mr: 1 }} />
          Download
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <EditIcon sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem onClick={handleMenuClose} sx={{ color: 'error.main' }}>
          <DeleteIcon sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>
    </Box>
  )
}

export default ContentGrid
