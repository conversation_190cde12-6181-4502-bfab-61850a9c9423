'use client'

import React, { useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  Switch,
  FormControlLabel,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Alert,
  TextField,
  <PERSON>lider,
  Divider
} from '@mui/material'
import {
  ExpandMore as ExpandMoreIcon,
  SmartToy as AIIcon,
  Games as GamesIcon,
  Security as SecurityIcon,
  Analytics as AnalyticsIcon,
  Notifications as NotificationsIcon,
  Hub as IntegrationIcon
} from '@mui/icons-material'
import { motion } from 'framer-motion'

const featureCategories = [
  {
    title: 'AI & Machine Learning',
    icon: <AIIcon />,
    description: 'AI-powered features and automation',
    features: [
      { key: 'ai_content_tagging', name: 'AI Content Tagging', description: 'Automatically tag content using AI', enabled: true },
      { key: 'ai_recommendations', name: 'AI Recommendations', description: 'Personalized learning path suggestions', enabled: true },
      { key: 'ai_chat_assistant', name: 'AI Chat Assistant', description: 'AI-powered help and support', enabled: false },
      { key: 'ai_content_generation', name: 'AI Content Generation', description: 'Generate quiz questions and summaries', enabled: false }
    ]
  },
  {
    title: 'Gamification',
    icon: <GamesIcon />,
    description: 'Engagement and motivation features',
    features: [
      { key: 'badges_system', name: 'Badges & Achievements', description: 'Award badges for milestones', enabled: true },
      { key: 'leaderboards', name: 'Leaderboards', description: 'Competitive learning rankings', enabled: false },
      { key: 'points_system', name: 'Points System', description: 'Earn points for activities', enabled: true },
      { key: 'streaks', name: 'Learning Streaks', description: 'Track consecutive learning days', enabled: false }
    ]
  },
  {
    title: 'Security & Compliance',
    icon: <SecurityIcon />,
    description: 'Security and compliance features',
    features: [
      { key: 'audit_logging', name: 'Audit Logging', description: 'Detailed activity logs', enabled: true },
      { key: 'data_encryption', name: 'Data Encryption', description: 'Encrypt sensitive data at rest', enabled: true },
      { key: 'gdpr_compliance', name: 'GDPR Compliance Tools', description: 'Data export and deletion tools', enabled: true },
      { key: 'sso_integration', name: 'SSO Integration', description: 'Single sign-on support', enabled: false }
    ]
  },
  {
    title: 'Analytics & Reporting',
    icon: <AnalyticsIcon />,
    description: 'Data insights and reporting',
    features: [
      { key: 'advanced_analytics', name: 'Advanced Analytics', description: 'Detailed learning analytics', enabled: true },
      { key: 'custom_reports', name: 'Custom Reports', description: 'Build custom report dashboards', enabled: false },
      { key: 'real_time_tracking', name: 'Real-time Tracking', description: 'Live progress monitoring', enabled: true },
      { key: 'predictive_analytics', name: 'Predictive Analytics', description: 'Predict learner outcomes', enabled: false }
    ]
  },
  {
    title: 'Communication',
    icon: <NotificationsIcon />,
    description: 'Notification and messaging features',
    features: [
      { key: 'email_notifications', name: 'Email Notifications', description: 'Send email updates', enabled: true },
      { key: 'push_notifications', name: 'Push Notifications', description: 'Browser and mobile push', enabled: false },
      { key: 'sms_notifications', name: 'SMS Notifications', description: 'Text message alerts', enabled: false },
      { key: 'in_app_messaging', name: 'In-app Messaging', description: 'Direct messaging system', enabled: false }
    ]
  },
  {
    title: 'Integrations',
    icon: <IntegrationIcon />,
    description: 'Third-party integrations',
    features: [
      { key: 'lti_integration', name: 'LTI Integration', description: 'Learning Tools Interoperability', enabled: false },
      { key: 'api_access', name: 'API Access', description: 'REST API for integrations', enabled: true },
      { key: 'webhook_support', name: 'Webhook Support', description: 'Event-driven integrations', enabled: false },
      { key: 'scorm_support', name: 'SCORM Support', description: 'SCORM package compatibility', enabled: true }
    ]
  }
]

export default function FeatureToggles() {
  const [features, setFeatures] = useState(() => {
    const initialState: Record<string, boolean> = {}
    featureCategories.forEach(category => {
      category.features.forEach(feature => {
        initialState[feature.key] = feature.enabled
      })
    })
    return initialState
  })

  const handleFeatureToggle = (featureKey: string) => {
    setFeatures(prev => ({
      ...prev,
      [featureKey]: !prev[featureKey]
    }))
  }

  return (
    <Grid container spacing={3}>
      {/* Header */}
      <Grid item xs={12}>
        <Alert severity="info" sx={{ mb: 2 }}>
          Feature toggles allow you to enable or disable platform features. Some features may require additional configuration or have usage limits.
        </Alert>
      </Grid>

      {/* Feature Categories */}
      {featureCategories.map((category, index) => (
        <Grid item xs={12} key={category.title}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Accordion defaultExpanded={index === 0}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box display="flex" alignItems="center" width="100%">
                  <Box sx={{ mr: 2, color: 'primary.main' }}>
                    {category.icon}
                  </Box>
                  <Box flexGrow={1}>
                    <Typography variant="h6" fontWeight="bold">
                      {category.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {category.description}
                    </Typography>
                  </Box>
                  <Chip 
                    label={`${category.features.filter(f => features[f.key]).length}/${category.features.length} enabled`}
                    size="small"
                    color="primary"
                    variant="outlined"
                  />
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={2}>
                  {category.features.map((feature) => (
                    <Grid item xs={12} md={6} key={feature.key}>
                      <Card variant="outlined" sx={{ height: '100%' }}>
                        <CardContent>
                          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
                            <Typography variant="subtitle1" fontWeight="medium">
                              {feature.name}
                            </Typography>
                            <Switch
                              checked={features[feature.key]}
                              onChange={() => handleFeatureToggle(feature.key)}
                              color="primary"
                            />
                          </Box>
                          <Typography variant="body2" color="text.secondary">
                            {feature.description}
                          </Typography>
                          
                          {/* Feature-specific configuration */}
                          {features[feature.key] && feature.key === 'ai_recommendations' && (
                            <Box mt={2}>
                              <Divider sx={{ mb: 2 }} />
                              <Typography variant="body2" gutterBottom>
                                Recommendation Sensitivity
                              </Typography>
                              <Slider
                                defaultValue={0.7}
                                min={0.1}
                                max={1.0}
                                step={0.1}
                                marks
                                valueLabelDisplay="auto"
                                valueLabelFormat={(value) => `${Math.round(value * 100)}%`}
                              />
                            </Box>
                          )}
                          
                          {features[feature.key] && feature.key === 'points_system' && (
                            <Box mt={2}>
                              <Divider sx={{ mb: 2 }} />
                              <TextField
                                label="Points per completed lesson"
                                type="number"
                                defaultValue={10}
                                size="small"
                                fullWidth
                              />
                            </Box>
                          )}
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </AccordionDetails>
            </Accordion>
          </motion.div>
        </Grid>
      ))}

      {/* Summary */}
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Feature Summary
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6} md={3}>
                <Typography variant="h4" color="primary" fontWeight="bold">
                  {Object.values(features).filter(Boolean).length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Features Enabled
                </Typography>
              </Grid>
              <Grid item xs={6} md={3}>
                <Typography variant="h4" color="text.secondary" fontWeight="bold">
                  {Object.values(features).filter(v => !v).length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Features Disabled
                </Typography>
              </Grid>
              <Grid item xs={6} md={3}>
                <Typography variant="h4" color="success.main" fontWeight="bold">
                  {featureCategories.reduce((acc, cat) => acc + cat.features.length, 0)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Features
                </Typography>
              </Grid>
              <Grid item xs={6} md={3}>
                <Typography variant="h4" color="warning.main" fontWeight="bold">
                  {Math.round((Object.values(features).filter(Boolean).length / Object.values(features).length) * 100)}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Adoption Rate
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  )
}
