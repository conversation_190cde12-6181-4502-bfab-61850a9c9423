-- Row Level Security (RLS) Policies for Learner Groups Feature
-- This migration adds comprehensive RLS policies for secure multi-tenant group functionality

-- Enable RLS on all group-related tables
ALTER TABLE groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_milestones ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_member_activity ENABLE ROW LEVEL SECURITY;
ALTER TABLE learner_group_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_rewards ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE group_kudos ENABLE ROW LEVEL SECURITY;

-- Groups policies
CREATE POLICY "Users can view groups in their tenant" ON groups
  FOR SELECT USING (
    tenant_id = (auth.jwt() ->> 'tenant_id')::integer
  );

CREATE POLICY "Users can create groups in their tenant" ON groups
  FOR INSERT WITH CHECK (
    tenant_id = (auth.jwt() ->> 'tenant_id')::integer AND
    created_by = auth.uid()
  );

CREATE POLICY "Group admins can update their groups" ON groups
  FOR UPDATE USING (
    tenant_id = (auth.jwt() ->> 'tenant_id')::integer AND
    (created_by = auth.uid() OR 
     EXISTS (
       SELECT 1 FROM group_members 
       WHERE group_id = groups.id 
       AND learner_id = auth.uid() 
       AND role = 'admin'
     ))
  );

CREATE POLICY "Group admins can delete their groups" ON groups
  FOR DELETE USING (
    tenant_id = (auth.jwt() ->> 'tenant_id')::integer AND
    (created_by = auth.uid() OR 
     EXISTS (
       SELECT 1 FROM group_members 
       WHERE group_id = groups.id 
       AND learner_id = auth.uid() 
       AND role = 'admin'
     ))
  );

-- Group members policies
CREATE POLICY "Users can view group members for groups they belong to" ON group_members
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM groups g 
      WHERE g.id = group_members.group_id 
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    ) AND
    (group_id IN (
      SELECT group_id FROM group_members gm2 
      WHERE gm2.learner_id = auth.uid()
    ) OR 
    EXISTS (
      SELECT 1 FROM groups g 
      WHERE g.id = group_members.group_id 
      AND g.visibility = 'public'
    ))
  );

CREATE POLICY "Users can join public groups" ON group_members
  FOR INSERT WITH CHECK (
    learner_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM groups g 
      WHERE g.id = group_members.group_id 
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
      AND g.visibility IN ('public', 'invite_only')
    )
  );

CREATE POLICY "Group admins can manage members" ON group_members
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM groups g 
      WHERE g.id = group_members.group_id 
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    ) AND
    (EXISTS (
      SELECT 1 FROM group_members gm2 
      WHERE gm2.group_id = group_members.group_id 
      AND gm2.learner_id = auth.uid() 
      AND gm2.role IN ('admin', 'moderator')
    ) OR learner_id = auth.uid())
  );

-- Group assignments policies
CREATE POLICY "Group members can view assignments" ON group_assignments
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM group_members gm 
      JOIN groups g ON g.id = gm.group_id
      WHERE gm.group_id = group_assignments.group_id 
      AND gm.learner_id = auth.uid()
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    )
  );

CREATE POLICY "Group admins can manage assignments" ON group_assignments
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM group_members gm 
      JOIN groups g ON g.id = gm.group_id
      WHERE gm.group_id = group_assignments.group_id 
      AND gm.learner_id = auth.uid() 
      AND gm.role IN ('admin', 'moderator')
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    )
  );

-- Group progress policies
CREATE POLICY "Group members can view progress" ON group_progress
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM group_members gm 
      JOIN groups g ON g.id = gm.group_id
      WHERE gm.group_id = group_progress.group_id 
      AND gm.learner_id = auth.uid()
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    )
  );

CREATE POLICY "System can update group progress" ON group_progress
  FOR ALL USING (true);

-- Group messages policies
CREATE POLICY "Group members can view messages" ON group_messages
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM group_members gm 
      JOIN groups g ON g.id = gm.group_id
      WHERE gm.group_id = group_messages.group_id 
      AND gm.learner_id = auth.uid()
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    )
  );

CREATE POLICY "Group members can send messages" ON group_messages
  FOR INSERT WITH CHECK (
    sender_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM group_members gm 
      JOIN groups g ON g.id = gm.group_id
      WHERE gm.group_id = group_messages.group_id 
      AND gm.learner_id = auth.uid()
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    )
  );

CREATE POLICY "Users can update their own messages" ON group_messages
  FOR UPDATE USING (
    sender_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM group_members gm 
      JOIN groups g ON g.id = gm.group_id
      WHERE gm.group_id = group_messages.group_id 
      AND gm.learner_id = auth.uid()
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    )
  );

-- Group milestones policies
CREATE POLICY "Group members can view milestones" ON group_milestones
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM group_members gm 
      JOIN groups g ON g.id = gm.group_id
      WHERE gm.group_id = group_milestones.group_id 
      AND gm.learner_id = auth.uid()
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    )
  );

CREATE POLICY "Group admins can manage milestones" ON group_milestones
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM group_members gm 
      JOIN groups g ON g.id = gm.group_id
      WHERE gm.group_id = group_milestones.group_id 
      AND gm.learner_id = auth.uid() 
      AND gm.role IN ('admin', 'moderator')
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    )
  );

-- Group tasks policies
CREATE POLICY "Group members can view tasks" ON group_tasks
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM group_members gm 
      JOIN groups g ON g.id = gm.group_id
      WHERE gm.group_id = group_tasks.group_id 
      AND gm.learner_id = auth.uid()
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    )
  );

CREATE POLICY "Group admins can manage tasks" ON group_tasks
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM group_members gm 
      JOIN groups g ON g.id = gm.group_id
      WHERE gm.group_id = group_tasks.group_id 
      AND gm.learner_id = auth.uid() 
      AND gm.role IN ('admin', 'moderator')
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    )
  );

-- Task submissions policies
CREATE POLICY "Users can view their own submissions" ON task_submissions
  FOR SELECT USING (
    learner_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM group_tasks gt
      JOIN group_members gm ON gm.group_id = gt.group_id
      JOIN groups g ON g.id = gm.group_id
      WHERE gt.id = task_submissions.task_id 
      AND gm.learner_id = auth.uid() 
      AND gm.role IN ('admin', 'moderator')
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    )
  );

CREATE POLICY "Users can submit their own work" ON task_submissions
  FOR INSERT WITH CHECK (
    learner_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM group_tasks gt
      JOIN group_members gm ON gm.group_id = gt.group_id
      JOIN groups g ON g.id = gm.group_id
      WHERE gt.id = task_submissions.task_id 
      AND gm.learner_id = auth.uid()
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    )
  );

CREATE POLICY "Users can update their own submissions" ON task_submissions
  FOR UPDATE USING (
    learner_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM group_tasks gt
      JOIN group_members gm ON gm.group_id = gt.group_id
      JOIN groups g ON g.id = gm.group_id
      WHERE gt.id = task_submissions.task_id 
      AND gm.learner_id = auth.uid() 
      AND gm.role IN ('admin', 'moderator')
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    )
  );

-- Group member activity policies
CREATE POLICY "Users can manage their own activity" ON group_member_activity
  FOR ALL USING (
    learner_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM group_members gm 
      JOIN groups g ON g.id = gm.group_id
      WHERE gm.group_id = group_member_activity.group_id 
      AND gm.learner_id = auth.uid()
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    )
  );

-- Learner group preferences policies
CREATE POLICY "Users can manage their own preferences" ON learner_group_preferences
  FOR ALL USING (
    learner_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM group_members gm 
      JOIN groups g ON g.id = gm.group_id
      WHERE gm.group_id = learner_group_preferences.group_id 
      AND gm.learner_id = auth.uid()
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    )
  );

-- Group rewards policies
CREATE POLICY "Group members can view rewards" ON group_rewards
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM group_members gm 
      JOIN groups g ON g.id = gm.group_id
      WHERE gm.group_id = group_rewards.group_id 
      AND gm.learner_id = auth.uid()
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    )
  );

CREATE POLICY "Group admins can manage rewards" ON group_rewards
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM group_members gm 
      JOIN groups g ON g.id = gm.group_id
      WHERE gm.group_id = group_rewards.group_id 
      AND gm.learner_id = auth.uid() 
      AND gm.role IN ('admin', 'moderator')
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    )
  );

-- Group posts policies
CREATE POLICY "Group members can view posts" ON group_posts
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM group_members gm 
      JOIN groups g ON g.id = gm.group_id
      WHERE gm.group_id = group_posts.group_id 
      AND gm.learner_id = auth.uid()
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    )
  );

CREATE POLICY "Group members can create posts" ON group_posts
  FOR INSERT WITH CHECK (
    author_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM group_members gm 
      JOIN groups g ON g.id = gm.group_id
      WHERE gm.group_id = group_posts.group_id 
      AND gm.learner_id = auth.uid()
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    )
  );

CREATE POLICY "Users can update their own posts" ON group_posts
  FOR UPDATE USING (
    author_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM group_members gm 
      JOIN groups g ON g.id = gm.group_id
      WHERE gm.group_id = group_posts.group_id 
      AND gm.learner_id = auth.uid()
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    )
  );

-- Group kudos policies
CREATE POLICY "Group members can view kudos" ON group_kudos
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM group_members gm 
      JOIN groups g ON g.id = gm.group_id
      WHERE gm.group_id = group_kudos.group_id 
      AND gm.learner_id = auth.uid()
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    )
  );

CREATE POLICY "Group members can give kudos" ON group_kudos
  FOR INSERT WITH CHECK (
    giver_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM group_members gm 
      JOIN groups g ON g.id = gm.group_id
      WHERE gm.group_id = group_kudos.group_id 
      AND gm.learner_id = auth.uid()
      AND g.tenant_id = (auth.jwt() ->> 'tenant_id')::integer
    )
  );
