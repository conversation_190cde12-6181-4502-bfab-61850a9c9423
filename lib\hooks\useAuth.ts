import { useCallback, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'
import { AuthService } from '@/lib/auth'
import { useAuthStore } from '@/lib/store'

/**
 * Custom hook for authentication management
 * Provides a convenient interface to the authentication system
 * Integrates with AuthService, useAuthStore, and AuthProvider
 */
export const useAuth = () => {
  const router = useRouter()
  const { 
    user, 
    tenant, 
    isLoading, 
    setUser, 
    setTenant, 
    setLoading, 
    logout: storeLogout 
  } = useAuthStore()

  // Sign in function
  const signIn = useCallback(async (email: string, password: string) => {
    try {
      setLoading(true)
      const { data, error } = await AuthService.signIn(email, password)
      
      if (error) {
        const errorMessage = (error as any)?.message || 'Authentication failed'
        toast.error(errorMessage)
        return { success: false, error: errorMessage }
      }

      toast.success('Welcome back!')
      return { success: true, data }
    } catch (err) {
      const errorMessage = 'An unexpected error occurred. Please try again.'
      toast.error(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [setLoading])

  // Sign up function
  const signUp = useCallback(async (
    email: string, 
    password: string, 
    fullName: string, 
    tenantId: number
  ) => {
    try {
      setLoading(true)
      const { data, error } = await AuthService.signUp(email, password, fullName, tenantId)
      
      if (error) {
        const errorMessage = (error as any)?.message || 'Registration failed'
        toast.error(errorMessage)
        return { success: false, error: errorMessage }
      }

      toast.success('Registration successful! Please check your email to verify your account.')
      return { success: true, data }
    } catch (err) {
      const errorMessage = 'An unexpected error occurred. Please try again.'
      toast.error(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [setLoading])

  // Sign out function
  const signOut = useCallback(async () => {
    try {
      setLoading(true)
      const { error } = await AuthService.signOut()
      
      if (error) {
        const errorMessage = (error as any)?.message || 'Sign out failed'
        toast.error(errorMessage)
        return { success: false, error: errorMessage }
      }

      toast.success('Signed out successfully')
      router.push('/login')
      return { success: true }
    } catch (err) {
      const errorMessage = 'An unexpected error occurred during sign out.'
      toast.error(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [setLoading, router])

  // Update user profile
  const updateProfile = useCallback(async (updates: any) => {
    if (!user?.id) {
      toast.error('User not authenticated')
      return { success: false, error: 'User not authenticated' }
    }

    try {
      setLoading(true)
      const updatedProfile = await AuthService.updateUserProfile(user.id, updates)
      
      if (updatedProfile) {
        setUser(updatedProfile)
        toast.success('Profile updated successfully')
        return { success: true, data: updatedProfile }
      }
      
      return { success: false, error: 'Failed to update profile' }
    } catch (err) {
      const errorMessage = 'Failed to update profile. Please try again.'
      toast.error(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [user?.id, setUser, setLoading])

  // Upload avatar
  const uploadAvatar = useCallback(async (file: File) => {
    if (!user?.id) {
      toast.error('User not authenticated')
      return { success: false, error: 'User not authenticated' }
    }

    try {
      setLoading(true)
      const { url, error } = await AuthService.uploadAvatar(user.id, file)
      
      if (error) {
        const errorMessage = (error as any)?.message || 'Avatar upload failed'
        toast.error(errorMessage)
        return { success: false, error: errorMessage }
      }

      if (url) {
        // Update user profile with new avatar URL
        const updatedProfile = await AuthService.updateUserProfile(user.id, { avatar_url: url })
        if (updatedProfile) {
          setUser(updatedProfile)
        }
        toast.success('Avatar updated successfully')
        return { success: true, url }
      }
      
      return { success: false, error: 'Failed to upload avatar' }
    } catch (err) {
      const errorMessage = 'Failed to upload avatar. Please try again.'
      toast.error(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }, [user?.id, setUser, setLoading])

  // Check if user has specific permission
  const hasPermission = useCallback((permission: string) => {
    if (!user?.permissions) return false
    return user.permissions.includes(permission) || user.permissions.includes('admin')
  }, [user?.permissions])

  // Check if user has any of the specified roles
  const hasRole = useCallback((roles: string | string[]) => {
    if (!user?.role) return false
    const roleArray = Array.isArray(roles) ? roles : [roles]
    return roleArray.includes(user.role)
  }, [user?.role])

  // Check if user is authenticated
  const isAuthenticated = Boolean(user && !isLoading)

  // Check if user is admin
  const isAdmin = user?.role === 'admin'

  // Check if user is instructor
  const isInstructor = user?.role === 'instructor'

  // Check if user is learner
  const isLearner = user?.role === 'learner'

  return {
    // State
    user,
    tenant,
    isLoading,
    isAuthenticated,
    isAdmin,
    isInstructor,
    isLearner,
    
    // Actions
    signIn,
    signUp,
    signOut,
    updateProfile,
    uploadAvatar,
    
    // Utilities
    hasPermission,
    hasRole,
  }
}

export default useAuth
