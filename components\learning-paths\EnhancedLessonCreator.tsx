'use client'

import React, { useState, useCallback } from 'react'
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Chip,
  Grid,
  Card,
  CardContent,
  CardMedia,
  IconButton,
  LinearProgress,
  Alert,
  Tabs,
  Tab,
  Paper,
  Divider
} from '@mui/material'
import {
  CloudUpload as UploadIcon,
  YouTube as YouTubeIcon,
  PictureAsPdf as PdfIcon,
  Slideshow as PptIcon,
  Link as LinkIcon,
  Preview as PreviewIcon,
  Delete as DeleteIcon,
  AutoAwesome as AIIcon,
  Close as CloseIcon
} from '@mui/icons-material'
import { useDropzone } from 'react-dropzone'
import { motion, AnimatePresence } from 'framer-motion'
import { AITaggingService } from '@/lib/services/aiTagging'
import { StorageService, UploadProgress, FileUploadResult } from '@/lib/services/storageService'

interface LessonContent {
  type: 'video' | 'pdf' | 'ppt' | 'quiz' | 'text' | 'interactive'
  title: string
  description: string
  file?: File
  url?: string
  uploadResult?: FileUploadResult
  metadata?: any
  preview?: {
    thumbnail?: string
    duration?: number
    size?: number
    pages?: number
  }
}

interface EnhancedLessonCreatorProps {
  open: boolean
  onClose: () => void
  onSave: (lesson: LessonContent) => void
  moduleId: string
  editingLesson?: any
}

const SUPPORTED_FILE_TYPES = {
  video: ['video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov'],
  pdf: ['application/pdf'],
  ppt: ['application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'],
  document: ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
}

const MAX_FILE_SIZE = 100 * 1024 * 1024 // 100MB

export default function EnhancedLessonCreator({
  open,
  onClose,
  onSave,
  moduleId,
  editingLesson
}: EnhancedLessonCreatorProps) {
  const [activeTab, setActiveTab] = useState(0)
  const [lessonData, setLessonData] = useState<LessonContent>({
    type: 'video',
    title: '',
    description: '',
    metadata: {}
  })
  const [uploadProgress, setUploadProgress] = useState<UploadProgress>({ loaded: 0, total: 0, percentage: 0 })
  const [isUploading, setIsUploading] = useState(false)
  const [errors, setErrors] = useState<string[]>([])
  const [aiTags, setAiTags] = useState<string[]>([])
  const [isGeneratingTags, setIsGeneratingTags] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState<FileUploadResult[]>([])

  const uploadFileToStorage = useCallback(async (file: File) => {
    try {
      setIsUploading(true)
      setErrors([])

      const uploadResult = await StorageService.uploadFile(file, (progress) => {
        setUploadProgress(progress)
      })

      setLessonData(prev => ({
        ...prev,
        uploadResult,
        url: uploadResult.url,
        metadata: {
          ...prev.metadata,
          storagePath: uploadResult.path,
          uploadedAt: new Date().toISOString()
        }
      }))

      setUploadedFiles(prev => [...prev, uploadResult])
      
    } catch (error) {
      console.error('Upload error:', error)
      setErrors([error instanceof Error ? error.message : 'Upload failed'])
    } finally {
      setIsUploading(false)
      setUploadProgress({ loaded: 0, total: 0, percentage: 0 })
    }
  }, [])

  const generatePreview = useCallback(async (file: File, type: LessonContent['type']) => {
    try {
      if (type === 'video') {
        const thumbnail = await StorageService.generateThumbnail(file)
        const video = document.createElement('video')
        video.src = URL.createObjectURL(file)
        video.onloadedmetadata = () => {
          setLessonData(prev => ({
            ...prev,
            preview: {
              thumbnail: thumbnail || undefined,
              duration: Math.round(video.duration),
              size: file.size
            }
          }))
          URL.revokeObjectURL(video.src)
        }
      } else if (type === 'pdf') {
        setLessonData(prev => ({
          ...prev,
          preview: {
            size: file.size,
            pages: 0 // Would need PDF.js to get actual page count
          }
        }))
      } else {
        setLessonData(prev => ({
          ...prev,
          preview: {
            size: file.size
          }
        }))
      }
    } catch (error) {
      console.error('Error generating preview:', error)
    }
  }, [])

  // File upload with drag and drop
  const onDrop = useCallback(async (acceptedFiles: File[], rejectedFiles: any[]) => {
    setErrors([])
    
    if (rejectedFiles.length > 0) {
      const errorMessages = rejectedFiles.map(({ file, errors }) => 
        `${file.name}: ${errors.map((e: any) => e.message).join(', ')}`
      )
      setErrors(errorMessages)
      return
    }

    const file = acceptedFiles[0]
    if (!file) return

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      setErrors([`File size must be less than ${MAX_FILE_SIZE / (1024 * 1024)}MB`])
      return
    }

    // Determine lesson type based on file type
    let lessonType: LessonContent['type'] = 'text'
    if (SUPPORTED_FILE_TYPES.video.includes(file.type)) {
      lessonType = 'video'
    } else if (SUPPORTED_FILE_TYPES.pdf.includes(file.type)) {
      lessonType = 'pdf'
    } else if (SUPPORTED_FILE_TYPES.ppt.includes(file.type)) {
      lessonType = 'ppt'
    }

    setLessonData(prev => ({
      ...prev,
      type: lessonType,
      file,
      title: prev.title || file.name.replace(/\.[^/.]+$/, ''),
      metadata: {
        ...prev.metadata,
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type
      }
    }))

    // Generate preview and upload file with proper error handling
    try {
      await generatePreview(file, lessonType)
      await uploadFileToStorage(file)
    } catch (error) {
      console.error('Error processing file:', error)
      setErrors([error instanceof Error ? error.message : 'Failed to process file'])
    }
  }, [generatePreview, uploadFileToStorage])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'video/*': SUPPORTED_FILE_TYPES.video,
      'application/pdf': SUPPORTED_FILE_TYPES.pdf,
      'application/vnd.ms-powerpoint': SUPPORTED_FILE_TYPES.ppt,
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': SUPPORTED_FILE_TYPES.ppt
    },
    maxFiles: 1,
    maxSize: MAX_FILE_SIZE
  })

  const generateAITags = async () => {
    if (!lessonData.title && !lessonData.description) return
    
    setIsGeneratingTags(true)
    try {
      let content = ''
      if (lessonData.file) {
        content = await AITaggingService.extractContentFromFile(lessonData.file)
      } else if (lessonData.url) {
        const youtubeData = AITaggingService.extractYouTubeMetadata(lessonData.url)
        content = youtubeData.title
      }

      const tagResponse = await AITaggingService.generateTags({
        title: lessonData.title,
        description: lessonData.description,
        content,
        type: lessonData.type
      })
      
      setAiTags(tagResponse.tags)
      
      // Auto-update lesson metadata with AI suggestions
      setLessonData(prev => ({
        ...prev,
        metadata: {
          ...prev.metadata,
          estimatedDuration: tagResponse.estimatedDuration,
          category: tagResponse.category,
          difficulty: tagResponse.difficulty,
          skills: tagResponse.skills,
          prerequisites: tagResponse.prerequisites,
          aiGenerated: true
        }
      }))
      
      setIsGeneratingTags(false)
    } catch (error) {
      console.error('Error generating AI tags:', error)
      setIsGeneratingTags(false)
    }
  }

  const handleSave = async () => {
    if (!lessonData.title.trim()) {
      setErrors(['Title is required'])
      return
    }

    // If there's a file but no upload result, upload it first
    if (lessonData.file && !lessonData.uploadResult) {
      await uploadFileToStorage(lessonData.file)
    }

    try {
      onSave(lessonData)
      onClose()
      
      // Reset form
      setLessonData({
        type: 'video',
        title: '',
        description: '',
        metadata: {}
      })
      setUploadedFiles([])
      setAiTags([])
      setErrors([])
    } catch (error) {
      console.error('Error saving lesson:', error)
      setErrors(['Failed to save lesson'])
    }
  }

  const renderUploadArea = () => (
    <Paper
      {...getRootProps()}
      sx={{
        p: 4,
        border: '2px dashed',
        borderColor: isDragActive ? 'primary.main' : 'grey.300',
        bgcolor: isDragActive ? 'primary.50' : 'grey.50',
        cursor: 'pointer',
        textAlign: 'center',
        transition: 'all 0.3s ease'
      }}
    >
      <input {...getInputProps()} />
      <UploadIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
      <Typography variant="h6" gutterBottom>
        {isDragActive ? 'Drop files here' : 'Drag & drop files or click to browse'}
      </Typography>
      <Typography variant="body2" color="text.secondary">
        Supports: Video (MP4, WebM), PDF, PowerPoint (PPT, PPTX)
      </Typography>
      <Typography variant="caption" color="text.secondary">
        Maximum file size: 100MB
      </Typography>
    </Paper>
  )

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { minHeight: '70vh' }
      }}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">
            {editingLesson ? 'Edit Lesson' : 'Create New Lesson'}
          </Typography>
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
            <Tab label="Upload File" icon={<UploadIcon />} />
            <Tab label="YouTube Link" icon={<YouTubeIcon />} />
            <Tab label="External Link" icon={<LinkIcon />} />
          </Tabs>
        </Box>

        {errors.length > 0 && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {errors.map((error, index) => (
              <div key={index}>{error}</div>
            ))}
          </Alert>
        )}

        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            {activeTab === 0 && renderUploadArea()}
          </motion.div>
        </AnimatePresence>

        <Grid container spacing={2} sx={{ mt: 2 }}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Lesson Title"
              value={lessonData.title}
              onChange={(e) => setLessonData(prev => ({ ...prev, title: e.target.value }))}
              required
            />
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Description"
              multiline
              rows={3}
              value={lessonData.description}
              onChange={(e) => setLessonData(prev => ({ ...prev, description: e.target.value }))}
            />
          </Grid>
        </Grid>

        <Box sx={{ mt: 3 }}>
          <Box display="flex" alignItems="center" gap={2} mb={2}>
            <Typography variant="subtitle2">AI-Generated Tags</Typography>
            <Button
              size="small"
              startIcon={<AIIcon />}
              onClick={generateAITags}
              disabled={isGeneratingTags}
            >
              {isGeneratingTags ? 'Generating...' : 'Generate Tags'}
            </Button>
          </Box>
          
          <Box display="flex" flexWrap="wrap" gap={1}>
            {aiTags.map((tag, index) => (
              <Chip
                key={index}
                label={tag}
                size="small"
                onClick={() => {
                  // Add tag to lesson metadata
                  setLessonData(prev => ({
                    ...prev,
                    metadata: {
                      ...prev.metadata,
                      tags: [...(prev.metadata?.tags || []), tag]
                    }
                  }))
                }}
              />
            ))}
          </Box>
        </Box>

        {isUploading && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" gutterBottom>
              Uploading... {uploadProgress.percentage}%
            </Typography>
            <LinearProgress variant="determinate" value={uploadProgress.percentage} />
            <Typography variant="caption" color="text.secondary">
              {(uploadProgress.loaded / (1024 * 1024)).toFixed(2)} MB / {(uploadProgress.total / (1024 * 1024)).toFixed(2)} MB
            </Typography>
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          variant="contained"
          onClick={handleSave}
          disabled={isUploading || !lessonData.title.trim()}
        >
          {isUploading ? 'Saving...' : 'Save Lesson'}
        </Button>
      </DialogActions>
    </Dialog>
  )
}
