-- Enable Row Level Security on all tables
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_paths ENABLE ROW LEVEL SECURITY;
ALTER TABLE modules ENABLE ROW LEVEL SECURITY;
ALTER TABLE lessons ENABLE ROW LEVEL SECURITY;
ALTER TABLE learner_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_library ENABLE ROW LEVEL SECURITY;
ALTER TABLE competencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_competencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE path_competencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Helper function to get user's tenant_id
CREATE OR REPLACE FUNCTION get_user_tenant_id()
R<PERSON>URNS INTEGER AS $$
BEGIN
    RETURN (
        SELECT tenant_id 
        FROM users 
        WHERE id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user has permission
CREATE OR REPLACE FUNCTION user_has_permission(permission_name TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM users u
        JOIN roles r ON u.role_id = r.id
        WHERE u.id = auth.uid()
        AND (
            'admin' = ANY(r.permissions) OR
            permission_name = ANY(r.permissions)
        )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Tenants policies
CREATE POLICY "Users can view their own tenant" ON tenants
    FOR SELECT USING (id = get_user_tenant_id());

CREATE POLICY "Admins can update their tenant" ON tenants
    FOR UPDATE USING (
        id = get_user_tenant_id() AND
        user_has_permission('admin')
    );

-- Roles policies
CREATE POLICY "Users can view roles in their tenant" ON roles
    FOR SELECT USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Admins can manage roles in their tenant" ON roles
    FOR ALL USING (
        tenant_id = get_user_tenant_id() AND
        user_has_permission('manage_roles')
    );

-- Users policies
CREATE POLICY "Users can view users in their tenant" ON users
    FOR SELECT USING (
        tenant_id = get_user_tenant_id() AND
        (user_has_permission('view_learners') OR id = auth.uid())
    );

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (id = auth.uid());

CREATE POLICY "Admins can manage users in their tenant" ON users
    FOR ALL USING (
        tenant_id = get_user_tenant_id() AND
        user_has_permission('manage_learners')
    );

-- Learning paths policies
CREATE POLICY "Users can view learning paths in their tenant" ON learning_paths
    FOR SELECT USING (
        tenant_id = get_user_tenant_id() AND
        (user_has_permission('view_learning_paths') OR is_live = true)
    );

CREATE POLICY "Users can create learning paths in their tenant" ON learning_paths
    FOR INSERT WITH CHECK (
        tenant_id = get_user_tenant_id() AND
        user_has_permission('create_learning_paths')
    );

CREATE POLICY "Users can update learning paths they created or have permission" ON learning_paths
    FOR UPDATE USING (
        tenant_id = get_user_tenant_id() AND
        (created_by = auth.uid() OR user_has_permission('edit_learning_paths'))
    );

CREATE POLICY "Users can delete learning paths they created or have permission" ON learning_paths
    FOR DELETE USING (
        tenant_id = get_user_tenant_id() AND
        (created_by = auth.uid() OR user_has_permission('delete_learning_paths'))
    );

-- Modules policies
CREATE POLICY "Users can view modules of accessible learning paths" ON modules
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM learning_paths lp
            WHERE lp.id = modules.path_id
            AND lp.tenant_id = get_user_tenant_id()
            AND (user_has_permission('view_learning_paths') OR lp.is_live = true)
        )
    );

CREATE POLICY "Users can manage modules of their learning paths" ON modules
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM learning_paths lp
            WHERE lp.id = modules.path_id
            AND lp.tenant_id = get_user_tenant_id()
            AND (lp.created_by = auth.uid() OR user_has_permission('edit_learning_paths'))
        )
    );

-- Lessons policies
CREATE POLICY "Users can view lessons of accessible modules" ON lessons
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM modules m
            JOIN learning_paths lp ON m.path_id = lp.id
            WHERE m.id = lessons.module_id
            AND lp.tenant_id = get_user_tenant_id()
            AND (user_has_permission('view_learning_paths') OR lp.is_live = true)
        )
    );

CREATE POLICY "Users can manage lessons of their learning paths" ON lessons
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM modules m
            JOIN learning_paths lp ON m.path_id = lp.id
            WHERE m.id = lessons.module_id
            AND lp.tenant_id = get_user_tenant_id()
            AND (lp.created_by = auth.uid() OR user_has_permission('edit_learning_paths'))
        )
    );

-- Learner assignments policies
CREATE POLICY "Users can view their own assignments" ON learner_assignments
    FOR SELECT USING (
        learner_id = auth.uid() OR
        (user_has_permission('view_learners') AND 
         EXISTS (SELECT 1 FROM learning_paths lp WHERE lp.id = path_id AND lp.tenant_id = get_user_tenant_id()))
    );

CREATE POLICY "Users can create assignments in their tenant" ON learner_assignments
    FOR INSERT WITH CHECK (
        user_has_permission('assign_learning_paths') AND
        EXISTS (SELECT 1 FROM learning_paths lp WHERE lp.id = path_id AND lp.tenant_id = get_user_tenant_id()) AND
        EXISTS (SELECT 1 FROM users u WHERE u.id = learner_id AND u.tenant_id = get_user_tenant_id())
    );

CREATE POLICY "Users can update assignments they created or have permission" ON learner_assignments
    FOR UPDATE USING (
        assigned_by = auth.uid() OR
        (user_has_permission('assign_learning_paths') AND 
         EXISTS (SELECT 1 FROM learning_paths lp WHERE lp.id = path_id AND lp.tenant_id = get_user_tenant_id()))
    );

-- Progress policies
CREATE POLICY "Users can view their own progress" ON progress
    FOR SELECT USING (learner_id = auth.uid());

CREATE POLICY "Admins can view all progress in their tenant" ON progress
    FOR SELECT USING (
        user_has_permission('view_reports') AND
        EXISTS (
            SELECT 1 FROM learner_assignments la
            JOIN learning_paths lp ON la.path_id = lp.id
            WHERE la.id = progress.assignment_id
            AND lp.tenant_id = get_user_tenant_id()
        )
    );

CREATE POLICY "Users can update their own progress" ON progress
    FOR ALL USING (learner_id = auth.uid());

-- Content library policies
CREATE POLICY "Users can view content in their tenant" ON content_library
    FOR SELECT USING (
        tenant_id = get_user_tenant_id() AND
        (is_public = true OR user_has_permission('view_content'))
    );

CREATE POLICY "Users can upload content to their tenant" ON content_library
    FOR INSERT WITH CHECK (
        tenant_id = get_user_tenant_id() AND
        user_has_permission('upload_content')
    );

CREATE POLICY "Users can manage content they uploaded or have permission" ON content_library
    FOR ALL USING (
        tenant_id = get_user_tenant_id() AND
        (uploaded_by = auth.uid() OR user_has_permission('manage_content'))
    );

-- Competencies policies
CREATE POLICY "Users can view competencies in their tenant" ON competencies
    FOR SELECT USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Admins can manage competencies in their tenant" ON competencies
    FOR ALL USING (
        tenant_id = get_user_tenant_id() AND
        user_has_permission('manage_competencies')
    );

-- User competencies policies
CREATE POLICY "Users can view their own competencies" ON user_competencies
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admins can view all user competencies in their tenant" ON user_competencies
    FOR SELECT USING (
        user_has_permission('view_competencies') AND
        EXISTS (SELECT 1 FROM users u WHERE u.id = user_competencies.user_id AND u.tenant_id = get_user_tenant_id())
    );

CREATE POLICY "Users can update their own competencies" ON user_competencies
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Admins can manage user competencies in their tenant" ON user_competencies
    FOR ALL USING (
        user_has_permission('manage_competencies') AND
        EXISTS (SELECT 1 FROM users u WHERE u.id = user_competencies.user_id AND u.tenant_id = get_user_tenant_id())
    );

-- Path competencies policies
CREATE POLICY "Users can view path competencies for accessible paths" ON path_competencies
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM learning_paths lp
            WHERE lp.id = path_competencies.path_id
            AND lp.tenant_id = get_user_tenant_id()
            AND (user_has_permission('view_learning_paths') OR lp.is_live = true)
        )
    );

CREATE POLICY "Users can manage path competencies for their paths" ON path_competencies
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM learning_paths lp
            WHERE lp.id = path_competencies.path_id
            AND lp.tenant_id = get_user_tenant_id()
            AND (lp.created_by = auth.uid() OR user_has_permission('edit_learning_paths'))
        )
    );

-- Notifications policies
CREATE POLICY "Users can view their own notifications" ON notifications
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "System can create notifications for users in tenant" ON notifications
    FOR INSERT WITH CHECK (
        tenant_id = get_user_tenant_id() AND
        EXISTS (SELECT 1 FROM users u WHERE u.id = notifications.user_id AND u.tenant_id = get_user_tenant_id())
    );

CREATE POLICY "Users can update their own notifications" ON notifications
    FOR UPDATE USING (user_id = auth.uid());

-- Audit logs policies
CREATE POLICY "Admins can view audit logs in their tenant" ON audit_logs
    FOR SELECT USING (
        tenant_id = get_user_tenant_id() AND
        user_has_permission('view_audit_logs')
    );

CREATE POLICY "System can create audit logs" ON audit_logs
    FOR INSERT WITH CHECK (tenant_id = get_user_tenant_id());
