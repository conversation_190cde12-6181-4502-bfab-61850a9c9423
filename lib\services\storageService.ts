import { supabase } from '@/lib/supabase'

export interface UploadProgress {
  loaded: number
  total: number
  percentage: number
}

export interface FileUploadResult {
  url: string
  path: string
  metadata: {
    size: number
    type: string
    name: string
    lastModified: number
  }
}

export interface StorageError {
  message: string
  code?: string
}

export type ContentType = 'videos' | 'documents' | 'presentations' | 'images' | 'audio' | 'other'

export class StorageService {
  private static readonly MAX_FILE_SIZE = 100 * 1024 * 1024 // 100MB
  private static readonly BUCKET_NAME = 'content'
  
  // File type mappings
  private static readonly FILE_TYPE_MAP: Record<string, ContentType> = {
    // Videos
    'video/mp4': 'videos',
    'video/webm': 'videos',
    'video/ogg': 'videos',
    'video/avi': 'videos',
    'video/mov': 'videos',
    'video/wmv': 'videos',
    'video/flv': 'videos',
    'video/mkv': 'videos',
    
    // Documents
    'application/pdf': 'documents',
    'application/msword': 'documents',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'documents',
    'text/plain': 'documents',
    'text/rtf': 'documents',
    
    // Presentations
    'application/vnd.ms-powerpoint': 'presentations',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'presentations',
    'application/vnd.oasis.opendocument.presentation': 'presentations',
    
    // Images
    'image/jpeg': 'images',
    'image/jpg': 'images',
    'image/png': 'images',
    'image/gif': 'images',
    'image/webp': 'images',
    'image/svg+xml': 'images',
    'image/bmp': 'images',
    
    // Audio
    'audio/mpeg': 'audio',
    'audio/wav': 'audio',
    'audio/ogg': 'audio',
    'audio/mp3': 'audio',
    'audio/aac': 'audio'
  }

  private static getCurrentUserContext() {
    // Development fallback - in production this would get from auth context
    return {
      user: {
        id: '5cd09c93-eb00-470f-a605-c6d0d057bdd6',
        tenant_id: 3
      },
      tenant: {
        id: 3,
        name: 'Development Tenant'
      }
    }
  }

  private static getContentType(mimeType: string): ContentType {
    return this.FILE_TYPE_MAP[mimeType] || 'other'
  }

  private static generateFilePath(
    tenantId: number, 
    contentType: ContentType, 
    fileName: string,
    userId: string
  ): string {
    const timestamp = Date.now()
    const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_')
    return `tenant_${tenantId}/content/${contentType}/${userId}/${timestamp}_${sanitizedFileName}`
  }

  private static validateFile(file: File): { valid: boolean; error?: string } {
    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      return {
        valid: false,
        error: `File size must be less than ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`
      }
    }

    // Check if file type is supported
    const contentType = this.getContentType(file.type)
    if (contentType === 'other' && !this.isAllowedFileType(file.type)) {
      return {
        valid: false,
        error: 'File type not supported'
      }
    }

    return { valid: true }
  }

  private static isAllowedFileType(mimeType: string): boolean {
    const allowedTypes = [
      'application/json',
      'text/csv',
      'application/zip',
      'application/x-zip-compressed'
    ]
    return allowedTypes.includes(mimeType)
  }

  static async uploadFile(
    file: File,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<FileUploadResult> {
    try {
      // Validate file
      const validation = this.validateFile(file)
      if (!validation.valid) {
        throw new Error(validation.error)
      }

      const { user, tenant } = this.getCurrentUserContext()
      const contentType = this.getContentType(file.type)
      const filePath = this.generateFilePath(tenant.id, contentType, file.name, user.id)

      // Create a promise that tracks upload progress
      const uploadPromise = new Promise<FileUploadResult>((resolve, reject) => {
        const xhr = new XMLHttpRequest()
        
        // Track upload progress
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable && onProgress) {
            const progress: UploadProgress = {
              loaded: event.loaded,
              total: event.total,
              percentage: Math.round((event.loaded / event.total) * 100)
            }
            onProgress(progress)
          }
        })

        xhr.addEventListener('load', async () => {
          if (xhr.status === 200) {
            try {
              // Get the public URL
              const { data: { publicUrl } } = supabase.storage
                .from(this.BUCKET_NAME)
                .getPublicUrl(filePath)

              resolve({
                url: publicUrl,
                path: filePath,
                metadata: {
                  size: file.size,
                  type: file.type,
                  name: file.name,
                  lastModified: file.lastModified
                }
              })
            } catch (error) {
              reject(new Error('Failed to get file URL'))
            }
          } else {
            reject(new Error(`Upload failed with status ${xhr.status}`))
          }
        })

        xhr.addEventListener('error', () => {
          reject(new Error('Upload failed'))
        })

        // Use Supabase storage upload
        this.performSupabaseUpload(file, filePath)
          .then(() => {
            // Trigger the load event manually since we're using Supabase
            xhr.dispatchEvent(new Event('load'))
          })
          .catch((error) => {
            reject(error)
          })
      })

      return await uploadPromise

    } catch (error) {
      console.error('File upload error:', error)
      throw error
    }
  }

  private static async performSupabaseUpload(file: File, filePath: string): Promise<void> {
    const { error } = await supabase.storage
      .from(this.BUCKET_NAME)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      })

    if (error) {
      throw new Error(`Upload failed: ${error.message}`)
    }
  }

  static async deleteFile(filePath: string): Promise<void> {
    try {
      const { error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .remove([filePath])

      if (error) {
        throw new Error(`Delete failed: ${error.message}`)
      }
    } catch (error) {
      console.error('File deletion error:', error)
      throw error
    }
  }

  static async getSignedUrl(filePath: string, expiresIn: number = 3600): Promise<string> {
    try {
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .createSignedUrl(filePath, expiresIn)

      if (error) {
        throw new Error(`Failed to create signed URL: ${error.message}`)
      }

      return data.signedUrl
    } catch (error) {
      console.error('Signed URL creation error:', error)
      throw error
    }
  }

  static async listFiles(
    tenantId: number, 
    contentType?: ContentType,
    limit: number = 100
  ): Promise<any[]> {
    try {
      const prefix = contentType 
        ? `tenant_${tenantId}/content/${contentType}/`
        : `tenant_${tenantId}/content/`

      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .list(prefix, {
          limit,
          sortBy: { column: 'created_at', order: 'desc' }
        })

      if (error) {
        throw new Error(`Failed to list files: ${error.message}`)
      }

      return data || []
    } catch (error) {
      console.error('File listing error:', error)
      throw error
    }
  }

  static getPublicUrl(filePath: string): string {
    const { data: { publicUrl } } = supabase.storage
      .from(this.BUCKET_NAME)
      .getPublicUrl(filePath)
    
    return publicUrl
  }

  static async generateThumbnail(file: File): Promise<string | null> {
    try {
      if (!file.type.startsWith('video/')) {
        return null
      }

      return new Promise((resolve) => {
        const video = document.createElement('video')
        video.src = URL.createObjectURL(file)
        video.currentTime = 1 // Seek to 1 second for thumbnail
        
        video.onloadeddata = () => {
          const canvas = document.createElement('canvas')
          canvas.width = video.videoWidth
          canvas.height = video.videoHeight
          
          const ctx = canvas.getContext('2d')
          ctx?.drawImage(video, 0, 0)
          
          const thumbnail = canvas.toDataURL('image/jpeg', 0.8)
          URL.revokeObjectURL(video.src)
          resolve(thumbnail)
        }
        
        video.onerror = () => {
          URL.revokeObjectURL(video.src)
          resolve(null)
        }
      })
    } catch (error) {
      console.error('Thumbnail generation error:', error)
      return null
    }
  }
}
