'use client'

import React, { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Typo<PERSON>,
  Ta<PERSON>,
  Tab,
  AppBar,
  <PERSON>l<PERSON>,
  Button,
  IconButton,
  Chip,
  Alert,
  CircularProgress,
  Fade,
  useTheme,
  useMediaQuery
} from '@mui/material'
import {
  Save as SaveIcon,
  Preview as PreviewIcon,
  History as HistoryIcon,
  Settings as SettingsIcon,
  Security as SecurityIcon,
  Palette as PaletteIcon,
  People as PeopleIcon,
  ToggleOn as ToggleOnIcon,
  SmartToy as AIIcon,
  Api as APIIcon,
  Notifications as NotificationsIcon,
  Analytics as AnalyticsIcon
} from '@mui/icons-material'
import { motion, AnimatePresence } from 'framer-motion'

import { useSettingsStore } from '@/lib/store'
import { useAllSettings } from '@/lib/hooks/useSettings'

// Import settings components (we'll create these next)
import GeneralSettings from '@/components/settings/GeneralSettings'
import BrandingSettings from '@/components/settings/BrandingSettings'
import SecuritySettings from '@/components/settings/SecuritySettings'
import RoleManagement from '@/components/settings/RoleManagement'
import FeatureToggles from '@/components/settings/FeatureToggles'
import AISettings from '@/components/settings/AISettings'
import IntegrationSettings from '@/components/settings/IntegrationSettings'
import NotificationSettings from '@/components/settings/NotificationSettings'
import AnalyticsSettings from '@/components/settings/AnalyticsSettings'
import AuditLogs from '@/components/settings/AuditLogs'

interface TabPanelProps {
  children?: React.ReactNode
  index: number
  value: number
}

function TabPanel({ children, value, index, ...other }: TabPanelProps) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && (
        <AnimatePresence mode="wait">
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Box sx={{ py: 3 }}>
              {children}
            </Box>
          </motion.div>
        </AnimatePresence>
      )}
    </div>
  )
}

const settingsTabs = [
  {
    label: 'General',
    icon: <SettingsIcon />,
    component: GeneralSettings,
    description: 'Platform configuration and maintenance'
  },
  {
    label: 'Branding',
    icon: <PaletteIcon />,
    component: BrandingSettings,
    description: 'Customize logos, colors, and themes'
  },
  {
    label: 'Security',
    icon: <SecurityIcon />,
    component: SecuritySettings,
    description: 'Authentication and compliance settings'
  },
  {
    label: 'Users & Roles',
    icon: <PeopleIcon />,
    component: RoleManagement,
    description: 'Manage user roles and permissions'
  },
  {
    label: 'Features',
    icon: <ToggleOnIcon />,
    component: FeatureToggles,
    description: 'Toggle platform features and configurations'
  },
  {
    label: 'AI & Automation',
    icon: <AIIcon />,
    component: AISettings,
    description: 'Configure AI models and automation rules'
  },
  {
    label: 'Integrations',
    icon: <APIIcon />,
    component: IntegrationSettings,
    description: 'API keys, webhooks, and external services'
  },
  {
    label: 'Notifications',
    icon: <NotificationsIcon />,
    component: NotificationSettings,
    description: 'Email, SMS, and push notification settings'
  },
  {
    label: 'Analytics',
    icon: <AnalyticsIcon />,
    component: AnalyticsSettings,
    description: 'Usage analytics and reporting configuration'
  }
]

export default function SettingsPage() {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  
  const { 
    activeTab, 
    unsavedChanges, 
    previewMode,
    setActiveTab, 
    setUnsavedChanges,
    setPreviewMode 
  } = useSettingsStore()
  
  const [showAuditLogs, setShowAuditLogs] = useState(false)
  const { isLoading, isError, error } = useAllSettings()

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    if (unsavedChanges) {
      const confirmLeave = window.confirm(
        'You have unsaved changes. Are you sure you want to leave this tab?'
      )
      if (!confirmLeave) return
    }
    
    setActiveTab(newValue)
    setUnsavedChanges(false)
  }

  // Handle save all settings
  const handleSaveAll = async () => {
    try {
      // This would trigger save across all components
      // Implementation depends on how we structure the form state
      console.log('Saving all settings...')
      setUnsavedChanges(false)
    } catch (error) {
      console.error('Failed to save settings:', error)
    }
  }

  // Handle preview mode toggle
  const handlePreviewToggle = () => {
    setPreviewMode(!previewMode)
  }

  // Handle audit logs toggle
  const handleAuditLogsToggle = () => {
    setShowAuditLogs(!showAuditLogs)
  }

  if (isLoading) {
    return (
      <Box 
        display="flex" 
        justifyContent="center" 
        alignItems="center" 
        minHeight="60vh"
      >
        <CircularProgress size={48} />
      </Box>
    )
  }

  if (isError) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          Failed to load settings: {error?.message || 'Unknown error'}
        </Alert>
      </Container>
    )
  }

  const CurrentComponent = settingsTabs[activeTab]?.component

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" fontWeight="bold" gutterBottom>
          Admin Settings
        </Typography>
        <Typography variant="body1" color="text.secondary" gutterBottom>
          Configure platform-wide settings, branding, security, and features for your organization.
        </Typography>
        
        {/* Unsaved changes indicator */}
        <Fade in={unsavedChanges}>
          <Alert severity="warning" sx={{ mt: 2, mb: 2 }}>
            You have unsaved changes. Don't forget to save your settings.
          </Alert>
        </Fade>
      </Box>

      {/* Action Bar */}
      <AppBar 
        position="static" 
        color="default" 
        elevation={1}
        sx={{ 
          borderRadius: 1, 
          mb: 3,
          bgcolor: 'background.paper'
        }}
      >
        <Toolbar>
          <Box sx={{ flexGrow: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="h6" component="div">
              {settingsTabs[activeTab]?.label}
            </Typography>
            <Chip 
              label={settingsTabs[activeTab]?.description}
              size="small"
              variant="outlined"
              sx={{ display: { xs: 'none', sm: 'flex' } }}
            />
          </Box>
          
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant={previewMode ? 'contained' : 'outlined'}
              startIcon={<PreviewIcon />}
              onClick={handlePreviewToggle}
              size="small"
            >
              Preview
            </Button>
            
            <Button
              variant="outlined"
              startIcon={<HistoryIcon />}
              onClick={handleAuditLogsToggle}
              size="small"
            >
              Audit
            </Button>
            
            <Button
              variant="contained"
              startIcon={<SaveIcon />}
              onClick={handleSaveAll}
              disabled={!unsavedChanges}
              size="small"
            >
              Save All
            </Button>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Settings Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant={isMobile ? 'scrollable' : 'standard'}
          scrollButtons="auto"
          allowScrollButtonsMobile
          aria-label="settings tabs"
        >
          {settingsTabs.map((tab, index) => (
            <Tab
              key={index}
              icon={tab.icon}
              label={tab.label}
              iconPosition="start"
              id={`settings-tab-${index}`}
              aria-controls={`settings-tabpanel-${index}`}
              sx={{
                minHeight: 48,
                textTransform: 'none',
                fontWeight: activeTab === index ? 600 : 400
              }}
            />
          ))}
        </Tabs>
      </Box>

      {/* Tab Content */}
      <Container maxWidth="lg" disableGutters>
        {settingsTabs.map((tab, index) => (
          <TabPanel key={index} value={activeTab} index={index}>
            {CurrentComponent && <CurrentComponent />}
          </TabPanel>
        ))}
      </Container>

      {/* Audit Logs Drawer/Modal */}
      {showAuditLogs && (
        <AuditLogs 
          open={showAuditLogs} 
          onClose={handleAuditLogsToggle} 
        />
      )}
    </Box>
  )
}
