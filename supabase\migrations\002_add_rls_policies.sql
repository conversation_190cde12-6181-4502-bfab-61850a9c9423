-- Enable RLS on all tables
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_paths ENABLE ROW LEVEL SECURITY;
ALTER TABLE modules ENABLE ROW LEVEL SECURITY;
ALTER TABLE lessons ENABLE ROW LEVEL SECURITY;
ALTER TABLE learner_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE progress ENABLE ROW LEVEL SECURITY;

-- Create function to get current user's tenant_id
CREATE OR REPLACE FUNCTION get_current_tenant_id()
RETURNS INTEGER AS $$
BEGIN
  RETURN (
    SELECT tenant_id 
    FROM users 
    WHERE id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Tenants policies
CREATE POLICY "Users can view their own tenant" ON tenants
  FOR SELECT USING (id = get_current_tenant_id());

-- Users policies
CREATE POLICY "Users can view users in their tenant" ON users
  FOR SELECT USING (tenant_id = get_current_tenant_id());

CREATE POLICY "Users can update their own profile" ON users
  FOR UPDATE USING (id = auth.uid());

-- Roles policies
CREATE POLICY "Users can view roles in their tenant" ON roles
  FOR SELECT USING (tenant_id = get_current_tenant_id());

-- Learning paths policies
CREATE POLICY "Users can view learning paths in their tenant" ON learning_paths
  FOR SELECT USING (tenant_id = get_current_tenant_id());

CREATE POLICY "Users can create learning paths in their tenant" ON learning_paths
  FOR INSERT WITH CHECK (tenant_id = get_current_tenant_id());

CREATE POLICY "Users can update learning paths in their tenant" ON learning_paths
  FOR UPDATE USING (tenant_id = get_current_tenant_id());

CREATE POLICY "Users can delete learning paths in their tenant" ON learning_paths
  FOR DELETE USING (tenant_id = get_current_tenant_id());

-- Modules policies
CREATE POLICY "Users can view modules in their tenant" ON modules
  FOR SELECT USING (
    path_id IN (
      SELECT id FROM learning_paths WHERE tenant_id = get_current_tenant_id()
    )
  );

CREATE POLICY "Users can create modules in their tenant" ON modules
  FOR INSERT WITH CHECK (
    path_id IN (
      SELECT id FROM learning_paths WHERE tenant_id = get_current_tenant_id()
    )
  );

CREATE POLICY "Users can update modules in their tenant" ON modules
  FOR UPDATE USING (
    path_id IN (
      SELECT id FROM learning_paths WHERE tenant_id = get_current_tenant_id()
    )
  );

CREATE POLICY "Users can delete modules in their tenant" ON modules
  FOR DELETE USING (
    path_id IN (
      SELECT id FROM learning_paths WHERE tenant_id = get_current_tenant_id()
    )
  );

-- Lessons policies
CREATE POLICY "Users can view lessons in their tenant" ON lessons
  FOR SELECT USING (
    module_id IN (
      SELECT m.id FROM modules m
      JOIN learning_paths lp ON m.path_id = lp.id
      WHERE lp.tenant_id = get_current_tenant_id()
    )
  );

CREATE POLICY "Users can create lessons in their tenant" ON lessons
  FOR INSERT WITH CHECK (
    module_id IN (
      SELECT m.id FROM modules m
      JOIN learning_paths lp ON m.path_id = lp.id
      WHERE lp.tenant_id = get_current_tenant_id()
    )
  );

CREATE POLICY "Users can update lessons in their tenant" ON lessons
  FOR UPDATE USING (
    module_id IN (
      SELECT m.id FROM modules m
      JOIN learning_paths lp ON m.path_id = lp.id
      WHERE lp.tenant_id = get_current_tenant_id()
    )
  );

CREATE POLICY "Users can delete lessons in their tenant" ON lessons
  FOR DELETE USING (
    module_id IN (
      SELECT m.id FROM modules m
      JOIN learning_paths lp ON m.path_id = lp.id
      WHERE lp.tenant_id = get_current_tenant_id()
    )
  );

-- Learner assignments policies
CREATE POLICY "Users can view assignments in their tenant" ON learner_assignments
  FOR SELECT USING (
    path_id IN (
      SELECT id FROM learning_paths WHERE tenant_id = get_current_tenant_id()
    )
  );

CREATE POLICY "Users can create assignments in their tenant" ON learner_assignments
  FOR INSERT WITH CHECK (
    path_id IN (
      SELECT id FROM learning_paths WHERE tenant_id = get_current_tenant_id()
    )
  );

-- Progress policies
CREATE POLICY "Users can view progress in their tenant" ON progress
  FOR SELECT USING (
    lesson_id IN (
      SELECT l.id FROM lessons l
      JOIN modules m ON l.module_id = m.id
      JOIN learning_paths lp ON m.path_id = lp.id
      WHERE lp.tenant_id = get_current_tenant_id()
    )
  );

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_learning_paths_tenant_id ON learning_paths(tenant_id);
CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);
CREATE INDEX IF NOT EXISTS idx_modules_path_id ON modules(path_id);
CREATE INDEX IF NOT EXISTS idx_lessons_module_id ON lessons(module_id);
CREATE INDEX IF NOT EXISTS idx_learner_assignments_path_id ON learner_assignments(path_id);
CREATE INDEX IF NOT EXISTS idx_progress_lesson_id ON progress(lesson_id);
CREATE INDEX IF NOT EXISTS idx_progress_learner_id ON progress(learner_id);

-- Add composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_learning_paths_tenant_live ON learning_paths(tenant_id, is_live);
CREATE INDEX IF NOT EXISTS idx_learning_paths_tenant_category ON learning_paths(tenant_id, category);
