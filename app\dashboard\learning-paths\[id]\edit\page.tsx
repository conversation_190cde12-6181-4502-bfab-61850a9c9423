'use client'

import React, { useState, useEffect, useCallback, use } from 'react'
import {
  Box,
  <PERSON>po<PERSON>,
  Button,
  Alert,
  Container,
  Skeleton,
  Tabs,
  Tab,
  Paper,
  Snackbar,
  CircularProgress,
  Fab,
  Tooltip,
} from '@mui/material'
import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  Undo as UndoIcon,
  Redo as RedoIcon,
  Preview as PreviewIcon,
  History as HistoryIcon,
} from '@mui/icons-material'
import { useRouter } from 'next/navigation'

import { useLearningPath, useUpdateLearningPath } from '@/lib/hooks/useLearningPaths'
import { useLearningPathsStore } from '@/lib/store'
import EnhancedModuleManager from '@/components/learning-paths/EnhancedModuleManager'
import PathBasicInfo from '@/components/learning-paths/PathBasicInfo'
import PathPreview from '@/components/learning-paths/PathPreview'
import PathAssignments from '@/components/learning-paths/PathAssignments'

interface LearningPathEditProps {
  params: Promise<{
    id: string
  }>
}

export default function LearningPathEditPage({ params }: LearningPathEditProps) {
  const router = useRouter()
  const resolvedParams = use(params)
  const { data: path, isLoading, error } = useLearningPath(resolvedParams.id)
  const updateMutation = useUpdateLearningPath()
  const { setIsCreating } = useLearningPathsStore()

  // Enhanced state management
  const [activeTab, setActiveTab] = useState(0)
  const [pathData, setPathData] = useState<any>({})
  const [modules, setModules] = useState<any[]>([])
  const [assignments, setAssignments] = useState<any[]>([])
  const [isSaving, setIsSaving] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' | 'warning' | 'info' })

  // Undo/Redo functionality
  const [history, setHistory] = useState<any[]>([])
  const [historyIndex, setHistoryIndex] = useState(-1)
  const [isUndoRedoAction, setIsUndoRedoAction] = useState(false)

  useEffect(() => {
    setIsCreating(true)
    return () => setIsCreating(false)
  }, [setIsCreating])

  // Save state to history for undo/redo
  const saveToHistory = useCallback((state: any) => {
    if (isUndoRedoAction) return

    const newHistory = history.slice(0, historyIndex + 1)
    newHistory.push(JSON.parse(JSON.stringify(state)))

    // Limit history to 50 entries
    if (newHistory.length > 50) {
      newHistory.shift()
    } else {
      setHistoryIndex(prev => prev + 1)
    }

    setHistory(newHistory)
  }, [history, historyIndex, isUndoRedoAction])

  // Undo functionality
  const handleUndo = useCallback(() => {
    if (historyIndex > 0) {
      setIsUndoRedoAction(true)
      const previousState = history[historyIndex - 1]
      setPathData(previousState.pathData)
      setModules(previousState.modules)
      setAssignments(previousState.assignments)
      setHistoryIndex(prev => prev - 1)
      setHasUnsavedChanges(true)
      setTimeout(() => setIsUndoRedoAction(false), 100)
    }
  }, [history, historyIndex])

  // Redo functionality
  const handleRedo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      setIsUndoRedoAction(true)
      const nextState = history[historyIndex + 1]
      setPathData(nextState.pathData)
      setModules(nextState.modules)
      setAssignments(nextState.assignments)
      setHistoryIndex(prev => prev + 1)
      setHasUnsavedChanges(true)
      setTimeout(() => setIsUndoRedoAction(false), 100)
    }
  }, [history, historyIndex])

  // Note: Keyboard shortcuts can be added later with react-hotkeys-hook

  useEffect(() => {
    if (path) {
      const initialData = {
        title: path.title || '',
        description: path.description || '',
        objectives: path.objectives || [],
        duration: path.duration || 4,
        difficulty: path.difficulty || 'beginner',
        category: path.category || '',
        tags: path.tags || [],
        is_template: path.is_template || false
      }

      setPathData(initialData)
      setModules(path.modules || [])
      setAssignments(path.assignments || [])

      // Initialize history
      saveToHistory({
        pathData: initialData,
        modules: path.modules || [],
        assignments: path.assignments || []
      })
    }
  }, [path, saveToHistory])

  // Track changes for history (separate from the change handler to avoid loops)
  useEffect(() => {
    if (pathData.title) { // Only track if we have actual data
      saveToHistory({
        pathData,
        modules,
        assignments
      })
    }
  }, [pathData, modules, assignments, saveToHistory])

  const handleBack = () => {
    router.push(`/dashboard/learning-paths/${resolvedParams.id}`)
  }

  // Enhanced data change handlers with history tracking
  const handleDataChange = useCallback((newData: any) => {
    setPathData(prevPathData => ({ ...prevPathData, ...newData }))
    setHasUnsavedChanges(true)
  }, [])

  const handleModulesChange = useCallback((newModules: any[]) => {
    setModules(newModules)
    setHasUnsavedChanges(true)
  }, [])

  const handleAssignmentsChange = useCallback((newAssignments: any[]) => {
    setAssignments(newAssignments)
    setHasUnsavedChanges(true)
  }, [])

  // Tab change handler
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue)
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      const updates = {
        ...pathData,
        modules: modules,
        assignments: assignments,
        metadata: {
          ...path?.metadata,
          estimated_hours: modules.reduce((total, module) => 
            total + (module.lessons?.reduce((lessonTotal: number, lesson: any) => 
              lessonTotal + (lesson.estimated_duration || 0), 0) || 0), 0) / 60,
          skills: pathData.tags || []
        }
      }

      await updateMutation.mutateAsync({ id: resolvedParams.id, updates })
      setHasUnsavedChanges(false)
      setSnackbar({
        open: true,
        message: 'Learning path saved successfully!',
        severity: 'success'
      })
    } catch (error) {
      console.error('Failed to save changes:', error)
      setSnackbar({
        open: true,
        message: 'Failed to save learning path. Please try again.',
        severity: 'error'
      })
    } finally {
      setIsSaving(false)
    }
  }

  if (error) {
    return (
      <Container maxWidth="lg">
        <Box py={3}>
          <Alert severity="error">
            Failed to load learning path. Please try again.
          </Alert>
        </Box>
      </Container>
    )
  }

  if (isLoading) {
    return (
      <Container maxWidth="lg">
        <Box py={3}>
          <Box display="flex" alignItems="center" mb={4}>
            <Skeleton variant="rectangular" width={150} height={36} sx={{ mr: 2 }} />
            <Box>
              <Skeleton variant="text" width={300} height={32} />
              <Skeleton variant="text" width={200} height={20} />
            </Box>
          </Box>
          
          <Skeleton variant="rectangular" width="100%" height={400} />
        </Box>
      </Container>
    )
  }

  if (!path) {
    return (
      <Container maxWidth="lg">
        <Box py={3}>
          <Alert severity="warning">
            Learning path not found.
          </Alert>
        </Box>
      </Container>
    )
  }

  // Create a mock wizard step for editing (we'll use the basic info step)
  const editStep = {
    id: 0,
    title: 'Edit Learning Path',
    description: 'Update your learning path details',
    component: 'BasicInfo',
    isCompleted: true,
    isActive: true,
    validation: (data: any) => !!(data.title && data.description && data.category)
  }

  return (
    <Container maxWidth="xl">
      <Box py={3}>
        {/* Enhanced Header with Undo/Redo */}
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={4}>
          <Box display="flex" alignItems="center" gap={2}>
            <Button
              startIcon={<ArrowBackIcon />}
              onClick={handleBack}
              variant="outlined"
            >
              Back to Path
            </Button>
            <Box>
              <Typography variant="h4" fontWeight="bold">
                Edit Learning Path
                {hasUnsavedChanges && (
                  <Typography component="span" color="warning.main" sx={{ ml: 1 }}>
                    (Unsaved Changes)
                  </Typography>
                )}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Update your learning path details and structure
              </Typography>
            </Box>
          </Box>

          <Box display="flex" alignItems="center" gap={1}>
            <Tooltip title="Undo (Ctrl+Z)">
              <span>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleUndo}
                  disabled={historyIndex <= 0}
                  startIcon={<UndoIcon />}
                >
                  Undo
                </Button>
              </span>
            </Tooltip>

            <Tooltip title="Redo (Ctrl+Y)">
              <span>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleRedo}
                  disabled={historyIndex >= history.length - 1}
                  startIcon={<RedoIcon />}
                >
                  Redo
                </Button>
              </span>
            </Tooltip>

            <Button
              variant="outlined"
              startIcon={<PreviewIcon />}
              onClick={() => setShowPreview(!showPreview)}
            >
              {showPreview ? 'Hide Preview' : 'Preview'}
            </Button>

            <Button
              variant="contained"
              startIcon={isSaving ? <CircularProgress size={16} /> : <SaveIcon />}
              onClick={handleSave}
              disabled={!pathData.title || isSaving}
              size="large"
            >
              {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
          </Box>
        </Box>

        {/* Enhanced Tabbed Interface */}
        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            sx={{ borderBottom: 1, borderColor: 'divider' }}
          >
            <Tab label="Basic Information" />
            <Tab label="Modules & Content" />
            <Tab label="Assignments" />
            <Tab label="Preview" />
          </Tabs>
        </Paper>

        {/* Tab Content */}
        {activeTab === 0 && (
          <PathBasicInfo
            data={pathData}
            onChange={handleDataChange}
          />
        )}

        {activeTab === 1 && (
          <EnhancedModuleManager
            modules={modules}
            onChange={handleModulesChange}
            learningPathId={resolvedParams.id}
          />
        )}

        {activeTab === 2 && (
          <PathAssignments
            assignments={assignments}
            onChange={handleAssignmentsChange}
            learningPathId={resolvedParams.id}
          />
        )}

        {activeTab === 3 && (
          <PathPreview
            pathData={pathData}
            modules={modules}
            assignments={assignments}
          />
        )}

        {/* Floating Action Buttons */}
        <Box sx={{ position: 'fixed', bottom: 24, right: 24, display: 'flex', flexDirection: 'column', gap: 1 }}>
          <Tooltip title="View History">
            <Fab size="small" color="secondary">
              <HistoryIcon />
            </Fab>
          </Tooltip>
        </Box>

        {/* Snackbar for notifications */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
          message={snackbar.message}
        />
      </Box>
    </Container>
  )
}
