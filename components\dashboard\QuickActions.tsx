'use client'

import {
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from '@mui/material'
import {
  Add as AddIcon,
  Upload as UploadIcon,
  People as PeopleIcon,
  Assessment as ReportsIcon,
  School as SchoolIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material'
import { useRouter } from 'next/navigation'

const quickActions = [
  {
    title: 'Create Learning Path',
    description: 'Build a new learning journey',
    icon: <AddIcon />,
    href: '/dashboard/learning-paths/create',
    color: 'primary',
  },
  {
    title: 'Upload Content',
    description: 'Add videos, documents, or quizzes',
    icon: <UploadIcon />,
    href: '/dashboard/content/upload',
    color: 'info',
  },
  {
    title: 'Invite Learners',
    description: 'Add new users to your platform',
    icon: <PeopleIcon />,
    href: '/dashboard/learners/invite',
    color: 'success',
  },
  {
    title: 'View Reports',
    description: 'Check progress and analytics',
    icon: <ReportsIcon />,
    href: '/dashboard/reports',
    color: 'warning',
  },
]

const recentTasks = [
  {
    title: 'Review pending assignments',
    count: 12,
    urgent: true,
  },
  {
    title: 'Update course materials',
    count: 3,
    urgent: false,
  },
  {
    title: 'Check completion rates',
    count: 1,
    urgent: false,
  },
]

export default function QuickActions() {
  const router = useRouter()

  const handleActionClick = (href: string) => {
    router.push(href)
  }

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Typography variant="h6" fontWeight="bold" gutterBottom>
          Quick Actions
        </Typography>

        <Box mb={3}>
          {quickActions.map((action, index) => (
            <Button
              key={index}
              fullWidth
              variant="outlined"
              startIcon={action.icon}
              onClick={() => handleActionClick(action.href)}
              sx={{
                mb: 1,
                justifyContent: 'flex-start',
                textAlign: 'left',
                py: 1.5,
                borderColor: `${action.color}.main`,
                color: `${action.color}.main`,
                '&:hover': {
                  backgroundColor: `${action.color}.main`,
                  color: `${action.color}.contrastText`,
                  borderColor: `${action.color}.main`,
                },
              }}
            >
              <Box>
                <Typography variant="body2" fontWeight="medium">
                  {action.title}
                </Typography>
                <Typography variant="caption" sx={{ opacity: 0.8 }}>
                  {action.description}
                </Typography>
              </Box>
            </Button>
          ))}
        </Box>

        <Divider sx={{ my: 2 }} />

        <Typography variant="h6" fontWeight="bold" gutterBottom>
          Pending Tasks
        </Typography>

        <List sx={{ p: 0 }}>
          {recentTasks.map((task, index) => (
            <ListItem
              key={index}
              sx={{
                px: 0,
                py: 1,
                borderBottom: index < recentTasks.length - 1 ? 1 : 0,
                borderColor: 'divider',
              }}
            >
              <ListItemIcon sx={{ minWidth: 36 }}>
                <Box
                  sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: task.urgent ? 'error.main' : 'info.main',
                  }}
                />
              </ListItemIcon>
              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="medium">
                    {task.title}
                  </Typography>
                }
                secondary={
                  <Typography variant="caption" color="text.secondary">
                    {task.count} item{task.count !== 1 ? 's' : ''}
                  </Typography>
                }
              />
            </ListItem>
          ))}
        </List>

        <Button
          fullWidth
          variant="text"
          size="small"
          sx={{ mt: 2 }}
          onClick={() => router.push('/dashboard/tasks')}
        >
          View All Tasks
        </Button>
      </CardContent>
    </Card>
  )
}
