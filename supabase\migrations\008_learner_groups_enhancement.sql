-- Enhanced Learner Groups Feature Migration
-- This migration adds tables and features for the comprehensive learner study groups functionality

-- Enhance existing groups table
ALTER TABLE groups 
  ADD COLUMN IF NOT EXISTS start_date TIMESTAMPTZ,
  ADD COLUMN IF NOT EXISTS end_date TIMESTAMPTZ,
  ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'archived', 'draft')),
  ADD COLUMN IF NOT EXISTS tags TEXT[] DEFAULT '{}',
  ADD COLUMN IF NOT EXISTS parent_group_id INTEGER REFERENCES groups(id) ON DELETE SET NULL,
  ADD COLUMN IF NOT EXISTS settings JSONB DEFAULT '{}',
  ADD COLUMN IF NOT EXISTS visibility VARCHAR(20) DEFAULT 'private' CHECK (visibility IN ('public', 'private', 'invite_only'));

-- Update group_members table to support learner_id and enhanced roles
ALTER TABLE group_members 
  ADD COLUMN IF NOT EXISTS learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
  ADD COLUMN IF NOT EXISTS added_by UUID REFERENCES users(id),
  DROP CONSTRAINT IF EXISTS group_members_role_check,
  ADD CONSTRAINT group_members_role_check CHECK (role IN ('member', 'moderator', 'admin'));

-- Update existing group_members to use learner_id
UPDATE group_members SET learner_id = user_id WHERE learner_id IS NULL;

-- Create group_assignments table for learning path assignments
CREATE TABLE IF NOT EXISTS group_assignments (
  id BIGSERIAL PRIMARY KEY,
  group_id INTEGER NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
  path_id INTEGER NOT NULL REFERENCES learning_paths(id) ON DELETE CASCADE,
  assigned_by UUID NOT NULL REFERENCES users(id),
  assigned_at TIMESTAMPTZ DEFAULT NOW(),
  start_date TIMESTAMPTZ,
  due_date TIMESTAMPTZ,
  is_mandatory BOOLEAN DEFAULT true,
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(group_id, path_id)
);

-- Create group_progress table for tracking group learning progress
CREATE TABLE IF NOT EXISTS group_progress (
  id BIGSERIAL PRIMARY KEY,
  group_id INTEGER NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
  path_id INTEGER NOT NULL REFERENCES learning_paths(id) ON DELETE CASCADE,
  total_members INTEGER DEFAULT 0,
  completed_members INTEGER DEFAULT 0,
  in_progress_members INTEGER DEFAULT 0,
  not_started_members INTEGER DEFAULT 0,
  average_score DECIMAL(5,2),
  completion_rate DECIMAL(5,2) DEFAULT 0,
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(group_id, path_id)
);

-- Create group_messages table for group communication
CREATE TABLE IF NOT EXISTS group_messages (
  id BIGSERIAL PRIMARY KEY,
  group_id INTEGER NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  message TEXT NOT NULL,
  message_type VARCHAR(20) DEFAULT 'chat' CHECK (message_type IN ('chat', 'announcement', 'system')),
  channels TEXT[] DEFAULT '{"in_app"}',
  metadata JSONB DEFAULT '{}',
  sent_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create group_milestones table for group deadlines and milestones
CREATE TABLE IF NOT EXISTS group_milestones (
  id BIGSERIAL PRIMARY KEY,
  group_id INTEGER NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  due_date TIMESTAMPTZ NOT NULL,
  completion_criteria JSONB DEFAULT '{}',
  is_completed BOOLEAN DEFAULT false,
  completed_at TIMESTAMPTZ,
  created_by UUID NOT NULL REFERENCES users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create group_tasks table for collaborative tasks
CREATE TABLE IF NOT EXISTS group_tasks (
  id BIGSERIAL PRIMARY KEY,
  group_id INTEGER NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  type VARCHAR(20) DEFAULT 'assignment' CHECK (type IN ('quiz', 'assignment', 'discussion', 'project', 'peer_review')),
  due_date TIMESTAMPTZ,
  points INTEGER DEFAULT 0,
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('draft', 'active', 'completed', 'overdue')),
  created_by UUID NOT NULL REFERENCES users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create task_submissions table for task submissions
CREATE TABLE IF NOT EXISTS task_submissions (
  id BIGSERIAL PRIMARY KEY,
  task_id INTEGER NOT NULL REFERENCES group_tasks(id) ON DELETE CASCADE,
  learner_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  content JSONB NOT NULL,
  submitted_at TIMESTAMPTZ DEFAULT NOW(),
  score DECIMAL(5,2),
  feedback TEXT,
  status VARCHAR(20) DEFAULT 'submitted' CHECK (status IN ('submitted', 'graded', 'returned')),
  graded_by UUID REFERENCES users(id),
  graded_at TIMESTAMPTZ,
  UNIQUE(task_id, learner_id)
);

-- Create group_member_activity table for tracking read status and activity
CREATE TABLE IF NOT EXISTS group_member_activity (
  id BIGSERIAL PRIMARY KEY,
  group_id INTEGER NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
  learner_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  last_read_at TIMESTAMPTZ DEFAULT NOW(),
  last_active_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(group_id, learner_id)
);

-- Create learner_group_preferences table for notification settings
CREATE TABLE IF NOT EXISTS learner_group_preferences (
  id BIGSERIAL PRIMARY KEY,
  learner_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  group_id INTEGER NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
  settings JSONB DEFAULT '{
    "email_notifications": true,
    "push_notifications": true,
    "chat_notifications": true,
    "assignment_notifications": true,
    "milestone_notifications": true,
    "announcement_notifications": true
  }',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(learner_id, group_id)
);

-- Create group_rewards table for gamification
CREATE TABLE IF NOT EXISTS group_rewards (
  id BIGSERIAL PRIMARY KEY,
  group_id INTEGER NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
  type VARCHAR(20) DEFAULT 'badge' CHECK (type IN ('badge', 'points', 'certificate')),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  criteria JSONB DEFAULT '{}',
  points_value INTEGER DEFAULT 0,
  badge_icon VARCHAR(255),
  earned_at TIMESTAMPTZ,
  earned_by UUID[] DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create group_posts table for social features
CREATE TABLE IF NOT EXISTS group_posts (
  id BIGSERIAL PRIMARY KEY,
  group_id INTEGER NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
  author_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  type VARCHAR(20) DEFAULT 'text' CHECK (type IN ('text', 'question', 'resource', 'announcement')),
  attachments JSONB DEFAULT '[]',
  likes_count INTEGER DEFAULT 0,
  comments_count INTEGER DEFAULT 0,
  is_pinned BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create group_kudos table for peer recognition
CREATE TABLE IF NOT EXISTS group_kudos (
  id BIGSERIAL PRIMARY KEY,
  group_id INTEGER NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
  giver_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  receiver_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  reason TEXT NOT NULL,
  points INTEGER DEFAULT 1,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_groups_tenant_status ON groups(tenant_id, status);
CREATE INDEX IF NOT EXISTS idx_groups_visibility ON groups(visibility);
CREATE INDEX IF NOT EXISTS idx_group_members_learner_id ON group_members(learner_id);
CREATE INDEX IF NOT EXISTS idx_group_members_role ON group_members(role);
CREATE INDEX IF NOT EXISTS idx_group_assignments_group_id ON group_assignments(group_id);
CREATE INDEX IF NOT EXISTS idx_group_assignments_path_id ON group_assignments(path_id);
CREATE INDEX IF NOT EXISTS idx_group_progress_group_id ON group_progress(group_id);
CREATE INDEX IF NOT EXISTS idx_group_messages_group_id ON group_messages(group_id);
CREATE INDEX IF NOT EXISTS idx_group_messages_sender_id ON group_messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_group_messages_sent_at ON group_messages(sent_at);
CREATE INDEX IF NOT EXISTS idx_group_milestones_group_id ON group_milestones(group_id);
CREATE INDEX IF NOT EXISTS idx_group_milestones_due_date ON group_milestones(due_date);
CREATE INDEX IF NOT EXISTS idx_group_tasks_group_id ON group_tasks(group_id);
CREATE INDEX IF NOT EXISTS idx_group_tasks_status ON group_tasks(status);
CREATE INDEX IF NOT EXISTS idx_task_submissions_task_id ON task_submissions(task_id);
CREATE INDEX IF NOT EXISTS idx_task_submissions_learner_id ON task_submissions(learner_id);
CREATE INDEX IF NOT EXISTS idx_group_member_activity_group_learner ON group_member_activity(group_id, learner_id);
CREATE INDEX IF NOT EXISTS idx_learner_group_preferences_learner ON learner_group_preferences(learner_id);
CREATE INDEX IF NOT EXISTS idx_group_rewards_group_id ON group_rewards(group_id);
CREATE INDEX IF NOT EXISTS idx_group_posts_group_id ON group_posts(group_id);
CREATE INDEX IF NOT EXISTS idx_group_posts_author_id ON group_posts(author_id);
CREATE INDEX IF NOT EXISTS idx_group_kudos_group_id ON group_kudos(group_id);
CREATE INDEX IF NOT EXISTS idx_group_kudos_receiver_id ON group_kudos(receiver_id);

-- Add updated_at triggers
CREATE TRIGGER update_groups_updated_at BEFORE UPDATE ON groups FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_group_assignments_updated_at BEFORE UPDATE ON group_assignments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_group_progress_updated_at BEFORE UPDATE ON group_progress FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_group_milestones_updated_at BEFORE UPDATE ON group_milestones FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_group_tasks_updated_at BEFORE UPDATE ON group_tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_group_member_activity_updated_at BEFORE UPDATE ON group_member_activity FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_learner_group_preferences_updated_at BEFORE UPDATE ON learner_group_preferences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_group_posts_updated_at BEFORE UPDATE ON group_posts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add constraints
ALTER TABLE group_progress ADD CONSTRAINT group_progress_members_check 
  CHECK (total_members = completed_members + in_progress_members + not_started_members);
ALTER TABLE group_progress ADD CONSTRAINT group_progress_completion_rate_check 
  CHECK (completion_rate >= 0 AND completion_rate <= 100);
ALTER TABLE group_progress ADD CONSTRAINT group_progress_average_score_check 
  CHECK (average_score IS NULL OR (average_score >= 0 AND average_score <= 100));

-- Insert sample data for development
INSERT INTO groups (tenant_id, name, description, status, visibility, created_by) VALUES
  (3, 'JavaScript Fundamentals Study Group', 'Learn JavaScript basics together', 'active', 'public', '5cd09c93-eb00-470f-a605-c6d0d057bdd6'),
  (3, 'React Advanced Concepts', 'Deep dive into React patterns and best practices', 'active', 'private', '5cd09c93-eb00-470f-a605-c6d0d057bdd6'),
  (3, 'Data Science Bootcamp', 'Collaborative learning for data science fundamentals', 'active', 'invite_only', '5cd09c93-eb00-470f-a605-c6d0d057bdd6')
ON CONFLICT DO NOTHING;

-- Add sample group members
INSERT INTO group_members (group_id, learner_id, role, added_by) VALUES
  (1, '5cd09c93-eb00-470f-a605-c6d0d057bdd6', 'admin', '5cd09c93-eb00-470f-a605-c6d0d057bdd6'),
  (2, '5cd09c93-eb00-470f-a605-c6d0d057bdd6', 'admin', '5cd09c93-eb00-470f-a605-c6d0d057bdd6'),
  (3, '5cd09c93-eb00-470f-a605-c6d0d057bdd6', 'admin', '5cd09c93-eb00-470f-a605-c6d0d057bdd6')
ON CONFLICT DO NOTHING;

-- Add sample group messages
INSERT INTO group_messages (group_id, sender_id, message, message_type) VALUES
  (1, '5cd09c93-eb00-470f-a605-c6d0d057bdd6', 'Welcome to the JavaScript Fundamentals Study Group! Let''s learn together.', 'announcement'),
  (1, '5cd09c93-eb00-470f-a605-c6d0d057bdd6', 'Has anyone started working on the first assignment?', 'chat'),
  (2, '5cd09c93-eb00-470f-a605-c6d0d057bdd6', 'This group focuses on advanced React concepts. Please share your experience level.', 'announcement')
ON CONFLICT DO NOTHING;

-- Add sample group milestones
INSERT INTO group_milestones (group_id, title, description, due_date, created_by) VALUES
  (1, 'Complete Module 1: Variables and Functions', 'All members should complete the first module', NOW() + INTERVAL '2 weeks', '5cd09c93-eb00-470f-a605-c6d0d057bdd6'),
  (1, 'Group Project: Build a Calculator', 'Collaborate on building a simple calculator app', NOW() + INTERVAL '1 month', '5cd09c93-eb00-470f-a605-c6d0d057bdd6'),
  (2, 'React Hooks Deep Dive', 'Master useState, useEffect, and custom hooks', NOW() + INTERVAL '3 weeks', '5cd09c93-eb00-470f-a605-c6d0d057bdd6')
ON CONFLICT DO NOTHING;
