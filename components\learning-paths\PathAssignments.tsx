'use client'

import React, { useState } from 'react'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Chip,
  Alert,
  Autocomplete,
  Switch,
  FormControlLabel,
  Divider
} from '@mui/material'
import {
  Add as AddIcon,
  People as PeopleIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  Upload as UploadIcon
} from '@mui/icons-material'
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'

import { PathCreationForm } from '@/lib/types/learning-paths'

interface PathAssignmentsProps {
  assignments: any[]
  onChange: (assignments: any[]) => void
  pathData: Partial<PathCreationForm>
}

const PathAssignments: React.FC<PathAssignmentsProps> = ({
  assignments,
  onChange,
  pathData
}) => {
  const [assignmentType, setAssignmentType] = useState<'individual' | 'group' | 'department' | 'bulk'>('individual')
  const [selectedLearners, setSelectedLearners] = useState<any[]>([])
  const [selectedGroups, setSelectedGroups] = useState<any[]>([])
  const [selectedDepartments, setSelectedDepartments] = useState<any[]>([])
  const [dueDate, setDueDate] = useState<Date | null>(null)
  const [autoAssign, setAutoAssign] = useState(false)
  const [assignmentRules, setAssignmentRules] = useState<any[]>([])

  // Mock data - in real app, these would come from API
  const mockLearners = [
    { id: '1', name: 'John Smith', email: '<EMAIL>', department: 'Engineering' },
    { id: '2', name: 'Sarah Johnson', email: '<EMAIL>', department: 'Marketing' },
    { id: '3', name: 'Mike Chen', email: '<EMAIL>', department: 'Sales' },
    { id: '4', name: 'Emily Davis', email: '<EMAIL>', department: 'HR' }
  ]

  const mockGroups = [
    { id: '1', name: 'New Hires 2024', memberCount: 15 },
    { id: '2', name: 'Senior Developers', memberCount: 8 },
    { id: '3', name: 'Sales Team Q1', memberCount: 12 },
    { id: '4', name: 'Leadership Circle', memberCount: 6 }
  ]

  const mockDepartments = [
    { id: '1', name: 'Engineering', memberCount: 45 },
    { id: '2', name: 'Marketing', memberCount: 23 },
    { id: '3', name: 'Sales', memberCount: 34 },
    { id: '4', name: 'HR', memberCount: 12 },
    { id: '5', name: 'Finance', memberCount: 18 }
  ]

  const handleAddAssignment = () => {
    const newAssignment = {
      id: `assignment-${Date.now()}`,
      type: assignmentType,
      learners: assignmentType === 'individual' ? selectedLearners : [],
      groups: assignmentType === 'group' ? selectedGroups : [],
      departments: assignmentType === 'department' ? selectedDepartments : [],
      due_date: dueDate?.toISOString(),
      auto_assign: autoAssign,
      created_at: new Date().toISOString()
    }

    onChange([...assignments, newAssignment])
    
    // Reset form
    setSelectedLearners([])
    setSelectedGroups([])
    setSelectedDepartments([])
    setDueDate(null)
  }

  const handleRemoveAssignment = (assignmentId: string) => {
    onChange(assignments.filter(a => a.id !== assignmentId))
  }

  const getAssignmentSummary = (assignment: any) => {
    switch (assignment.type) {
      case 'individual':
        return `${assignment.learners.length} individual learner${assignment.learners.length !== 1 ? 's' : ''}`
      case 'group':
        return `${assignment.groups.length} group${assignment.groups.length !== 1 ? 's' : ''}`
      case 'department':
        return `${assignment.departments.length} department${assignment.departments.length !== 1 ? 's' : ''}`
      case 'bulk':
        return 'Bulk upload assignment'
      default:
        return 'Unknown assignment type'
    }
  }

  return (
    <Box>
      <Grid container spacing={3}>
        {/* Assignment Type Selection */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Assignment Type
              </Typography>
              <FormControl fullWidth>
                <InputLabel>How would you like to assign this path?</InputLabel>
                <Select
                  value={assignmentType}
                  label="How would you like to assign this path?"
                  onChange={(e) => setAssignmentType(e.target.value as any)}
                >
                  <MenuItem value="individual">
                    <Box display="flex" alignItems="center" gap={1}>
                      <PersonIcon />
                      Individual Learners
                    </Box>
                  </MenuItem>
                  <MenuItem value="group">
                    <Box display="flex" alignItems="center" gap={1}>
                      <PeopleIcon />
                      Groups
                    </Box>
                  </MenuItem>
                  <MenuItem value="department">
                    <Box display="flex" alignItems="center" gap={1}>
                      <BusinessIcon />
                      Departments
                    </Box>
                  </MenuItem>
                  <MenuItem value="bulk">
                    <Box display="flex" alignItems="center" gap={1}>
                      <UploadIcon />
                      Bulk Upload (CSV)
                    </Box>
                  </MenuItem>
                </Select>
              </FormControl>
            </CardContent>
          </Card>
        </Grid>

        {/* Assignment Configuration */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Assignment Configuration
              </Typography>

              {assignmentType === 'individual' && (
                <Autocomplete
                  multiple
                  options={mockLearners}
                  getOptionLabel={(option) => `${option.name} (${option.email})`}
                  value={selectedLearners}
                  onChange={(_, newValue) => setSelectedLearners(newValue)}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => (
                      <Chip
                        variant="outlined"
                        label={option.name}
                        {...getTagProps({ index })}
                        key={option.id}
                      />
                    ))
                  }
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Select Learners"
                      placeholder="Search and select learners..."
                    />
                  )}
                />
              )}

              {assignmentType === 'group' && (
                <Autocomplete
                  multiple
                  options={mockGroups}
                  getOptionLabel={(option) => `${option.name} (${option.memberCount} members)`}
                  value={selectedGroups}
                  onChange={(_, newValue) => setSelectedGroups(newValue)}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => (
                      <Chip
                        variant="outlined"
                        label={option.name}
                        {...getTagProps({ index })}
                        key={option.id}
                      />
                    ))
                  }
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Select Groups"
                      placeholder="Search and select groups..."
                    />
                  )}
                />
              )}

              {assignmentType === 'department' && (
                <Autocomplete
                  multiple
                  options={mockDepartments}
                  getOptionLabel={(option) => `${option.name} (${option.memberCount} members)`}
                  value={selectedDepartments}
                  onChange={(_, newValue) => setSelectedDepartments(newValue)}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => (
                      <Chip
                        variant="outlined"
                        label={option.name}
                        {...getTagProps({ index })}
                        key={option.id}
                      />
                    ))
                  }
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Select Departments"
                      placeholder="Search and select departments..."
                    />
                  )}
                />
              )}

              {assignmentType === 'bulk' && (
                <Box>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Upload a CSV file with learner emails or IDs to assign this path in bulk.
                  </Alert>
                  <Button
                    variant="outlined"
                    startIcon={<UploadIcon />}
                    component="label"
                    fullWidth
                  >
                    Upload CSV File
                    <input type="file" accept=".csv" hidden />
                  </Button>
                </Box>
              )}

              <Box mt={3}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DateTimePicker
                    label="Due Date (Optional)"
                    value={dueDate}
                    onChange={setDueDate}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        helperText: 'Leave empty for no due date'
                      }
                    }}
                  />
                </LocalizationProvider>
              </Box>

              <Box mt={2}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={autoAssign}
                      onChange={(e) => setAutoAssign(e.target.checked)}
                    />
                  }
                  label="Auto-assign to new members matching criteria"
                />
              </Box>

              <Box mt={3}>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleAddAssignment}
                  disabled={
                    (assignmentType === 'individual' && selectedLearners.length === 0) ||
                    (assignmentType === 'group' && selectedGroups.length === 0) ||
                    (assignmentType === 'department' && selectedDepartments.length === 0)
                  }
                >
                  Add Assignment
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Current Assignments */}
        {assignments.length > 0 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Current Assignments
                </Typography>
                
                {assignments.map((assignment, index) => (
                  <Box key={assignment.id}>
                    <Box display="flex" justifyContent="space-between" alignItems="center" py={2}>
                      <Box>
                        <Typography variant="body1" fontWeight="medium">
                          {getAssignmentSummary(assignment)}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {assignment.due_date 
                            ? `Due: ${new Date(assignment.due_date).toLocaleDateString()}`
                            : 'No due date'
                          }
                          {assignment.auto_assign && ' • Auto-assign enabled'}
                        </Typography>
                      </Box>
                      <Button
                        color="error"
                        onClick={() => handleRemoveAssignment(assignment.id)}
                        size="small"
                      >
                        Remove
                      </Button>
                    </Box>
                    {index < assignments.length - 1 && <Divider />}
                  </Box>
                ))}
              </CardContent>
            </Card>
          </Grid>
        )}

        {/* Assignment Summary */}
        <Grid item xs={12}>
          <Alert severity="info">
            <Typography variant="body2">
              <strong>Assignment Summary:</strong> This learning path will be assigned to{' '}
              {assignments.length === 0 
                ? 'no one yet' 
                : `${assignments.length} assignment rule${assignments.length !== 1 ? 's' : ''}`
              }.
              {assignments.length === 0 && ' You can skip this step and assign learners later.'}
            </Typography>
          </Alert>
        </Grid>
      </Grid>
    </Box>
  )
}

export default PathAssignments
