'use client'

import { useState, useEffect } from 'react'
import {
  Box,
  Typography,
  Avatar,
  Chip,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  useTheme,
} from '@mui/material'
import {
  WbSunny as SunIcon,
  Brightness3 as MoonIcon,
  Psychology as BrainIcon,
  EmojiEvents as TrophyIcon,
  Favorite as HeartIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'

interface PersonalizedWelcomeProps {
  user: any
  greeting: string
  gamification: any
  currentMood: string | null
  onMoodChange: (mood: string) => void
}

const inspirationalQuotes = [
  "Great leaders create more leaders, not followers.",
  "The best way to predict the future is to create it.",
  "Success is not final, failure is not fatal: it is the courage to continue that counts.",
  "Innovation distinguishes between a leader and a follower.",
  "The only way to do great work is to love what you do.",
]

const moodEmojis = ['😊', '😴', '🔥', '🧘', '🤔', '💪', '🎯', '✨']

export default function PersonalizedWelcome({
  user,
  greeting,
  gamification,
  currentMood,
  onMoodChange,
}: PersonalizedWelcomeProps) {
  const theme = useTheme()
  const [currentQuote, setCurrentQuote] = useState('')
  const [showMoodSelector, setShowMoodSelector] = useState(false)

  useEffect(() => {
    // Rotate inspirational quotes
    const randomQuote = inspirationalQuotes[Math.floor(Math.random() * inspirationalQuotes.length)]
    setCurrentQuote(randomQuote)
  }, [])

  const getTimeIcon = () => {
    const hour = new Date().getHours()
    if (hour >= 6 && hour < 18) {
      return <SunIcon sx={{ color: 'orange' }} />
    }
    return <MoonIcon sx={{ color: 'purple' }} />
  }

  const getUserStats = () => {
    // Mock stats - in real app, these would come from API
    return {
      usersManaged: 247,
      coursesCreated: 12,
      weeklyGoal: 85, // percentage
    }
  }

  const stats = getUserStats()

  return (
    <motion.div
      initial={{ opacity: 0, y: -30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
    >
      <Card
        sx={{
          mb: 3,
          background: theme.palette.mode === 'dark'
            ? 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)'
            : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
            opacity: 0.3,
          },
        }}
      >
        <CardContent sx={{ position: 'relative', zIndex: 1 }}>
          <Box display="flex" justifyContent="space-between" alignItems="flex-start">
            <Box flex={1}>
              <Box display="flex" alignItems="center" gap={2} mb={2}>
                {getTimeIcon()}
                <Typography variant="h4" fontWeight="bold">
                  {greeting}, {user?.full_name || user?.email?.split('@')[0] || 'Admin'}!
                </Typography>
                <motion.div
                  animate={{ rotate: [0, 10, -10, 0] }}
                  transition={{ duration: 2, repeat: Infinity, repeatDelay: 5 }}
                >
                  👋
                </motion.div>
              </Box>

              <Typography variant="body1" sx={{ mb: 2, opacity: 0.9 }}>
                {currentQuote}
              </Typography>

              <Box display="flex" gap={2} flexWrap="wrap">
                <Chip
                  icon={<TrophyIcon />}
                  label={`Level ${gamification.level} - ${gamification.rank}`}
                  sx={{ 
                    bgcolor: 'rgba(255,255,255,0.2)', 
                    color: 'white',
                    '& .MuiChip-icon': { color: 'white' }
                  }}
                />
                <Chip
                  icon={<BrainIcon />}
                  label={`${stats.usersManaged} Users Managed`}
                  sx={{ 
                    bgcolor: 'rgba(255,255,255,0.2)', 
                    color: 'white',
                    '& .MuiChip-icon': { color: 'white' }
                  }}
                />
                <Chip
                  icon={<HeartIcon />}
                  label={`${stats.coursesCreated} Courses Created`}
                  sx={{ 
                    bgcolor: 'rgba(255,255,255,0.2)', 
                    color: 'white',
                    '& .MuiChip-icon': { color: 'white' }
                  }}
                />
              </Box>
            </Box>

            <Box display="flex" flexDirection="column" alignItems="center" gap={2}>
              <Avatar
                src={user?.avatar_url}
                sx={{ 
                  width: 80, 
                  height: 80,
                  border: '3px solid rgba(255,255,255,0.3)',
                  boxShadow: '0 8px 32px rgba(0,0,0,0.2)',
                }}
              >
                {user?.full_name?.[0] || user?.email?.[0] || 'A'}
              </Avatar>

              {/* Mood Selector */}
              <Box position="relative">
                <Tooltip title="How are you feeling today?">
                  <IconButton
                    onClick={() => setShowMoodSelector(!showMoodSelector)}
                    sx={{ 
                      bgcolor: 'rgba(255,255,255,0.2)',
                      color: 'white',
                      '&:hover': { bgcolor: 'rgba(255,255,255,0.3)' }
                    }}
                  >
                    {currentMood || '😊'}
                  </IconButton>
                </Tooltip>

                {showMoodSelector && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    style={{
                      position: 'absolute',
                      top: '100%',
                      right: 0,
                      zIndex: 10,
                      background: 'rgba(255,255,255,0.95)',
                      borderRadius: 12,
                      padding: 8,
                      display: 'flex',
                      gap: 4,
                      boxShadow: '0 8px 32px rgba(0,0,0,0.2)',
                      backdropFilter: 'blur(10px)',
                    }}
                  >
                    {moodEmojis.map((emoji) => (
                      <IconButton
                        key={emoji}
                        onClick={() => {
                          onMoodChange(emoji)
                          setShowMoodSelector(false)
                        }}
                        sx={{ 
                          fontSize: '1.5rem',
                          '&:hover': { transform: 'scale(1.2)' }
                        }}
                      >
                        {emoji}
                      </IconButton>
                    ))}
                  </motion.div>
                )}
              </Box>
            </Box>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  )
}
