'use client'

import { useState } from 'react'
import {
  Box,
  Drawer,
  App<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  List,
  Typo<PERSON>,
  Divider,
  IconButton,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  useTheme,
  useMediaQuery,
} from '@mui/material'
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  School as SchoolIcon,
  People as PeopleIcon,
  LibraryBooks as LibraryIcon,
  Assessment as ReportsIcon,
  Psychology as CompetenciesIcon,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  Logout as LogoutIcon,
  AccountCircle as AccountIcon,
  Groups as GroupsIcon,
} from '@mui/icons-material'

import { useAuthStore, useUIStore } from '@/lib/store'
import { AuthService } from '@/lib/auth'
import SidebarItem from '@/components/layout/SidebarItem'
import NotificationPanel from '@/components/layout/NotificationPanel'

const drawerWidth = 280

interface DashboardLayoutProps {
  children: React.ReactNode
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const { user, logout } = useAuthStore()
  const { sidebarOpen, setSidebarOpen } = useUIStore()
  
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [notificationAnchor, setNotificationAnchor] = useState<null | HTMLElement>(null)

  const handleDrawerToggle = () => {
    setSidebarOpen(!sidebarOpen)
  }

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleProfileMenuClose = () => {
    setAnchorEl(null)
  }

  const handleNotificationOpen = (event: React.MouseEvent<HTMLElement>) => {
    setNotificationAnchor(event.currentTarget)
  }

  const handleNotificationClose = () => {
    setNotificationAnchor(null)
  }

  const handleLogout = async () => {
    await AuthService.signOut()
    handleProfileMenuClose()
  }

  const sidebarItems = [
    {
      text: 'Dashboard',
      icon: <DashboardIcon />,
      href: '/dashboard',
      permission: 'view_dashboard',
    },
    {
      text: 'Learning Paths',
      icon: <SchoolIcon />,
      href: '/dashboard/learning-paths',
      permission: 'view_learning_paths',
    },
    {
      text: 'Learners',
      icon: <PeopleIcon />,
      href: '/dashboard/learners',
      permission: 'view_learners',
    },
    {
      text: 'Groups & Batches',
      icon: <GroupsIcon />,
      href: '/dashboard/groups',
      permission: 'view_groups',
    },
    {
      text: 'Content Library',
      icon: <LibraryIcon />,
      href: '/dashboard/content',
      permission: 'view_content',
    },
    {
      text: 'Reports',
      icon: <ReportsIcon />,
      href: '/dashboard/reports',
      permission: 'view_reports',
    },
    {
      text: 'Competencies',
      icon: <CompetenciesIcon />,
      href: '/dashboard/competencies',
      permission: 'view_competencies',
    },
    {
      text: 'Settings',
      icon: <SettingsIcon />,
      href: '/dashboard/settings',
      permission: 'view_settings',
    },
  ]

  const drawer = (
    <Box>
      <Box
        sx={{
          p: 2,
          display: 'flex',
          alignItems: 'center',
          minHeight: 64,
        }}
      >
        <Typography variant="h6" noWrap component="div" fontWeight="bold">
          ZenithLearn AI
        </Typography>
      </Box>
      <Divider />
      <List sx={{ px: 1 }}>
        {sidebarItems.map((item) => (
          <SidebarItem
            key={item.text}
            text={item.text}
            icon={item.icon}
            href={item.href}
            permission={item.permission}
          />
        ))}
      </List>
    </Box>
  )

  return (
    <Box sx={{ display: 'flex' }}>
      <AppBar
        position="fixed"
        sx={{
          width: { md: `calc(100% - ${sidebarOpen ? drawerWidth : 0}px)` },
          ml: { md: sidebarOpen ? `${drawerWidth}px` : 0 },
          transition: theme.transitions.create(['width', 'margin'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>

          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            Admin Dashboard
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <IconButton
              color="inherit"
              onClick={handleNotificationOpen}
              aria-label="notifications"
            >
              <Badge badgeContent={3} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>

            <IconButton
              onClick={handleProfileMenuOpen}
              sx={{ p: 0 }}
              aria-label="account menu"
            >
              <Avatar
                src={user?.avatar_url}
                alt={user?.full_name || user?.email}
                sx={{ width: 32, height: 32 }}
              >
                {user?.full_name?.[0] || user?.email?.[0]}
              </Avatar>
            </IconButton>
          </Box>
        </Toolbar>
      </AppBar>

      <Box
        component="nav"
        sx={{ width: { md: sidebarOpen ? drawerWidth : 0 }, flexShrink: { md: 0 } }}
      >
        <Drawer
          variant={isMobile ? 'temporary' : 'persistent'}
          open={sidebarOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile.
          }}
          sx={{
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
            },
          }}
        >
          {drawer}
        </Drawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { md: `calc(100% - ${sidebarOpen ? drawerWidth : 0}px)` },
          minHeight: '100vh',
          bgcolor: 'background.default',
          transition: theme.transitions.create(['width'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
        }}
      >
        <Toolbar />
        {children}
      </Box>

      {/* Profile Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleProfileMenuClose}
        onClick={handleProfileMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={handleProfileMenuClose}>
          <AccountIcon sx={{ mr: 1 }} />
          Profile
        </MenuItem>
        <MenuItem onClick={handleLogout}>
          <LogoutIcon sx={{ mr: 1 }} />
          Logout
        </MenuItem>
      </Menu>

      {/* Notification Panel */}
      <NotificationPanel
        anchorEl={notificationAnchor}
        open={Boolean(notificationAnchor)}
        onClose={handleNotificationClose}
      />
    </Box>
  )
}
