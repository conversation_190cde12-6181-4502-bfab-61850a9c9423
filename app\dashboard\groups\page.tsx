'use client'

import { useState, useEffect } from 'react'
import {
  Box,
  Typo<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  Skeleton,
} from '@mui/material'
import {
  Add as AddIcon,
  Groups as GroupsIcon,
  People as PeopleIcon,
  School as SchoolIcon,
  TrendingUp as TrendingUpIcon,
  FilterList as FilterIcon,
  ViewModule as ViewModuleIcon,
  ViewList as ViewListIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'

import { useGroupsStore } from '@/lib/store'
import { GroupsService } from '@/lib/services/groups'
import GroupsDashboard from '@/components/groups/GroupsDashboard'
import GroupForm from '@/components/groups/GroupForm'
import GroupFilters from '@/components/groups/GroupFilters'
import { Group } from '@/lib/types/groups'

export default function GroupsPage() {
  const {
    groups,
    loading,
    filters,
    viewMode,
    selectedGroups,
    setGroups,
    setLoading,
    setFilters,
    setViewMode,
    addGroup,
    updateGroup,
    deleteGroup,
  } = useGroupsStore()

  const [createModalOpen, setCreateModalOpen] = useState(false)
  const [editingGroup, setEditingGroup] = useState<Group | null>(null)
  const [filtersOpen, setFiltersOpen] = useState(false)
  const [stats, setStats] = useState({
    totalGroups: 0,
    activeGroups: 0,
    totalMembers: 0,
    averageCompletion: 0,
  })

  useEffect(() => {
    loadGroups()
    loadStats()
  }, [filters])

  const loadGroups = async () => {
    try {
      setLoading(true)
      const response = await GroupsService.getGroups(filters)
      setGroups(response.data)
    } catch (error) {
      console.error('Failed to load groups:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const statsData = await GroupsService.getGroupStats()
      setStats(statsData)
    } catch (error) {
      console.error('Failed to load stats:', error)
    }
  }

  const handleCreateGroup = async (groupData: Partial<Group>) => {
    try {
      const newGroup = await GroupsService.createGroup(groupData)
      addGroup(newGroup)
      setCreateModalOpen(false)
    } catch (error) {
      console.error('Failed to create group:', error)
    }
  }

  const handleEditGroup = async (groupData: Partial<Group>) => {
    if (!editingGroup) return
    
    try {
      const updatedGroup = await GroupsService.updateGroup(editingGroup.id, groupData)
      updateGroup(editingGroup.id, updatedGroup)
      setEditingGroup(null)
    } catch (error) {
      console.error('Failed to update group:', error)
    }
  }

  const handleDeleteGroup = async (groupId: number) => {
    try {
      await GroupsService.deleteGroup(groupId)
      deleteGroup(groupId)
    } catch (error) {
      console.error('Failed to delete group:', error)
    }
  }

  const handleViewGroup = (group: Group) => {
    // Navigate to group details page
    window.location.href = `/dashboard/groups/${group.id}`
  }

  const statsCards = [
    {
      title: 'Total Groups',
      value: stats.totalGroups,
      icon: <GroupsIcon />,
      color: 'primary',
      change: '+12%',
    },
    {
      title: 'Active Groups',
      value: stats.activeGroups,
      icon: <TrendingUpIcon />,
      color: 'success',
      change: '+8%',
    },
    {
      title: 'Total Members',
      value: stats.totalMembers,
      icon: <PeopleIcon />,
      color: 'info',
      change: '+15%',
    },
    {
      title: 'Avg. Completion',
      value: `${stats.averageCompletion}%`,
      icon: <SchoolIcon />,
      color: 'warning',
      change: '+5%',
    },
  ]

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            Groups & Batches
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Filters">
              <IconButton
                onClick={() => setFiltersOpen(!filtersOpen)}
                color={filtersOpen ? 'primary' : 'default'}
              >
                <FilterIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Grid View">
              <IconButton
                onClick={() => setViewMode('grid')}
                color={viewMode === 'grid' ? 'primary' : 'default'}
              >
                <ViewModuleIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="List View">
              <IconButton
                onClick={() => setViewMode('list')}
                color={viewMode === 'list' ? 'primary' : 'default'}
              >
                <ViewListIcon />
              </IconButton>
            </Tooltip>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setCreateModalOpen(true)}
              sx={{ ml: 1 }}
            >
              Create Group
            </Button>
          </Box>
        </Box>
        
        <Typography variant="body1" color="text.secondary">
          Organize learners into groups and batches for collaborative learning and streamlined management.
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {statsCards.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={stat.title}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card sx={{ height: '100%' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box
                      sx={{
                        p: 1,
                        borderRadius: 2,
                        bgcolor: `${stat.color}.light`,
                        color: `${stat.color}.main`,
                        mr: 2,
                      }}
                    >
                      {stat.icon}
                    </Box>
                    <Box>
                      <Typography variant="h4" fontWeight="bold">
                        {loading ? <Skeleton width={60} /> : stat.value}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {stat.title}
                      </Typography>
                    </Box>
                  </Box>
                  <Chip
                    label={stat.change}
                    size="small"
                    color="success"
                    variant="outlined"
                  />
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Filters */}
      {filtersOpen && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
        >
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <GroupFilters
                filters={filters}
                onFiltersChange={setFilters}
                onReset={() => setFilters({})}
              />
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Groups Dashboard */}
      <GroupsDashboard
        groups={groups}
        loading={loading}
        viewMode={viewMode}
        selectedGroups={selectedGroups}
        onCreateGroup={() => setCreateModalOpen(true)}
        onEditGroup={setEditingGroup}
        onDeleteGroup={handleDeleteGroup}
        onViewGroup={handleViewGroup}
      />

      {/* Create Group Modal */}
      <GroupForm
        open={createModalOpen}
        onClose={() => setCreateModalOpen(false)}
        onSubmit={handleCreateGroup}
        title="Create New Group"
      />

      {/* Edit Group Modal */}
      <GroupForm
        open={!!editingGroup}
        onClose={() => setEditingGroup(null)}
        onSubmit={handleEditGroup}
        group={editingGroup}
        title="Edit Group"
      />
    </Box>
  )
}
