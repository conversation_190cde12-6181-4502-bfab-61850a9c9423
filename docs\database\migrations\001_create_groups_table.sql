-- Migration: 001_create_groups_table.sql
-- Description: Create and enhance the groups table with tenant isolation and hierarchy support
-- Date: 2024-01-15
-- Author: ZenithLearn AI Development Team

-- ============================================================================
-- GROUPS TABLE CREATION AND ENHANCEMENT
-- ============================================================================

-- Add missing columns to existing groups table
ALTER TABLE groups 
  ADD COLUMN IF NOT EXISTS start_date TIMESTAMPTZ,
  ADD COLUMN IF NOT EXISTS end_date TIMESTAMPTZ,
  ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active',
  ADD COLUMN IF NOT EXISTS tags TEXT[] DEFAULT '{}',
  ADD COLUMN IF NOT EXISTS parent_group_id INTEGER,
  ADD COLUMN IF NOT EXISTS settings JSONB DEFAULT '{}';

-- Ensure required columns are NOT NULL
ALTER TABLE groups ALTER COLUMN tenant_id SET NOT NULL;
ALTER TABLE groups ALTER COLUMN created_by SET NOT NULL;

-- ============================================================================
-- CONSTRAINTS
-- ============================================================================

-- Add constraints with conditional logic to avoid conflicts
DO $$
BEGIN
  -- Add status check constraint
  IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'groups_status_check') THEN
    ALTER TABLE groups ADD CONSTRAINT groups_status_check 
      CHECK (status IN ('active', 'inactive', 'archived', 'draft'));
  END IF;
  
  -- Add date check constraint
  IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'groups_date_check') THEN
    ALTER TABLE groups ADD CONSTRAINT groups_date_check 
      CHECK (start_date IS NULL OR end_date IS NULL OR start_date <= end_date);
  END IF;
  
  -- Add parent foreign key constraint
  IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'groups_parent_fk') THEN
    ALTER TABLE groups ADD CONSTRAINT groups_parent_fk 
      FOREIGN KEY (parent_group_id) REFERENCES groups(id) ON DELETE SET NULL;
  END IF;
  
  -- Add self-parent check constraint
  IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'groups_no_self_parent') THEN
    ALTER TABLE groups ADD CONSTRAINT groups_no_self_parent 
      CHECK (id != parent_group_id);
  END IF;
  
  -- Add unique name per tenant constraint
  IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'groups_name_tenant_unique') THEN
    ALTER TABLE groups ADD CONSTRAINT groups_name_tenant_unique 
      UNIQUE (tenant_id, name);
  END IF;
END $$;

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Create indexes for optimal query performance
CREATE INDEX IF NOT EXISTS idx_groups_tenant_id ON groups(tenant_id);
CREATE INDEX IF NOT EXISTS idx_groups_status ON groups(status);
CREATE INDEX IF NOT EXISTS idx_groups_parent_id ON groups(parent_group_id);
CREATE INDEX IF NOT EXISTS idx_groups_created_by ON groups(created_by);
CREATE INDEX IF NOT EXISTS idx_groups_tags ON groups USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_groups_created_at ON groups(created_at);
CREATE INDEX IF NOT EXISTS idx_groups_start_date ON groups(start_date);
CREATE INDEX IF NOT EXISTS idx_groups_end_date ON groups(end_date);

-- ============================================================================
-- TRIGGERS
-- ============================================================================

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for groups table
DROP TRIGGER IF EXISTS update_groups_updated_at ON groups;
CREATE TRIGGER update_groups_updated_at
  BEFORE UPDATE ON groups
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- ROW LEVEL SECURITY (RLS)
-- ============================================================================

-- Enable RLS on groups table
ALTER TABLE groups ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist to avoid conflicts
DROP POLICY IF EXISTS "Users can view groups in their tenant" ON groups;
DROP POLICY IF EXISTS "Users can create groups in their tenant" ON groups;
DROP POLICY IF EXISTS "Users can update groups in their tenant" ON groups;
DROP POLICY IF EXISTS "Users can delete groups in their tenant" ON groups;

-- Create comprehensive RLS policies for groups table
CREATE POLICY "Users can view groups in their tenant" ON groups
  FOR SELECT USING (
    tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid())
  );

CREATE POLICY "Users can create groups in their tenant" ON groups
  FOR INSERT WITH CHECK (
    tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid())
    AND created_by = auth.uid()
  );

CREATE POLICY "Users can update groups in their tenant" ON groups
  FOR UPDATE USING (
    tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid())
  );

CREATE POLICY "Users can delete groups in their tenant" ON groups
  FOR DELETE USING (
    tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid())
  );

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE groups IS 'Core groups table with tenant isolation and hierarchy support';
COMMENT ON COLUMN groups.id IS 'Primary key for the group';
COMMENT ON COLUMN groups.tenant_id IS 'Foreign key to tenants table for multi-tenancy';
COMMENT ON COLUMN groups.name IS 'Human-readable name of the group';
COMMENT ON COLUMN groups.description IS 'Optional description of the group purpose';
COMMENT ON COLUMN groups.start_date IS 'Optional start date for the group activities';
COMMENT ON COLUMN groups.end_date IS 'Optional end date for the group activities';
COMMENT ON COLUMN groups.status IS 'Current status: active, inactive, archived, or draft';
COMMENT ON COLUMN groups.tags IS 'Array of tags for categorization and filtering';
COMMENT ON COLUMN groups.parent_group_id IS 'Optional parent group for hierarchical structure';
COMMENT ON COLUMN groups.settings IS 'JSONB field for flexible group configuration';
COMMENT ON COLUMN groups.created_by IS 'UUID of the user who created the group';
COMMENT ON COLUMN groups.created_at IS 'Timestamp when the group was created';
COMMENT ON COLUMN groups.updated_at IS 'Timestamp when the group was last updated';

-- ============================================================================
-- VERIFICATION
-- ============================================================================

-- Verify the table structure
DO $$
DECLARE
  column_count INTEGER;
  constraint_count INTEGER;
  index_count INTEGER;
  policy_count INTEGER;
BEGIN
  -- Count columns
  SELECT COUNT(*) INTO column_count
  FROM information_schema.columns
  WHERE table_name = 'groups' AND table_schema = 'public';
  
  -- Count constraints
  SELECT COUNT(*) INTO constraint_count
  FROM pg_constraint
  WHERE conrelid = 'groups'::regclass;
  
  -- Count indexes
  SELECT COUNT(*) INTO index_count
  FROM pg_indexes
  WHERE tablename = 'groups' AND schemaname = 'public';
  
  -- Count RLS policies
  SELECT COUNT(*) INTO policy_count
  FROM pg_policies
  WHERE tablename = 'groups' AND schemaname = 'public';
  
  -- Log results
  RAISE NOTICE 'Groups table migration completed successfully:';
  RAISE NOTICE '  - Columns: %', column_count;
  RAISE NOTICE '  - Constraints: %', constraint_count;
  RAISE NOTICE '  - Indexes: %', index_count;
  RAISE NOTICE '  - RLS Policies: %', policy_count;
  
  -- Verify minimum requirements
  IF column_count < 13 THEN
    RAISE EXCEPTION 'Groups table missing required columns. Expected at least 13, found %', column_count;
  END IF;
  
  IF policy_count < 4 THEN
    RAISE EXCEPTION 'Groups table missing RLS policies. Expected at least 4, found %', policy_count;
  END IF;
END $$;

-- ============================================================================
-- MIGRATION COMPLETE
-- ============================================================================

-- Log successful completion
SELECT 'Migration 001_create_groups_table.sql completed successfully at ' || NOW() as migration_status;
