'use client'

import { useState } from 'react'
import {
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Box,
  Alert,
  Button,
  Chip,
  LinearProgress,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Tooltip,
  Skeleton,
  Divider,
} from '@mui/material'
import {
  Warning as WarningIcon,
  TrendingDown as TrendingDownIcon,
  Person as PersonIcon,
  School as SchoolIcon,
  Assignment as AssignmentIcon,
  Refresh as RefreshIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'
import { useQuery } from '@tanstack/react-query'

import { supabase } from '@/lib/supabase'
import { useAuthStore } from '@/lib/store'

interface SkillGap {
  id: number
  learner_id: string
  competency_id: number
  gap_score: number
  confidence_score: number
  status: 'open' | 'addressed' | 'dismissed'
  detected_at: string
  learner: {
    full_name: string
    email: string
    avatar_url?: string
    department?: string
  }
  competency: {
    name: string
    category: string
  }
  suggested_path?: {
    id: number
    title: string
  }
}

export default function SkillGapDetector() {
  const { user } = useAuthStore()
  const [selectedGap, setSelectedGap] = useState<SkillGap | null>(null)

  // Fetch skill gaps
  const { data: skillGaps = [], isLoading, refetch } = useQuery({
    queryKey: ['skill-gaps', user?.tenant_id],
    queryFn: async (): Promise<SkillGap[]> => {
      if (!user?.tenant_id) return []

      const { data, error } = await supabase
        .from('skill_gaps')
        .select(`
          *,
          users!skill_gaps_learner_id_fkey(
            full_name,
            email,
            avatar_url,
            department
          ),
          competencies!skill_gaps_competency_id_fkey(
            name,
            category
          ),
          learning_paths!skill_gaps_suggested_path_id_fkey(
            id,
            title
          )
        `)
        .eq('users.tenant_id', user.tenant_id)
        .eq('status', 'open')
        .order('gap_score', { ascending: false })
        .limit(10)

      if (error) throw error

      return data?.map(gap => ({
        ...gap,
        learner: gap.users,
        competency: gap.competencies,
        suggested_path: gap.learning_paths,
      })) || []
    },
    enabled: !!user?.tenant_id,
    refetchInterval: 60000, // Refresh every minute
  })

  // Calculate summary statistics
  const criticalGaps = skillGaps.filter(gap => gap.gap_score >= 0.8).length
  const moderateGaps = skillGaps.filter(gap => gap.gap_score >= 0.5 && gap.gap_score < 0.8).length
  const totalLearners = new Set(skillGaps.map(gap => gap.learner_id)).size

  const getGapSeverity = (score: number) => {
    if (score >= 0.8) return { label: 'Critical', color: 'error' as const }
    if (score >= 0.5) return { label: 'Moderate', color: 'warning' as const }
    return { label: 'Low', color: 'info' as const }
  }

  const getGapIcon = (score: number) => {
    if (score >= 0.8) return <WarningIcon color="error" />
    if (score >= 0.5) return <TrendingDownIcon color="warning" />
    return <TrendingDownIcon color="info" />
  }

  const handleAssignPath = (gap: SkillGap) => {
    // TODO: Implement path assignment logic
    console.log('Assign path for gap:', gap)
  }

  const handleViewLearner = (gap: SkillGap) => {
    // TODO: Navigate to learner profile
    console.log('View learner:', gap.learner)
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Skill Gap Analysis
          </Typography>
          <Box display="flex" flexDirection="column" gap={2}>
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} variant="rectangular" height={80} />
            ))}
          </Box>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6">
            Skill Gap Analysis
          </Typography>
          <Tooltip title="Refresh Analysis">
            <IconButton onClick={() => refetch()} size="small">
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>

        {/* Summary Statistics */}
        <Box display="flex" gap={2} mb={3}>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <Alert 
              severity="error" 
              variant="outlined"
              sx={{ flex: 1, minWidth: 0 }}
            >
              <Typography variant="body2" fontWeight="bold">
                {criticalGaps} Critical Gaps
              </Typography>
            </Alert>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <Alert 
              severity="warning" 
              variant="outlined"
              sx={{ flex: 1, minWidth: 0 }}
            >
              <Typography variant="body2" fontWeight="bold">
                {moderateGaps} Moderate Gaps
              </Typography>
            </Alert>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <Alert 
              severity="info" 
              variant="outlined"
              sx={{ flex: 1, minWidth: 0 }}
            >
              <Typography variant="body2" fontWeight="bold">
                {totalLearners} Learners Affected
              </Typography>
            </Alert>
          </motion.div>
        </Box>

        {skillGaps.length === 0 ? (
          <Alert severity="success">
            <Typography variant="body2">
              Great! No significant skill gaps detected at this time.
            </Typography>
          </Alert>
        ) : (
          <>
            <Typography variant="body2" color="textSecondary" gutterBottom>
              Top skill gaps requiring attention:
            </Typography>
            
            <List disablePadding>
              {skillGaps.map((gap, index) => {
                const severity = getGapSeverity(gap.gap_score)
                
                return (
                  <motion.div
                    key={gap.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <ListItem
                      divider={index < skillGaps.length - 1}
                      sx={{
                        px: 0,
                        '&:hover': {
                          backgroundColor: 'action.hover',
                          borderRadius: 1,
                        },
                      }}
                    >
                      <ListItemAvatar>
                        <Avatar
                          src={gap.learner.avatar_url}
                          sx={{ width: 40, height: 40 }}
                        >
                          {gap.learner.full_name?.[0] || gap.learner.email[0]}
                        </Avatar>
                      </ListItemAvatar>
                      
                      <ListItemText
                        primary={
                          <Box display="flex" alignItems="center" gap={1}>
                            <Typography variant="subtitle2" noWrap>
                              {gap.learner.full_name || gap.learner.email}
                            </Typography>
                            <Chip
                              label={severity.label}
                              size="small"
                              color={severity.color}
                              variant="outlined"
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="textSecondary">
                              Missing: <strong>{gap.competency.name}</strong>
                              {gap.competency.category && (
                                <> • {gap.competency.category}</>
                              )}
                            </Typography>
                            <Box display="flex" alignItems="center" gap={1} mt={0.5}>
                              <Typography variant="caption" color="textSecondary">
                                Gap Score:
                              </Typography>
                              <LinearProgress
                                variant="determinate"
                                value={gap.gap_score * 100}
                                color={severity.color}
                                sx={{ flex: 1, maxWidth: 100 }}
                              />
                              <Typography variant="caption" color="textSecondary">
                                {Math.round(gap.gap_score * 100)}%
                              </Typography>
                            </Box>
                            {gap.suggested_path && (
                              <Typography variant="caption" color="primary.main">
                                Suggested: {gap.suggested_path.title}
                              </Typography>
                            )}
                          </Box>
                        }
                      />
                      
                      <ListItemSecondaryAction>
                        <Box display="flex" gap={0.5}>
                          <Tooltip title="View Learner Profile">
                            <IconButton
                              size="small"
                              onClick={() => handleViewLearner(gap)}
                            >
                              <ViewIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          
                          {gap.suggested_path && (
                            <Tooltip title="Assign Suggested Path">
                              <IconButton
                                size="small"
                                color="primary"
                                onClick={() => handleAssignPath(gap)}
                              >
                                <AssignmentIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          )}
                        </Box>
                      </ListItemSecondaryAction>
                    </ListItem>
                  </motion.div>
                )
              })}
            </List>

            <Divider sx={{ my: 2 }} />
            
            <Box display="flex" justifyContent="center">
              <Button
                variant="outlined"
                size="small"
                onClick={() => console.log('View all gaps')}
              >
                View All Skill Gaps
              </Button>
            </Box>
          </>
        )}
      </CardContent>
    </Card>
  )
}
