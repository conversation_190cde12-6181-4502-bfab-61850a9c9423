import { test, expect } from '@playwright/test'

test.describe('Login Page', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login')
  })

  test('should display login form', async ({ page }) => {
    // Check if the login form elements are present
    await expect(page.getByText('ZenithLearn AI')).toBeVisible()
    await expect(page.getByText('Sign in to your admin dashboard')).toBeVisible()
    await expect(page.getByLabel('Email Address')).toBeVisible()
    await expect(page.getByLabel('Password')).toBeVisible()
    await expect(page.getByRole('button', { name: 'Sign In' })).toBeVisible()
  })

  test('should show validation errors for empty fields', async ({ page }) => {
    // Click submit without filling fields
    await page.getByRole('button', { name: 'Sign In' }).click()

    // Check for validation errors
    await expect(page.getByText('Please enter a valid email address')).toBeVisible()
    await expect(page.getByText('Password must be at least 6 characters')).toBeVisible()
  })

  test('should show validation error for invalid email', async ({ page }) => {
    // Fill invalid email
    await page.getByLabel('Email Address').fill('invalid-email')
    await page.getByLabel('Password').fill('password123')
    await page.getByRole('button', { name: 'Sign In' }).click()

    // Check for email validation error
    await expect(page.getByText('Please enter a valid email address')).toBeVisible()
  })

  test('should show validation error for short password', async ({ page }) => {
    // Fill valid email but short password
    await page.getByLabel('Email Address').fill('<EMAIL>')
    await page.getByLabel('Password').fill('123')
    await page.getByRole('button', { name: 'Sign In' }).click()

    // Check for password validation error
    await expect(page.getByText('Password must be at least 6 characters')).toBeVisible()
  })

  test('should have forgot password link', async ({ page }) => {
    await expect(page.getByText('Forgot your password?')).toBeVisible()
  })

  test('should have contact administrator link', async ({ page }) => {
    await expect(page.getByText('Contact your administrator')).toBeVisible()
  })

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    // Check if elements are still visible and properly arranged
    await expect(page.getByText('ZenithLearn AI')).toBeVisible()
    await expect(page.getByLabel('Email Address')).toBeVisible()
    await expect(page.getByLabel('Password')).toBeVisible()
    await expect(page.getByRole('button', { name: 'Sign In' })).toBeVisible()
  })
})
