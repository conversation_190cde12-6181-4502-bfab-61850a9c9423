'use client'

import React from 'react'
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  TextField,
  Switch,
  FormControlLabel,
  Alert,
  IconButton,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider,
  Chip
} from '@mui/material'
import {
  Close as CloseIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Notifications as NotificationIcon
} from '@mui/icons-material'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'

import { useLearners, useSendMessage } from '@/lib/hooks/useLearners'
import { MessageData } from '@/lib/types/learners'

const messageSchema = z.object({
  subject: z.string().min(1, 'Subject is required'),
  content: z.string().min(1, 'Message content is required'),
  send_email: z.boolean().optional(),
  send_notification: z.boolean().optional()
})

type MessageFormData = z.infer<typeof messageSchema>

interface MessageModalProps {
  open: boolean
  onClose: () => void
  recipientIds: string[]
}

const MessageModal: React.FC<MessageModalProps> = ({
  open,
  onClose,
  recipientIds
}) => {
  const { data: learnersData } = useLearners()
  const sendMessageMutation = useSendMessage()

  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { errors, isSubmitting }
  } = useForm<MessageFormData>({
    resolver: zodResolver(messageSchema),
    defaultValues: {
      subject: '',
      content: '',
      send_email: true,
      send_notification: true
    }
  })

  const allLearners = learnersData?.data || []
  const recipients = allLearners.filter(learner => recipientIds.includes(learner.id))

  const messageContent = watch('content')
  const characterCount = messageContent?.length || 0
  const maxCharacters = 1000

  const onSubmit = async (data: MessageFormData) => {
    try {
      const messageData: MessageData = {
        recipient_ids: recipientIds,
        subject: data.subject,
        content: data.content,
        send_email: data.send_email,
        send_notification: data.send_notification
      }

      await sendMessageMutation.mutateAsync(messageData)
      onClose()
      reset()
    } catch (error) {
      console.error('Error sending message:', error)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
      reset()
    }
  }

  const getMessagePreview = () => {
    const subject = watch('subject')
    const content = watch('content')
    
    if (!subject && !content) return null

    return (
      <Box
        sx={{
          p: 2,
          border: '1px solid',
          borderColor: 'divider',
          borderRadius: 1,
          backgroundColor: 'background.paper'
        }}
      >
        <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
          {subject || 'No subject'}
        </Typography>
        <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
          {content || 'No content'}
        </Typography>
      </Box>
    )
  }

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{ sx: { borderRadius: 2 } }}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6" fontWeight="bold">
            Send Message
          </Typography>
          <IconButton onClick={handleClose} disabled={isSubmitting}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <form onSubmit={handleSubmit(onSubmit)}>
        <DialogContent dividers>
          {/* Recipients */}
          <Box mb={3}>
            <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
              Recipients ({recipients.length})
            </Typography>
            <Box
              sx={{
                maxHeight: 200,
                overflow: 'auto',
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 1
              }}
            >
              <List dense>
                {recipients.map((recipient, index) => (
                  <React.Fragment key={recipient.id}>
                    <ListItem>
                      <ListItemAvatar>
                        <Avatar src={recipient.avatar_url} sx={{ width: 32, height: 32 }}>
                          <PersonIcon />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={recipient.full_name}
                        secondary={recipient.email}
                      />
                      <Chip
                        label={recipient.status}
                        size="small"
                        color={recipient.status === 'active' ? 'success' : 'default'}
                        variant="outlined"
                      />
                    </ListItem>
                    {index < recipients.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </Box>
          </Box>

          {/* Subject */}
          <Box mb={3}>
            <Controller
              name="subject"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label="Subject"
                  error={!!errors.subject}
                  helperText={errors.subject?.message}
                  required
                />
              )}
            />
          </Box>

          {/* Message Content */}
          <Box mb={3}>
            <Controller
              name="content"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label="Message"
                  multiline
                  rows={6}
                  error={!!errors.content}
                  helperText={
                    errors.content?.message || 
                    `${characterCount}/${maxCharacters} characters`
                  }
                  inputProps={{ maxLength: maxCharacters }}
                  required
                />
              )}
            />
          </Box>

          {/* Delivery Options */}
          <Box mb={3}>
            <Typography variant="subtitle2" fontWeight="medium" gutterBottom>
              Delivery Options
            </Typography>
            
            <Box display="flex" flexDirection="column" gap={1}>
              <Controller
                name="send_email"
                control={control}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Switch
                        checked={field.value}
                        onChange={field.onChange}
                      />
                    }
                    label={
                      <Box display="flex" alignItems="center" gap={1}>
                        <EmailIcon fontSize="small" />
                        Send via email
                      </Box>
                    }
                  />
                )}
              />

              <Controller
                name="send_notification"
                control={control}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Switch
                        checked={field.value}
                        onChange={field.onChange}
                      />
                    }
                    label={
                      <Box display="flex" alignItems="center" gap={1}>
                        <NotificationIcon fontSize="small" />
                        Send in-app notification
                      </Box>
                    }
                  />
                )}
              />
            </Box>
          </Box>

          {/* Message Preview */}
          <Box mb={2}>
            <Typography variant="subtitle2" fontWeight="medium" gutterBottom>
              Preview
            </Typography>
            {getMessagePreview()}
          </Box>

          {sendMessageMutation.error && (
            <Alert severity="error">
              {(sendMessageMutation.error as any)?.message || 'Failed to send message'}
            </Alert>
          )}
        </DialogContent>

        <DialogActions sx={{ p: 3 }}>
          <Button onClick={handleClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={isSubmitting || characterCount === 0}
          >
            {isSubmitting ? 'Sending...' : 'Send Message'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  )
}

export default MessageModal
