import { serve } from 'https://deno.land/std/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.45.4';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface ReportsAnalytics {
  totalReports: number;
  activeReports: number;
  scheduledReports: number;
  sharedReports: number;
  totalViews: number;
  avgGenerationTime: number;
  popularTemplates: Array<{
    id: number;
    name: string;
    usage: number;
  }>;
  reportTrends: Array<{
    date: string;
    count: number;
  }>;
  reportTypes: Array<{
    type: string;
    count: number;
  }>;
}

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Get Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    );

    // Get user from JWT
    const {
      data: { user },
      error: userError,
    } = await supabaseClient.auth.getUser();

    if (userError || !user) {
      throw new Error('Unauthorized');
    }

    // Get request body
    const { tenant_id } = await req.json();

    if (!tenant_id) {
      throw new Error('Tenant ID is required');
    }

    // Get total reports count
    const { count: totalReports, error: reportsError } = await supabaseClient
      .from('report_templates')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', tenant_id);

    if (reportsError) {
      throw reportsError;
    }

    // Get active reports count (reports that have been used recently)
    const { count: activeReports, error: activeError } = await supabaseClient
      .from('report_executions')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', tenant_id)
      .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()); // Last 30 days

    if (activeError) {
      throw activeError;
    }

    // Get scheduled reports count
    const { count: scheduledReports, error: scheduledError } = await supabaseClient
      .from('report_schedules')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', tenant_id)
      .eq('is_active', true);

    if (scheduledError) {
      throw scheduledError;
    }

    // Get shared reports count
    const { count: sharedReports, error: sharedError } = await supabaseClient
      .from('report_shares')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', tenant_id);

    if (sharedError) {
      throw sharedError;
    }

    // Get total views from report executions
    const { data: viewsData, error: viewsError } = await supabaseClient
      .from('report_executions')
      .select('view_count')
      .eq('tenant_id', tenant_id);

    if (viewsError) {
      throw viewsError;
    }

    const totalViews = viewsData.reduce((sum, item) => sum + (item.view_count || 0), 0);

    // Get average generation time
    const { data: timingData, error: timingError } = await supabaseClient
      .from('report_executions')
      .select('generation_time_ms')
      .eq('tenant_id', tenant_id)
      .not('generation_time_ms', 'is', null)
      .limit(100); // Last 100 executions

    if (timingError) {
      throw timingError;
    }

    const avgGenerationTime = timingData.length > 0
      ? timingData.reduce((sum, item) => sum + item.generation_time_ms, 0) / timingData.length / 1000 // Convert to seconds
      : 0;

    // Get popular templates
    const { data: templatesData, error: templatesError } = await supabaseClient
      .from('report_templates')
      .select(`
        id,
        name,
        report_executions!inner(id)
      `)
      .eq('tenant_id', tenant_id);

    if (templatesError) {
      throw templatesError;
    }

    const popularTemplates = templatesData
      .map(template => ({
        id: template.id,
        name: template.name,
        usage: template.report_executions?.length || 0,
      }))
      .sort((a, b) => b.usage - a.usage)
      .slice(0, 5);

    // Get report trends (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const { data: trendsData, error: trendsError } = await supabaseClient
      .from('report_executions')
      .select('created_at')
      .eq('tenant_id', tenant_id)
      .gte('created_at', sixMonthsAgo.toISOString())
      .order('created_at', { ascending: true });

    if (trendsError) {
      throw trendsError;
    }

    // Group by month
    const monthlyTrends = trendsData.reduce((acc, execution) => {
      const month = new Date(execution.created_at).toISOString().slice(0, 7); // YYYY-MM
      acc[month] = (acc[month] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const reportTrends = Object.entries(monthlyTrends).map(([date, count]) => ({
      date,
      count,
    }));

    // Get report types distribution
    const { data: typesData, error: typesError } = await supabaseClient
      .from('report_templates')
      .select('category')
      .eq('tenant_id', tenant_id);

    if (typesError) {
      throw typesError;
    }

    const typesCounts = typesData.reduce((acc, template) => {
      const type = template.category || 'Other';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const reportTypes = Object.entries(typesCounts).map(([type, count]) => ({
      type,
      count,
    }));

    const analytics: ReportsAnalytics = {
      totalReports: totalReports || 0,
      activeReports: activeReports || 0,
      scheduledReports: scheduledReports || 0,
      sharedReports: sharedReports || 0,
      totalViews,
      avgGenerationTime: Math.round(avgGenerationTime * 100) / 100,
      popularTemplates,
      reportTrends,
      reportTypes,
    };

    return new Response(JSON.stringify(analytics), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });

  } catch (error) {
    console.error('Error in get-reports-analytics:', error);
    return new Response(
      JSON.stringify({ 
        error: error.message || 'Internal server error',
        details: error.toString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});

/* 
Database Schema for Reports System:

-- Report Templates
CREATE TABLE report_templates (
  id BIGSERIAL PRIMARY KEY,
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  name TEXT NOT NULL,
  description TEXT,
  category TEXT,
  metrics_config JSONB,
  filters_config JSONB,
  visualizations_config JSONB,
  is_pre_built BOOLEAN DEFAULT false,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Report Executions
CREATE TABLE report_executions (
  id BIGSERIAL PRIMARY KEY,
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  template_id BIGINT REFERENCES report_templates(id),
  executed_by UUID REFERENCES users(id),
  status TEXT DEFAULT 'pending',
  generation_time_ms INTEGER,
  view_count INTEGER DEFAULT 0,
  output_format TEXT,
  output_url TEXT,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Report Schedules
CREATE TABLE report_schedules (
  id BIGSERIAL PRIMARY KEY,
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  template_id BIGINT REFERENCES report_templates(id),
  name TEXT NOT NULL,
  frequency TEXT NOT NULL,
  next_run_at TIMESTAMP WITH TIME ZONE,
  last_run_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true,
  recipients JSONB,
  delivery_config JSONB,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Report Shares
CREATE TABLE report_shares (
  id BIGSERIAL PRIMARY KEY,
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  report_id BIGINT REFERENCES report_executions(id),
  shared_by UUID REFERENCES users(id),
  shared_with UUID REFERENCES users(id),
  permissions TEXT DEFAULT 'view',
  share_link TEXT,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Row Level Security
ALTER TABLE report_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE report_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE report_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE report_shares ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can access reports from their tenant" ON report_templates
  FOR ALL USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);

CREATE POLICY "Users can access executions from their tenant" ON report_executions
  FOR ALL USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);

CREATE POLICY "Users can access schedules from their tenant" ON report_schedules
  FOR ALL USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);

CREATE POLICY "Users can access shares from their tenant" ON report_shares
  FOR ALL USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);
*/
