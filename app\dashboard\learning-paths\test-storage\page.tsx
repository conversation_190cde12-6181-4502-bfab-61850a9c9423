'use client'

import React, { useState } from 'react'
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Grid,
  <PERSON>ert,
  Ta<PERSON>,
  Tab,
  Divider
} from '@mui/material'
import {
  CloudUpload as UploadIcon,
  Build as TestIcon,
  Storage as StorageIcon,
  Security as SecurityIcon
} from '@mui/icons-material'
import StorageTestComponent from '@/components/learning-paths/StorageTestComponent'
import SimpleStorageTest from '@/components/learning-paths/SimpleStorageTest'
import EnhancedLessonCreator from '@/components/learning-paths/EnhancedLessonCreator'
import LessonCreatorTest from '@/components/learning-paths/LessonCreatorTest'
import FileManager from '@/components/learning-paths/FileManager'
import { FileUploadResult } from '@/lib/services/storageService'

interface TabPanelProps {
  children?: React.ReactNode
  index: number
  value: number
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`storage-tabpanel-${index}`}
      aria-labelledby={`storage-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  )
}

export default function StorageTestPage() {
  const [activeTab, setActiveTab] = useState(0)
  const [lessonDialogOpen, setLessonDialogOpen] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState<FileUploadResult[]>([])

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue)
  }

  const handleLessonSave = (lessonData: any) => {
    console.log('Lesson saved:', lessonData)
    if (lessonData.uploadResult) {
      setUploadedFiles(prev => [...prev, lessonData.uploadResult])
    }
    setLessonDialogOpen(false)
  }

  const handleFileDelete = (filePath: string) => {
    setUploadedFiles(prev => prev.filter(file => file.path !== filePath))
  }

  const handleFileView = (file: FileUploadResult) => {
    window.open(file.url, '_blank')
  }

  return (
    <Box>
      {/* Header */}
      <Box mb={4}>
        <Typography variant="h4" fontWeight="bold" gutterBottom>
          Storage Integration Test Suite
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Test and validate the Supabase Storage integration for ZenithLearn AI
        </Typography>
      </Box>

      {/* Overview Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <StorageIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
              <Typography variant="h6" gutterBottom>
                Storage Service
              </Typography>
              <Typography variant="body2" color="text.secondary">
                File upload, validation, and management
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <SecurityIcon sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
              <Typography variant="h6" gutterBottom>
                Tenant Isolation
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Secure multi-tenant file storage
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <UploadIcon sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
              <Typography variant="h6" gutterBottom>
                Enhanced Upload
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Drag-and-drop with progress tracking
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <TestIcon sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
              <Typography variant="h6" gutterBottom>
                Automated Tests
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Comprehensive validation suite
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Important Notice */}
      <Alert severity="warning" sx={{ mb: 3 }}>
        <Typography variant="subtitle2" gutterBottom>
          Storage Setup Required
        </Typography>
        <Typography variant="body2">
          Before running tests, ensure that:
        </Typography>
        <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
          <li>Supabase Storage buckets are created (content, avatars)</li>
          <li>Row Level Security (RLS) policies are configured</li>
          <li>Tenant isolation functions are deployed</li>
          <li>File type and size restrictions are in place</li>
        </ul>
      </Alert>

      {/* Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={handleTabChange}>
            <Tab label="Simple Test" />
            <Tab label="Automated Tests" />
            <Tab label="Lesson Creator" />
            <Tab label="File Manager" />
            <Tab label="Documentation" />
          </Tabs>
        </Box>

        <TabPanel value={activeTab} index={0}>
          <SimpleStorageTest />
        </TabPanel>

        <TabPanel value={activeTab} index={1}>
          <StorageTestComponent />
        </TabPanel>

        <TabPanel value={activeTab} index={2}>
          <LessonCreatorTest />
        </TabPanel>

        <TabPanel value={activeTab} index={3}>
          <Box>
            <Typography variant="h6" gutterBottom>
              File Management Interface
            </Typography>
            <Typography variant="body2" color="text.secondary" mb={3}>
              View and manage uploaded files with secure access controls.
            </Typography>

            <FileManager
              files={uploadedFiles}
              onFileDelete={handleFileDelete}
              onFileView={handleFileView}
              showUploadStats={true}
              tenantId={3}
            />
          </Box>
        </TabPanel>

        <TabPanel value={activeTab} index={4}>
          <Box>
            <Typography variant="h6" gutterBottom>
              Storage Implementation Documentation
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      Security Features
                    </Typography>
                    <ul style={{ margin: 0, paddingLeft: '20px' }}>
                      <li>Tenant-isolated storage buckets</li>
                      <li>Row Level Security (RLS) policies</li>
                      <li>File type and size validation</li>
                      <li>Secure URL generation</li>
                      <li>Automatic cleanup of orphaned files</li>
                    </ul>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      Supported File Types
                    </Typography>
                    <ul style={{ margin: 0, paddingLeft: '20px' }}>
                      <li>Videos: MP4, WebM, AVI, MOV</li>
                      <li>Documents: PDF, DOC, DOCX</li>
                      <li>Presentations: PPT, PPTX</li>
                      <li>Images: JPG, PNG, GIF, WebP</li>
                      <li>Audio: MP3, WAV, OGG</li>
                    </ul>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      Storage Path Structure
                    </Typography>
                    <Typography variant="body2" color="text.secondary" mb={2}>
                      Files are organized using the following pattern for tenant isolation:
                    </Typography>
                    <Box sx={{ bgcolor: 'grey.100', p: 2, borderRadius: 1, fontFamily: 'monospace' }}>
                      tenant_&#123;tenant_id&#125;/content/&#123;content_type&#125;/&#123;user_id&#125;/&#123;timestamp&#125;_&#123;filename&#125;
                    </Box>
                    <Typography variant="caption" color="text.secondary" mt={1} display="block">
                      Example: tenant_3/content/videos/5cd09c93-eb00-470f-a605-c6d0d057bdd6/1703123456789_lesson-intro.mp4
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        </TabPanel>
      </Card>
    </Box>
  )
}
