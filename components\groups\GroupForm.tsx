'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogContent,
  Dialog<PERSON>ctions,
  TextField,
  Button,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Autocomplete,
  Grid,
  Typography,
  Divider,
  Switch,
  FormControlLabel,
  Alert,
} from '@mui/material'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { useForm, Controller } from 'react-hook-form'
import { motion } from 'framer-motion'

import { Group } from '@/lib/types/groups'

interface GroupFormProps {
  open: boolean
  onClose: () => void
  onSubmit: (data: Partial<Group>) => void
  group?: Group | null
  title?: string
  loading?: boolean
}

interface GroupFormData {
  name: string
  description: string
  status: 'active' | 'inactive' | 'archived' | 'draft'
  start_date: Date | null
  end_date: Date | null
  tags: string[]
  parent_group_id: number | null
  settings: {
    notifications: {
      email_enabled: boolean
      sms_enabled: boolean
      in_app_enabled: boolean
      announcement_frequency: 'immediate' | 'daily' | 'weekly'
    }
    communication: {
      chat_enabled: boolean
      file_sharing_enabled: boolean
      video_calls_enabled: boolean
    }
    progress: {
      auto_progress_tracking: boolean
      milestone_reminders: boolean
      completion_certificates: boolean
    }
    access: {
      self_enrollment: boolean
      member_invite_permissions: string[]
      content_access_level: 'full' | 'restricted'
    }
  }
}

const defaultSettings = {
  notifications: {
    email_enabled: true,
    sms_enabled: false,
    in_app_enabled: true,
    announcement_frequency: 'immediate' as const,
  },
  communication: {
    chat_enabled: true,
    file_sharing_enabled: true,
    video_calls_enabled: false,
  },
  progress: {
    auto_progress_tracking: true,
    milestone_reminders: true,
    completion_certificates: true,
  },
  access: {
    self_enrollment: false,
    member_invite_permissions: ['admin', 'moderator'],
    content_access_level: 'full' as const,
  },
}

const availableTags = [
  'Engineering',
  'Marketing',
  'Sales',
  'HR',
  'Finance',
  'Operations',
  'Product',
  'Design',
  'Customer Support',
  'Leadership',
  'Onboarding',
  'Training',
  'Certification',
  'Compliance',
  'Skills Development',
]

export default function GroupForm({
  open,
  onClose,
  onSubmit,
  group,
  title = 'Create Group',
  loading = false,
}: GroupFormProps) {
  const [activeTab, setActiveTab] = useState(0)
  const [tagInput, setTagInput] = useState('')

  const {
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors, isValid },
  } = useForm<GroupFormData>({
    defaultValues: {
      name: '',
      description: '',
      status: 'active',
      start_date: null,
      end_date: null,
      tags: [],
      parent_group_id: null,
      settings: defaultSettings,
    },
  })

  const watchedStartDate = watch('start_date')
  const watchedEndDate = watch('end_date')

  useEffect(() => {
    if (group) {
      reset({
        name: group.name,
        description: group.description || '',
        status: group.status,
        start_date: group.start_date ? new Date(group.start_date) : null,
        end_date: group.end_date ? new Date(group.end_date) : null,
        tags: group.tags || [],
        parent_group_id: group.parent_group_id || null,
        settings: { ...defaultSettings, ...group.settings },
      })
    } else {
      reset({
        name: '',
        description: '',
        status: 'active',
        start_date: null,
        end_date: null,
        tags: [],
        parent_group_id: null,
        settings: defaultSettings,
      })
    }
  }, [group, reset])

  const handleFormSubmit = (data: GroupFormData) => {
    const formattedData = {
      ...data,
      start_date: data.start_date?.toISOString(),
      end_date: data.end_date?.toISOString(),
      parent_group_id: data.parent_group_id || undefined,
    }
    onSubmit(formattedData)
  }

  const handleClose = () => {
    reset()
    setActiveTab(0)
    onClose()
  }

  const tabs = [
    { label: 'Basic Info', value: 0 },
    { label: 'Settings', value: 1 },
  ]

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { minHeight: '600px' },
      }}
    >
      <DialogTitle>
        <Typography variant="h6" component="div">
          {title}
        </Typography>
      </DialogTitle>

      <DialogContent dividers>
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', gap: 1, mb: 3 }}>
            {tabs.map((tab) => (
              <Button
                key={tab.value}
                variant={activeTab === tab.value ? 'contained' : 'outlined'}
                onClick={() => setActiveTab(tab.value)}
                size="small"
              >
                {tab.label}
              </Button>
            ))}
          </Box>

          <form onSubmit={handleSubmit(handleFormSubmit)}>
            {activeTab === 0 && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Controller
                      name="name"
                      control={control}
                      rules={{ required: 'Group name is required' }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Group Name"
                          fullWidth
                          error={!!errors.name}
                          helperText={errors.name?.message}
                          placeholder="Enter group name"
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Controller
                      name="description"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Description"
                          fullWidth
                          multiline
                          rows={3}
                          placeholder="Describe the purpose and goals of this group"
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="status"
                      control={control}
                      render={({ field }) => (
                        <FormControl fullWidth>
                          <InputLabel>Status</InputLabel>
                          <Select {...field} label="Status">
                            <MenuItem value="active">Active</MenuItem>
                            <MenuItem value="inactive">Inactive</MenuItem>
                            <MenuItem value="draft">Draft</MenuItem>
                            <MenuItem value="archived">Archived</MenuItem>
                          </Select>
                        </FormControl>
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="parent_group_id"
                      control={control}
                      render={({ field }) => (
                        <FormControl fullWidth>
                          <InputLabel>Parent Group (Optional)</InputLabel>
                          <Select {...field} label="Parent Group (Optional)">
                            <MenuItem value="">None</MenuItem>
                            {/* TODO: Load available parent groups */}
                          </Select>
                        </FormControl>
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="start_date"
                      control={control}
                      render={({ field }) => (
                        <DatePicker
                          {...field}
                          label="Start Date (Optional)"
                          slotProps={{
                            textField: {
                              fullWidth: true,
                              error: !!(watchedEndDate && field.value && field.value > watchedEndDate),
                              helperText: watchedEndDate && field.value && field.value > watchedEndDate
                                ? 'Start date must be before end date'
                                : '',
                            },
                          }}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="end_date"
                      control={control}
                      render={({ field }) => (
                        <DatePicker
                          {...field}
                          label="End Date (Optional)"
                          minDate={watchedStartDate || undefined}
                          slotProps={{
                            textField: {
                              fullWidth: true,
                            },
                          }}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Controller
                      name="tags"
                      control={control}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          multiple
                          freeSolo
                          options={availableTags}
                          value={field.value}
                          onChange={(_, newValue) => field.onChange(newValue)}
                          renderTags={(value, getTagProps) =>
                            value.map((option, index) => (
                              <Chip
                                variant="outlined"
                                label={option}
                                {...getTagProps({ index })}
                                key={option}
                              />
                            ))
                          }
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Tags"
                              placeholder="Add tags to categorize this group"
                              helperText="Press Enter to add custom tags"
                            />
                          )}
                        />
                      )}
                    />
                  </Grid>
                </Grid>
              </motion.div>
            )}

            {activeTab === 1 && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Box sx={{ space: 3 }}>
                  {/* Notifications Settings */}
                  <Box sx={{ mb: 4 }}>
                    <Typography variant="h6" gutterBottom>
                      Notifications
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={4}>
                        <Controller
                          name="settings.notifications.email_enabled"
                          control={control}
                          render={({ field }) => (
                            <FormControlLabel
                              control={<Switch {...field} checked={field.value} />}
                              label="Email Notifications"
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <Controller
                          name="settings.notifications.sms_enabled"
                          control={control}
                          render={({ field }) => (
                            <FormControlLabel
                              control={<Switch {...field} checked={field.value} />}
                              label="SMS Notifications"
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <Controller
                          name="settings.notifications.in_app_enabled"
                          control={control}
                          render={({ field }) => (
                            <FormControlLabel
                              control={<Switch {...field} checked={field.value} />}
                              label="In-App Notifications"
                            />
                          )}
                        />
                      </Grid>
                    </Grid>
                  </Box>

                  <Divider sx={{ my: 3 }} />

                  {/* Communication Settings */}
                  <Box sx={{ mb: 4 }}>
                    <Typography variant="h6" gutterBottom>
                      Communication
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={4}>
                        <Controller
                          name="settings.communication.chat_enabled"
                          control={control}
                          render={({ field }) => (
                            <FormControlLabel
                              control={<Switch {...field} checked={field.value} />}
                              label="Group Chat"
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <Controller
                          name="settings.communication.file_sharing_enabled"
                          control={control}
                          render={({ field }) => (
                            <FormControlLabel
                              control={<Switch {...field} checked={field.value} />}
                              label="File Sharing"
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <Controller
                          name="settings.communication.video_calls_enabled"
                          control={control}
                          render={({ field }) => (
                            <FormControlLabel
                              control={<Switch {...field} checked={field.value} />}
                              label="Video Calls"
                            />
                          )}
                        />
                      </Grid>
                    </Grid>
                  </Box>

                  <Divider sx={{ my: 3 }} />

                  {/* Progress Settings */}
                  <Box sx={{ mb: 4 }}>
                    <Typography variant="h6" gutterBottom>
                      Progress Tracking
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={4}>
                        <Controller
                          name="settings.progress.auto_progress_tracking"
                          control={control}
                          render={({ field }) => (
                            <FormControlLabel
                              control={<Switch {...field} checked={field.value} />}
                              label="Auto Progress Tracking"
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <Controller
                          name="settings.progress.milestone_reminders"
                          control={control}
                          render={({ field }) => (
                            <FormControlLabel
                              control={<Switch {...field} checked={field.value} />}
                              label="Milestone Reminders"
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <Controller
                          name="settings.progress.completion_certificates"
                          control={control}
                          render={({ field }) => (
                            <FormControlLabel
                              control={<Switch {...field} checked={field.value} />}
                              label="Completion Certificates"
                            />
                          )}
                        />
                      </Grid>
                    </Grid>
                  </Box>

                  <Divider sx={{ my: 3 }} />

                  {/* Access Settings */}
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      Access Control
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Controller
                          name="settings.access.self_enrollment"
                          control={control}
                          render={({ field }) => (
                            <FormControlLabel
                              control={<Switch {...field} checked={field.value} />}
                              label="Allow Self-Enrollment"
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Controller
                          name="settings.access.content_access_level"
                          control={control}
                          render={({ field }) => (
                            <FormControl fullWidth>
                              <InputLabel>Content Access Level</InputLabel>
                              <Select {...field} label="Content Access Level">
                                <MenuItem value="full">Full Access</MenuItem>
                                <MenuItem value="restricted">Restricted Access</MenuItem>
                              </Select>
                            </FormControl>
                          )}
                        />
                      </Grid>
                    </Grid>
                  </Box>
                </Box>
              </motion.div>
            )}
          </form>
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2 }}>
        <Button onClick={handleClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={handleSubmit(handleFormSubmit)}
          variant="contained"
          disabled={loading || !isValid}
        >
          {loading ? 'Saving...' : group ? 'Update Group' : 'Create Group'}
        </Button>
      </DialogActions>
    </Dialog>
  )
}
