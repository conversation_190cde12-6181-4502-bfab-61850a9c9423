import { serve } from 'https://deno.land/std@0.223.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.45.4';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

    if (!supabaseUrl || !supabaseKey) {
      return new Response(
        JSON.stringify({ error: 'Supabase configuration is missing' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    const supabaseClient = createClient(supabaseUrl, supabaseKey);

    // Get request body
    const { user_id } = await req.json();

    if (!user_id) {
      return new Response(
        JSON.stringify({ error: 'user_id is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Get user information
    const { data: userData, error: userError } = await supabaseClient
      .from('users')
      .select(`
        id,
        email,
        full_name,
        tenant_id,
        tenants:tenants(name)
      `)
      .eq('id', user_id)
      .single();

    if (userError || !userData) {
      return new Response(
        JSON.stringify({ error: 'User not found' }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Generate a temporary password (in a real implementation, this would be more secure)
    const tempPassword = generateTempPassword();

    // Create auth user (in a real implementation, you'd use Supabase Auth Admin API)
    // For now, we'll just log what would happen
    console.log(`Would create auth user for ${userData.email} with temp password: ${tempPassword}`);

    // Prepare welcome email content
    const emailContent = {
      to: userData.email,
      subject: `Welcome to ${userData.tenants?.name || 'ZenithLearn AI'}!`,
      html: generateWelcomeEmailHTML(userData.full_name, userData.tenants?.name || 'ZenithLearn AI', userData.email, tempPassword),
      text: generateWelcomeEmailText(userData.full_name, userData.tenants?.name || 'ZenithLearn AI', userData.email, tempPassword)
    };

    // In a real implementation, you would send the email using a service like:
    // - SendGrid
    // - AWS SES
    // - Resend
    // - Postmark
    // etc.

    console.log('Welcome email content:', emailContent);

    // Mock email sending
    console.log(`Sending welcome email to ${userData.email}`);
    console.log(`Subject: ${emailContent.subject}`);
    console.log(`Content: ${emailContent.text}`);

    // Store notification in database
    const { error: notificationError } = await supabaseClient
      .from('notifications')
      .insert({
        user_id: userData.id,
        type: 'welcome',
        title: 'Welcome to the platform!',
        content: `Your account has been created. Check your email for login instructions.`,
        data: {
          email_sent: true,
          temp_password_generated: true
        },
        created_at: new Date().toISOString(),
        read: false
      });

    if (notificationError) {
      console.error('Error creating welcome notification:', notificationError);
    }

    return new Response(
      JSON.stringify({ 
        success: true,
        message: 'Welcome email sent successfully',
        user: {
          id: userData.id,
          email: userData.email,
          full_name: userData.full_name
        }
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Error in send-welcome-email:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});

function generateTempPassword(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let password = '';
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}

function generateWelcomeEmailHTML(name: string, orgName: string, email: string, tempPassword: string): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Welcome to ${orgName}</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #1976d2;">Welcome to ${orgName}!</h1>
        
        <p>Hi ${name},</p>
        
        <p>Your learning account has been created successfully. You can now access the platform and start your learning journey.</p>
        
        <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>Your Login Credentials:</h3>
          <p><strong>Email:</strong> ${email}</p>
          <p><strong>Temporary Password:</strong> ${tempPassword}</p>
        </div>
        
        <p><strong>Important:</strong> Please change your password after your first login for security purposes.</p>
        
        <p>If you have any questions or need assistance, please don't hesitate to contact your administrator.</p>
        
        <p>Happy learning!</p>
        
        <p>Best regards,<br>The ${orgName} Team</p>
      </div>
    </body>
    </html>
  `;
}

function generateWelcomeEmailText(name: string, orgName: string, email: string, tempPassword: string): string {
  return `
Welcome to ${orgName}!

Hi ${name},

Your learning account has been created successfully. You can now access the platform and start your learning journey.

Your Login Credentials:
Email: ${email}
Temporary Password: ${tempPassword}

Important: Please change your password after your first login for security purposes.

If you have any questions or need assistance, please don't hesitate to contact your administrator.

Happy learning!

Best regards,
The ${orgName} Team
  `.trim();
}
