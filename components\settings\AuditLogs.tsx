'use client'

import React, { useState } from 'react'
import {
  <PERSON>,
  Dialog,
  <PERSON>alogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  IconButton,
  Typography,
  Grid,
  Card,
  CardContent,
  Pagination,
  InputAdornment
} from '@mui/material'
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  Close as CloseIcon
} from '@mui/icons-material'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { motion } from 'framer-motion'

interface AuditLogsProps {
  open: boolean
  onClose: () => void
}

const mockAuditLogs = [
  {
    id: '1',
    timestamp: '2024-01-15T14:30:00Z',
    admin: '<PERSON>',
    action: 'UPDATE',
    setting: 'General Settings',
    details: 'Changed platform name from "LMS" to "ZenithLearn AI"',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
  },
  {
    id: '2',
    timestamp: '2024-01-15T13:15:00Z',
    admin: 'Sarah Johnson',
    action: 'UPDATE',
    setting: 'Security Settings',
    details: 'Enabled MFA requirement for all users',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
  },
  {
    id: '3',
    timestamp: '2024-01-15T12:00:00Z',
    admin: 'Mike Wilson',
    action: 'CREATE',
    setting: 'Role Management',
    details: 'Created new role "Content Manager" with content creation permissions',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
  },
  {
    id: '4',
    timestamp: '2024-01-15T11:45:00Z',
    admin: 'John Smith',
    action: 'UPDATE',
    setting: 'Feature Toggles',
    details: 'Enabled AI content tagging feature',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
  },
  {
    id: '5',
    timestamp: '2024-01-15T10:30:00Z',
    admin: 'Sarah Johnson',
    action: 'UPDATE',
    setting: 'Branding Settings',
    details: 'Updated primary color to #1976d2',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
  }
]

const actionColors: Record<string, 'success' | 'info' | 'warning' | 'error'> = {
  CREATE: 'success',
  UPDATE: 'info',
  DELETE: 'error',
  VIEW: 'warning'
}

export default function AuditLogs({ open, onClose }: AuditLogsProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [actionFilter, setActionFilter] = useState('')
  const [adminFilter, setAdminFilter] = useState('')
  const [dateFrom, setDateFrom] = useState<Date | null>(null)
  const [dateTo, setDateTo] = useState<Date | null>(null)
  const [page, setPage] = useState(1)
  const [rowsPerPage] = useState(10)

  const handleExport = () => {
    // Implementation for exporting audit logs
    console.log('Exporting audit logs...')
  }

  const handleRefresh = () => {
    // Implementation for refreshing audit logs
    console.log('Refreshing audit logs...')
  }

  const filteredLogs = mockAuditLogs.filter(log => {
    const matchesSearch = searchTerm === '' || 
      log.details.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.setting.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesAction = actionFilter === '' || log.action === actionFilter
    const matchesAdmin = adminFilter === '' || log.admin === adminFilter
    
    return matchesSearch && matchesAction && matchesAdmin
  })

  const uniqueAdmins = Array.from(new Set(mockAuditLogs.map(log => log.admin)))

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Dialog 
        open={open} 
        onClose={onClose}
        maxWidth="xl"
        fullWidth
        PaperProps={{
          sx: { height: '90vh' }
        }}
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6" fontWeight="bold">
              Settings Audit Logs
            </Typography>
            <Box display="flex" gap={1}>
              <IconButton onClick={handleRefresh} size="small">
                <RefreshIcon />
              </IconButton>
              <IconButton onClick={handleExport} size="small">
                <DownloadIcon />
              </IconButton>
              <IconButton onClick={onClose} size="small">
                <CloseIcon />
              </IconButton>
            </Box>
          </Box>
        </DialogTitle>

        <DialogContent>
          {/* Summary Cards */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent sx={{ textAlign: 'center', py: 2 }}>
                  <Typography variant="h4" color="primary" fontWeight="bold">
                    {mockAuditLogs.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Events
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent sx={{ textAlign: 'center', py: 2 }}>
                  <Typography variant="h4" color="success.main" fontWeight="bold">
                    {mockAuditLogs.filter(log => log.action === 'CREATE').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Created
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent sx={{ textAlign: 'center', py: 2 }}>
                  <Typography variant="h4" color="info.main" fontWeight="bold">
                    {mockAuditLogs.filter(log => log.action === 'UPDATE').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Updated
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent sx={{ textAlign: 'center', py: 2 }}>
                  <Typography variant="h4" color="text.secondary" fontWeight="bold">
                    {uniqueAdmins.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Admins
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Filters */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={3}>
                  <TextField
                    fullWidth
                    size="small"
                    placeholder="Search logs..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      )
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={2}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Action</InputLabel>
                    <Select
                      value={actionFilter}
                      onChange={(e) => setActionFilter(e.target.value)}
                      label="Action"
                    >
                      <MenuItem value="">All Actions</MenuItem>
                      <MenuItem value="CREATE">Create</MenuItem>
                      <MenuItem value="UPDATE">Update</MenuItem>
                      <MenuItem value="DELETE">Delete</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={2}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Admin</InputLabel>
                    <Select
                      value={adminFilter}
                      onChange={(e) => setAdminFilter(e.target.value)}
                      label="Admin"
                    >
                      <MenuItem value="">All Admins</MenuItem>
                      {uniqueAdmins.map(admin => (
                        <MenuItem key={admin} value={admin}>{admin}</MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={2}>
                  <DatePicker
                    label="From Date"
                    value={dateFrom}
                    onChange={setDateFrom}
                    slotProps={{
                      textField: { size: 'small', fullWidth: true }
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={2}>
                  <DatePicker
                    label="To Date"
                    value={dateTo}
                    onChange={setDateTo}
                    slotProps={{
                      textField: { size: 'small', fullWidth: true }
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={1}>
                  <Button
                    variant="outlined"
                    startIcon={<FilterIcon />}
                    fullWidth
                    size="small"
                  >
                    Filter
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Audit Logs Table */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            <TableContainer component={Paper} variant="outlined">
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Timestamp</TableCell>
                    <TableCell>Admin</TableCell>
                    <TableCell>Action</TableCell>
                    <TableCell>Setting Category</TableCell>
                    <TableCell>Details</TableCell>
                    <TableCell>IP Address</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredLogs
                    .slice((page - 1) * rowsPerPage, page * rowsPerPage)
                    .map((log) => (
                    <TableRow key={log.id} hover>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(log.timestamp).toLocaleString()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          {log.admin}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={log.action}
                          color={actionColors[log.action]}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {log.setting}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" sx={{ maxWidth: 300 }}>
                          {log.details}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {log.ipAddress}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </motion.div>

          {/* Pagination */}
          <Box display="flex" justifyContent="center" mt={3}>
            <Pagination
              count={Math.ceil(filteredLogs.length / rowsPerPage)}
              page={page}
              onChange={(event, newPage) => setPage(newPage)}
              color="primary"
            />
          </Box>
        </DialogContent>

        <DialogActions>
          <Button onClick={onClose}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  )
}
