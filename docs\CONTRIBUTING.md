# Contributing to Groups and Batches Feature

Thank you for your interest in contributing to the ZenithLearn AI Groups and Batches feature! This document provides guidelines and information for contributors.

## 🤝 How to Contribute

### Types of Contributions

We welcome several types of contributions:

- **🐛 Bug Reports**: Help us identify and fix issues
- **✨ Feature Requests**: Suggest new functionality or improvements
- **📝 Documentation**: Improve or add to our documentation
- **🔧 Code Contributions**: Submit bug fixes or new features
- **🧪 Testing**: Help improve test coverage and quality
- **🎨 UI/UX Improvements**: Enhance user experience and design

### Getting Started

1. **Fork the Repository**
   ```bash
   git clone https://github.com/your-username/zenithlearn-ai-admin.git
   cd zenithlearn-ai-admin
   ```

2. **Set Up Development Environment**
   ```bash
   npm install
   cp .env.example .env.local
   # Configure your environment variables
   ```

3. **Run Database Migrations**
   ```bash
   supabase start
   supabase db push
   ```

4. **Start Development Server**
   ```bash
   npm run dev
   ```

## 📋 Development Guidelines

### Code Style

We use ESLint and Prettier for code formatting. Please ensure your code follows our style guidelines:

```bash
# Check linting
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format
```

### TypeScript Standards

- Use strict TypeScript configuration
- Provide proper type definitions for all functions and components
- Avoid `any` types - use proper interfaces or union types
- Document complex types with JSDoc comments

```typescript
/**
 * Creates a new group with the specified configuration
 * @param groupData - The group data to create
 * @returns Promise resolving to the created group
 */
async function createGroup(groupData: GroupCreate): Promise<Group> {
  // Implementation
}
```

### Component Guidelines

- Use functional components with hooks
- Implement proper prop types with TypeScript interfaces
- Include accessibility attributes (ARIA labels, roles, etc.)
- Follow Material-UI design patterns
- Implement responsive design for all screen sizes

```tsx
interface GroupCardProps {
  group: Group
  onClick?: (group: Group) => void
  variant?: 'default' | 'compact'
}

export function GroupCard({ group, onClick, variant = 'default' }: GroupCardProps) {
  return (
    <Card
      onClick={() => onClick?.(group)}
      role="button"
      tabIndex={0}
      aria-label={`Group: ${group.name}`}
    >
      {/* Component content */}
    </Card>
  )
}
```

### Database Guidelines

- Always use migrations for schema changes
- Include proper indexes for performance
- Implement Row Level Security (RLS) policies
- Add appropriate constraints and validations
- Document complex queries and functions

```sql
-- Migration: Add new column to groups table
-- File: migrations/xxx_add_group_category.sql

ALTER TABLE groups ADD COLUMN category VARCHAR(50);
CREATE INDEX idx_groups_category ON groups(category);

COMMENT ON COLUMN groups.category IS 'Categorization for group types (e.g., onboarding, training, project)';
```

## 🧪 Testing Requirements

### Unit Tests

All new components and functions must include unit tests:

```typescript
// __tests__/components/GroupCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { GroupCard } from '@/components/groups/GroupCard'

describe('GroupCard', () => {
  const mockGroup = {
    id: 1,
    name: 'Test Group',
    description: 'Test description',
    status: 'active' as const,
    // ... other required fields
  }

  it('renders group information correctly', () => {
    render(<GroupCard group={mockGroup} />)
    
    expect(screen.getByText('Test Group')).toBeInTheDocument()
    expect(screen.getByText('Test description')).toBeInTheDocument()
  })

  it('calls onClick when clicked', () => {
    const handleClick = jest.fn()
    render(<GroupCard group={mockGroup} onClick={handleClick} />)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledWith(mockGroup)
  })
})
```

### Integration Tests

Test component interactions and API integrations:

```typescript
// __tests__/integration/GroupManagement.test.tsx
import { renderWithProviders } from '@/test-utils'
import { GroupManagement } from '@/components/groups/GroupManagement'

describe('Group Management Integration', () => {
  it('creates a group successfully', async () => {
    const { user } = renderWithProviders(<GroupManagement />)
    
    await user.click(screen.getByText('Create Group'))
    await user.type(screen.getByLabelText('Group Name'), 'New Test Group')
    await user.click(screen.getByText('Save'))
    
    expect(screen.getByText('Group created successfully')).toBeInTheDocument()
  })
})
```

### Test Coverage

Maintain minimum 80% test coverage for new code:

```bash
npm run test:coverage
```

## 📝 Documentation Standards

### Code Documentation

- Use JSDoc for functions and complex logic
- Include examples for public APIs
- Document component props and usage patterns
- Explain complex business logic

```typescript
/**
 * Calculates group progress statistics based on member completion data
 * 
 * @example
 * ```typescript
 * const progress = calculateGroupProgress(members, assignments)
 * console.log(`Completion rate: ${progress.completionRate}%`)
 * ```
 * 
 * @param members - Array of group members with progress data
 * @param assignments - Array of learning path assignments
 * @returns Calculated progress statistics
 */
function calculateGroupProgress(
  members: GroupMember[], 
  assignments: GroupAssignment[]
): GroupProgressStats {
  // Implementation
}
```

### README Updates

When adding new features, update relevant documentation:

- Feature overview in main README
- API documentation for new endpoints
- Component documentation for new UI elements
- Migration guides for breaking changes

## 🔄 Pull Request Process

### Before Submitting

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/group-analytics-enhancement
   ```

2. **Make Your Changes**
   - Follow coding standards
   - Add tests for new functionality
   - Update documentation

3. **Test Your Changes**
   ```bash
   npm run test
   npm run lint
   npm run type-check
   npm run build
   ```

4. **Commit Changes**
   ```bash
   git add .
   git commit -m "feat: add advanced analytics for group progress tracking"
   ```

### Commit Message Format

Use conventional commit format:

```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**
```
feat(groups): add bulk member assignment functionality
fix(progress): resolve calculation error for completion rates
docs(api): update group creation endpoint documentation
test(components): add unit tests for GroupCard component
```

### Pull Request Template

When creating a pull request, include:

```markdown
## Description
Brief description of changes made.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] Accessibility testing completed

## Screenshots (if applicable)
Include screenshots for UI changes.

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests added/updated
- [ ] No breaking changes (or breaking changes documented)
```

## 🐛 Bug Reports

### Bug Report Template

```markdown
**Bug Description**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected Behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Environment:**
- OS: [e.g. iOS]
- Browser [e.g. chrome, safari]
- Version [e.g. 22]
- Node.js version
- Next.js version

**Additional Context**
Add any other context about the problem here.
```

## ✨ Feature Requests

### Feature Request Template

```markdown
**Is your feature request related to a problem? Please describe.**
A clear and concise description of what the problem is.

**Describe the solution you'd like**
A clear and concise description of what you want to happen.

**Describe alternatives you've considered**
A clear and concise description of any alternative solutions or features you've considered.

**Additional context**
Add any other context or screenshots about the feature request here.

**Implementation Notes**
If you have ideas about how this could be implemented, please share them.
```

## 🔒 Security Guidelines

### Security Best Practices

- Never commit sensitive information (API keys, passwords, etc.)
- Use environment variables for configuration
- Implement proper input validation
- Follow OWASP security guidelines
- Test for SQL injection and XSS vulnerabilities

### Reporting Security Issues

For security vulnerabilities, <NAME_EMAIL> instead of creating a public issue.

## 📞 Getting Help

### Communication Channels

- **GitHub Discussions**: For general questions and discussions
- **Discord**: Real-time chat with the community
- **Email**: <EMAIL> for development questions

### Development Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [Material-UI Documentation](https://mui.com/)
- [Supabase Documentation](https://supabase.com/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)

## 📄 License

By contributing to this project, you agree that your contributions will be licensed under the same license as the project (MIT License).

## 🙏 Recognition

Contributors will be recognized in:
- CONTRIBUTORS.md file
- Release notes for significant contributions
- Annual contributor appreciation posts

Thank you for contributing to ZenithLearn AI! Your efforts help make learning more accessible and effective for everyone. 🚀
