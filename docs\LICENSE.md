# MIT License

Copyright (c) 2024 ZenithLearn AI

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

---

## Third-Party Licenses

This project includes several third-party libraries and components. Below are their respective licenses:

### Material-UI (MUI)
- **License**: MIT License
- **Copyright**: Copyright (c) 2014 Call-Em-All
- **Website**: https://mui.com/
- **License URL**: https://github.com/mui/material-ui/blob/master/LICENSE

### React
- **License**: MIT License
- **Copyright**: Copyright (c) Meta Platforms, Inc. and affiliates.
- **Website**: https://reactjs.org/
- **License URL**: https://github.com/facebook/react/blob/main/LICENSE

### Next.js
- **License**: MIT License
- **Copyright**: Copyright (c) 2024 Vercel, Inc.
- **Website**: https://nextjs.org/
- **License URL**: https://github.com/vercel/next.js/blob/canary/license.md

### Supabase JavaScript Client
- **License**: MIT License
- **Copyright**: Copyright (c) 2020-present Supabase
- **Website**: https://supabase.com/
- **License URL**: https://github.com/supabase/supabase-js/blob/master/LICENSE

### Zustand
- **License**: MIT License
- **Copyright**: Copyright (c) 2019 Paul Henschel
- **Website**: https://github.com/pmndrs/zustand
- **License URL**: https://github.com/pmndrs/zustand/blob/main/license

### React Hook Form
- **License**: MIT License
- **Copyright**: Copyright (c) 2019-present Beier(Bill) Luo
- **Website**: https://react-hook-form.com/
- **License URL**: https://github.com/react-hook-form/react-hook-form/blob/master/LICENSE

### Framer Motion
- **License**: MIT License
- **Copyright**: Copyright (c) 2018 Framer B.V.
- **Website**: https://www.framer.com/motion/
- **License URL**: https://github.com/framer/motion/blob/main/LICENSE

### Chart.js
- **License**: MIT License
- **Copyright**: Copyright (c) 2014-2024 Chart.js Contributors
- **Website**: https://www.chartjs.org/
- **License URL**: https://github.com/chartjs/Chart.js/blob/master/LICENSE.md

### @dnd-kit
- **License**: MIT License
- **Copyright**: Copyright (c) 2021, Claudéric Demers
- **Website**: https://dndkit.com/
- **License URL**: https://github.com/clauderic/dnd-kit/blob/master/LICENSE

### React Dropzone
- **License**: MIT License
- **Copyright**: Copyright (c) 2021 react-dropzone
- **Website**: https://react-dropzone.js.org/
- **License URL**: https://github.com/react-dropzone/react-dropzone/blob/master/LICENSE

### TipTap
- **License**: MIT License
- **Copyright**: Copyright (c) 2021 überdosis GbR
- **Website**: https://tiptap.dev/
- **License URL**: https://github.com/ueberdosis/tiptap/blob/main/LICENSE.md

### TanStack Query (React Query)
- **License**: MIT License
- **Copyright**: Copyright (c) 2021-present Tanner Linsley
- **Website**: https://tanstack.com/query
- **License URL**: https://github.com/TanStack/query/blob/main/LICENSE

---

## Attribution Requirements

When using this software, please include the following attribution in your application:

```
Powered by ZenithLearn AI Groups and Batches Feature
Licensed under MIT License
```

## Trademark Notice

"ZenithLearn AI" is a trademark of ZenithLearn AI. The use of this trademark is subject to our trademark policy.

## Patent Notice

This software may be covered by patents. The license granted herein does not include any patent rights.

## Export Control

This software may be subject to export control laws and regulations. Users are responsible for compliance with all applicable export control laws.

## Disclaimer

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

## Contact Information

For questions about this license or to request permissions beyond the scope of this license, please contact:

**ZenithLearn AI Legal Team**
- Email: <EMAIL>
- Website: https://zenithlearn.ai/legal
- Address: [Company Address]

---

*Last Updated: January 2024*
