-- ZenithLearn AI - Comprehensive Achievements Schema
-- Migration: 007_achievements_schema.sql

-- Create badges table for achievement badges
CREATE TABLE IF NOT EXISTS badges (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    criteria TEXT NOT NULL,
    icon_url TEXT,
    points INTEGER DEFAULT 0,
    rarity VARCHAR(20) DEFAULT 'common' CHECK (rarity IN ('common', 'uncommon', 'rare', 'epic', 'legendary')),
    category VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create certificates table for course/achievement certificates
CREATE TABLE IF NOT EXISTS certificates (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    name <PERSON><PERSON>HA<PERSON>(100) NOT NULL,
    description TEXT,
    template_url TEXT,
    criteria TEXT NOT NULL,
    points INTEGER DEFAULT 0,
    category VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create learner_rewards table for unified achievement tracking
CREATE TABLE IF NOT EXISTS learner_rewards (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    reward_type VARCHAR(20) CHECK (reward_type IN ('badge', 'certificate', 'points', 'milestone')),
    badge_id INTEGER REFERENCES badges(id) ON DELETE CASCADE,
    certificate_id INTEGER REFERENCES certificates(id) ON DELETE CASCADE,
    points INTEGER DEFAULT 0,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    evidence_json JSONB DEFAULT '{}',
    shared_count INTEGER DEFAULT 0,
    is_featured BOOLEAN DEFAULT FALSE,
    metadata_json JSONB DEFAULT '{}'
);

-- Create learner_milestones table for progress tracking
CREATE TABLE IF NOT EXISTS learner_milestones (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    milestone_type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    target_value INTEGER NOT NULL,
    current_value INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'in_progress' CHECK (status IN ('not_started', 'in_progress', 'completed', 'expired')),
    due_date TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata_json JSONB DEFAULT '{}'
);

-- Create leaderboards table for competitive elements
CREATE TABLE IF NOT EXISTS leaderboards (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    leaderboard_type VARCHAR(50) NOT NULL,
    score INTEGER NOT NULL,
    rank_position INTEGER,
    time_period VARCHAR(20) CHECK (time_period IN ('daily', 'weekly', 'monthly', 'all_time')),
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata_json JSONB DEFAULT '{}'
);

-- Create learner_activity table for tracking engagement
CREATE TABLE IF NOT EXISTS learner_activity (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    activity_type VARCHAR(50) NOT NULL,
    activity_data JSONB NOT NULL,
    points_earned INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create learner_notifications table for achievement alerts
CREATE TABLE IF NOT EXISTS learner_notifications (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    notification_type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    action_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE
);

-- Create learner_preferences table for customization
CREATE TABLE IF NOT EXISTS learner_preferences (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    achievement_display_json JSONB DEFAULT '{}',
    notification_settings_json JSONB DEFAULT '{}',
    accessibility_json JSONB DEFAULT '{}',
    privacy_settings_json JSONB DEFAULT '{}',
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create learner_insights table for AI-generated recommendations
CREATE TABLE IF NOT EXISTS learner_insights (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    insight_type VARCHAR(50) NOT NULL,
    insight TEXT NOT NULL,
    confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    action_items TEXT[],
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE
);

-- Create learner_recommendations table for achievement suggestions
CREATE TABLE IF NOT EXISTS learner_recommendations (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    recommendation_type VARCHAR(50) NOT NULL,
    badge_id INTEGER REFERENCES badges(id) ON DELETE CASCADE,
    certificate_id INTEGER REFERENCES certificates(id) ON DELETE CASCADE,
    confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    reasoning TEXT,
    is_dismissed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create external_credentials table for third-party integrations
CREATE TABLE IF NOT EXISTS external_credentials (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    platform VARCHAR(50) NOT NULL,
    credential_id VARCHAR(255) NOT NULL,
    credential_data JSONB NOT NULL,
    sync_status VARCHAR(20) DEFAULT 'pending' CHECK (sync_status IN ('pending', 'synced', 'failed')),
    last_synced_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_badges_tenant_category ON badges(tenant_id, category);
CREATE INDEX IF NOT EXISTS idx_badges_rarity ON badges(rarity);
CREATE INDEX IF NOT EXISTS idx_certificates_tenant_category ON certificates(tenant_id, category);
CREATE INDEX IF NOT EXISTS idx_learner_rewards_learner_tenant ON learner_rewards(learner_id, tenant_id);
CREATE INDEX IF NOT EXISTS idx_learner_rewards_type ON learner_rewards(reward_type);
CREATE INDEX IF NOT EXISTS idx_learner_rewards_earned_at ON learner_rewards(earned_at DESC);
CREATE INDEX IF NOT EXISTS idx_learner_milestones_learner_status ON learner_milestones(learner_id, status);
CREATE INDEX IF NOT EXISTS idx_leaderboards_type_period ON leaderboards(leaderboard_type, time_period);
CREATE INDEX IF NOT EXISTS idx_leaderboards_rank ON leaderboards(rank_position);
CREATE INDEX IF NOT EXISTS idx_learner_activity_learner_type ON learner_activity(learner_id, activity_type);
CREATE INDEX IF NOT EXISTS idx_learner_notifications_learner_unread ON learner_notifications(learner_id, is_read);
CREATE INDEX IF NOT EXISTS idx_learner_insights_learner_active ON learner_insights(learner_id, is_active);
CREATE INDEX IF NOT EXISTS idx_learner_recommendations_learner_dismissed ON learner_recommendations(learner_id, is_dismissed);

-- Enable Row Level Security
ALTER TABLE badges ENABLE ROW LEVEL SECURITY;
ALTER TABLE certificates ENABLE ROW LEVEL SECURITY;
ALTER TABLE learner_rewards ENABLE ROW LEVEL SECURITY;
ALTER TABLE learner_milestones ENABLE ROW LEVEL SECURITY;
ALTER TABLE leaderboards ENABLE ROW LEVEL SECURITY;
ALTER TABLE learner_activity ENABLE ROW LEVEL SECURITY;
ALTER TABLE learner_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE learner_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE learner_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE learner_recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE external_credentials ENABLE ROW LEVEL SECURITY;

-- RLS Policies for tenant isolation and user access control

-- Badges policies (admin can manage, learners can view)
CREATE POLICY "Badges are viewable by tenant members" ON badges
    FOR SELECT USING (tenant_id = (auth.jwt() ->> 'tenant_id')::integer);

CREATE POLICY "Badges are manageable by tenant admins" ON badges
    FOR ALL USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::integer AND
        auth.jwt() ->> 'role' IN ('admin', 'instructor')
    );

-- Certificates policies
CREATE POLICY "Certificates are viewable by tenant members" ON certificates
    FOR SELECT USING (tenant_id = (auth.jwt() ->> 'tenant_id')::integer);

CREATE POLICY "Certificates are manageable by tenant admins" ON certificates
    FOR ALL USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::integer AND
        auth.jwt() ->> 'role' IN ('admin', 'instructor')
    );

-- Learner rewards policies (learners can view their own, admins can view all)
CREATE POLICY "Learner rewards are viewable by owner" ON learner_rewards
    FOR SELECT USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::integer AND
        (learner_id = auth.uid() OR auth.jwt() ->> 'role' IN ('admin', 'instructor'))
    );

CREATE POLICY "Learner rewards are manageable by system" ON learner_rewards
    FOR INSERT WITH CHECK (
        tenant_id = (auth.jwt() ->> 'tenant_id')::integer AND
        auth.jwt() ->> 'role' IN ('admin', 'instructor', 'system')
    );

-- Learner milestones policies
CREATE POLICY "Learner milestones are viewable by owner" ON learner_milestones
    FOR SELECT USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::integer AND
        (learner_id = auth.uid() OR auth.jwt() ->> 'role' IN ('admin', 'instructor'))
    );

CREATE POLICY "Learner milestones are manageable by system" ON learner_milestones
    FOR ALL USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::integer AND
        auth.jwt() ->> 'role' IN ('admin', 'instructor', 'system')
    );

-- Leaderboards policies (viewable by all tenant members)
CREATE POLICY "Leaderboards are viewable by tenant members" ON leaderboards
    FOR SELECT USING (tenant_id = (auth.jwt() ->> 'tenant_id')::integer);

CREATE POLICY "Leaderboards are manageable by system" ON leaderboards
    FOR ALL USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::integer AND
        auth.jwt() ->> 'role' IN ('admin', 'system')
    );

-- Learner activity policies (own data only)
CREATE POLICY "Learner activity is viewable by owner" ON learner_activity
    FOR SELECT USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::integer AND
        (learner_id = auth.uid() OR auth.jwt() ->> 'role' IN ('admin', 'instructor'))
    );

CREATE POLICY "Learner activity is insertable by owner" ON learner_activity
    FOR INSERT WITH CHECK (
        tenant_id = (auth.jwt() ->> 'tenant_id')::integer AND
        learner_id = auth.uid()
    );

-- Learner notifications policies (own notifications only)
CREATE POLICY "Learner notifications are viewable by owner" ON learner_notifications
    FOR SELECT USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::integer AND
        learner_id = auth.uid()
    );

CREATE POLICY "Learner notifications are manageable by owner" ON learner_notifications
    FOR UPDATE USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::integer AND
        learner_id = auth.uid()
    );

-- Learner preferences policies (own preferences only)
CREATE POLICY "Learner preferences are viewable by owner" ON learner_preferences
    FOR SELECT USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::integer AND
        learner_id = auth.uid()
    );

CREATE POLICY "Learner preferences are manageable by owner" ON learner_preferences
    FOR ALL USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::integer AND
        learner_id = auth.uid()
    );

-- Learner insights policies (own insights only)
CREATE POLICY "Learner insights are viewable by owner" ON learner_insights
    FOR SELECT USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::integer AND
        learner_id = auth.uid()
    );

-- Learner recommendations policies (own recommendations only)
CREATE POLICY "Learner recommendations are viewable by owner" ON learner_recommendations
    FOR SELECT USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::integer AND
        learner_id = auth.uid()
    );

CREATE POLICY "Learner recommendations are manageable by owner" ON learner_recommendations
    FOR UPDATE USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::integer AND
        learner_id = auth.uid()
    );

-- External credentials policies (own credentials only)
CREATE POLICY "External credentials are viewable by owner" ON external_credentials
    FOR SELECT USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::integer AND
        learner_id = auth.uid()
    );

CREATE POLICY "External credentials are manageable by owner" ON external_credentials
    FOR ALL USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::integer AND
        learner_id = auth.uid()
    );
