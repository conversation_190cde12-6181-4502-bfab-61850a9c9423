import { serve } from 'https://deno.land/std/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.45.4';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface LearnerAnalytics {
  total_learners: number;
  active_learners: number;
  inactive_learners: number;
  suspended_learners: number;
  average_completion_rate: number;
  average_engagement_score: number;
  at_risk_learners: number;
  overdue_assignments: number;
  top_performers: any[];
  engagement_trends: any[];
}

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

    if (!supabaseUrl || !supabaseKey) {
      return new Response(
        JSON.stringify({ error: 'Supabase configuration is missing' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    const supabaseClient = createClient(supabaseUrl, supabaseKey);

    // Get request body
    const { tenant_id } = await req.json();

    if (!tenant_id) {
      return new Response(
        JSON.stringify({ error: 'tenant_id is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Get total learners count by status
    const { data: learnerCounts, error: countError } = await supabaseClient
      .from('users')
      .select('status')
      .eq('tenant_id', tenant_id)
      .neq('role_id', 1); // Exclude admin users

    if (countError) {
      throw countError;
    }

    const statusCounts = learnerCounts.reduce((acc: any, user: any) => {
      acc[user.status] = (acc[user.status] || 0) + 1;
      return acc;
    }, {});

    const total_learners = learnerCounts.length;
    const active_learners = statusCounts.active || 0;
    const inactive_learners = statusCounts.inactive || 0;
    const suspended_learners = statusCounts.suspended || 0;

    // Get completion rates
    const { data: progressData, error: progressError } = await supabaseClient
      .from('progress')
      .select(`
        completion_percentage,
        users!inner(tenant_id)
      `)
      .eq('users.tenant_id', tenant_id);

    if (progressError) {
      throw progressError;
    }

    const completionRates = progressData.map((p: any) => p.completion_percentage);
    const average_completion_rate = completionRates.length > 0 
      ? completionRates.reduce((a: number, b: number) => a + b, 0) / completionRates.length 
      : 0;

    // Get overdue assignments
    const { data: overdueData, error: overdueError } = await supabaseClient
      .from('learner_assignments')
      .select(`
        id,
        users!learner_assignments_learner_id_fkey!inner(tenant_id)
      `)
      .eq('users.tenant_id', tenant_id)
      .eq('status', 'overdue');

    if (overdueError) {
      throw overdueError;
    }

    const overdue_assignments = overdueData.length;

    // Calculate engagement scores (mock calculation for now)
    const average_engagement_score = Math.floor(Math.random() * 30) + 70; // 70-100 range

    // Get at-risk learners (those with low engagement or overdue assignments)
    const { data: atRiskData, error: atRiskError } = await supabaseClient
      .from('users')
      .select(`
        id,
        full_name,
        learner_assignments!learner_assignments_learner_id_fkey!inner(status)
      `)
      .eq('tenant_id', tenant_id)
      .neq('role_id', 1)
      .eq('learner_assignments.status', 'overdue');

    if (atRiskError) {
      throw atRiskError;
    }

    const at_risk_learners = atRiskData.length;

    // Get top performers
    const { data: topPerformersData, error: topPerformersError } = await supabaseClient
      .from('users')
      .select(`
        id,
        full_name,
        email,
        avatar_url
      `)
      .eq('tenant_id', tenant_id)
      .neq('role_id', 1)
      .limit(5);

    if (topPerformersError) {
      throw topPerformersError;
    }

    const top_performers = topPerformersData || [];

    // Generate engagement trends (mock data)
    const engagement_trends = Array.from({ length: 30 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (29 - i));
      return {
        date: date.toISOString().split('T')[0],
        active_users: Math.floor(Math.random() * 50) + 20,
        completions: Math.floor(Math.random() * 20) + 5,
        new_registrations: Math.floor(Math.random() * 10) + 1
      };
    });

    // Get recent activity (mock data)
    const recent_activity = Array.from({ length: 10 }, (_, i) => ({
      id: `activity_${i}`,
      user_id: `user_${i}`,
      type: ['login', 'lesson_completed', 'path_completed', 'quiz_taken'][Math.floor(Math.random() * 4)],
      title: `Activity ${i + 1}`,
      description: `Mock activity description ${i + 1}`,
      timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
    }));

    // Get alerts (mock data)
    const alerts = Array.from({ length: 5 }, (_, i) => ({
      id: i + 1,
      user_id: `user_${i}`,
      type: ['low_activity', 'overdue_assignment', 'poor_performance'][Math.floor(Math.random() * 3)],
      severity: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)],
      message: `Alert message ${i + 1}`,
      suggested_actions: [`Action ${i + 1}`, `Action ${i + 2}`],
      created_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
    }));

    const analytics: LearnerAnalytics = {
      total_learners,
      active_learners,
      inactive_learners,
      suspended_learners,
      average_completion_rate: Math.round(average_completion_rate * 100) / 100,
      average_engagement_score,
      at_risk_learners,
      overdue_assignments,
      top_performers,
      engagement_trends
    };

    const response = {
      analytics,
      recent_activity,
      alerts
    };

    return new Response(
      JSON.stringify(response),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Error in get-learner-analytics:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});