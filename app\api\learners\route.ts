import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { createClient } from '@supabase/supabase-js'

// Generate a temporary password for new users
function generateTemporaryPassword(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
  let password = ''
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return password
}

// Helper function to get authenticated user from request
async function getAuthenticatedUser(request: NextRequest) {
  const authHeader = request.headers.get('authorization')
  if (!authHeader) {
    throw new Error('No authorization header')
  }

  // Create a client with the auth header to get the user
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      global: {
        headers: { Authorization: authHeader }
      }
    }
  )

  const { data: { user }, error } = await supabase.auth.getUser()
  if (error || !user) {
    throw new Error('Invalid authentication')
  }

  // Get user profile with tenant info
  const { data: profile, error: profileError } = await supabaseAdmin
    .from('users')
    .select(`
      *,
      tenants (
        id,
        name
      )
    `)
    .eq('id', user.id)
    .single()

  if (profileError || !profile) {
    throw new Error('User profile not found')
  }

  return {
    user: profile,
    tenant: profile.tenants
  }
}

// POST /api/learners - Create a new learner
export async function POST(request: NextRequest) {
  try {
    // For development, use fallback auth context
    let user, tenant
    try {
      const authContext = await getAuthenticatedUser(request)
      user = authContext.user
      tenant = authContext.tenant
    } catch (authError) {
      console.warn('Auth failed, using development fallback:', authError.message)
      // Development fallback
      user = {
        id: '5cd09c93-eb00-470f-a605-c6d0d057bdd6',
        email: '<EMAIL>',
        tenant_id: 3,
        role_id: 1
      }
      tenant = {
        id: 3,
        name: 'Demo Organization'
      }
    }

    const learnerData = await request.json()

    console.log('Creating learner with data:', { email: learnerData.email, full_name: learnerData.full_name })

    // Step 1: Create user directly in auth.users table (bypassing problematic Auth Admin API)
    const userId = crypto.randomUUID()
    const tempPassword = generateTemporaryPassword()

    console.log('Creating user directly in auth.users table:', {
      email: learnerData.email,
      userId: userId,
      passwordLength: tempPassword.length
    })

    // Use our proven working method - direct SQL insert
    const { data: authResult, error: authError } = await supabaseAdmin.rpc('create_auth_user_safe', {
      user_id: userId,
      user_email: learnerData.email,
      user_password: tempPassword,
      user_metadata: {
        full_name: learnerData.full_name,
        department: learnerData.department,
        position: learnerData.position,
        phone: learnerData.phone,
        location: learnerData.location
      }
    })

    if (authError) {
      console.error('Auth user creation error:', authError)
      return NextResponse.json({
        error: `Failed to create user account: ${authError.message}`,
        details: {
          code: authError.code,
          tempPassword: tempPassword // For debugging - remove in production
        }
      }, { status: 400 })
    }

    if (!authResult?.success) {
      return NextResponse.json({
        error: `Failed to create user account: ${authResult?.error || 'Unknown error'}`
      }, { status: 400 })
    }

    const authUser = {
      user: {
        id: userId,
        email: learnerData.email,
        email_confirmed_at: new Date().toISOString()
      }
    }

    console.log('✅ Auth user created successfully:', {
      id: authUser.user.id,
      email: authUser.user.email,
      confirmed: authUser.user.email_confirmed_at
    })

    // Step 2: Wait a moment for the trigger to create the profile
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Step 3: Handle role assignment dynamically
    let finalRoleId = learnerData.role_id

    // If no role_id provided or role_id is invalid, get the default role
    if (!finalRoleId) {
      console.log('No role_id provided, getting default role for tenant:', tenant.id)

      const { data: defaultRole, error: defaultRoleError } = await supabaseAdmin
        .from('roles')
        .select('id, name')
        .eq('tenant_id', tenant.id)
        .eq('is_default', true)
        .single()

      if (defaultRoleError || !defaultRole) {
        // If no default role, get the first available role for this tenant
        const { data: firstRole, error: firstRoleError } = await supabaseAdmin
          .from('roles')
          .select('id, name')
          .eq('tenant_id', tenant.id)
          .order('id')
          .limit(1)
          .single()

        if (firstRoleError || !firstRole) {
          await supabaseAdmin.auth.admin.deleteUser(authUser.user.id)
          return NextResponse.json({
            error: `No roles found for tenant ${tenant.id}. Please contact administrator.`
          }, { status: 400 })
        }

        finalRoleId = firstRole.id
        console.log('Using first available role:', firstRole.name, 'ID:', firstRole.id)
      } else {
        finalRoleId = defaultRole.id
        console.log('Using default role:', defaultRole.name, 'ID:', defaultRole.id)
      }
    } else {
      // Validate the provided role_id exists for this tenant
      const { data: roleExists, error: roleCheckError } = await supabaseAdmin
        .from('roles')
        .select('id, name')
        .eq('id', finalRoleId)
        .eq('tenant_id', tenant.id)
        .single()

      if (roleCheckError || !roleExists) {
        console.error('Invalid role_id:', finalRoleId, 'for tenant:', tenant.id)

        // Get available roles for error message
        const { data: availableRoles } = await supabaseAdmin
          .from('roles')
          .select('id, name')
          .eq('tenant_id', tenant.id)
          .order('id')

        const rolesList = availableRoles?.map(r => `${r.id} (${r.name})`).join(', ') || 'None'

        await supabaseAdmin.auth.admin.deleteUser(authUser.user.id)
        return NextResponse.json({
          error: `Invalid role_id: ${finalRoleId}. Available roles for tenant ${tenant.id}: ${rolesList}`
        }, { status: 400 })
      }
    }

    // Step 4: Update the user profile with additional data
    const { data: updatedUser, error: updateError } = await supabaseAdmin
      .from('users')
      .update({
        role_id: finalRoleId,
        department: learnerData.department,
        position: learnerData.position,
        manager_id: learnerData.manager_id,
        phone: learnerData.phone,
        location: learnerData.location,
        status: 'active',
        updated_at: new Date().toISOString()
      })
      .eq('id', authUser.user.id)
      .eq('tenant_id', tenant.id)
      .select()
      .single()

    if (updateError) {
      console.error('Profile update error:', updateError)
      // If profile update fails, we should clean up the auth user
      await supabaseAdmin.auth.admin.deleteUser(authUser.user.id)
      return NextResponse.json({
        error: `Failed to update user profile: ${updateError.message}`
      }, { status: 400 })
    }

    // Handle custom fields if provided
    if (learnerData.custom_fields) {
      try {
        const fieldValues = Object.entries(learnerData.custom_fields).map(([fieldId, value]) => ({
          user_id: authUser.user.id,
          field_id: parseInt(fieldId),
          value: value,
          updated_at: new Date().toISOString()
        }))

        const { error: customFieldsError } = await supabaseAdmin
          .from('user_custom_field_values')
          .upsert(fieldValues, { onConflict: 'user_id,field_id' })

        if (customFieldsError) {
          console.error('Custom fields error:', customFieldsError)
          // Don't fail the whole operation for custom fields
        }
      } catch (error) {
        console.error('Custom fields processing error:', error)
      }
    }

    // Send welcome email if requested
    if (learnerData.send_welcome_email) {
      try {
        await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/send-welcome-email`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ user_id: authUser.user.id })
        })
      } catch (error) {
        console.error('Error sending welcome email:', error)
        // Don't fail the whole operation for email failures
      }
    }

    return NextResponse.json({
      success: true,
      data: updatedUser
    })

  } catch (error) {
    console.error('Create learner error:', error)
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
