import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface AIPathRequest {
  prompt: string
  options: {
    difficulty?: string
    duration?: number
    category?: string
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Parse request body
    const { prompt, options }: AIPathRequest = await req.json()

    if (!prompt || prompt.trim().length === 0) {
      return new Response(
        JSON.stringify({ error: 'Prompt is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get OpenAI API key from environment
    const openaiApiKey = Deno.env.get('OPENAI_API_KEY')
    if (!openaiApiKey) {
      console.error('OpenAI API key not found')
      return new Response(
        JSON.stringify({ error: 'AI service not configured' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Construct the AI prompt
    const systemPrompt = `You are an expert learning path designer. Create a comprehensive learning path based on the user's requirements. 

Return a JSON object with the following structure:
{
  "title": "Learning Path Title",
  "description": "Detailed description of what learners will achieve",
  "difficulty": "beginner|intermediate|advanced",
  "estimated_duration": number_of_weeks,
  "skills": ["skill1", "skill2", "skill3"],
  "modules": [
    {
      "title": "Module Title",
      "lessons": [
        {
          "title": "Lesson Title",
          "description": "Lesson description",
          "type": "video|pdf|quiz|link|simulation|project",
          "estimated_duration": minutes_as_number
        }
      ]
    }
  ]
}

Guidelines:
- Create 3-5 modules per path
- Each module should have 3-6 lessons
- Mix different lesson types (video, pdf, quiz, etc.)
- Ensure logical progression from basic to advanced concepts
- Include practical exercises and assessments
- Lesson durations should be realistic (5-60 minutes)
- Total path duration should align with the estimated weeks`

    const userPrompt = `Create a learning path for: ${prompt}

Requirements:
- Difficulty level: ${options.difficulty || 'beginner'}
- Duration: ${options.duration || 4} weeks
- Category: ${options.category || 'General'}

Please ensure the content is comprehensive, engaging, and follows best practices for adult learning.`

    // Call OpenAI API
    const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.7,
        max_tokens: 2000,
      }),
    })

    if (!openaiResponse.ok) {
      const errorText = await openaiResponse.text()
      console.error('OpenAI API error:', errorText)
      return new Response(
        JSON.stringify({ error: 'Failed to generate learning path' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const openaiData = await openaiResponse.json()
    const aiResponse = openaiData.choices[0]?.message?.content

    if (!aiResponse) {
      return new Response(
        JSON.stringify({ error: 'No response from AI service' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Parse the AI response
    let learningPath
    try {
      learningPath = JSON.parse(aiResponse)
    } catch (parseError) {
      console.error('Failed to parse AI response:', parseError)
      // Try to extract JSON from the response if it's wrapped in markdown
      const jsonMatch = aiResponse.match(/```json\n([\s\S]*?)\n```/)
      if (jsonMatch) {
        try {
          learningPath = JSON.parse(jsonMatch[1])
        } catch (secondParseError) {
          console.error('Failed to parse extracted JSON:', secondParseError)
          return new Response(
            JSON.stringify({ error: 'Invalid response format from AI service' }),
            { 
              status: 500, 
              headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
            }
          )
        }
      } else {
        return new Response(
          JSON.stringify({ error: 'Invalid response format from AI service' }),
          { 
            status: 500, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }
    }

    // Validate the response structure
    if (!learningPath.title || !learningPath.modules || !Array.isArray(learningPath.modules)) {
      return new Response(
        JSON.stringify({ error: 'Invalid learning path structure from AI service' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Ensure all required fields have defaults
    const sanitizedPath = {
      title: learningPath.title,
      description: learningPath.description || 'AI-generated learning path',
      difficulty: learningPath.difficulty || options.difficulty || 'beginner',
      estimated_duration: learningPath.estimated_duration || options.duration || 4,
      skills: learningPath.skills || [],
      modules: learningPath.modules.map((module: any, moduleIndex: number) => ({
        title: module.title || `Module ${moduleIndex + 1}`,
        lessons: (module.lessons || []).map((lesson: any, lessonIndex: number) => ({
          title: lesson.title || `Lesson ${lessonIndex + 1}`,
          description: lesson.description || '',
          type: lesson.type || 'video',
          estimated_duration: lesson.estimated_duration || 30
        }))
      }))
    }

    return new Response(
      JSON.stringify(sanitizedPath),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in ai-generate-path function:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

/* To deploy this function:
1. Make sure you have the Supabase CLI installed
2. Set your OpenAI API key: supabase secrets set OPENAI_API_KEY=your_key_here
3. Deploy: supabase functions deploy ai-generate-path
*/
