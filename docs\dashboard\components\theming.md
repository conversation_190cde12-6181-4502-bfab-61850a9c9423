# Material-UI Theming Guide

## 🎨 Overview

The ZenithLearn AI Learner Dashboard uses Material-UI's powerful theming system to create a consistent, customizable, and accessible design language across all components.

## 🎯 Theme Configuration

### Base Theme Setup
```typescript
// theme/index.ts
import { createTheme } from '@mui/material/styles';

export const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
      light: '#42a5f5',
      dark: '#1565c0',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#9c27b0',
      light: '#ba68c8',
      dark: '#7b1fa2',
      contrastText: '#ffffff',
    },
    success: {
      main: '#4caf50',
      light: '#81c784',
      dark: '#388e3c',
    },
    warning: {
      main: '#ff9800',
      light: '#ffb74d',
      dark: '#f57c00',
    },
    error: {
      main: '#f44336',
      light: '#e57373',
      dark: '#d32f2f',
    },
    background: {
      default: '#f5f5f5',
      paper: '#ffffff',
    },
    text: {
      primary: 'rgba(0, 0, 0, 0.87)',
      secondary: 'rgba(0, 0, 0, 0.6)',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 700,
      lineHeight: 1.2,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 600,
      lineHeight: 1.3,
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 600,
      lineHeight: 1.3,
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    h6: {
      fontSize: '1.125rem',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.6,
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.5,
    },
    caption: {
      fontSize: '0.75rem',
      lineHeight: 1.4,
    },
  },
  spacing: 8, // Base spacing unit (8px)
  shape: {
    borderRadius: 12,
  },
});
```

### Dark Theme Variant
```typescript
export const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#90caf9',
      light: '#e3f2fd',
      dark: '#42a5f5',
    },
    background: {
      default: '#121212',
      paper: '#1e1e1e',
    },
    text: {
      primary: '#ffffff',
      secondary: 'rgba(255, 255, 255, 0.7)',
    },
  },
  // Inherit other properties from base theme
  ...theme,
});
```

## 🎨 Component Customization

### Global Component Overrides
```typescript
export const theme = createTheme({
  // ... palette and typography
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
          border: '1px solid rgba(0, 0, 0, 0.05)',
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
            transform: 'translateY(-2px)',
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 600,
          padding: '10px 24px',
          boxShadow: 'none',
          '&:hover': {
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          },
        },
        contained: {
          background: 'linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)',
          '&:hover': {
            background: 'linear-gradient(45deg, #1565c0 30%, #1976d2 90%)',
          },
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 20,
          fontWeight: 500,
        },
      },
    },
    MuiLinearProgress: {
      styleOverrides: {
        root: {
          borderRadius: 10,
          height: 8,
        },
        bar: {
          borderRadius: 10,
        },
      },
    },
    MuiAvatar: {
      styleOverrides: {
        root: {
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
        },
      },
    },
  },
});
```

### Dashboard-Specific Variants
```typescript
// Custom component variants for dashboard
declare module '@mui/material/Card' {
  interface CardPropsVariantOverrides {
    dashboard: true;
    widget: true;
    feature: true;
  }
}

export const theme = createTheme({
  components: {
    MuiCard: {
      variants: [
        {
          props: { variant: 'dashboard' },
          style: {
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            '& .MuiTypography-root': {
              color: 'white',
            },
          },
        },
        {
          props: { variant: 'widget' },
          style: {
            minHeight: 200,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            textAlign: 'center',
          },
        },
        {
          props: { variant: 'feature' },
          style: {
            border: '2px solid',
            borderColor: 'primary.main',
            background: 'rgba(25, 118, 210, 0.05)',
          },
        },
      ],
    },
  },
});
```

## 🌈 Color System

### Semantic Color Palette
```typescript
// Extended color palette for dashboard
const dashboardColors = {
  // Learning states
  learning: {
    main: '#2196f3',
    light: '#64b5f6',
    dark: '#1976d2',
  },
  completed: {
    main: '#4caf50',
    light: '#81c784',
    dark: '#388e3c',
  },
  inProgress: {
    main: '#ff9800',
    light: '#ffb74d',
    dark: '#f57c00',
  },
  
  // Mood colors
  excited: '#ff4081',
  happy: '#4caf50',
  neutral: '#9e9e9e',
  tired: '#ff9800',
  stressed: '#f44336',
  
  // Gamification
  bronze: '#cd7f32',
  silver: '#c0c0c0',
  gold: '#ffd700',
  platinum: '#e5e4e2',
  
  // AI insights
  insight: '#9c27b0',
  recommendation: '#2196f3',
  warning: '#ff5722',
  achievement: '#4caf50',
};

// Extend theme with custom colors
declare module '@mui/material/styles' {
  interface Palette {
    dashboard: typeof dashboardColors;
  }
  
  interface PaletteOptions {
    dashboard?: typeof dashboardColors;
  }
}
```

### Gradient Definitions
```typescript
export const gradients = {
  primary: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  secondary: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
  success: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
  warning: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
  error: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
  
  // Time-based gradients
  morning: 'linear-gradient(135deg, #FFE082 0%, #FFCC02 100%)',
  afternoon: 'linear-gradient(135deg, #42A5F5 0%, #1976D2 100%)',
  evening: 'linear-gradient(135deg, #7E57C2 0%, #5E35B1 100%)',
  
  // Mood gradients
  excited: 'linear-gradient(135deg, #ff4081 0%, #ff6ec7 100%)',
  happy: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',
  neutral: 'linear-gradient(135deg, #9e9e9e 0%, #bdbdbd 100%)',
  tired: 'linear-gradient(135deg, #ff9800 0%, #ffb74d 100%)',
  stressed: 'linear-gradient(135deg, #f44336 0%, #e57373 100%)',
};
```

## 📱 Responsive Design

### Breakpoint System
```typescript
export const theme = createTheme({
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 900,
      lg: 1200,
      xl: 1536,
    },
  },
});

// Usage in components
const useResponsiveStyles = () => {
  const theme = useTheme();
  
  return {
    container: {
      padding: theme.spacing(2),
      [theme.breakpoints.up('md')]: {
        padding: theme.spacing(4),
      },
      [theme.breakpoints.up('lg')]: {
        padding: theme.spacing(6),
      },
    },
    grid: {
      spacing: 2,
      [theme.breakpoints.up('md')]: {
        spacing: 3,
      },
    },
  };
};
```

### Responsive Typography
```typescript
export const theme = createTheme({
  typography: {
    h1: {
      fontSize: '1.75rem',
      [theme.breakpoints.up('sm')]: {
        fontSize: '2rem',
      },
      [theme.breakpoints.up('md')]: {
        fontSize: '2.5rem',
      },
    },
    h2: {
      fontSize: '1.5rem',
      [theme.breakpoints.up('sm')]: {
        fontSize: '1.75rem',
      },
      [theme.breakpoints.up('md')]: {
        fontSize: '2rem',
      },
    },
  },
});
```

## 🎭 Animation & Transitions

### Theme-Level Animations
```typescript
export const theme = createTheme({
  transitions: {
    duration: {
      shortest: 150,
      shorter: 200,
      short: 250,
      standard: 300,
      complex: 375,
      enteringScreen: 225,
      leavingScreen: 195,
    },
    easing: {
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
      easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',
    },
  },
});
```

### Custom Animation Utilities
```typescript
// Animation helper functions
export const animations = {
  fadeIn: {
    '@keyframes fadeIn': {
      from: { opacity: 0 },
      to: { opacity: 1 },
    },
    animation: 'fadeIn 0.3s ease-in-out',
  },
  
  slideUp: {
    '@keyframes slideUp': {
      from: { transform: 'translateY(20px)', opacity: 0 },
      to: { transform: 'translateY(0)', opacity: 1 },
    },
    animation: 'slideUp 0.4s ease-out',
  },
  
  pulse: {
    '@keyframes pulse': {
      '0%': { transform: 'scale(1)' },
      '50%': { transform: 'scale(1.05)' },
      '100%': { transform: 'scale(1)' },
    },
    animation: 'pulse 2s infinite',
  },
};
```

## ♿ Accessibility Features

### High Contrast Theme
```typescript
export const highContrastTheme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#000000',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#ffffff',
      contrastText: '#000000',
    },
    background: {
      default: '#ffffff',
      paper: '#ffffff',
    },
    text: {
      primary: '#000000',
      secondary: '#000000',
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          border: '2px solid #000000',
          '&:focus': {
            outline: '3px solid #0066cc',
            outlineOffset: '2px',
          },
        },
      },
    },
  },
});
```

### Focus Management
```typescript
export const theme = createTheme({
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          '&:focus-visible': {
            outline: '2px solid',
            outlineColor: 'primary.main',
            outlineOffset: '2px',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          '&:focus-within': {
            outline: '2px solid',
            outlineColor: 'primary.main',
            outlineOffset: '2px',
          },
        },
      },
    },
  },
});
```

## 🔧 Theme Usage in Components

### Using Theme in Components
```typescript
import { useTheme } from '@mui/material/styles';

const DashboardComponent = () => {
  const theme = useTheme();
  
  return (
    <Card
      sx={{
        background: theme.palette.mode === 'dark' 
          ? gradients.primary 
          : theme.palette.background.paper,
        color: theme.palette.text.primary,
        borderRadius: theme.shape.borderRadius,
        padding: theme.spacing(3),
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(2),
        },
      }}
    >
      <Typography variant="h6" sx={{ mb: 2 }}>
        Dashboard Widget
      </Typography>
    </Card>
  );
};
```

### Theme Provider Setup
```typescript
// app/layout.tsx
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { theme } from './theme';

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body>
        <ThemeProvider theme={theme}>
          <CssBaseline />
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
```

---

**Next Steps**: Explore [Core Components](./core-components.md) to see theming in action or check [Creative Features](./creative-features.md) for advanced styling examples.
