'use client'

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { Box, CircularProgress } from '@mui/material'

import { AuthService } from '@/lib/auth'
import { useAuthStore } from '@/lib/store'

interface AuthProviderProps {
  children: React.ReactNode
}

const AuthProvider = ({ children }: AuthProviderProps) => {
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const pathname = usePathname()
  const { user, setUser, setTenant, setLoading } = useAuthStore()

  // Public routes that don't require authentication
  const publicRoutes = ['/login', '/signup', '/forgot-password', '/reset-password']
  const isPublicRoute = publicRoutes.includes(pathname)

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        setLoading(true)
        
        // Check if user is already authenticated
        const currentUser = await AuthService.getCurrentUser()
        
        if (currentUser) {
          setUser(currentUser)
          
          // Fetch tenant information
          if (currentUser.tenant_id) {
            const tenant = await AuthService.getTenant(currentUser.tenant_id)
            setTenant(tenant)
          }
          
          // Redirect to dashboard if on public route
          if (isPublicRoute) {
            router.push('/dashboard')
          }
        } else {
          // Redirect to login if not authenticated and not on public route
          if (!isPublicRoute) {
            router.push('/login')
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error)
        if (!isPublicRoute) {
          router.push('/login')
        }
      } finally {
        setLoading(false)
        setIsLoading(false)
      }
    }

    initializeAuth()

    // Listen for auth state changes
    const { data: { subscription } } = AuthService.onAuthStateChange(async (user) => {
      if (user) {
        setUser(user)
        if (user.tenant_id) {
          const tenant = await AuthService.getTenant(user.tenant_id)
          setTenant(tenant)
        }
        if (isPublicRoute) {
          router.push('/dashboard')
        }
      } else {
        setUser(null)
        setTenant(null)
        if (!isPublicRoute) {
          router.push('/login')
        }
      }
    })

    return () => {
      subscription?.unsubscribe()
    }
  }, [pathname, isPublicRoute, router, setUser, setTenant, setLoading])

  // Show loading spinner while initializing
  if (isLoading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
        bgcolor="background.default"
      >
        <CircularProgress size={40} />
      </Box>
    )
  }

  // Show children if authenticated or on public route
  if (user || isPublicRoute) {
    return <>{children}</>
  }

  // Show loading while redirecting
  return (
    <Box
      display="flex"
      justifyContent="center"
      alignItems="center"
      minHeight="100vh"
      bgcolor="background.default"
    >
      <CircularProgress size={40} />
    </Box>
  )
}

export default AuthProvider
