import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing user creation with Auth Admin API...')

    const { email, fullName } = await request.json()

    if (!email || !fullName) {
      return NextResponse.json({
        success: false,
        error: 'Email and full name are required'
      }, { status: 400 })
    }

    // Generate a temporary password
    function generateTemporaryPassword(): string {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
      let password = ''
      for (let i = 0; i < 12; i++) {
        password += chars.charAt(Math.floor(Math.random() * chars.length))
      }
      return password
    }

    console.log('Creating user with email:', email)
    
    // Test the Auth Admin API
    const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password: generateTemporaryPassword(),
      email_confirm: true,
      user_metadata: {
        full_name: fullName
      }
    })

    if (authError) {
      console.log('❌ Auth user creation error:', {
        message: authError.message,
        status: authError.status,
        code: authError.code,
        details: authError
      })
      return NextResponse.json({
        success: false,
        error: authError.message,
        details: {
          code: authError.code,
          status: authError.status,
          fullError: authError
        }
      }, { status: 400 })
    }

    if (!authUser.user) {
      return NextResponse.json({
        success: false,
        error: 'No user returned from auth creation'
      }, { status: 400 })
    }

    console.log('✅ Auth user created successfully:', authUser.user.id)

    // Wait for trigger to create profile
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Check if profile was created
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', authUser.user.id)
      .single()

    if (profileError) {
      console.log('❌ Profile check error:', profileError.message)
      // Clean up auth user
      await supabaseAdmin.auth.admin.deleteUser(authUser.user.id)
      return NextResponse.json({
        success: false,
        error: `Profile creation failed: ${profileError.message}`
      }, { status: 400 })
    }

    console.log('✅ Profile created successfully')

    return NextResponse.json({
      success: true,
      message: 'User created successfully',
      data: {
        authUser: {
          id: authUser.user.id,
          email: authUser.user.email,
          created_at: authUser.user.created_at
        },
        profile: profile
      }
    })

  } catch (error) {
    console.error('❌ Test user creation error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
