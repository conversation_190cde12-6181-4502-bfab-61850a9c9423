# Database Schema Documentation

This document provides comprehensive documentation for the Groups and Batches database schema, including table structures, relationships, constraints, and migration scripts.

## 📊 Schema Overview

The Groups and Batches feature introduces 7 new tables to the ZenithLearn AI database:

```mermaid
erDiagram
    tenants ||--o{ groups : "belongs to"
    users ||--o{ groups : "created by"
    groups ||--o{ group_members : "contains"
    groups ||--o{ group_assignments : "has"
    groups ||--o{ group_progress : "tracks"
    groups ||--o{ group_messages : "receives"
    groups ||--o{ group_milestones : "achieves"
    groups ||--o{ groups : "parent of"
    learning_paths ||--o{ group_assignments : "assigned to"
    learning_paths ||--o{ group_progress : "progress in"
    users ||--o{ group_members : "member of"
    tenants ||--o{ assignment_templates : "owns"
```

## 🗃️ Table Definitions

### 1. groups
**Purpose**: Core group information with tenant isolation and hierarchy support

```sql
CREATE TABLE groups (
  id BIGSERIAL PRIMARY KEY,
  tenant_id BIGINT NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  start_date TIMESTAMPTZ,
  end_date TIMESTAMPTZ,
  status VARCHAR(20) NOT NULL DEFAULT 'active',
  tags TEXT[] DEFAULT '{}',
  parent_group_id BIGINT REFERENCES groups(id) ON DELETE SET NULL,
  settings JSONB DEFAULT '{}',
  created_by UUID NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Constraints:**
- `groups_status_check`: Status must be in ('active', 'inactive', 'archived', 'draft')
- `groups_date_check`: Start date must be before end date
- `groups_name_tenant_unique`: Unique name per tenant
- `groups_no_self_parent`: Group cannot be its own parent

**Indexes:**
- `idx_groups_tenant_id`: Fast tenant-based queries
- `idx_groups_status`: Status filtering
- `idx_groups_parent_id`: Hierarchy navigation
- `idx_groups_tags`: Tag-based search (GIN index)

### 2. group_members
**Purpose**: Role-based membership with audit tracking

```sql
CREATE TABLE group_members (
  id BIGSERIAL PRIMARY KEY,
  group_id BIGINT NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
  learner_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  role VARCHAR(20) DEFAULT 'member',
  joined_at TIMESTAMPTZ DEFAULT NOW(),
  added_by UUID
);
```

**Constraints:**
- `group_members_role_check`: Role must be in ('member', 'moderator', 'admin')
- `group_members_unique`: Unique group-learner combination

**Indexes:**
- `idx_group_members_group_id`: Group-based queries
- `idx_group_members_learner_id`: User-based queries
- `idx_group_members_role`: Role filtering

### 3. group_assignments
**Purpose**: Links groups to learning paths with scheduling

```sql
CREATE TABLE group_assignments (
  id BIGSERIAL PRIMARY KEY,
  group_id BIGINT NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
  path_id BIGINT NOT NULL REFERENCES learning_paths(id) ON DELETE CASCADE,
  assigned_by UUID NOT NULL,
  assigned_at TIMESTAMPTZ DEFAULT NOW(),
  start_date TIMESTAMPTZ,
  due_date TIMESTAMPTZ,
  is_mandatory BOOLEAN DEFAULT true,
  settings JSONB DEFAULT '{}'
);
```

**Constraints:**
- `group_assignments_unique`: Unique group-path combination
- `group_assignments_date_check`: Start date must be before due date

**Indexes:**
- `idx_group_assignments_group_id`: Group-based queries
- `idx_group_assignments_path_id`: Path-based queries
- `idx_group_assignments_due_date`: Deadline tracking

### 4. group_progress
**Purpose**: Real-time progress tracking and analytics

```sql
CREATE TABLE group_progress (
  id BIGSERIAL PRIMARY KEY,
  group_id BIGINT NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
  path_id BIGINT NOT NULL REFERENCES learning_paths(id) ON DELETE CASCADE,
  total_members INTEGER NOT NULL DEFAULT 0,
  completed_members INTEGER NOT NULL DEFAULT 0,
  in_progress_members INTEGER NOT NULL DEFAULT 0,
  not_started_members INTEGER NOT NULL DEFAULT 0,
  average_score DECIMAL(5,2),
  completion_rate DECIMAL(5,2) NOT NULL DEFAULT 0,
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Constraints:**
- `group_progress_unique`: Unique group-path combination
- `group_progress_members_check`: Total equals sum of status counts
- `group_progress_completion_rate_check`: Rate between 0-100
- `group_progress_average_score_check`: Score between 0-100

### 5. group_messages
**Purpose**: Multi-channel communication system

```sql
CREATE TABLE group_messages (
  id BIGSERIAL PRIMARY KEY,
  group_id BIGINT NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL,
  message TEXT NOT NULL,
  message_type VARCHAR(20) NOT NULL DEFAULT 'chat',
  channels TEXT[] DEFAULT '{"in_app"}',
  metadata JSONB DEFAULT '{}',
  sent_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Constraints:**
- `group_messages_type_check`: Type in ('announcement', 'chat', 'system')
- `group_messages_channels_check`: Valid channel array

**Indexes:**
- `idx_group_messages_group_id`: Group-based queries
- `idx_group_messages_type`: Message type filtering
- `idx_group_messages_channels`: Channel-based search (GIN index)

### 6. group_milestones
**Purpose**: Timeline and achievement tracking

```sql
CREATE TABLE group_milestones (
  id BIGSERIAL PRIMARY KEY,
  group_id BIGINT NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  due_date TIMESTAMPTZ NOT NULL,
  completion_criteria JSONB DEFAULT '{}',
  is_completed BOOLEAN DEFAULT false,
  completed_at TIMESTAMPTZ,
  created_by UUID NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Constraints:**
- `group_milestones_completion_check`: Completion logic validation

**Indexes:**
- `idx_group_milestones_group_id`: Group-based queries
- `idx_group_milestones_due_date`: Timeline sorting
- `idx_group_milestones_completed`: Status filtering

### 7. assignment_templates
**Purpose**: Reusable assignment configurations

```sql
CREATE TABLE assignment_templates (
  id BIGSERIAL PRIMARY KEY,
  tenant_id BIGINT NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  template_data JSONB NOT NULL DEFAULT '{}',
  created_by UUID NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Constraints:**
- `assignment_templates_name_tenant_unique`: Unique name per tenant

## 🔒 Row Level Security (RLS)

All tables implement comprehensive RLS policies for tenant isolation and access control:

### Groups Table Policies
```sql
-- View groups in user's tenant
CREATE POLICY "Users can view groups in their tenant" ON groups
  FOR SELECT USING (
    tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid())
  );

-- Create groups in user's tenant
CREATE POLICY "Users can create groups in their tenant" ON groups
  FOR INSERT WITH CHECK (
    tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid())
    AND created_by = auth.uid()
  );
```

### Member-based Policies
For tables related to groups (members, assignments, progress, messages, milestones):

```sql
-- Example: Group members policy
CREATE POLICY "Users can view group members in their tenant" ON group_members
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM groups g 
      WHERE g.id = group_members.group_id 
      AND g.tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid())
    )
  );
```

## ⚡ Database Functions

### Progress Calculation Function
```sql
CREATE OR REPLACE FUNCTION refresh_group_progress(group_id_param BIGINT DEFAULT NULL)
RETURNS void AS $$
DECLARE
  assignment_record RECORD;
  total_members_count INTEGER;
  completed_count INTEGER;
  in_progress_count INTEGER;
  not_started_count INTEGER;
  avg_score DECIMAL(5,2);
  completion_rate_calc DECIMAL(5,2);
BEGIN
  -- Iterate through group assignments
  FOR assignment_record IN 
    SELECT ga.group_id, ga.path_id
    FROM group_assignments ga
    WHERE (group_id_param IS NULL OR ga.group_id = group_id_param)
  LOOP
    -- Calculate progress statistics
    -- ... (detailed implementation in migration files)
    
    -- Upsert progress record
    INSERT INTO group_progress (...)
    VALUES (...)
    ON CONFLICT (group_id, path_id)
    DO UPDATE SET ...;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### Auto-update Triggers
```sql
-- Trigger on progress table changes
CREATE TRIGGER trigger_update_group_progress
  AFTER INSERT OR UPDATE OR DELETE ON progress
  FOR EACH ROW
  EXECUTE FUNCTION update_group_progress_on_learner_change();

-- Trigger on member changes
CREATE TRIGGER trigger_update_group_progress_on_member_change
  AFTER INSERT OR DELETE ON group_members
  FOR EACH ROW
  EXECUTE FUNCTION update_group_progress_on_member_change();
```

## 🔄 Real-time Configuration

Supabase Realtime is enabled for tables requiring live updates:

```sql
-- Enable realtime for live updates
ALTER PUBLICATION supabase_realtime ADD TABLE group_progress;
ALTER PUBLICATION supabase_realtime ADD TABLE group_messages;
ALTER PUBLICATION supabase_realtime ADD TABLE group_members;
ALTER PUBLICATION supabase_realtime ADD TABLE groups;
```

## 📈 Performance Optimizations

### Index Strategy
- **Primary Keys**: Automatic B-tree indexes
- **Foreign Keys**: Indexes on all foreign key columns
- **Search Fields**: GIN indexes for arrays and JSONB
- **Timestamp Fields**: Indexes for date-based queries
- **Composite Indexes**: For common multi-column queries

### Query Optimization
- **Materialized Views**: For complex analytics queries
- **Partial Indexes**: For filtered queries on large tables
- **Expression Indexes**: For computed values and functions

### Caching Strategy
- **Application-level Caching**: Redis for frequently accessed data
- **Database-level Caching**: PostgreSQL shared buffers optimization
- **CDN Caching**: Static assets and computed reports

## 🧪 Test Data

Sample data is created during migration for testing:

```sql
-- Sample groups
INSERT INTO groups (tenant_id, name, description, status, tags, created_by) VALUES
('Engineering Team Q1 2024', 'Frontend and backend engineers', 'active', '{"Engineering", "Q1"}', user_id),
('Marketing Bootcamp', 'New marketing team onboarding', 'active', '{"Marketing", "Onboarding"}', user_id);

-- Sample members, assignments, milestones, etc.
```

## 🔧 Migration Scripts

Migration scripts are located in `docs/database/migrations/` and should be executed in order:

1. `001_create_groups_table.sql`
2. `002_enhance_group_members.sql`
3. `003_create_group_assignments.sql`
4. `004_create_group_progress.sql`
5. `005_create_group_messages.sql`
6. `006_create_group_milestones.sql`
7. `007_create_assignment_templates.sql`
8. `008_create_functions_and_triggers.sql`
9. `009_enable_realtime.sql`
10. `010_create_test_data.sql`

## 📊 Schema Statistics

After successful migration:

| Table | Columns | Indexes | Constraints | RLS Policies |
|-------|---------|---------|-------------|--------------|
| groups | 13 | 6 | 4 | 4 |
| group_members | 6 | 4 | 2 | 4 |
| group_assignments | 10 | 6 | 2 | 4 |
| group_progress | 11 | 4 | 4 | 4 |
| group_messages | 8 | 5 | 2 | 4 |
| group_milestones | 10 | 5 | 1 | 4 |
| assignment_templates | 9 | 3 | 1 | 4 |

This schema provides a robust foundation for the Groups and Batches feature with proper security, performance, and scalability considerations.
