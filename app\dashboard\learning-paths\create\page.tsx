'use client'

import React, { useState } from 'react'
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>per,
  Step,
  <PERSON><PERSON>abel,
  Button,
  Paper,
  Container,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material'
import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  Publish as PublishIcon
} from '@mui/icons-material'
import { useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'

import { useCreateLearningPath } from '@/lib/hooks/useLearningPaths'
import { useLearningPathsStore } from '@/lib/store'
import { PathCreationForm, WizardStep } from '@/lib/types/learning-paths'
import PathCreationWizard from '@/components/learning-paths/PathCreationWizard'

const steps: WizardStep[] = [
  {
    id: 0,
    title: 'Basic Information',
    description: 'Set up the fundamental details of your learning path',
    component: 'BasicInfo',
    isCompleted: false,
    isActive: true,
    validation: (data: any) => !!(data.title && data.description && data.category)
  },
  {
    id: 1,
    title: 'Structure & Modules',
    description: 'Design the learning path structure with modules and lessons',
    component: 'Structure',
    isCompleted: false,
    isActive: false,
    validation: (data: any) => !!(data.modules && data.modules.length > 0)
  },
  {
    id: 2,
    title: 'Assignments',
    description: 'Configure learner assignments and access rules',
    component: 'Assignments',
    isCompleted: false,
    isActive: false,
    validation: () => true // Optional step
  },
  {
    id: 3,
    title: 'Review & Publish',
    description: 'Review your learning path and publish or save as draft',
    component: 'Review',
    isCompleted: false,
    isActive: false,
    validation: () => true
  }
]

export default function CreateLearningPathPage() {
  const router = useRouter()
  const { wizardStep, setWizardStep, setIsCreating } = useLearningPathsStore()
  const createMutation = useCreateLearningPath()

  const [pathData, setPathData] = useState<Partial<PathCreationForm>>({
    title: '',
    description: '',
    objectives: [],
    duration: 4,
    difficulty: 'beginner',
    category: '',
    tags: [],
    is_template: false
  })
  const [modules, setModules] = useState<any[]>([])
  const [assignments, setAssignments] = useState<any[]>([])
  const [showExitDialog, setShowExitDialog] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  React.useEffect(() => {
    setIsCreating(true)
    return () => setIsCreating(false)
  }, [setIsCreating])

  const handleNext = () => {
    if (wizardStep < steps.length - 1) {
      setWizardStep(wizardStep + 1)
    }
  }

  const handleBack = () => {
    if (wizardStep > 0) {
      setWizardStep(wizardStep - 1)
    }
  }

  const handleStepClick = (stepIndex: number) => {
    // Allow navigation to previous steps or current step
    if (stepIndex <= wizardStep) {
      setWizardStep(stepIndex)
    }
  }

  const handleDataChange = (newData: Partial<PathCreationForm>) => {
    setPathData(prev => ({ ...prev, ...newData }))
  }

  const handleModulesChange = (newModules: any[]) => {
    setModules(newModules)
  }

  const handleAssignmentsChange = (newAssignments: any[]) => {
    setAssignments(newAssignments)
  }

  const handleSaveDraft = async () => {
    setIsSaving(true)
    try {
      console.log('Saving draft with data:', { pathData, modules, assignments })

      const pathPayload = {
        ...pathData,
        status: 'draft' as const,
        modules: modules,
        assignments: assignments
      }

      const result = await createMutation.mutateAsync(pathPayload as any)
      console.log('Draft saved successfully:', result)
      router.push('/dashboard/learning-paths')
    } catch (error) {
      console.error('Failed to save draft:', error)
      // The error will be shown via toast from the mutation
    } finally {
      setIsSaving(false)
    }
  }

  const handlePublish = async () => {
    setIsSaving(true)
    try {
      console.log('Publishing path with data:', { pathData, modules, assignments })

      const pathPayload = {
        ...pathData,
        status: 'published' as const,
        modules: modules,
        assignments: assignments
      }

      const result = await createMutation.mutateAsync(pathPayload as any)
      console.log('Path published successfully:', result)
      router.push('/dashboard/learning-paths')
    } catch (error) {
      console.error('Failed to publish path:', error)
      // The error will be shown via toast from the mutation
    } finally {
      setIsSaving(false)
    }
  }

  const handleExit = () => {
    if (pathData.title || modules.length > 0) {
      setShowExitDialog(true)
    } else {
      router.push('/dashboard/learning-paths')
    }
  }

  const confirmExit = () => {
    setShowExitDialog(false)
    router.push('/dashboard/learning-paths')
  }

  const currentStep = steps[wizardStep]
  const isStepValid = currentStep?.validation?.({ ...pathData, modules, assignments }) ?? true
  const canProceed = isStepValid && wizardStep < steps.length - 1
  const canPublish = wizardStep === steps.length - 1 && pathData.title && modules.length > 0

  return (
    <Container maxWidth="lg">
      <Box py={3}>
        {/* Header */}
        <Box display="flex" alignItems="center" mb={4}>
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={handleExit}
            sx={{ mr: 2 }}
          >
            Back to Learning Paths
          </Button>
          <Box>
            <Typography variant="h4" fontWeight="bold">
              Create Learning Path
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Build an engaging learning experience with AI assistance
            </Typography>
          </Box>
        </Box>

        {/* Progress Stepper */}
        <Paper sx={{ p: 3, mb: 3 }}>
          <Stepper activeStep={wizardStep} alternativeLabel>
            {steps.map((step, index) => (
              <Step 
                key={step.id}
                completed={index < wizardStep}
                sx={{ cursor: index <= wizardStep ? 'pointer' : 'default' }}
                onClick={() => handleStepClick(index)}
              >
                <StepLabel>
                  <Typography variant="subtitle2" fontWeight="bold">
                    {step.title}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {step.description}
                  </Typography>
                </StepLabel>
              </Step>
            ))}
          </Stepper>
        </Paper>

        {/* Step Content */}
        <Paper sx={{ p: 3, mb: 3, minHeight: 400 }}>
          <AnimatePresence mode="wait">
            <motion.div
              key={wizardStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <PathCreationWizard
                step={currentStep}
                pathData={pathData}
                modules={modules}
                assignments={assignments}
                onDataChange={handleDataChange}
                onModulesChange={handleModulesChange}
                onAssignmentsChange={handleAssignmentsChange}
              />
            </motion.div>
          </AnimatePresence>
        </Paper>

        {/* Navigation */}
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Button
            onClick={handleBack}
            disabled={wizardStep === 0}
            size="large"
          >
            Previous
          </Button>

          <Box display="flex" gap={2}>
            <Button
              variant="outlined"
              startIcon={<SaveIcon />}
              onClick={handleSaveDraft}
              disabled={!pathData.title || isSaving}
              size="large"
            >
              Save Draft
            </Button>

            {wizardStep === steps.length - 1 ? (
              <Button
                variant="contained"
                startIcon={<PublishIcon />}
                onClick={handlePublish}
                disabled={!canPublish || isSaving}
                size="large"
              >
                Publish Path
              </Button>
            ) : (
              <Button
                variant="contained"
                onClick={handleNext}
                disabled={!canProceed}
                size="large"
              >
                Next
              </Button>
            )}
          </Box>
        </Box>

        {/* Validation Alert */}
        {!isStepValid && (
          <Alert severity="warning" sx={{ mt: 2 }}>
            Please complete all required fields before proceeding to the next step.
          </Alert>
        )}

        {/* Exit Confirmation Dialog */}
        <Dialog open={showExitDialog} onClose={() => setShowExitDialog(false)}>
          <DialogTitle>Exit Learning Path Creation</DialogTitle>
          <DialogContent>
            <Typography>
              You have unsaved changes. Are you sure you want to exit? Your progress will be lost.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowExitDialog(false)}>
              Continue Editing
            </Button>
            <Button onClick={handleSaveDraft} variant="outlined">
              Save Draft & Exit
            </Button>
            <Button onClick={confirmExit} color="error">
              Exit Without Saving
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Container>
  )
}
