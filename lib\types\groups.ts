// Groups and Batches Types for ZenithLearn AI

export interface Group {
  id: number
  tenant_id: number
  name: string
  description?: string
  start_date?: string
  end_date?: string
  status: 'active' | 'inactive' | 'archived' | 'draft'
  tags?: string[]
  parent_group_id?: number
  settings?: GroupSettings
  created_by: string
  created_at: string
  updated_at: string
  // Computed fields
  member_count?: number
  assigned_paths_count?: number
  completion_rate?: number
  members?: GroupMember[]
  assignments?: GroupAssignment[]
  progress?: GroupProgress[]
  children?: Group[]
}

export interface GroupMember {
  id: number
  group_id: number
  learner_id: string
  role: 'member' | 'moderator' | 'admin'
  joined_at: string
  added_by: string
  // Computed fields from user table
  full_name?: string
  email?: string
  avatar_url?: string
  department?: string
  progress?: LearnerGroupProgress[]
}

export interface GroupAssignment {
  id: number
  group_id: number
  path_id: number
  assigned_by: string
  assigned_at: string
  start_date?: string
  due_date?: string
  is_mandatory: boolean
  settings?: AssignmentSettings
  // Computed fields from learning_paths table
  path_title?: string
  path_description?: string
  estimated_duration?: number
  difficulty_level?: string
}

export interface GroupProgress {
  id: number
  group_id: number
  path_id: number
  total_members: number
  completed_members: number
  in_progress_members: number
  not_started_members: number
  average_score?: number
  completion_rate: number
  updated_at: string
  // Computed fields
  path_title?: string
}

export interface LearnerGroupProgress {
  learner_id: string
  path_id: number
  status: 'not_started' | 'in_progress' | 'completed'
  progress_percentage: number
  score?: number
  time_spent?: number
  last_activity?: string
  completed_at?: string
}

export interface GroupMessage {
  id: number
  group_id: number
  sender_id: string
  message: string
  message_type: 'announcement' | 'chat' | 'system'
  channels: string[]
  metadata?: MessageMetadata
  sent_at: string
  // Computed fields
  sender_name?: string
  sender_avatar?: string
}

export interface GroupMilestone {
  id: number
  group_id: number
  title: string
  description?: string
  due_date: string
  completion_criteria?: any
  is_completed: boolean
  completed_at?: string
  created_by: string
  created_at: string
  // Computed fields
  days_remaining?: number
  progress_percentage?: number
}

export interface AssignmentTemplate {
  id: number
  tenant_id: number
  name: string
  description?: string
  template_data: TemplateData
  created_by: string
  created_at: string
  updated_at: string
}

// Settings and Configuration Types
export interface GroupSettings {
  notifications?: {
    email_enabled: boolean
    sms_enabled: boolean
    in_app_enabled: boolean
    announcement_frequency: 'immediate' | 'daily' | 'weekly'
  }
  communication?: {
    chat_enabled: boolean
    file_sharing_enabled: boolean
    video_calls_enabled: boolean
  }
  progress?: {
    auto_progress_tracking: boolean
    milestone_reminders: boolean
    completion_certificates: boolean
  }
  access?: {
    self_enrollment: boolean
    member_invite_permissions: string[]
    content_access_level: 'full' | 'restricted'
  }
}

export interface AssignmentSettings {
  auto_assign_new_members: boolean
  grace_period_days?: number
  reminder_schedule?: string[]
  completion_requirements?: {
    minimum_score?: number
    time_limit?: number
    attempts_allowed?: number
  }
}

export interface MessageMetadata {
  priority?: 'low' | 'normal' | 'high' | 'urgent'
  read_receipts?: boolean
  delivery_status?: {
    email?: 'sent' | 'delivered' | 'failed'
    sms?: 'sent' | 'delivered' | 'failed'
    in_app?: 'sent' | 'read'
  }
  attachments?: {
    name: string
    url: string
    type: string
    size: number
  }[]
}

export interface TemplateData {
  paths: {
    path_id: number
    start_date?: string
    due_date?: string
    is_mandatory: boolean
    settings?: AssignmentSettings
  }[]
  default_settings?: GroupSettings
  milestones?: {
    title: string
    description?: string
    due_date_offset_days: number
    completion_criteria?: any
  }[]
}

// Filter and Search Types
export interface GroupFilters {
  search: string
  status: 'all' | 'active' | 'inactive' | 'archived' | 'draft'
  tags: string[]
  created_by: string
  date_range: {
    start: string
    end: string
  }
  member_count_range: {
    min: number
    max: number
  }
  completion_rate_range: {
    min: number
    max: number
  }
  has_overdue_assignments: boolean
  parent_group_id?: number
}

export interface GroupMemberFilters {
  search: string
  role: 'all' | 'member' | 'moderator' | 'admin'
  department: string
  progress_status: 'all' | 'not_started' | 'in_progress' | 'completed' | 'overdue'
  engagement_level: 'all' | 'high' | 'medium' | 'low' | 'at_risk'
}

// AI and Automation Types
export interface SmartGroupingSuggestion {
  id: string
  name: string
  description: string
  criteria: {
    type: 'skill_level' | 'department' | 'performance' | 'learning_style' | 'custom'
    parameters: any
  }
  suggested_members: string[]
  confidence_score: number
  reasoning: string
}

export interface AutoAssignmentRule {
  id: number
  group_id: number
  name: string
  description?: string
  trigger_conditions: {
    type: 'progress_threshold' | 'time_based' | 'skill_gap' | 'performance_drop'
    parameters: any
  }
  actions: {
    type: 'assign_path' | 'reassign_group' | 'send_notification' | 'schedule_meeting'
    parameters: any
  }[]
  is_active: boolean
  created_by: string
  created_at: string
}

// Component Props Types
export interface GroupDashboardProps {
  groups: Group[]
  loading: boolean
  onCreateGroup: () => void
  onEditGroup: (group: Group) => void
  onDeleteGroup: (groupId: number) => void
  onViewGroup: (group: Group) => void
}

export interface GroupFormProps {
  group?: Group
  open: boolean
  onClose: () => void
  onSubmit: (data: Partial<Group>) => void
  loading?: boolean
}

export interface MemberManagementProps {
  group: Group
  members: GroupMember[]
  availableLearners: any[]
  onAddMembers: (learnerIds: string[]) => void
  onRemoveMember: (memberId: number) => void
  onUpdateMemberRole: (memberId: number, role: string) => void
}

export interface GroupProgressProps {
  group: Group
  progress: GroupProgress[]
  detailedProgress?: LearnerGroupProgress[]
  onViewDetails: (pathId: number) => void
}

// API Response Types
export interface GroupsResponse {
  data: Group[]
  count: number
  page: number
  limit: number
}

export interface GroupMembersResponse {
  data: GroupMember[]
  count: number
}

export interface GroupProgressResponse {
  data: GroupProgress[]
  summary: {
    total_groups: number
    active_groups: number
    average_completion_rate: number
    total_learners: number
  }
}

// Learner-specific Group Types
export interface LearnerGroup extends Group {
  my_role: 'member' | 'moderator' | 'admin'
  unread_messages_count: number
  my_progress: LearnerGroupProgress[]
  recent_activity: GroupActivity[]
  is_favorite: boolean
  notification_settings: LearnerGroupNotificationSettings
}

export interface GroupActivity {
  id: string
  group_id: number
  type: 'message' | 'assignment' | 'milestone' | 'member_joined' | 'member_left' | 'progress_update'
  title: string
  description: string
  actor_id: string
  actor_name: string
  actor_avatar?: string
  created_at: string
  metadata?: any
}

export interface LearnerGroupNotificationSettings {
  email_notifications: boolean
  push_notifications: boolean
  chat_notifications: boolean
  assignment_notifications: boolean
  milestone_notifications: boolean
  announcement_notifications: boolean
}

export interface GroupTask {
  id: number
  group_id: number
  title: string
  description: string
  type: 'quiz' | 'assignment' | 'discussion' | 'project' | 'peer_review'
  due_date?: string
  points?: number
  status: 'draft' | 'active' | 'completed' | 'overdue'
  created_by: string
  created_at: string
  // Learner-specific fields
  my_submission?: TaskSubmission
  my_status: 'not_started' | 'in_progress' | 'submitted' | 'completed'
  submissions_count: number
  completion_rate: number
}

export interface TaskSubmission {
  id: number
  task_id: number
  learner_id: string
  content: any
  submitted_at: string
  score?: number
  feedback?: string
  status: 'submitted' | 'graded' | 'returned'
}

export interface GroupWhiteboard {
  id: number
  group_id: number
  title: string
  content: any // Konva canvas data
  created_by: string
  created_at: string
  updated_at: string
  active_users: string[]
}

export interface GroupReward {
  id: number
  group_id: number
  type: 'badge' | 'points' | 'certificate'
  title: string
  description: string
  criteria: any
  points_value?: number
  badge_icon?: string
  earned_at?: string
  earned_by?: string[]
}

export interface StudyBuddy {
  id: string
  name: string
  avatar_url?: string
  shared_courses: number
  compatibility_score: number
  last_active: string
  status: 'online' | 'offline' | 'away'
}

export interface GroupInsight {
  id: string
  group_id: number
  type: 'performance' | 'engagement' | 'recommendation' | 'alert'
  title: string
  description: string
  action_items: string[]
  confidence_score: number
  created_at: string
  is_read: boolean
}

export interface GroupPost {
  id: number
  group_id: number
  author_id: string
  author_name: string
  author_avatar?: string
  content: string
  type: 'text' | 'question' | 'resource' | 'announcement'
  attachments?: PostAttachment[]
  likes_count: number
  comments_count: number
  is_liked: boolean
  is_pinned: boolean
  created_at: string
  updated_at: string
}

export interface PostAttachment {
  id: string
  name: string
  url: string
  type: string
  size: number
}

export interface GroupKudos {
  id: number
  group_id: number
  giver_id: string
  giver_name: string
  receiver_id: string
  receiver_name: string
  reason: string
  points: number
  created_at: string
}

// Feature Flags
export interface GroupFeatureFlags {
  enable_group_creation: boolean
  enable_group_archiving: boolean
  enable_group_tags: boolean
  enable_group_hierarchy: boolean
  enable_bulk_member_add: boolean
  enable_group_communication: boolean
  enable_group_chat: boolean
  enable_group_scheduling: boolean
  enable_ai_grouping: boolean
  enable_auto_reassignment: boolean
  enable_assignment_templates: boolean
  enable_group_customization: boolean
  enable_group_integrations: boolean
  enable_advanced_analytics: boolean
  enable_learner_drilldown: boolean
  enable_deadline_enforcement: boolean
  restrict_group_creation: boolean
  restrict_group_dates: boolean
  restrict_member_removal: boolean
  restrict_mandatory_paths: boolean
  restrict_sms_channel: boolean
}
