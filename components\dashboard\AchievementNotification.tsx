'use client'

import { useEffect, useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  IconButton,
  Avatar,
  Chip,
  useTheme,
} from '@mui/material'
import {
  Close as CloseIcon,
  EmojiEvents as TrophyIcon,
  Star as StarIcon,
} from '@mui/icons-material'
import { motion, AnimatePresence } from 'framer-motion'

interface Achievement {
  id: string
  title: string
  description: string
  icon: string
  progress: number
  maxProgress: number
  isCompleted: boolean
  reward?: {
    type: 'points' | 'badge' | 'unlock'
    value: any
  }
  unlockedAt?: string
}

interface AchievementNotificationProps {
  achievement: Achievement
  onClose: () => void
}

export default function AchievementNotification({ achievement, onClose }: AchievementNotificationProps) {
  const theme = useTheme()
  const [showConfetti, setShowConfetti] = useState(false)

  useEffect(() => {
    // Auto-close after 5 seconds
    const timer = setTimeout(() => {
      onClose()
    }, 5000)

    // Show confetti effect
    setShowConfetti(true)
    const confettiTimer = setTimeout(() => {
      setShowConfetti(false)
    }, 3000)

    return () => {
      clearTimeout(timer)
      clearTimeout(confettiTimer)
    }
  }, [onClose])

  // Create confetti particles
  const confettiParticles = Array.from({ length: 50 }, (_, i) => ({
    id: i,
    x: Math.random() * 100,
    y: Math.random() * 100,
    rotation: Math.random() * 360,
    color: ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'][Math.floor(Math.random() * 6)],
    size: Math.random() * 8 + 4,
    delay: Math.random() * 2,
  }))

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.5, y: -100 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.5, y: -100 }}
      transition={{ 
        type: "spring", 
        stiffness: 300, 
        damping: 20,
        duration: 0.6 
      }}
      style={{
        position: 'fixed',
        top: 24,
        right: 24,
        zIndex: 10000,
        maxWidth: 400,
        width: '100%',
      }}
    >
      {/* Confetti Effect */}
      <AnimatePresence>
        {showConfetti && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              pointerEvents: 'none',
              overflow: 'hidden',
              zIndex: -1,
            }}
          >
            {confettiParticles.map((particle) => (
              <motion.div
                key={particle.id}
                initial={{
                  x: `${particle.x}%`,
                  y: '-10%',
                  rotate: 0,
                  opacity: 1,
                }}
                animate={{
                  y: '110%',
                  rotate: particle.rotation,
                  opacity: 0,
                }}
                transition={{
                  duration: 3,
                  delay: particle.delay,
                  ease: 'easeOut',
                }}
                style={{
                  position: 'absolute',
                  width: particle.size,
                  height: particle.size,
                  backgroundColor: particle.color,
                  borderRadius: '50%',
                }}
              />
            ))}
          </Box>
        )}
      </AnimatePresence>

      <Card
        sx={{
          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
          color: 'white',
          boxShadow: theme.shadows[8],
          border: '2px solid',
          borderColor: 'rgba(255, 255, 255, 0.3)',
          position: 'relative',
          overflow: 'visible',
        }}
      >
        {/* Glow Effect */}
        <Box
          sx={{
            position: 'absolute',
            top: -2,
            left: -2,
            right: -2,
            bottom: -2,
            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
            borderRadius: 'inherit',
            filter: 'blur(8px)',
            opacity: 0.6,
            zIndex: -1,
          }}
        />

        <CardContent sx={{ position: 'relative' }}>
          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
            <Box display="flex" alignItems="center" gap={2}>
              <motion.div
                animate={{ 
                  rotate: [0, 10, -10, 0],
                  scale: [1, 1.1, 1]
                }}
                transition={{ 
                  duration: 2, 
                  repeat: Infinity,
                  repeatDelay: 3
                }}
              >
                <Avatar
                  sx={{
                    width: 48,
                    height: 48,
                    bgcolor: 'rgba(255, 255, 255, 0.2)',
                    fontSize: '1.5rem',
                    border: '2px solid rgba(255, 255, 255, 0.3)',
                  }}
                >
                  {achievement.icon}
                </Avatar>
              </motion.div>
              <Box>
                <Typography variant="h6" fontWeight="bold">
                  Achievement Unlocked!
                </Typography>
                <Box display="flex" alignItems="center" gap={1}>
                  <TrophyIcon fontSize="small" />
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    New milestone reached
                  </Typography>
                </Box>
              </Box>
            </Box>
            <IconButton
              onClick={onClose}
              size="small"
              sx={{ 
                color: 'rgba(255, 255, 255, 0.8)',
                '&:hover': { 
                  color: 'white',
                  bgcolor: 'rgba(255, 255, 255, 0.1)'
                }
              }}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>

          <Box mb={2}>
            <Typography variant="h5" fontWeight="bold" mb={1}>
              {achievement.title}
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.9 }}>
              {achievement.description}
            </Typography>
          </Box>

          {achievement.reward && (
            <Box display="flex" alignItems="center" gap={1} mb={2}>
              <Typography variant="body2" fontWeight="medium">
                Reward:
              </Typography>
              <Chip
                icon={<StarIcon />}
                label={
                  achievement.reward.type === 'points' 
                    ? `+${achievement.reward.value} XP`
                    : achievement.reward.type === 'badge'
                    ? `Badge: ${achievement.reward.value}`
                    : `Unlocked: ${achievement.reward.value}`
                }
                sx={{
                  bgcolor: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  fontWeight: 'bold',
                  '& .MuiChip-icon': {
                    color: 'white'
                  }
                }}
              />
            </Box>
          )}

          {/* Progress Bar (if applicable) */}
          {achievement.progress !== undefined && achievement.maxProgress !== undefined && (
            <Box mb={2}>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                <Typography variant="body2" fontWeight="medium">
                  Progress
                </Typography>
                <Typography variant="body2" fontWeight="bold">
                  {achievement.progress}/{achievement.maxProgress}
                </Typography>
              </Box>
              <Box
                sx={{
                  width: '100%',
                  height: 8,
                  bgcolor: 'rgba(255, 255, 255, 0.2)',
                  borderRadius: 4,
                  overflow: 'hidden',
                }}
              >
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${(achievement.progress / achievement.maxProgress) * 100}%` }}
                  transition={{ duration: 1, delay: 0.5 }}
                  style={{
                    height: '100%',
                    background: 'linear-gradient(90deg, rgba(255,255,255,0.8) 0%, rgba(255,255,255,1) 100%)',
                    borderRadius: 4,
                  }}
                />
              </Box>
            </Box>
          )}

          {/* Celebration Message */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1 }}
          >
            <Box
              sx={{
                textAlign: 'center',
                p: 1.5,
                bgcolor: 'rgba(255, 255, 255, 0.1)',
                borderRadius: 2,
                border: '1px solid rgba(255, 255, 255, 0.2)',
              }}
            >
              <Typography variant="body2" fontWeight="bold" mb={0.5}>
                🎉 Congratulations! 🎉
              </Typography>
              <Typography variant="caption" sx={{ opacity: 0.9 }}>
                Keep up the excellent work and unlock more achievements!
              </Typography>
            </Box>
          </motion.div>
        </CardContent>

        {/* Sparkle Effects */}
        <Box
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            fontSize: '1.2rem',
          }}
        >
          <motion.div
            animate={{ 
              rotate: [0, 360],
              scale: [1, 1.2, 1]
            }}
            transition={{ 
              duration: 4, 
              repeat: Infinity,
              ease: "linear"
            }}
          >
            ✨
          </motion.div>
        </Box>

        <Box
          sx={{
            position: 'absolute',
            bottom: 8,
            left: 8,
            fontSize: '1rem',
          }}
        >
          <motion.div
            animate={{ 
              rotate: [360, 0],
              scale: [1, 1.3, 1]
            }}
            transition={{ 
              duration: 3, 
              repeat: Infinity,
              ease: "linear",
              delay: 1
            }}
          >
            ⭐
          </motion.div>
        </Box>
      </Card>
    </motion.div>
  )
}
