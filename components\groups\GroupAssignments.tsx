'use client'

import { useState } from 'react'
import {
  Box,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  LinearProgress,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Autocomplete,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
} from '@mui/material'
import {
  Add as AddIcon,
  School as SchoolIcon,
  Schedule as ScheduleIcon,
  Assignment as AssignmentIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid'

import { Group, GroupAssignment } from '@/lib/types/groups'
import { GroupsService } from '@/lib/services/groups'

interface GroupAssignmentsProps {
  group: Group
  assignments: GroupAssignment[]
  onRefresh: () => void
}

interface AssignPathDialogProps {
  open: boolean
  onClose: () => void
  onAssign: (pathId: number, settings: any) => void
  loading?: boolean
}

function AssignPathDialog({ open, onClose, onAssign, loading }: AssignPathDialogProps) {
  const [selectedPath, setSelectedPath] = useState<any>(null)
  const [startDate, setStartDate] = useState<Date | null>(null)
  const [dueDate, setDueDate] = useState<Date | null>(null)
  const [isMandatory, setIsMandatory] = useState(true)
  const [autoAssignNewMembers, setAutoAssignNewMembers] = useState(true)

  // Mock learning paths data
  const availablePaths = [
    { id: 1, title: 'JavaScript Fundamentals', description: 'Learn the basics of JavaScript' },
    { id: 2, title: 'React Development', description: 'Build modern web applications with React' },
    { id: 3, title: 'Node.js Backend', description: 'Server-side development with Node.js' },
  ]

  const handleAssign = () => {
    if (!selectedPath) return

    const settings = {
      start_date: startDate?.toISOString(),
      due_date: dueDate?.toISOString(),
      is_mandatory: isMandatory,
      settings: {
        auto_assign_new_members: autoAssignNewMembers,
      },
    }

    onAssign(selectedPath.id, settings)
    
    // Reset form
    setSelectedPath(null)
    setStartDate(null)
    setDueDate(null)
    setIsMandatory(true)
    setAutoAssignNewMembers(true)
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Assign Learning Path</DialogTitle>
      <DialogContent>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <Autocomplete
              options={availablePaths}
              getOptionLabel={(option) => option.title}
              value={selectedPath}
              onChange={(_, newValue) => setSelectedPath(newValue)}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Select Learning Path"
                  placeholder="Search for a learning path"
                />
              )}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <DatePicker
              label="Start Date (Optional)"
              value={startDate}
              onChange={setStartDate}
              slotProps={{
                textField: { fullWidth: true },
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <DatePicker
              label="Due Date (Optional)"
              value={dueDate}
              onChange={setDueDate}
              minDate={startDate || undefined}
              slotProps={{
                textField: { fullWidth: true },
              }}
            />
          </Grid>

          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={isMandatory}
                  onChange={(e) => setIsMandatory(e.target.checked)}
                />
              }
              label="Mandatory Assignment"
            />
          </Grid>

          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={autoAssignNewMembers}
                  onChange={(e) => setAutoAssignNewMembers(e.target.checked)}
                />
              }
              label="Auto-assign to new members"
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={handleAssign}
          variant="contained"
          disabled={!selectedPath || loading}
        >
          {loading ? 'Assigning...' : 'Assign Path'}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default function GroupAssignments({ group, assignments, onRefresh }: GroupAssignmentsProps) {
  const [assignDialogOpen, setAssignDialogOpen] = useState(false)
  const [loading, setLoading] = useState(false)

  const handleAssignPath = async (pathId: number, settings: any) => {
    try {
      setLoading(true)
      await GroupsService.assignPathToGroup(group.id, pathId, settings)
      setAssignDialogOpen(false)
      onRefresh()
    } catch (error) {
      console.error('Failed to assign path:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleRemoveAssignment = async (assignmentId: number) => {
    try {
      await GroupsService.removePathFromGroup(assignmentId)
      onRefresh()
    } catch (error) {
      console.error('Failed to remove assignment:', error)
    }
  }

  const getStatusColor = (assignment: GroupAssignment) => {
    const now = new Date()
    const dueDate = assignment.due_date ? new Date(assignment.due_date) : null
    const startDate = assignment.start_date ? new Date(assignment.start_date) : null

    if (dueDate && now > dueDate) return 'error'
    if (startDate && now < startDate) return 'info'
    return 'success'
  }

  const getStatusLabel = (assignment: GroupAssignment) => {
    const now = new Date()
    const dueDate = assignment.due_date ? new Date(assignment.due_date) : null
    const startDate = assignment.start_date ? new Date(assignment.start_date) : null

    if (dueDate && now > dueDate) return 'Overdue'
    if (startDate && now < startDate) return 'Scheduled'
    return 'Active'
  }

  const columns: GridColDef[] = [
    {
      field: 'path_title',
      headerName: 'Learning Path',
      flex: 1,
      minWidth: 250,
      renderCell: (params) => (
        <Box>
          <Typography variant="body2" fontWeight="medium">
            {params.row.path_title || 'Unknown Path'}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {params.row.path_description}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={getStatusLabel(params.row)}
          color={getStatusColor(params.row) as any}
          size="small"
          variant="outlined"
        />
      ),
    },
    {
      field: 'is_mandatory',
      headerName: 'Type',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value ? 'Mandatory' : 'Optional'}
          color={params.value ? 'error' : 'default'}
          size="small"
          variant="outlined"
        />
      ),
    },
    {
      field: 'start_date',
      headerName: 'Start Date',
      width: 120,
      renderCell: (params) => (
        <Typography variant="body2" color="text.secondary">
          {params.value ? new Date(params.value).toLocaleDateString() : 'N/A'}
        </Typography>
      ),
    },
    {
      field: 'due_date',
      headerName: 'Due Date',
      width: 120,
      renderCell: (params) => (
        <Typography variant="body2" color="text.secondary">
          {params.value ? new Date(params.value).toLocaleDateString() : 'N/A'}
        </Typography>
      ),
    },
    {
      field: 'assigned_at',
      headerName: 'Assigned',
      width: 120,
      renderCell: (params) => (
        <Typography variant="body2" color="text.secondary">
          {new Date(params.value).toLocaleDateString()}
        </Typography>
      ),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 100,
      getActions: (params) => [
        <GridActionsCellItem
          key="edit"
          icon={<EditIcon />}
          label="Edit"
          onClick={() => {
            // TODO: Open edit assignment dialog
          }}
        />,
        <GridActionsCellItem
          key="remove"
          icon={<DeleteIcon />}
          label="Remove"
          onClick={() => handleRemoveAssignment(params.row.id)}
          sx={{ color: 'error.main' }}
        />,
      ],
    },
  ]

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          Learning Path Assignments ({assignments.length})
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setAssignDialogOpen(true)}
        >
          Assign Path
        </Button>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <SchoolIcon color="primary" />
                <Box>
                  <Typography variant="h6">
                    {assignments.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Assignments
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <AssignmentIcon color="success" />
                <Box>
                  <Typography variant="h6">
                    {assignments.filter(a => a.is_mandatory).length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Mandatory
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <ScheduleIcon color="warning" />
                <Box>
                  <Typography variant="h6">
                    {assignments.filter(a => {
                      const dueDate = a.due_date ? new Date(a.due_date) : null
                      return dueDate && new Date() > dueDate
                    }).length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Overdue
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <SchoolIcon color="info" />
                <Box>
                  <Typography variant="h6">
                    {assignments.filter(a => !a.is_mandatory).length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Optional
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Assignments List */}
      {assignments.length === 0 ? (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 8 }}>
            <SchoolIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No learning paths assigned
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Assign learning paths to help group members develop their skills
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setAssignDialogOpen(true)}
            >
              Assign First Path
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <DataGrid
            rows={assignments}
            columns={columns}
            autoHeight
            checkboxSelection
            disableRowSelectionOnClick
            pageSizeOptions={[10, 25, 50]}
            initialState={{
              pagination: { paginationModel: { pageSize: 25 } },
            }}
            sx={{
              border: 'none',
              '& .MuiDataGrid-cell:focus': {
                outline: 'none',
              },
            }}
          />
        </Card>
      )}

      {/* Assign Path Dialog */}
      <AssignPathDialog
        open={assignDialogOpen}
        onClose={() => setAssignDialogOpen(false)}
        onAssign={handleAssignPath}
        loading={loading}
      />
    </Box>
  )
}
