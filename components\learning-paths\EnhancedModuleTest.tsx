'use client'

import React, { useState } from 'react'
import {
  Box,
  Typography,
  Button,
  Paper,
  Alert,
  Card,
  CardContent,
  Grid,
} from '@mui/material'
import {
  PlayArrow as PlayIcon,
  CheckCircle as CheckIcon,
} from '@mui/icons-material'

import EnhancedModuleManager from './EnhancedModuleManager'
import PathBasicInfo from './PathBasicInfo'
import { Module } from '@/lib/types/learning-paths'

export default function EnhancedModuleTest() {
  const [modules, setModules] = useState<Module[]>([
    {
      id: 1,
      learning_path_id: 1,
      title: 'Introduction to React',
      description: 'Learn the basics of React development',
      order_index: 0,
      is_optional: false,
      estimated_duration: 120,
      category: 'video',
      lessons: [
        {
          id: 1,
          module_id: 1,
          title: 'What is React?',
          description: 'Introduction to React concepts',
          type: 'video',
          order_index: 0,
          estimated_duration: 30,
          is_optional: false,
          content_url: '',
          metadata: {}
        },
        {
          id: 2,
          module_id: 1,
          title: 'Setting up React',
          description: 'How to set up a React project',
          type: 'video',
          order_index: 1,
          estimated_duration: 45,
          is_optional: false,
          content_url: '',
          metadata: {}
        }
      ],
      metadata: {}
    },
    {
      id: 2,
      learning_path_id: 1,
      title: 'React Components',
      description: 'Understanding React components and props',
      order_index: 1,
      is_optional: false,
      estimated_duration: 90,
      category: 'interactive',
      lessons: [
        {
          id: 3,
          module_id: 2,
          title: 'Creating Components',
          description: 'How to create React components',
          type: 'interactive',
          order_index: 0,
          estimated_duration: 45,
          is_optional: false,
          content_url: '',
          metadata: {}
        }
      ],
      metadata: {}
    }
  ])

  const [pathData, setPathData] = useState({
    title: 'React Development Course',
    description: 'Learn React from scratch to advanced concepts',
    difficulty: 'intermediate',
    duration: 8,
    category: 'Programming',
    tags: ['React', 'JavaScript', 'Frontend'],
    objectives: [
      'Understand React fundamentals',
      'Build interactive components',
      'Manage application state'
    ],
    prerequisites: [
      'Basic JavaScript knowledge',
      'HTML/CSS fundamentals'
    ],
    is_template: false,
    is_featured: true
  })

  const [testResults, setTestResults] = useState<string[]>([])

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`])
  }

  const handleModulesChange = (newModules: Module[]) => {
    setModules(newModules)
    addTestResult(`Modules updated: ${newModules.length} modules`)
  }

  const handlePathDataChange = (newData: any) => {
    setPathData(prev => ({ ...prev, ...newData }))
    addTestResult(`Path data updated: ${Object.keys(newData).join(', ')}`)
  }

  const runTests = () => {
    setTestResults([])
    addTestResult('Starting Enhanced Module Manager Tests...')
    
    // Test 1: Module count
    addTestResult(`✓ Initial module count: ${modules.length}`)
    
    // Test 2: Module structure validation
    const hasValidStructure = modules.every(module => 
      module.id && module.title && module.lessons
    )
    addTestResult(`✓ Module structure validation: ${hasValidStructure ? 'PASS' : 'FAIL'}`)
    
    // Test 3: Lesson count
    const totalLessons = modules.reduce((total, module) => total + (module.lessons?.length || 0), 0)
    addTestResult(`✓ Total lessons: ${totalLessons}`)
    
    // Test 4: Duration calculation
    const totalDuration = modules.reduce((total, module) => total + (module.estimated_duration || 0), 0)
    addTestResult(`✓ Total duration: ${totalDuration} minutes`)
    
    // Test 5: Path data validation
    const hasValidPathData = pathData.title && pathData.description && pathData.category
    addTestResult(`✓ Path data validation: ${hasValidPathData ? 'PASS' : 'FAIL'}`)
    
    addTestResult('✅ All tests completed successfully!')
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" fontWeight="bold" gutterBottom>
        Enhanced Module Manager Test
      </Typography>
      <Typography variant="body1" color="text.secondary" mb={3}>
        This component demonstrates the enhanced Learning Path Edit functionality with full CRUD operations for modules.
      </Typography>

      {/* Test Controls */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box display="flex" alignItems="center" gap={2} mb={2}>
          <Button
            variant="contained"
            startIcon={<PlayIcon />}
            onClick={runTests}
          >
            Run Tests
          </Button>
          <Button
            variant="outlined"
            onClick={() => setTestResults([])}
          >
            Clear Results
          </Button>
        </Box>

        {testResults.length > 0 && (
          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              Test Results:
            </Typography>
            <Box component="ul" sx={{ m: 0, pl: 2 }}>
              {testResults.map((result, index) => (
                <li key={index}>
                  <Typography variant="body2" component="span">
                    {result}
                  </Typography>
                </li>
              ))}
            </Box>
          </Alert>
        )}
      </Paper>

      {/* Enhanced Components */}
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Path Basic Information
              </Typography>
              <PathBasicInfo
                data={pathData}
                onChange={handlePathDataChange}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Enhanced Module Manager
              </Typography>
              <EnhancedModuleManager
                modules={modules}
                onChange={handleModulesChange}
                learningPathId="1"
              />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Feature Summary */}
      <Paper sx={{ p: 3, mt: 3, bgcolor: 'success.light', color: 'success.contrastText' }}>
        <Typography variant="h6" gutterBottom>
          <CheckIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Enhanced Features Implemented
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <Typography variant="body2" gutterBottom>
              <strong>Module Management:</strong>
            </Typography>
            <ul style={{ margin: 0, paddingLeft: 20 }}>
              <li>Drag-and-drop reordering</li>
              <li>Module templates for quick creation</li>
              <li>Bulk operations (select, duplicate, delete)</li>
              <li>Real-time validation</li>
              <li>Visual feedback and animations</li>
            </ul>
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant="body2" gutterBottom>
              <strong>Enhanced UI/UX:</strong>
            </Typography>
            <ul style={{ margin: 0, paddingLeft: 20 }}>
              <li>Tabbed interface for better organization</li>
              <li>Undo/Redo functionality</li>
              <li>Unsaved changes tracking</li>
              <li>Comprehensive preview mode</li>
              <li>Responsive design</li>
            </ul>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  )
}
