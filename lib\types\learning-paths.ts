// Learning Path Types for ZenithLearn AI

export interface Course {
  id: number
  tenant_id: number
  title: string
  description: string
  duration_hours: number
  difficulty_level: 'beginner' | 'intermediate' | 'advanced'
  category: string
  status: 'active' | 'inactive' | 'archived'
  metadata: any
  created_at: string
  updated_at: string
  instructor_id: string
  learning_paths?: LearningPath[]
}

export interface LearningPath {
  id: number
  course_id?: number
  title: string
  description: string
  objectives: string[]
  prerequisites: string[]
  difficulty_level: 'beginner' | 'intermediate' | 'advanced'
  estimated_duration: number // in weeks
  category: string
  tags: string[]
  is_live: boolean
  is_featured: boolean
  is_template?: boolean
  thumbnail_url?: string
  tenant_id: number
  created_by: string
  created_at: string
  updated_at: string
  published_at?: string
  modules?: Module[]
  learner_assignments?: LearnerAssignment[]
  course?: Course
  // Computed fields for compatibility
  status?: 'draft' | 'published' | 'archived'
  duration?: number
  difficulty?: 'beginner' | 'intermediate' | 'advanced'
  assignments?: LearnerAssignment[]
  metadata?: {
    estimated_hours: number
    completion_rate: number
    average_rating: number
    total_learners: number
    prerequisites: string[]
    skills: string[]
  }
}

export interface Module {
  id: number
  path_id: number
  title: string
  description: string
  order_index: number
  is_optional: boolean
  unlock_conditions: any
  lessons?: Lesson[]
  created_at: string
  updated_at: string
  // Computed fields for compatibility
  order?: number
  estimated_duration?: number // in minutes
  dependencies?: string[] // module IDs
}

export interface Lesson {
  id: number
  module_id: number
  title: string
  description: string
  type: LessonType
  content_url?: string
  content_text?: string
  content_metadata: any
  order_index: number
  estimated_duration: number // in minutes
  is_mandatory: boolean
  passing_score?: number
  max_attempts: number
  created_at: string
  updated_at: string
  // Computed fields for compatibility
  order?: number
  is_optional?: boolean
  content?: LessonContent
  dependencies?: string[] // lesson IDs
  interactions?: LessonInteraction[]
}

export type LessonType = 
  | 'video' 
  | 'pdf' 
  | 'quiz' 
  | 'link' 
  | 'simulation' 
  | 'project' 
  | 'discussion' 
  | 'scorm'

export interface LessonContent {
  // Video content
  video_url?: string
  video_duration?: number
  video_transcript?: string
  
  // PDF content
  pdf_url?: string
  pdf_pages?: number
  
  // Quiz content
  quiz_data?: QuizData
  
  // Link content
  external_url?: string
  link_description?: string
  
  // Simulation content
  simulation_data?: any
  simulation_type?: 'code' | 'scenario' | 'interactive'
  
  // Project content
  project_instructions?: string
  project_deliverables?: string[]
  project_rubric?: any
  
  // SCORM content
  scorm_package_url?: string
  scorm_version?: string
}

export interface QuizData {
  questions: QuizQuestion[]
  passing_score: number
  max_attempts: number
  time_limit?: number // in minutes
  randomize_questions: boolean
  show_correct_answers: boolean
}

export interface QuizQuestion {
  id: string
  type: 'multiple_choice' | 'true_false' | 'fill_blank' | 'essay'
  question: string
  options?: string[]
  correct_answer: string | string[]
  explanation?: string
  points: number
}

export interface LessonInteraction {
  id: string
  lesson_id: string
  type: 'poll' | 'qa' | 'comment' | 'rating'
  data: any
  created_at: string
}

export interface LearnerAssignment {
  id: number
  path_id: number
  learner_id: string
  assigned_by: string
  assigned_at: string
  due_date?: string
  status: 'assigned' | 'in_progress' | 'completed' | 'overdue'
  completion_percentage: number
  completed_at?: string
}

// Legacy interface for compatibility
export interface Assignment extends LearnerAssignment {
  group_id?: string
  department_id?: string
  started_at?: string
  notes?: string
}

export interface Gamification {
  points_enabled: boolean
  badges_enabled: boolean
  leaderboard_enabled: boolean
  rewards: GamificationReward[]
}

export interface GamificationReward {
  id: string
  type: 'points' | 'badge' | 'certificate'
  name: string
  description: string
  criteria: any
  value: number
  icon_url?: string
}

export interface PathTemplate {
  id: string
  title: string
  description: string
  category: string
  structure: Omit<LearningPath, 'id' | 'tenant_id' | 'created_by' | 'created_at' | 'updated_at'>
  usage_count: number
  is_public: boolean
  created_by: string
  created_at: string
}

export interface PathVersion {
  id: string
  path_id: string
  version_number: number
  changes_summary: string
  data: Partial<LearningPath>
  created_by: string
  created_at: string
}

export interface PathFeedback {
  id: string
  path_id: string
  learner_id: string
  rating: number
  comments: string
  suggestions: string[]
  created_at: string
}

export interface AIPathSuggestion {
  title: string
  description: string
  modules: {
    title: string
    lessons: {
      title: string
      type: LessonType
      description: string
      estimated_duration: number
    }[]
  }[]
  estimated_duration: number
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  skills: string[]
}

// Form interfaces for creation/editing
export interface PathCreationForm {
  title: string
  description: string
  objectives: string[]
  duration: number
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  category: string
  tags: string[]
  is_template: boolean
}

export interface ModuleCreationForm {
  title: string
  description: string
  is_optional: boolean
  estimated_duration: number
}

// Module Templates for Quick Creation
export interface ModuleTemplate {
  id: string
  name: string
  description: string
  icon: string
  color: string
  defaultLessons: {
    title: string
    type: LessonType
    description: string
    estimated_duration: number
  }[]
  category: 'video' | 'interactive' | 'assessment' | 'reading' | 'project'
}

// Enhanced Module with CRUD operations
export interface ModuleWithOperations extends Module {
  isEditing?: boolean
  isDuplicate?: boolean
  hasUnsavedChanges?: boolean
  validationErrors?: string[]
  prerequisites?: number[] // module IDs
  estimatedCompletionTime?: number
}

// Bulk Operations for Modules
export interface ModuleBulkOperation {
  type: 'delete' | 'duplicate' | 'reorder' | 'export'
  moduleIds: number[]
  targetPosition?: number
  newOrder?: number[]
}

export interface LessonCreationForm {
  title: string
  description: string
  type: LessonType
  is_optional: boolean
  estimated_duration: number
  content: Partial<LessonContent>
}

// API Response types
export interface PathsResponse {
  data: LearningPath[]
  count: number
  page: number
  limit: number
}

export interface PathResponse {
  data: LearningPath
}

// Filter and search types
export interface PathFilters {
  search: string
  status: string
  category: string
  difficulty: string
  tags: string[]
  created_by: string
  date_range: {
    start: string
    end: string
  }
}

export interface PathSortOptions {
  field: 'title' | 'created_at' | 'updated_at' | 'completion_rate' | 'total_learners' | 'difficulty' | 'duration'
  direction: 'asc' | 'desc'
}

// Wizard step types
export interface WizardStep {
  id: number
  title: string
  description: string
  component: string
  isCompleted: boolean
  isActive: boolean
  validation?: (data: any) => boolean
}

// Drag and drop types
export interface DragItem {
  id: string
  type: 'module' | 'lesson'
  data: Module | Lesson
}

export interface DropResult {
  draggedId: string
  targetId: string
  position: 'before' | 'after' | 'inside'
}
