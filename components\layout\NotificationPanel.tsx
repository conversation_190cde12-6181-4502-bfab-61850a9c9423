'use client'

import {
  <PERSON>u,
  <PERSON>uItem,
  Typography,
  Box,
  Avatar,
  Chip,
  Divider,
  But<PERSON>,
} from '@mui/material'
import {
  Circle as CircleIcon,
  School as SchoolIcon,
  People as PeopleIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material'

interface NotificationPanelProps {
  anchorEl: HTMLElement | null
  open: boolean
  onClose: () => void
}

// Mock notifications data
const notifications = [
  {
    id: 1,
    title: 'New learner enrolled',
    message: '<PERSON> has enrolled in "Advanced JavaScript" course',
    time: '2 minutes ago',
    type: 'learner',
    unread: true,
  },
  {
    id: 2,
    title: 'Course completion',
    message: '<PERSON> completed "React Fundamentals"',
    time: '1 hour ago',
    type: 'completion',
    unread: true,
  },
  {
    id: 3,
    title: 'Low engagement alert',
    message: 'Python Basics course has low engagement this week',
    time: '3 hours ago',
    type: 'alert',
    unread: false,
  },
  {
    id: 4,
    title: 'New content uploaded',
    message: 'Video "Introduction to AI" has been uploaded',
    time: '1 day ago',
    type: 'content',
    unread: false,
  },
]

const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'learner':
      return <PeopleIcon fontSize="small" />
    case 'completion':
      return <SchoolIcon fontSize="small" />
    case 'alert':
      return <AssessmentIcon fontSize="small" />
    case 'content':
      return <SchoolIcon fontSize="small" />
    default:
      return <CircleIcon fontSize="small" />
  }
}

const getNotificationColor = (type: string) => {
  switch (type) {
    case 'learner':
      return 'primary'
    case 'completion':
      return 'success'
    case 'alert':
      return 'warning'
    case 'content':
      return 'info'
    default:
      return 'default'
  }
}

export default function NotificationPanel({ anchorEl, open, onClose }: NotificationPanelProps) {
  const unreadCount = notifications.filter(n => n.unread).length

  return (
    <Menu
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      transformOrigin={{ horizontal: 'right', vertical: 'top' }}
      anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      PaperProps={{
        sx: {
          width: 360,
          maxHeight: 400,
          overflow: 'visible',
        },
      }}
    >
      <Box sx={{ p: 2, pb: 1 }}>
        <Typography variant="h6" fontWeight="bold">
          Notifications
        </Typography>
        {unreadCount > 0 && (
          <Chip
            label={`${unreadCount} new`}
            size="small"
            color="primary"
            sx={{ ml: 1 }}
          />
        )}
      </Box>
      
      <Divider />

      <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
        {notifications.map((notification) => (
          <MenuItem
            key={notification.id}
            onClick={onClose}
            sx={{
              py: 1.5,
              px: 2,
              borderLeft: notification.unread ? '3px solid' : '3px solid transparent',
              borderLeftColor: notification.unread ? 'primary.main' : 'transparent',
              backgroundColor: notification.unread ? 'action.hover' : 'transparent',
              '&:hover': {
                backgroundColor: 'action.selected',
              },
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'flex-start', width: '100%' }}>
              <Avatar
                sx={{
                  width: 32,
                  height: 32,
                  mr: 1.5,
                  bgcolor: `${getNotificationColor(notification.type)}.main`,
                }}
              >
                {getNotificationIcon(notification.type)}
              </Avatar>
              
              <Box sx={{ flex: 1, minWidth: 0 }}>
                <Typography
                  variant="subtitle2"
                  fontWeight={notification.unread ? 600 : 400}
                  sx={{ mb: 0.5 }}
                >
                  {notification.title}
                </Typography>
                
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical',
                    mb: 0.5,
                  }}
                >
                  {notification.message}
                </Typography>
                
                <Typography variant="caption" color="text.secondary">
                  {notification.time}
                </Typography>
              </Box>

              {notification.unread && (
                <CircleIcon
                  sx={{
                    fontSize: 8,
                    color: 'primary.main',
                    ml: 1,
                    mt: 0.5,
                  }}
                />
              )}
            </Box>
          </MenuItem>
        ))}
      </Box>

      <Divider />
      
      <Box sx={{ p: 1 }}>
        <Button
          fullWidth
          variant="text"
          size="small"
          onClick={onClose}
        >
          View All Notifications
        </Button>
      </Box>
    </Menu>
  )
}
