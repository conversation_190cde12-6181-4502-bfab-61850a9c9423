import { serve } from 'https://deno.land/std/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.45.4';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface ReportConfig {
  templateId?: number;
  name: string;
  description: string;
  metrics: string[];
  filters: Record<string, any>;
  visualizations: Array<{
    type: string;
    title: string;
    metrics: string[];
    config: Record<string, any>;
  }>;
  format: 'pdf' | 'excel' | 'csv' | 'json';
}

interface ReportData {
  metadata: {
    name: string;
    description: string;
    generatedAt: string;
    generatedBy: string;
    tenant: string;
  };
  metrics: Record<string, any>;
  visualizations: Array<{
    type: string;
    title: string;
    data: any;
    config: Record<string, any>;
  }>;
  summary: {
    keyInsights: string[];
    recommendations: string[];
    trends: string[];
  };
}

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const startTime = Date.now();

    // Get Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    );

    // Get user from JWT
    const {
      data: { user },
      error: userError,
    } = await supabaseClient.auth.getUser();

    if (userError || !user) {
      throw new Error('Unauthorized');
    }

    // Get request body
    const { tenant_id, config }: { tenant_id: string; config: ReportConfig } = await req.json();

    if (!tenant_id || !config) {
      throw new Error('Tenant ID and report config are required');
    }

    // Generate report data based on metrics
    const reportData = await generateReportData(supabaseClient, tenant_id, config, user);

    // Save report execution record
    const { data: execution, error: executionError } = await supabaseClient
      .from('report_executions')
      .insert({
        tenant_id,
        template_id: config.templateId,
        executed_by: user.id,
        status: 'completed',
        generation_time_ms: Date.now() - startTime,
        output_format: config.format,
        view_count: 0,
      })
      .select()
      .single();

    if (executionError) {
      console.error('Error saving execution:', executionError);
    }

    return new Response(JSON.stringify({
      success: true,
      executionId: execution?.id,
      data: reportData,
      generationTime: Date.now() - startTime,
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });

  } catch (error) {
    console.error('Error in generate-report:', error);
    return new Response(
      JSON.stringify({ 
        error: error.message || 'Internal server error',
        details: error.toString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});

async function generateReportData(
  supabaseClient: any,
  tenantId: string,
  config: ReportConfig,
  user: any
): Promise<ReportData> {
  const reportData: ReportData = {
    metadata: {
      name: config.name,
      description: config.description,
      generatedAt: new Date().toISOString(),
      generatedBy: user.email,
      tenant: tenantId,
    },
    metrics: {},
    visualizations: [],
    summary: {
      keyInsights: [],
      recommendations: [],
      trends: [],
    },
  };

  // Generate metrics data
  for (const metric of config.metrics) {
    reportData.metrics[metric] = await generateMetricData(supabaseClient, tenantId, metric, config.filters);
  }

  // Generate visualizations
  for (const viz of config.visualizations) {
    const vizData = await generateVisualizationData(supabaseClient, tenantId, viz, config.filters);
    reportData.visualizations.push({
      type: viz.type,
      title: viz.title,
      data: vizData,
      config: viz.config,
    });
  }

  // Generate AI insights and summary
  reportData.summary = await generateAISummary(reportData.metrics, reportData.visualizations);

  return reportData;
}

async function generateMetricData(
  supabaseClient: any,
  tenantId: string,
  metric: string,
  filters: Record<string, any>
): Promise<any> {
  // This would contain the actual metric calculation logic
  // For now, return mock data based on metric type
  
  switch (metric) {
    case 'completion_rate':
      // Get completion rates from progress table
      const { data: progressData } = await supabaseClient
        .from('progress')
        .select(`
          completion_percentage,
          users!inner(tenant_id)
        `)
        .eq('users.tenant_id', tenantId);
      
      const completionRates = progressData?.map(p => p.completion_percentage) || [];
      const avgCompletion = completionRates.length > 0 
        ? completionRates.reduce((a, b) => a + b, 0) / completionRates.length 
        : 0;
      
      return {
        value: Math.round(avgCompletion * 100) / 100,
        unit: '%',
        trend: '+5.2%',
        previousPeriod: Math.round((avgCompletion - 5.2) * 100) / 100,
      };

    case 'active_learners':
      // Get active learners count
      const { count: activeLearners } = await supabaseClient
        .from('users')
        .select('*', { count: 'exact', head: true })
        .eq('tenant_id', tenantId)
        .eq('status', 'active')
        .neq('role_id', 1); // Exclude admins
      
      return {
        value: activeLearners || 0,
        unit: 'users',
        trend: '+12.3%',
        previousPeriod: Math.max(0, (activeLearners || 0) - 15),
      };

    case 'avg_score':
      // Mock average score calculation
      return {
        value: 78.5,
        unit: 'points',
        trend: '+2.1%',
        previousPeriod: 76.9,
      };

    case 'time_spent':
      // Mock time spent calculation
      return {
        value: 145,
        unit: 'minutes',
        trend: '+8.7%',
        previousPeriod: 133,
      };

    default:
      return {
        value: Math.floor(Math.random() * 100),
        unit: 'units',
        trend: `${Math.random() > 0.5 ? '+' : '-'}${(Math.random() * 10).toFixed(1)}%`,
        previousPeriod: Math.floor(Math.random() * 100),
      };
  }
}

async function generateVisualizationData(
  supabaseClient: any,
  tenantId: string,
  visualization: any,
  filters: Record<string, any>
): Promise<any> {
  // Generate visualization data based on type
  switch (visualization.type) {
    case 'bar':
    case 'line':
      return {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
          label: visualization.title,
          data: Array.from({ length: 6 }, () => Math.floor(Math.random() * 100)),
        }],
      };

    case 'pie':
      return {
        labels: ['Completed', 'In Progress', 'Not Started'],
        datasets: [{
          data: [65, 25, 10],
          backgroundColor: ['#4CAF50', '#FF9800', '#F44336'],
        }],
      };

    case 'table':
      return {
        headers: ['Name', 'Progress', 'Score', 'Last Activity'],
        rows: Array.from({ length: 10 }, (_, i) => [
          `Learner ${i + 1}`,
          `${Math.floor(Math.random() * 100)}%`,
          `${Math.floor(Math.random() * 100)}`,
          `${Math.floor(Math.random() * 30)} days ago`,
        ]),
      };

    default:
      return {};
  }
}

async function generateAISummary(
  metrics: Record<string, any>,
  visualizations: any[]
): Promise<{ keyInsights: string[]; recommendations: string[]; trends: string[] }> {
  // In a real implementation, this would use OpenAI API to generate insights
  // For now, return mock insights based on the data
  
  const keyInsights = [
    'Overall completion rate has improved by 5.2% compared to last period',
    'Active learner engagement shows strong upward trend',
    'Quiz performance indicates good content comprehension',
  ];

  const recommendations = [
    'Consider expanding successful learning paths to more departments',
    'Implement additional interactive elements in lower-performing modules',
    'Schedule regular check-ins for learners showing declining engagement',
  ];

  const trends = [
    'Mobile learning adoption increasing by 15% month-over-month',
    'Video content shows 23% higher engagement than text-based materials',
    'Peer collaboration features driving 18% improvement in completion rates',
  ];

  return {
    keyInsights,
    recommendations,
    trends,
  };
}
