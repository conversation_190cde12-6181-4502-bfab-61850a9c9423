# Basic Usage Examples

This document provides practical examples of how to use the Groups and Batches feature in common scenarios. Each example includes complete code snippets and explanations.

## 🎯 Common Use Cases

### 1. Creating a New Group

**Scenario**: An administrator wants to create a new group for onboarding new employees.

```tsx
import { useState } from 'react'
import { useGroupsStore } from '@/lib/store'
import { GroupsService } from '@/lib/services/groups'
import GroupForm from '@/components/groups/GroupForm'

function CreateGroupExample() {
  const [formOpen, setFormOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const { addGroup } = useGroupsStore()

  const handleCreateGroup = async (groupData) => {
    try {
      setLoading(true)
      
      // Create group with default settings
      const newGroup = await GroupsService.createGroup({
        tenant_id: 1,
        name: groupData.name,
        description: groupData.description,
        status: 'active',
        tags: ['onboarding', 'new-employees'],
        settings: {
          notifications: {
            email_enabled: true,
            in_app_enabled: true,
            sms_enabled: false,
            announcement_frequency: 'immediate'
          },
          communication: {
            chat_enabled: true,
            file_sharing_enabled: true,
            video_calls_enabled: false
          },
          progress: {
            auto_progress_tracking: true,
            milestone_reminders: true,
            completion_certificates: true
          },
          access: {
            self_enrollment: false,
            member_invite_permissions: ['admin', 'moderator'],
            content_access_level: 'full'
          }
        },
        created_by: 'current-user-id'
      })

      // Update local state
      addGroup(newGroup)
      setFormOpen(false)
      
      console.log('Group created successfully:', newGroup)
    } catch (error) {
      console.error('Failed to create group:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <>
      <Button 
        variant="contained" 
        onClick={() => setFormOpen(true)}
      >
        Create Onboarding Group
      </Button>
      
      <GroupForm
        open={formOpen}
        onClose={() => setFormOpen(false)}
        onSubmit={handleCreateGroup}
        loading={loading}
        title="Create Onboarding Group"
      />
    </>
  )
}
```

### 2. Adding Members to a Group

**Scenario**: Add multiple new employees to an onboarding group.

```tsx
import { useState } from 'react'
import { GroupsService } from '@/lib/services/groups'

function AddMembersExample() {
  const [selectedLearners, setSelectedLearners] = useState([])
  const [loading, setLoading] = useState(false)
  
  // Sample learner data
  const availableLearners = [
    { id: 'user-1', full_name: 'John Doe', email: '<EMAIL>', department: 'Engineering' },
    { id: 'user-2', full_name: 'Jane Smith', email: '<EMAIL>', department: 'Marketing' },
    { id: 'user-3', full_name: 'Bob Johnson', email: '<EMAIL>', department: 'Sales' }
  ]

  const handleAddMembers = async (groupId) => {
    try {
      setLoading(true)
      
      // Add multiple members at once
      const memberIds = selectedLearners.map(learner => learner.id)
      await GroupsService.addGroupMembers(groupId, memberIds, 'member')
      
      console.log(`Added ${memberIds.length} members to group ${groupId}`)
      setSelectedLearners([])
    } catch (error) {
      console.error('Failed to add members:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Add New Employees to Onboarding Group
      </Typography>
      
      <Autocomplete
        multiple
        options={availableLearners}
        getOptionLabel={(option) => `${option.full_name} (${option.email})`}
        value={selectedLearners}
        onChange={(_, newValue) => setSelectedLearners(newValue)}
        renderInput={(params) => (
          <TextField
            {...params}
            label="Select Learners"
            placeholder="Search by name or email"
          />
        )}
        renderTags={(value, getTagProps) =>
          value.map((option, index) => (
            <Chip
              variant="outlined"
              label={option.full_name}
              {...getTagProps({ index })}
              key={option.id}
            />
          ))
        }
      />
      
      <Button
        variant="contained"
        onClick={() => handleAddMembers(1)} // Group ID 1
        disabled={selectedLearners.length === 0 || loading}
        sx={{ mt: 2 }}
      >
        {loading ? 'Adding...' : `Add ${selectedLearners.length} Member(s)`}
      </Button>
    </Box>
  )
}
```

### 3. Assigning Learning Paths

**Scenario**: Assign mandatory onboarding paths to a new employee group.

```tsx
import { useState } from 'react'
import { GroupsService } from '@/lib/services/groups'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'

function AssignPathsExample() {
  const [selectedPaths, setSelectedPaths] = useState([])
  const [dueDate, setDueDate] = useState(null)
  const [loading, setLoading] = useState(false)

  // Sample learning paths
  const onboardingPaths = [
    { id: 1, title: 'Company Orientation', estimated_duration: 120 },
    { id: 2, title: 'Safety Training', estimated_duration: 60 },
    { id: 3, title: 'IT Security Basics', estimated_duration: 90 },
    { id: 4, title: 'HR Policies', estimated_duration: 45 }
  ]

  const handleAssignPaths = async (groupId) => {
    try {
      setLoading(true)
      
      // Assign multiple paths with same settings
      const assignments = selectedPaths.map(path => ({
        group_id: groupId,
        path_id: path.id,
        assigned_by: 'current-user-id',
        due_date: dueDate?.toISOString(),
        is_mandatory: true,
        settings: {
          auto_assign_new_members: true,
          grace_period_days: 3,
          completion_requirements: {
            minimum_score: 80,
            attempts_allowed: 3
          }
        }
      }))

      // Bulk assign paths
      await Promise.all(
        assignments.map(assignment => 
          GroupsService.assignPathToGroup(
            assignment.group_id, 
            assignment.path_id, 
            assignment
          )
        )
      )
      
      console.log(`Assigned ${assignments.length} paths to group ${groupId}`)
      setSelectedPaths([])
      setDueDate(null)
    } catch (error) {
      console.error('Failed to assign paths:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Assign Onboarding Paths
      </Typography>
      
      <FormControl fullWidth sx={{ mb: 2 }}>
        <InputLabel>Select Learning Paths</InputLabel>
        <Select
          multiple
          value={selectedPaths}
          onChange={(e) => setSelectedPaths(e.target.value)}
          renderValue={(selected) => (
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
              {selected.map((path) => (
                <Chip key={path.id} label={path.title} />
              ))}
            </Box>
          )}
        >
          {onboardingPaths.map((path) => (
            <MenuItem key={path.id} value={path}>
              <Checkbox checked={selectedPaths.some(p => p.id === path.id)} />
              <ListItemText 
                primary={path.title}
                secondary={`${path.estimated_duration} minutes`}
              />
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      <DatePicker
        label="Due Date"
        value={dueDate}
        onChange={setDueDate}
        minDate={new Date()}
        slotProps={{
          textField: { fullWidth: true, sx: { mb: 2 } }
        }}
      />

      <Button
        variant="contained"
        onClick={() => handleAssignPaths(1)}
        disabled={selectedPaths.length === 0 || loading}
        fullWidth
      >
        {loading ? 'Assigning...' : `Assign ${selectedPaths.length} Path(s)`}
      </Button>
    </Box>
  )
}
```

### 4. Tracking Group Progress

**Scenario**: Display real-time progress for a group with visual charts.

```tsx
import { useState, useEffect } from 'react'
import { Line, Doughnut } from 'react-chartjs-2'
import { GroupsService } from '@/lib/services/groups'

function GroupProgressExample({ groupId }) {
  const [progress, setProgress] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadProgress()
    
    // Set up real-time updates
    const subscription = supabase
      .channel('group-progress')
      .on('postgres_changes', 
        { 
          event: 'UPDATE', 
          schema: 'public', 
          table: 'group_progress',
          filter: `group_id=eq.${groupId}`
        }, 
        (payload) => {
          setProgress(prev => 
            prev.map(p => 
              p.id === payload.new.id ? payload.new : p
            )
          )
        }
      )
      .subscribe()

    return () => subscription.unsubscribe()
  }, [groupId])

  const loadProgress = async () => {
    try {
      const response = await GroupsService.getGroupProgress(groupId)
      setProgress(response.data)
    } catch (error) {
      console.error('Failed to load progress:', error)
    } finally {
      setLoading(false)
    }
  }

  // Calculate overall statistics
  const totalMembers = progress[0]?.total_members || 0
  const overallCompletion = progress.reduce((acc, p) => acc + p.completion_rate, 0) / progress.length || 0
  
  // Prepare chart data
  const completionData = {
    labels: progress.map(p => p.path_title || `Path ${p.path_id}`),
    datasets: [{
      label: 'Completion Rate (%)',
      data: progress.map(p => p.completion_rate),
      backgroundColor: 'rgba(54, 162, 235, 0.8)',
      borderColor: 'rgba(54, 162, 235, 1)',
      borderWidth: 1
    }]
  }

  const statusData = {
    labels: ['Completed', 'In Progress', 'Not Started'],
    datasets: [{
      data: [
        progress.reduce((acc, p) => acc + p.completed_members, 0),
        progress.reduce((acc, p) => acc + p.in_progress_members, 0),
        progress.reduce((acc, p) => acc + p.not_started_members, 0)
      ],
      backgroundColor: [
        'rgba(75, 192, 192, 0.8)',
        'rgba(255, 206, 86, 0.8)',
        'rgba(255, 99, 132, 0.8)'
      ]
    }]
  }

  if (loading) {
    return <CircularProgress />
  }

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Group Progress Dashboard
      </Typography>
      
      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" color="primary">
                {totalMembers}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Members
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" color="success.main">
                {overallCompletion.toFixed(1)}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Overall Completion
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" color="info.main">
                {progress.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Assigned Paths
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" color="warning.main">
                {progress.reduce((acc, p) => acc + (p.average_score || 0), 0) / progress.length || 0}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Average Score
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Path Completion Rates
              </Typography>
              <Box sx={{ height: 300 }}>
                <Line 
                  data={completionData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                      y: {
                        beginAtZero: true,
                        max: 100
                      }
                    }
                  }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Status Distribution
              </Typography>
              <Box sx={{ height: 300 }}>
                <Doughnut 
                  data={statusData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false
                  }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Detailed Progress Table */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Detailed Progress
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Learning Path</TableCell>
                  <TableCell align="right">Total</TableCell>
                  <TableCell align="right">Completed</TableCell>
                  <TableCell align="right">In Progress</TableCell>
                  <TableCell align="right">Not Started</TableCell>
                  <TableCell align="right">Completion Rate</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {progress.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>{item.path_title || `Path ${item.path_id}`}</TableCell>
                    <TableCell align="right">{item.total_members}</TableCell>
                    <TableCell align="right">
                      <Chip 
                        label={item.completed_members} 
                        color="success" 
                        size="small" 
                      />
                    </TableCell>
                    <TableCell align="right">
                      <Chip 
                        label={item.in_progress_members} 
                        color="warning" 
                        size="small" 
                      />
                    </TableCell>
                    <TableCell align="right">
                      <Chip 
                        label={item.not_started_members} 
                        color="error" 
                        size="small" 
                      />
                    </TableCell>
                    <TableCell align="right">
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <LinearProgress
                          variant="determinate"
                          value={item.completion_rate}
                          sx={{ width: 60, height: 6 }}
                        />
                        <Typography variant="body2">
                          {item.completion_rate.toFixed(1)}%
                        </Typography>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  )
}
```

### 5. Sending Group Announcements

**Scenario**: Send a multi-channel announcement to all group members.

```tsx
import { useState } from 'react'
import { GroupsService } from '@/lib/services/groups'

function SendAnnouncementExample({ groupId }) {
  const [message, setMessage] = useState('')
  const [subject, setSubject] = useState('')
  const [channels, setChannels] = useState(['in_app'])
  const [priority, setPriority] = useState('normal')
  const [loading, setLoading] = useState(false)

  const handleSendAnnouncement = async () => {
    try {
      setLoading(true)
      
      await GroupsService.sendGroupMessage(groupId, {
        message,
        message_type: 'announcement',
        channels,
        metadata: {
          priority,
          subject,
          read_receipts: true
        }
      })
      
      console.log('Announcement sent successfully')
      
      // Reset form
      setMessage('')
      setSubject('')
      setChannels(['in_app'])
      setPriority('normal')
    } catch (error) {
      console.error('Failed to send announcement:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Send Group Announcement
        </Typography>
        
        <TextField
          fullWidth
          label="Subject"
          value={subject}
          onChange={(e) => setSubject(e.target.value)}
          sx={{ mb: 2 }}
        />
        
        <TextField
          fullWidth
          label="Message"
          multiline
          rows={4}
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Type your announcement here..."
          sx={{ mb: 2 }}
        />
        
        <FormControl fullWidth sx={{ mb: 2 }}>
          <InputLabel>Priority</InputLabel>
          <Select
            value={priority}
            label="Priority"
            onChange={(e) => setPriority(e.target.value)}
          >
            <MenuItem value="normal">Normal</MenuItem>
            <MenuItem value="high">High</MenuItem>
            <MenuItem value="urgent">Urgent</MenuItem>
          </Select>
        </FormControl>
        
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Delivery Channels
          </Typography>
          <FormControlLabel
            control={
              <Checkbox
                checked={channels.includes('in_app')}
                onChange={(e) => {
                  if (e.target.checked) {
                    setChannels([...channels, 'in_app'])
                  } else {
                    setChannels(channels.filter(c => c !== 'in_app'))
                  }
                }}
              />
            }
            label="In-App Notification"
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={channels.includes('email')}
                onChange={(e) => {
                  if (e.target.checked) {
                    setChannels([...channels, 'email'])
                  } else {
                    setChannels(channels.filter(c => c !== 'email'))
                  }
                }}
              />
            }
            label="Email"
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={channels.includes('sms')}
                onChange={(e) => {
                  if (e.target.checked) {
                    setChannels([...channels, 'sms'])
                  } else {
                    setChannels(channels.filter(c => c !== 'sms'))
                  }
                }}
              />
            }
            label="SMS"
          />
        </Box>
        
        <Button
          variant="contained"
          onClick={handleSendAnnouncement}
          disabled={!message.trim() || channels.length === 0 || loading}
          fullWidth
        >
          {loading ? 'Sending...' : 'Send Announcement'}
        </Button>
      </CardContent>
    </Card>
  )
}
```

### 6. Creating Milestones

**Scenario**: Set up milestones for tracking group achievements.

```tsx
import { useState } from 'react'
import { GroupsService } from '@/lib/services/groups'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'

function CreateMilestoneExample({ groupId }) {
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [dueDate, setDueDate] = useState(null)
  const [completionRate, setCompletionRate] = useState(80)
  const [loading, setLoading] = useState(false)

  const handleCreateMilestone = async () => {
    try {
      setLoading(true)
      
      await GroupsService.createMilestone({
        group_id: groupId,
        title,
        description,
        due_date: dueDate.toISOString(),
        completion_criteria: {
          completion_rate: completionRate,
          paths_required: 'all' // or specific path IDs
        }
      })
      
      console.log('Milestone created successfully')
      
      // Reset form
      setTitle('')
      setDescription('')
      setDueDate(null)
      setCompletionRate(80)
    } catch (error) {
      console.error('Failed to create milestone:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Create Group Milestone
        </Typography>
        
        <TextField
          fullWidth
          label="Milestone Title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          sx={{ mb: 2 }}
          placeholder="e.g., Complete Onboarding Training"
        />
        
        <TextField
          fullWidth
          label="Description"
          multiline
          rows={3}
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          sx={{ mb: 2 }}
          placeholder="Describe what needs to be accomplished"
        />
        
        <DatePicker
          label="Due Date"
          value={dueDate}
          onChange={setDueDate}
          minDate={new Date()}
          slotProps={{
            textField: { fullWidth: true, sx: { mb: 2 } }
          }}
        />
        
        <Box sx={{ mb: 2 }}>
          <Typography gutterBottom>
            Required Completion Rate: {completionRate}%
          </Typography>
          <Slider
            value={completionRate}
            onChange={(_, value) => setCompletionRate(value)}
            min={0}
            max={100}
            step={5}
            marks={[
              { value: 0, label: '0%' },
              { value: 50, label: '50%' },
              { value: 100, label: '100%' }
            ]}
            valueLabelDisplay="auto"
          />
        </Box>
        
        <Button
          variant="contained"
          onClick={handleCreateMilestone}
          disabled={!title.trim() || !dueDate || loading}
          fullWidth
        >
          {loading ? 'Creating...' : 'Create Milestone'}
        </Button>
      </CardContent>
    </Card>
  )
}
```

These examples demonstrate the core functionality of the Groups and Batches feature and can be adapted for specific use cases in your application. Each example includes proper error handling, loading states, and follows Material-UI design patterns.
