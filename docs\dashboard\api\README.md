# Dashboard API Documentation

## 📋 Overview

The ZenithLearn AI Learner Dashboard API provides three specialized endpoints that power the innovative features of the dashboard. Built with Next.js API routes and Supabase backend, these APIs deliver real-time data for AI insights, mood tracking, and daily challenges.

## 🏗 API Architecture

### Technology Stack
- **Framework**: Next.js 14 API Routes
- **Database**: Supabase PostgreSQL with RLS
- **Authentication**: Supabase Auth with JWT tokens
- **Validation**: Zod schema validation
- **Error Handling**: Structured error responses
- **Rate Limiting**: Built-in request throttling

### Base URL
```
Production: https://your-domain.vercel.app/api/dashboard
Development: http://localhost:3000/api/dashboard
```

### Authentication
All API endpoints require authentication via Supabase JWT tokens:

```typescript
// Request headers
{
  "Authorization": "Bearer <supabase_jwt_token>",
  "Content-Type": "application/json"
}
```

## 📊 API Endpoints Overview

| Endpoint | Method | Purpose | Features |
|----------|--------|---------|----------|
| `/api/dashboard/insights` | GET, POST | AI-powered learning insights | ML analysis, recommendations |
| `/api/dashboard/mood` | GET, POST | Mood tracking and wellness | Daily check-ins, trends |
| `/api/dashboard/challenges` | GET, POST, PUT | Daily learning challenges | Gamification, progress |

## 🔐 Authentication & Security

### JWT Token Validation
```typescript
import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';

export async function validateAuth(req: NextRequest) {
  const supabase = createServerSupabaseClient({ req });
  const { data: { user }, error } = await supabase.auth.getUser();
  
  if (error || !user) {
    throw new Error('Unauthorized');
  }
  
  return user;
}
```

### Row Level Security (RLS)
All database operations respect RLS policies:
- Users can only access their own data
- Tenant isolation enforced at database level
- Automatic user context injection

### Rate Limiting
API endpoints implement rate limiting:
- **Insights**: 10 requests per minute
- **Mood**: 20 requests per minute  
- **Challenges**: 30 requests per minute

## 📈 Response Format

### Success Response
```typescript
interface APIResponse<T> {
  success: true;
  data: T;
  message?: string;
  timestamp: string;
}
```

### Error Response
```typescript
interface APIError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}
```

### HTTP Status Codes
- **200** - Success
- **201** - Created
- **400** - Bad Request
- **401** - Unauthorized
- **403** - Forbidden
- **404** - Not Found
- **429** - Rate Limited
- **500** - Internal Server Error

## 🧠 AI Insights API

### Endpoint: `/api/dashboard/insights`

#### GET - Retrieve AI Insights
Fetch personalized learning insights and recommendations.

**Request:**
```bash
curl -X GET \
  https://your-domain.vercel.app/api/dashboard/insights \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json"
```

**Response:**
```typescript
{
  "success": true,
  "data": {
    "insights": [
      {
        "id": "insight_123",
        "type": "performance",
        "title": "Learning Efficiency Improvement",
        "content": "Your learning efficiency has increased by 15% this week...",
        "priority": "medium",
        "confidenceScore": 0.85,
        "actionItems": [
          "Continue current study schedule",
          "Focus on JavaScript fundamentals"
        ],
        "createdAt": "2024-01-15T10:30:00Z",
        "isRead": false
      }
    ],
    "summary": {
      "totalInsights": 5,
      "unreadCount": 3,
      "lastGenerated": "2024-01-15T10:30:00Z"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### POST - Generate New Insights
Trigger AI analysis to generate fresh insights.

**Request:**
```bash
curl -X POST \
  https://your-domain.vercel.app/api/dashboard/insights \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "forceRefresh": true,
    "analysisType": "comprehensive"
  }'
```

## 😊 Mood Tracking API

### Endpoint: `/api/dashboard/mood`

#### GET - Retrieve Mood Data
Fetch mood history and trends.

**Query Parameters:**
- `period` - Time period (day, week, month)
- `limit` - Number of entries to return

**Request:**
```bash
curl -X GET \
  "https://your-domain.vercel.app/api/dashboard/mood?period=week&limit=7" \
  -H "Authorization: Bearer <token>"
```

**Response:**
```typescript
{
  "success": true,
  "data": {
    "currentMood": {
      "mood": "happy",
      "energyLevel": 8,
      "timestamp": "2024-01-15T09:00:00Z"
    },
    "moodHistory": [
      {
        "id": "mood_123",
        "mood": "happy",
        "energyLevel": 8,
        "notes": "Great morning workout!",
        "timestamp": "2024-01-15T09:00:00Z"
      }
    ],
    "trends": {
      "averageMood": "happy",
      "averageEnergy": 7.2,
      "moodVariability": "low"
    }
  }
}
```

#### POST - Record Mood Entry
Submit a new mood check-in.

**Request:**
```bash
curl -X POST \
  https://your-domain.vercel.app/api/dashboard/mood \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "mood": "happy",
    "energyLevel": 8,
    "notes": "Feeling great today!"
  }'
```

## 🏆 Daily Challenges API

### Endpoint: `/api/dashboard/challenges`

#### GET - Retrieve Challenges
Fetch active and completed daily challenges.

**Query Parameters:**
- `status` - Filter by status (active, completed, all)
- `date` - Specific date (YYYY-MM-DD)

**Request:**
```bash
curl -X GET \
  "https://your-domain.vercel.app/api/dashboard/challenges?status=active" \
  -H "Authorization: Bearer <token>"
```

**Response:**
```typescript
{
  "success": true,
  "data": {
    "challenges": [
      {
        "id": 1,
        "challengeType": "learning_time",
        "title": "Study for 30 minutes",
        "description": "Spend at least 30 minutes learning today",
        "targetValue": 30,
        "currentValue": 15,
        "pointsReward": 50,
        "status": "active",
        "dueDate": "2024-01-15",
        "metadata": {
          "icon": "timer",
          "color": "#2196F3"
        }
      }
    ],
    "summary": {
      "totalChallenges": 4,
      "completedToday": 1,
      "pointsEarned": 75,
      "streak": 5
    }
  }
}
```

#### POST - Create Challenge
Generate new personalized challenges.

**Request:**
```bash
curl -X POST \
  https://your-domain.vercel.app/api/dashboard/challenges \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "challengeType": "quiz_completion",
    "difficulty": "medium",
    "category": "programming"
  }'
```

#### PUT - Update Challenge Progress
Update challenge completion status.

**Request:**
```bash
curl -X PUT \
  https://your-domain.vercel.app/api/dashboard/challenges \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "challengeId": 1,
    "currentValue": 30,
    "status": "completed"
  }'
```

## 🔧 Error Handling

### Common Error Codes
- `AUTH_REQUIRED` - Missing or invalid authentication
- `VALIDATION_ERROR` - Invalid request data
- `RATE_LIMITED` - Too many requests
- `RESOURCE_NOT_FOUND` - Requested resource doesn't exist
- `INTERNAL_ERROR` - Server-side error

### Error Response Example
```typescript
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid mood value provided",
    "details": {
      "field": "mood",
      "allowedValues": ["excited", "happy", "neutral", "tired", "stressed"]
    }
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 🧪 Testing APIs

### Using cURL
```bash
# Set your token
export TOKEN="your_supabase_jwt_token"

# Test insights endpoint
curl -X GET \
  http://localhost:3000/api/dashboard/insights \
  -H "Authorization: Bearer $TOKEN"
```

### Using Postman
Import the provided Postman collection for comprehensive API testing.

### Integration Tests
```typescript
import { createMocks } from 'node-mocks-http';
import handler from '../pages/api/dashboard/insights';

describe('/api/dashboard/insights', () => {
  it('returns insights for authenticated user', async () => {
    const { req, res } = createMocks({
      method: 'GET',
      headers: {
        authorization: 'Bearer valid_token'
      }
    });

    await handler(req, res);
    
    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toMatchObject({
      success: true,
      data: expect.objectContaining({
        insights: expect.any(Array)
      })
    });
  });
});
```

---

**Next Steps**: Explore individual API endpoint documentation or check the [Database Schema](../database/README.md) for data structure details.
