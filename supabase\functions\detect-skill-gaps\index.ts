import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // Get the authenticated user
    const {
      data: { user },
      error: userError,
    } = await supabaseClient.auth.getUser()

    if (userError || !user) {
      throw new Error('Unauthorized')
    }

    // Get user's tenant_id
    const { data: userData, error: userDataError } = await supabaseClient
      .from('users')
      .select('tenant_id')
      .eq('id', user.id)
      .single()

    if (userDataError || !userData) {
      throw new Error('User data not found')
    }

    const tenantId = userData.tenant_id

    // Get request body
    const { learner_id, competency_id, force_refresh } = await req.json()

    // If specific learner and competency provided, analyze that combination
    if (learner_id && competency_id) {
      const gap = await analyzeSpecificGap(supabaseClient, tenantId, learner_id, competency_id)
      return new Response(
        JSON.stringify({ gap }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Otherwise, run bulk analysis for all learners in the tenant
    const gaps = await analyzeBulkGaps(supabaseClient, tenantId, force_refresh)

    return new Response(
      JSON.stringify({ 
        gaps,
        total_gaps: gaps.length,
        critical_gaps: gaps.filter(g => g.gap_score >= 0.8).length,
        moderate_gaps: gaps.filter(g => g.gap_score >= 0.5 && g.gap_score < 0.8).length
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error in detect-skill-gaps function:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})

async function analyzeSpecificGap(supabaseClient: any, tenantId: number, learnerId: string, competencyId: number) {
  // Get learner's current competency level
  const { data: userCompetency } = await supabaseClient
    .from('user_competencies')
    .select('current_level, target_level')
    .eq('user_id', learnerId)
    .eq('competency_id', competencyId)
    .single()

  // Get competency details
  const { data: competency } = await supabaseClient
    .from('competencies')
    .select('name, category, metadata')
    .eq('id', competencyId)
    .eq('tenant_id', tenantId)
    .single()

  if (!competency) {
    throw new Error('Competency not found')
  }

  // Calculate gap score
  const currentLevel = userCompetency?.current_level || 0
  const targetLevel = userCompetency?.target_level || 3
  const maxLevel = 5

  // Gap score calculation (0 = no gap, 1 = maximum gap)
  const gapScore = Math.max(0, (targetLevel - currentLevel) / maxLevel)
  
  // Confidence score based on data availability
  const confidenceScore = userCompetency ? 0.9 : 0.6

  // Find suggested learning path
  const { data: suggestedPath } = await supabaseClient
    .from('path_competencies')
    .select(`
      learning_paths (
        id,
        title,
        description
      )
    `)
    .eq('competency_id', competencyId)
    .limit(1)
    .single()

  const gap = {
    learner_id: learnerId,
    competency_id: competencyId,
    competency_name: competency.name,
    competency_category: competency.category,
    current_level: currentLevel,
    target_level: targetLevel,
    gap_score: gapScore,
    confidence_score: confidenceScore,
    suggested_path_id: suggestedPath?.learning_paths?.id || null,
    suggested_path_title: suggestedPath?.learning_paths?.title || null,
    status: 'open'
  }

  // Save or update the gap in the database
  await supabaseClient
    .from('skill_gaps')
    .upsert({
      learner_id: learnerId,
      competency_id: competencyId,
      gap_score: gapScore,
      confidence_score: confidenceScore,
      suggested_path_id: suggestedPath?.learning_paths?.id || null,
      status: 'open',
      detected_at: new Date().toISOString()
    })

  return gap
}

async function analyzeBulkGaps(supabaseClient: any, tenantId: number, forceRefresh: boolean = false) {
  // Get all users in the tenant
  const { data: users } = await supabaseClient
    .from('users')
    .select('id, full_name, email')
    .eq('tenant_id', tenantId)
    .eq('is_active', true)

  // Get all competencies in the tenant
  const { data: competencies } = await supabaseClient
    .from('competencies')
    .select('id, name, category')
    .eq('tenant_id', tenantId)
    .eq('status', 'active')

  if (!users || !competencies) {
    return []
  }

  const gaps = []

  // Analyze each user-competency combination
  for (const user of users) {
    for (const competency of competencies) {
      // Skip if gap already exists and not forcing refresh
      if (!forceRefresh) {
        const { data: existingGap } = await supabaseClient
          .from('skill_gaps')
          .select('id')
          .eq('learner_id', user.id)
          .eq('competency_id', competency.id)
          .single()

        if (existingGap) continue
      }

      // Simulate AI analysis (in real implementation, this would use ML models)
      const gapScore = Math.random() * 0.8 + 0.1 // Random gap between 0.1 and 0.9
      const confidenceScore = Math.random() * 0.3 + 0.7 // Random confidence between 0.7 and 1.0

      // Only create gaps above a threshold
      if (gapScore > 0.4) {
        const gap = {
          learner_id: user.id,
          learner_name: user.full_name || user.email,
          competency_id: competency.id,
          competency_name: competency.name,
          competency_category: competency.category,
          gap_score: Math.round(gapScore * 100) / 100,
          confidence_score: Math.round(confidenceScore * 100) / 100,
          status: 'open'
        }

        gaps.push(gap)

        // Save to database
        await supabaseClient
          .from('skill_gaps')
          .upsert({
            learner_id: user.id,
            competency_id: competency.id,
            gap_score: gap.gap_score,
            confidence_score: gap.confidence_score,
            status: 'open',
            detected_at: new Date().toISOString()
          })
      }
    }
  }

  return gaps.sort((a, b) => b.gap_score - a.gap_score)
}
