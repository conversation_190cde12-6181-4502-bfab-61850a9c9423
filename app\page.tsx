'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Box, CircularProgress } from '@mui/material'
import { useAuth } from '@/lib/hooks/useAuth'

export default function Home() {
  const router = useRouter()
  const { user, isLoading } = useAuth()

  useEffect(() => {
    if (!isLoading) {
      if (user) {
        // Redirect based on user role
        if (user.role === 'learner') {
          router.push('/learner/dashboard');
        } else if (user.role === 'admin' || user.role === 'instructor') {
          router.push('/dashboard');
        } else {
          router.push('/learner/dashboard'); // Default to learner dashboard
        }
      } else {
        router.push('/login');
      }
    }
  }, [user, isLoading, router])

  return (
    <Box
      display="flex"
      justifyContent="center"
      alignItems="center"
      minHeight="100vh"
      bgcolor="background.default"
    >
      <CircularProgress size={40} />
    </Box>
  )
}
