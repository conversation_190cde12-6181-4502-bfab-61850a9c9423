# Core Dashboard Components

## 📋 Overview

Core components form the foundation of the ZenithLearn AI Learner Dashboard. These essential components provide the primary functionality that every learner interacts with daily.

## 🎯 PersonalizedWelcome Component

### Purpose
Dynamic welcome section that adapts to time of day and provides personalized greetings with motivational content.

### Key Features
- **Time-based Themes** - Morning, afternoon, and evening color schemes
- **Motivational Quotes** - Rotating inspirational messages
- **User Stats** - Level, badges, and streak display
- **Progress Visualization** - XP and level progression
- **Responsive Design** - Optimized for all screen sizes

### Props Interface
```typescript
interface PersonalizedWelcomeProps {
  user: User | null;
  dashboardData: DashboardData | undefined;
}
```

### Usage Example
```tsx
import PersonalizedWelcome from '@/app/components/dashboard/PersonalizedWelcome';

<PersonalizedWelcome 
  user={user}
  dashboardData={dashboardData}
/>
```

### Customization Options
- **Theme Colors** - Configurable gradient backgrounds
- **Quote Pool** - Customizable motivational messages
- **Stats Display** - Toggleable achievement metrics
- **Animation Speed** - Adjustable transition timing

---

## 📊 EnhancedProgressOverview Component

### Purpose
Comprehensive progress visualization with interactive charts, statistics, and performance analytics.

### Key Features
- **Overall Progress Bar** - Visual learning completion percentage
- **Weekly Activity Chart** - 7-day learning hours visualization
- **Statistics Grid** - Enrolled courses, completed courses, badges, streaks
- **Interactive Elements** - Hover effects and click actions
- **Real-time Updates** - Live data synchronization

### Props Interface
```typescript
interface EnhancedProgressOverviewProps {
  dashboardData: DashboardData | undefined;
}
```

### Data Structure
```typescript
interface DashboardData {
  enrolledCourses: number;
  completedCourses: number;
  totalBadges: number;
  learningStreak: number;
  averageProgress: number;
  weeklyProgress: number[]; // 7 days of hours
}
```

### Usage Example
```tsx
import EnhancedProgressOverview from '@/app/components/dashboard/EnhancedProgressOverview';

<EnhancedProgressOverview 
  dashboardData={dashboardData}
/>
```

---

## 📚 EnrolledCourses Component

### Purpose
Course management interface displaying enrolled courses with progress tracking and quick actions.

### Key Features
- **Course List** - Enrolled courses with thumbnails and metadata
- **Progress Tracking** - Visual progress bars and percentages
- **Status Indicators** - Active, completed, paused course states
- **Quick Actions** - Resume, view, and manage course buttons
- **Filtering** - Active vs completed course views
- **Responsive Cards** - Mobile-optimized course display

### Props Interface
```typescript
interface EnrolledCoursesProps {
  enrollments: CourseEnrollment[];
  onCourseClick: (courseId: string) => void;
  onResumeClick: (courseId: string) => void;
}
```

### Course Data Structure
```typescript
interface CourseEnrollment {
  id: string;
  course_id: string;
  status: 'active' | 'completed' | 'paused';
  progress_percentage: number;
  last_accessed_at: string;
  enrolled_at: string;
  course?: {
    title: string;
    thumbnail_url?: string;
    instructor_name: string;
    duration_hours: number;
    level: 'beginner' | 'intermediate' | 'advanced';
  };
}
```

### Usage Example
```tsx
import EnrolledCourses from '@/app/components/dashboard/EnrolledCourses';

<EnrolledCourses 
  enrollments={enrollments}
  onCourseClick={(courseId) => router.push(`/courses/${courseId}`)}
  onResumeClick={(courseId) => router.push(`/courses/${courseId}/continue`)}
/>
```

---

## 🔔 NotificationCenter Component

### Purpose
Real-time notification system displaying alerts, messages, and important updates.

### Key Features
- **Real-time Updates** - Live notification streaming
- **Categorized Notifications** - System, course, social, achievement types
- **Action Buttons** - Mark as read, dismiss, take action
- **Priority Indicators** - Visual importance levels
- **Timestamp Display** - Relative time formatting
- **Notification History** - Expandable past notifications

### Props Interface
```typescript
interface NotificationCenterProps {
  notifications: Notification[];
  onMarkAsRead: (notificationId: string) => void;
  onDismiss: (notificationId: string) => void;
}
```

### Notification Structure
```typescript
interface Notification {
  id: string;
  type: 'system' | 'course' | 'social' | 'achievement';
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high';
  actionUrl?: string;
  actionText?: string;
}
```

---

## 📅 LearningCalendar Component

### Purpose
Calendar interface for managing learning schedules, deadlines, and upcoming events.

### Key Features
- **Upcoming Deadlines** - Assignment and quiz due dates
- **Event Management** - Study sessions and live classes
- **Priority Indicators** - Color-coded importance levels
- **Quick Actions** - Join session, view details, set reminders
- **Calendar Integration** - Export to external calendars
- **Responsive Timeline** - Mobile-friendly event display

### Props Interface
```typescript
interface LearningCalendarProps {
  upcomingDeadlines: Deadline[];
  events: CalendarEvent[];
  onEventClick: (eventId: string) => void;
}
```

---

## 🔗 QuickLinks Component

### Purpose
Navigation shortcuts providing fast access to frequently used features and pages.

### Key Features
- **Icon-based Navigation** - Visual link identification
- **Customizable Links** - User-configurable shortcuts
- **Hover Effects** - Interactive feedback
- **Responsive Grid** - Adaptive layout for different screens
- **Badge Indicators** - Notification counts on links
- **Keyboard Navigation** - Accessibility support

### Usage Example
```tsx
import QuickLinks from '@/app/components/dashboard/QuickLinks';

<QuickLinks />
```

---

## 🏆 AchievementsBadges Component

### Purpose
Gamification showcase displaying earned badges, achievements, and progress milestones.

### Key Features
- **Badge Gallery** - Visual achievement display
- **Progress Indicators** - Completion percentages
- **Achievement Categories** - Learning, social, milestone badges
- **Unlock Animations** - Celebratory new achievement effects
- **Sharing Options** - Social media integration
- **Achievement Details** - Hover tooltips with descriptions

### Props Interface
```typescript
interface AchievementsBadgesProps {
  achievements: Achievement[];
  recentBadges: Badge[];
  onBadgeClick: (badgeId: string) => void;
}
```

---

## 🤖 AIRecommendations Component

### Purpose
AI-powered course recommendations based on learning patterns and preferences.

### Key Features
- **Personalized Suggestions** - ML-based course recommendations
- **Recommendation Reasons** - Explanation of why courses are suggested
- **Difficulty Matching** - Skill level appropriate suggestions
- **Interest Alignment** - Category and topic matching
- **Quick Enrollment** - One-click course enrollment
- **Feedback Loop** - Like/dislike for improved recommendations

### Props Interface
```typescript
interface AIRecommendationsProps {
  recommendations: CourseRecommendation[];
  onEnroll: (courseId: string) => void;
  onFeedback: (courseId: string, feedback: 'like' | 'dislike') => void;
}
```

---

## 👥 SocialFeatures Component

### Purpose
Social learning interface for peer connections, study groups, and collaborative learning.

### Key Features
- **Study Groups** - Join and manage learning communities
- **Peer Activity** - Recent achievements and progress updates
- **Discussion Forums** - Course-specific conversations
- **Leaderboards** - Anonymous performance comparisons
- **Collaboration Tools** - Shared notes and resources
- **Mentorship Matching** - Connect with experienced learners

---

## ⚙️ CustomizationPanel Component

### Purpose
Dashboard personalization interface allowing users to customize their learning environment.

### Key Features
- **Widget Visibility** - Show/hide dashboard components
- **Layout Options** - Rearrange component positions
- **Theme Selection** - Color scheme preferences
- **Notification Settings** - Customize alert preferences
- **Data Display** - Choose metrics and visualizations
- **Accessibility Options** - Font size, contrast, motion preferences

### Usage Example
```tsx
import CustomizationPanel from '@/app/components/dashboard/CustomizationPanel';

<CustomizationPanel 
  currentSettings={userSettings}
  onSettingsChange={(settings) => updateUserSettings(settings)}
/>
```

---

## 🔧 Component Integration

### Shared Hooks
All core components integrate with shared hooks for consistent data management:

```typescript
// Data fetching
const { dashboardData, isLoading, error } = useDashboardData();

// User authentication
const { user, isAuthenticated } = useAuth();

// Theme management
const theme = useTheme();

// Responsive design
const isMobile = useMediaQuery(theme.breakpoints.down('md'));
```

### Error Handling
Components implement consistent error boundaries and fallback states:

```typescript
if (error) {
  return <ErrorFallback error={error} onRetry={refetch} />;
}

if (isLoading) {
  return <ComponentSkeleton />;
}
```

---

**Next Steps**: Explore [Creative Features](./creative-features.md) for innovative dashboard components or check the [Theming Guide](./theming.md) for customization options.
