'use client'

import React from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Slider,
  Switch,
  FormControlLabel,
  Alert,
  Chip,
  LinearProgress
} from '@mui/material'
import {
  SmartToy as AIIcon,
  Psychology as BrainIcon,
  AutoAwesome as AutoIcon,
  Speed as SpeedIcon
} from '@mui/icons-material'
import { motion } from 'framer-motion'

export default function AISettings() {
  return (
    <Grid container spacing={3}>
      {/* AI Model Configuration */}
      <Grid item xs={12}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={3}>
                <BrainIcon sx={{ mr: 1 }} />
                <Typography variant="h6" fontWeight="bold">
                  AI Model Configuration
                </Typography>
                <Chip label="OpenAI GPT-4" color="primary" size="small" sx={{ ml: 2 }} />
              </Box>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>AI Model</InputLabel>
                    <Select defaultValue="gpt-4" label="AI Model">
                      <MenuItem value="gpt-4">OpenAI GPT-4</MenuItem>
                      <MenuItem value="gpt-3.5-turbo">OpenAI GPT-3.5 Turbo</MenuItem>
                      <MenuItem value="claude-3">Anthropic Claude 3</MenuItem>
                      <MenuItem value="gemini-pro">Google Gemini Pro</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Box>
                    <Typography gutterBottom>
                      Confidence Threshold: 85%
                    </Typography>
                    <Slider
                      defaultValue={0.85}
                      min={0.5}
                      max={0.95}
                      step={0.05}
                      marks
                      valueLabelDisplay="auto"
                      valueLabelFormat={(value) => `${Math.round(value * 100)}%`}
                    />
                  </Box>
                </Grid>
              </Grid>

              <Alert severity="info" sx={{ mt: 2 }}>
                Higher confidence thresholds reduce false positives but may miss some valid suggestions.
              </Alert>
            </CardContent>
          </Card>
        </motion.div>
      </Grid>

      {/* Content AI Features */}
      <Grid item xs={12}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={3}>
                <AutoIcon sx={{ mr: 1 }} />
                <Typography variant="h6" fontWeight="bold">
                  Content AI Features
                </Typography>
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Auto Content Tagging"
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 4 }}>
                    Automatically tag uploaded content with relevant keywords
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Content Summarization"
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 4 }}>
                    Generate automatic summaries for long-form content
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={<Switch />}
                    label="Quiz Generation"
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 4 }}>
                    Create quiz questions from content automatically
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={<Switch />}
                    label="Content Translation"
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 4 }}>
                    Translate content to multiple languages
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </motion.div>
      </Grid>

      {/* Recommendation Engine */}
      <Grid item xs={12}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={3}>
                <AIIcon sx={{ mr: 1 }} />
                <Typography variant="h6" fontWeight="bold">
                  Recommendation Engine
                </Typography>
              </Box>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Personalized Learning Paths"
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 4, mb: 2 }}>
                    Suggest learning paths based on user behavior and preferences
                  </Typography>

                  <Box>
                    <Typography variant="body2" gutterBottom>
                      Recommendation Frequency
                    </Typography>
                    <FormControl size="small" sx={{ minWidth: 120 }}>
                      <Select defaultValue="daily">
                        <MenuItem value="realtime">Real-time</MenuItem>
                        <MenuItem value="daily">Daily</MenuItem>
                        <MenuItem value="weekly">Weekly</MenuItem>
                      </Select>
                    </FormControl>
                  </Box>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Content Recommendations"
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 4, mb: 2 }}>
                    Recommend relevant content based on learning progress
                  </Typography>

                  <Box>
                    <Typography variant="body2" gutterBottom>
                      Max Recommendations per User
                    </Typography>
                    <Slider
                      defaultValue={5}
                      min={1}
                      max={10}
                      step={1}
                      marks
                      valueLabelDisplay="auto"
                    />
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </motion.div>
      </Grid>

      {/* Performance Monitoring */}
      <Grid item xs={12}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={3}>
                <SpeedIcon sx={{ mr: 1 }} />
                <Typography variant="h6" fontWeight="bold">
                  AI Performance Metrics
                </Typography>
              </Box>

              <Grid container spacing={3}>
                <Grid item xs={12} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="primary" fontWeight="bold">
                      98.5%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Accuracy Rate
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={98.5} 
                      sx={{ mt: 1 }}
                      color="primary"
                    />
                  </Box>
                </Grid>

                <Grid item xs={12} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="success.main" fontWeight="bold">
                      1.2s
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Avg Response Time
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={75} 
                      sx={{ mt: 1 }}
                      color="success"
                    />
                  </Box>
                </Grid>

                <Grid item xs={12} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="warning.main" fontWeight="bold">
                      2,847
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      API Calls Today
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={60} 
                      sx={{ mt: 1 }}
                      color="warning"
                    />
                  </Box>
                </Grid>

                <Grid item xs={12} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="info.main" fontWeight="bold">
                      $24.50
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Cost This Month
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={45} 
                      sx={{ mt: 1 }}
                      color="info"
                    />
                  </Box>
                </Grid>
              </Grid>

              <Alert severity="success" sx={{ mt: 3 }}>
                AI services are operating within normal parameters. All systems are healthy.
              </Alert>
            </CardContent>
          </Card>
        </motion.div>
      </Grid>

      {/* Content Moderation */}
      <Grid item xs={12}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                AI Content Moderation
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Automatic Content Screening"
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 4 }}>
                    Screen uploaded content for inappropriate material
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={<Switch />}
                    label="Real-time Chat Moderation"
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 4 }}>
                    Monitor and filter chat messages in real-time
                  </Typography>
                </Grid>
              </Grid>

              <Box mt={2}>
                <Typography variant="body2" gutterBottom>
                  Moderation Sensitivity
                </Typography>
                <Slider
                  defaultValue={0.7}
                  min={0.1}
                  max={1.0}
                  step={0.1}
                  marks={[
                    { value: 0.3, label: 'Lenient' },
                    { value: 0.7, label: 'Balanced' },
                    { value: 0.9, label: 'Strict' }
                  ]}
                  valueLabelDisplay="auto"
                  valueLabelFormat={(value) => `${Math.round(value * 100)}%`}
                />
              </Box>
            </CardContent>
          </Card>
        </motion.div>
      </Grid>
    </Grid>
  )
}
