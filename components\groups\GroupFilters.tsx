'use client'

import { useState } from 'react'
import {
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Autocomplete,
  Button,
  Grid,
  Typography,
  Slider,
  Switch,
  FormControlLabel,
  Collapse,
  IconButton,
} from '@mui/material'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Clear as ClearIcon,
} from '@mui/icons-material'

import { GroupFilters as GroupFiltersType } from '@/lib/types/groups'

interface GroupFiltersProps {
  filters: Partial<GroupFiltersType>
  onFiltersChange: (filters: Partial<GroupFiltersType>) => void
  onReset: () => void
}

const availableTags = [
  'Engineering',
  'Marketing',
  'Sales',
  'HR',
  'Finance',
  'Operations',
  'Product',
  'Design',
  'Customer Support',
  'Leadership',
  'Onboarding',
  'Training',
  'Certification',
  'Compliance',
  'Skills Development',
]

const statusOptions = [
  { value: 'all', label: 'All Statuses' },
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
  { value: 'draft', label: 'Draft' },
  { value: 'archived', label: 'Archived' },
]

export default function GroupFilters({
  filters,
  onFiltersChange,
  onReset,
}: GroupFiltersProps) {
  const [advancedOpen, setAdvancedOpen] = useState(false)

  const handleFilterChange = (key: keyof GroupFiltersType, value: any) => {
    onFiltersChange({ [key]: value })
  }

  const handleDateRangeChange = (field: 'start' | 'end', value: Date | null) => {
    const currentRange = filters.date_range || { start: '', end: '' }
    handleFilterChange('date_range', {
      ...currentRange,
      [field]: value ? value.toISOString().split('T')[0] : '',
    })
  }

  const handleMemberCountChange = (value: number | number[]) => {
    if (Array.isArray(value)) {
      handleFilterChange('member_count_range', {
        min: value[0],
        max: value[1],
      })
    }
  }

  const handleCompletionRateChange = (value: number | number[]) => {
    if (Array.isArray(value)) {
      handleFilterChange('completion_rate_range', {
        min: value[0],
        max: value[1],
      })
    }
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (filters.search) count++
    if (filters.status && filters.status !== 'all') count++
    if (filters.tags && filters.tags.length > 0) count++
    if (filters.created_by) count++
    if (filters.date_range?.start || filters.date_range?.end) count++
    if (filters.has_overdue_assignments) count++
    return count
  }

  const hasActiveFilters = getActiveFiltersCount() > 0

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          Filters
          {hasActiveFilters && (
            <Chip
              label={`${getActiveFiltersCount()} active`}
              size="small"
              color="primary"
              sx={{ ml: 1 }}
            />
          )}
        </Typography>
        <Box>
          <Button
            variant="outlined"
            size="small"
            onClick={() => setAdvancedOpen(!advancedOpen)}
            endIcon={advancedOpen ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            sx={{ mr: 1 }}
          >
            Advanced
          </Button>
          {hasActiveFilters && (
            <Button
              variant="outlined"
              size="small"
              onClick={onReset}
              startIcon={<ClearIcon />}
              color="error"
            >
              Clear All
            </Button>
          )}
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Basic Filters */}
        <Grid item xs={12} md={4}>
          <TextField
            fullWidth
            label="Search Groups"
            placeholder="Search by name or description..."
            value={filters.search || ''}
            onChange={(e) => handleFilterChange('search', e.target.value)}
          />
        </Grid>

        <Grid item xs={12} md={4}>
          <FormControl fullWidth>
            <InputLabel>Status</InputLabel>
            <Select
              value={filters.status || 'all'}
              label="Status"
              onChange={(e) => handleFilterChange('status', e.target.value)}
            >
              {statusOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={4}>
          <Autocomplete
            multiple
            options={availableTags}
            value={filters.tags || []}
            onChange={(_, newValue) => handleFilterChange('tags', newValue)}
            renderTags={(value, getTagProps) =>
              value.map((option, index) => (
                <Chip
                  variant="outlined"
                  label={option}
                  {...getTagProps({ index })}
                  key={option}
                />
              ))
            }
            renderInput={(params) => (
              <TextField
                {...params}
                label="Tags"
                placeholder="Filter by tags"
              />
            )}
          />
        </Grid>

        {/* Advanced Filters */}
        <Grid item xs={12}>
          <Collapse in={advancedOpen}>
            <Box sx={{ pt: 2 }}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Created By"
                    placeholder="Filter by creator"
                    value={filters.created_by || ''}
                    onChange={(e) => handleFilterChange('created_by', e.target.value)}
                  />
                </Grid>

                <Grid item xs={12} md={3}>
                  <DatePicker
                    label="Created From"
                    value={filters.date_range?.start ? new Date(filters.date_range.start) : null}
                    onChange={(value) => handleDateRangeChange('start', value)}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                      },
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={3}>
                  <DatePicker
                    label="Created To"
                    value={filters.date_range?.end ? new Date(filters.date_range.end) : null}
                    onChange={(value) => handleDateRangeChange('end', value)}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                      },
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography gutterBottom>
                    Member Count Range: {filters.member_count_range?.min || 0} - {filters.member_count_range?.max || 1000}
                  </Typography>
                  <Slider
                    value={[
                      filters.member_count_range?.min || 0,
                      filters.member_count_range?.max || 1000,
                    ]}
                    onChange={(_, value) => handleMemberCountChange(value)}
                    valueLabelDisplay="auto"
                    min={0}
                    max={1000}
                    step={10}
                    marks={[
                      { value: 0, label: '0' },
                      { value: 250, label: '250' },
                      { value: 500, label: '500' },
                      { value: 750, label: '750' },
                      { value: 1000, label: '1000+' },
                    ]}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography gutterBottom>
                    Completion Rate: {filters.completion_rate_range?.min || 0}% - {filters.completion_rate_range?.max || 100}%
                  </Typography>
                  <Slider
                    value={[
                      filters.completion_rate_range?.min || 0,
                      filters.completion_rate_range?.max || 100,
                    ]}
                    onChange={(_, value) => handleCompletionRateChange(value)}
                    valueLabelDisplay="auto"
                    min={0}
                    max={100}
                    step={5}
                    marks={[
                      { value: 0, label: '0%' },
                      { value: 25, label: '25%' },
                      { value: 50, label: '50%' },
                      { value: 75, label: '75%' },
                      { value: 100, label: '100%' },
                    ]}
                  />
                </Grid>

                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={filters.has_overdue_assignments || false}
                        onChange={(e) => handleFilterChange('has_overdue_assignments', e.target.checked)}
                      />
                    }
                    label="Has Overdue Assignments"
                  />
                </Grid>
              </Grid>
            </Box>
          </Collapse>
        </Grid>
      </Grid>

      {/* Filter Summary */}
      {hasActiveFilters && (
        <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: 1, borderColor: 'divider' }}>
          <Typography variant="subtitle2" gutterBottom>
            Active Filters:
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {filters.search && (
              <Chip
                label={`Search: "${filters.search}"`}
                onDelete={() => handleFilterChange('search', '')}
                size="small"
              />
            )}
            {filters.status && filters.status !== 'all' && (
              <Chip
                label={`Status: ${filters.status}`}
                onDelete={() => handleFilterChange('status', 'all')}
                size="small"
              />
            )}
            {filters.tags && filters.tags.length > 0 && (
              <Chip
                label={`Tags: ${filters.tags.length} selected`}
                onDelete={() => handleFilterChange('tags', [])}
                size="small"
              />
            )}
            {filters.created_by && (
              <Chip
                label={`Creator: ${filters.created_by}`}
                onDelete={() => handleFilterChange('created_by', '')}
                size="small"
              />
            )}
            {(filters.date_range?.start || filters.date_range?.end) && (
              <Chip
                label="Date Range"
                onDelete={() => handleFilterChange('date_range', { start: '', end: '' })}
                size="small"
              />
            )}
            {filters.has_overdue_assignments && (
              <Chip
                label="Has Overdue"
                onDelete={() => handleFilterChange('has_overdue_assignments', false)}
                size="small"
              />
            )}
          </Box>
        </Box>
      )}
    </Box>
  )
}
