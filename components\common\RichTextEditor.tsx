'use client'

import React from 'react'
import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Link from '@tiptap/extension-link'
import Image from '@tiptap/extension-image'
import Youtube from '@tiptap/extension-youtube'
import {
  Box,
  Paper,
  IconButton,
  Divider,
  Tooltip,
  ButtonGroup,
  Typography
} from '@mui/material'
import {
  FormatBold as BoldIcon,
  FormatItalic as ItalicIcon,
  FormatUnderlined as UnderlineIcon,
  FormatListBulleted as BulletListIcon,
  FormatListNumbered as NumberedListIcon,
  FormatQuote as BlockquoteIcon,
  Code as CodeIcon,
  Link as LinkIcon,
  Image as ImageIcon,
  YouTube as YouTubeIcon,
  Undo as UndoIcon,
  Redo as RedoIcon
} from '@mui/icons-material'

interface RichTextEditorProps {
  content?: string
  onChange?: (content: string) => void
  placeholder?: string
  minHeight?: number
  readOnly?: boolean
}

export default function RichTextEditor({
  content = '',
  onChange,
  placeholder = 'Start typing...',
  minHeight = 200,
  readOnly = false
}: RichTextEditorProps) {
  const editor = useEditor({
    extensions: [
      StarterKit,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 underline',
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto rounded-lg',
        },
      }),
      Youtube.configure({
        width: 640,
        height: 480,
        HTMLAttributes: {
          class: 'rounded-lg',
        },
      }),
    ],
    content,
    editable: !readOnly,
    onUpdate: ({ editor }) => {
      onChange?.(editor.getHTML())
    },
  })

  if (!editor) {
    return null
  }

  const addLink = () => {
    const url = window.prompt('Enter URL:')
    if (url) {
      editor.chain().focus().setLink({ href: url }).run()
    }
  }

  const addImage = () => {
    const url = window.prompt('Enter image URL:')
    if (url) {
      editor.chain().focus().setImage({ src: url }).run()
    }
  }

  const addYouTube = () => {
    const url = window.prompt('Enter YouTube URL:')
    if (url) {
      editor.commands.setYoutubeVideo({
        src: url,
        width: Math.min(640, window.innerWidth - 100),
        height: 480,
      })
    }
  }

  const MenuBar = () => {
    if (readOnly) return null

    return (
      <Box
        sx={{
          p: 1,
          borderBottom: 1,
          borderColor: 'divider',
          display: 'flex',
          flexWrap: 'wrap',
          gap: 1,
          alignItems: 'center'
        }}
      >
        <ButtonGroup size="small" variant="outlined">
          <Tooltip title="Bold">
            <IconButton
              onClick={() => editor.chain().focus().toggleBold().run()}
              color={editor.isActive('bold') ? 'primary' : 'default'}
              size="small"
            >
              <BoldIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Italic">
            <IconButton
              onClick={() => editor.chain().focus().toggleItalic().run()}
              color={editor.isActive('italic') ? 'primary' : 'default'}
              size="small"
            >
              <ItalicIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Code">
            <IconButton
              onClick={() => editor.chain().focus().toggleCode().run()}
              color={editor.isActive('code') ? 'primary' : 'default'}
              size="small"
            >
              <CodeIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </ButtonGroup>

        <Divider orientation="vertical" flexItem />

        <ButtonGroup size="small" variant="outlined">
          <Tooltip title="Bullet List">
            <IconButton
              onClick={() => editor.chain().focus().toggleBulletList().run()}
              color={editor.isActive('bulletList') ? 'primary' : 'default'}
              size="small"
            >
              <BulletListIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Numbered List">
            <IconButton
              onClick={() => editor.chain().focus().toggleOrderedList().run()}
              color={editor.isActive('orderedList') ? 'primary' : 'default'}
              size="small"
            >
              <NumberedListIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Blockquote">
            <IconButton
              onClick={() => editor.chain().focus().toggleBlockquote().run()}
              color={editor.isActive('blockquote') ? 'primary' : 'default'}
              size="small"
            >
              <BlockquoteIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </ButtonGroup>

        <Divider orientation="vertical" flexItem />

        <ButtonGroup size="small" variant="outlined">
          <Tooltip title="Add Link">
            <IconButton onClick={addLink} size="small">
              <LinkIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Add Image">
            <IconButton onClick={addImage} size="small">
              <ImageIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Add YouTube Video">
            <IconButton onClick={addYouTube} size="small">
              <YouTubeIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </ButtonGroup>

        <Divider orientation="vertical" flexItem />

        <ButtonGroup size="small" variant="outlined">
          <Tooltip title="Undo">
            <IconButton
              onClick={() => editor.chain().focus().undo().run()}
              disabled={!editor.can().undo()}
              size="small"
            >
              <UndoIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Redo">
            <IconButton
              onClick={() => editor.chain().focus().redo().run()}
              disabled={!editor.can().redo()}
              size="small"
            >
              <RedoIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </ButtonGroup>
      </Box>
    )
  }

  return (
    <Paper variant="outlined" sx={{ overflow: 'hidden' }}>
      <MenuBar />
      <Box
        sx={{
          minHeight,
          '& .ProseMirror': {
            padding: 2,
            minHeight: minHeight - 60, // Account for toolbar height
            outline: 'none',
            '& p.is-editor-empty:first-of-type::before': {
              content: `"${placeholder}"`,
              float: 'left',
              color: 'text.secondary',
              pointerEvents: 'none',
              height: 0
            },
            '& h1': {
              fontSize: '2rem',
              fontWeight: 'bold',
              marginBottom: '0.5rem'
            },
            '& h2': {
              fontSize: '1.5rem',
              fontWeight: 'bold',
              marginBottom: '0.5rem'
            },
            '& h3': {
              fontSize: '1.25rem',
              fontWeight: 'bold',
              marginBottom: '0.5rem'
            },
            '& ul, & ol': {
              paddingLeft: '1.5rem',
              marginBottom: '1rem'
            },
            '& li': {
              marginBottom: '0.25rem'
            },
            '& blockquote': {
              borderLeft: (theme) => `4px solid ${theme.palette.divider}`,
              paddingLeft: '1rem',
              marginLeft: 0,
              fontStyle: 'italic',
              color: 'text.secondary'
            },
            '& code': {
              backgroundColor: (theme) => theme.palette.mode === 'light' ? '#f5f5f5' : 'rgba(255, 255, 255, 0.1)',
              color: (theme) => theme.palette.mode === 'light' ? '#d32f2f' : '#ff6b6b',
              padding: '0.125rem 0.25rem',
              borderRadius: '0.25rem',
              fontSize: '0.875rem',
              fontFamily: 'monospace'
            },
            '& pre': {
              backgroundColor: (theme) => theme.palette.mode === 'light' ? '#f5f5f5' : 'rgba(255, 255, 255, 0.05)',
              border: (theme) => `1px solid ${theme.palette.divider}`,
              padding: '1rem',
              borderRadius: '0.5rem',
              overflow: 'auto',
              '& code': {
                backgroundColor: 'transparent',
                color: 'inherit',
                padding: 0
              }
            },
            '& a': {
              color: 'primary.main',
              textDecoration: 'underline'
            },
            '& img': {
              maxWidth: '100%',
              height: 'auto',
              borderRadius: '0.5rem',
              marginBottom: '1rem'
            },
            '& .youtube-embed': {
              marginBottom: '1rem'
            }
          }
        }}
      >
        <EditorContent editor={editor} />
      </Box>
      
      {!readOnly && (
        <Box
          sx={{
            p: 1,
            borderTop: 1,
            borderColor: 'divider',
            bgcolor: (theme) => theme.palette.mode === 'light' ? 'grey.50' : 'rgba(255, 255, 255, 0.05)',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}
        >
          <Typography variant="caption" color="text.secondary">
            {editor.storage.characterCount?.characters() || 0} characters
          </Typography>
          
          <Typography variant="caption" color="text.secondary">
            Use Ctrl+B for bold, Ctrl+I for italic
          </Typography>
        </Box>
      )}
    </Paper>
  )
}
