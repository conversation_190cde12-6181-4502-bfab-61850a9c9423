'use client'

import { useState, useEffect } from 'react'
import {
  Box,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Autocomplete,
  Chip,
  Avatar,
  Typography,
  IconButton,
  Menu,
  MenuItem,
  Alert,
  Grid,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
} from '@mui/material'
import {
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  PersonRemove as RemoveIcon,
  Edit as EditIcon,
  Upload as UploadIcon,
  Download as DownloadIcon,
  Search as SearchIcon,
} from '@mui/icons-material'
import { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid'
import { useDropzone } from 'react-dropzone'
import { motion } from 'framer-motion'

import { Group, GroupMember } from '@/lib/types/groups'
import { GroupsService } from '@/lib/services/groups'

interface MemberManagementProps {
  group: Group
  members: GroupMember[]
  onRefresh: () => void
}

interface AddMembersDialogProps {
  open: boolean
  onClose: () => void
  onAdd: (memberIds: string[], role: string) => void
  loading?: boolean
}

function AddMembersDialog({ open, onClose, onAdd, loading }: AddMembersDialogProps) {
  const [selectedLearners, setSelectedLearners] = useState<any[]>([])
  const [role, setRole] = useState('member')
  const [searchTerm, setSearchTerm] = useState('')
  const [availableLearners, setAvailableLearners] = useState<any[]>([])

  useEffect(() => {
    if (open) {
      // TODO: Load available learners from API
      setAvailableLearners([
        { id: '1', full_name: 'John Doe', email: '<EMAIL>', department: 'Engineering' },
        { id: '2', full_name: 'Jane Smith', email: '<EMAIL>', department: 'Marketing' },
        { id: '3', full_name: 'Bob Johnson', email: '<EMAIL>', department: 'Sales' },
      ])
    }
  }, [open])

  const handleAdd = () => {
    const memberIds = selectedLearners.map(learner => learner.id)
    onAdd(memberIds, role)
    setSelectedLearners([])
    setRole('member')
  }

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
    },
    onDrop: (files) => {
      // TODO: Handle CSV/Excel upload
      console.log('Files dropped:', files)
    },
  })

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Add Members to Group</DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={8}>
              <Autocomplete
                multiple
                options={availableLearners}
                getOptionLabel={(option) => `${option.full_name} (${option.email})`}
                value={selectedLearners}
                onChange={(_, newValue) => setSelectedLearners(newValue)}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip
                      variant="outlined"
                      label={option.full_name}
                      {...getTagProps({ index })}
                      key={option.id}
                    />
                  ))
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Select Learners"
                    placeholder="Search and select learners"
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Role</InputLabel>
                <Select
                  value={role}
                  label="Role"
                  onChange={(e) => setRole(e.target.value)}
                >
                  <MenuItem value="member">Member</MenuItem>
                  <MenuItem value="moderator">Moderator</MenuItem>
                  <MenuItem value="admin">Admin</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Box>

        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Or Upload CSV/Excel File
          </Typography>
          <Box
            {...getRootProps()}
            sx={{
              border: 2,
              borderColor: isDragActive ? 'primary.main' : 'grey.300',
              borderStyle: 'dashed',
              borderRadius: 2,
              p: 3,
              textAlign: 'center',
              cursor: 'pointer',
              bgcolor: isDragActive ? 'action.hover' : 'background.paper',
            }}
          >
            <input {...getInputProps()} />
            <UploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
            <Typography variant="body1" color="text.secondary">
              {isDragActive
                ? 'Drop the file here...'
                : 'Drag & drop a CSV or Excel file here, or click to select'}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Supported formats: CSV, XLSX
            </Typography>
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={handleAdd}
          variant="contained"
          disabled={selectedLearners.length === 0 || loading}
        >
          {loading ? 'Adding...' : `Add ${selectedLearners.length} Member(s)`}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default function MemberManagement({ group, members, onRefresh }: MemberManagementProps) {
  const [addDialogOpen, setAddDialogOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState('all')
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [selectedMember, setSelectedMember] = useState<GroupMember | null>(null)

  const handleAddMembers = async (memberIds: string[], role: string) => {
    try {
      setLoading(true)
      await GroupsService.addGroupMembers(group.id, memberIds, role)
      setAddDialogOpen(false)
      onRefresh()
    } catch (error) {
      console.error('Failed to add members:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleRemoveMember = async (memberId: number) => {
    try {
      await GroupsService.removeGroupMember(memberId)
      onRefresh()
    } catch (error) {
      console.error('Failed to remove member:', error)
    }
  }

  const handleUpdateRole = async (memberId: number, newRole: string) => {
    try {
      await GroupsService.updateMemberRole(memberId, newRole)
      onRefresh()
    } catch (error) {
      console.error('Failed to update member role:', error)
    }
  }

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, member: GroupMember) => {
    setAnchorEl(event.currentTarget)
    setSelectedMember(member)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
    setSelectedMember(null)
  }

  const filteredMembers = members.filter((member) => {
    const matchesSearch = !searchTerm || 
      member.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.email?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesRole = roleFilter === 'all' || member.role === roleFilter

    return matchesSearch && matchesRole
  })

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'error'
      case 'moderator':
        return 'warning'
      case 'member':
        return 'default'
      default:
        return 'default'
    }
  }

  const columns: GridColDef[] = [
    {
      field: 'learner',
      headerName: 'Member',
      flex: 1,
      minWidth: 250,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar
            src={params.row.avatar_url}
            alt={params.row.full_name}
            sx={{ width: 40, height: 40 }}
          >
            {params.row.full_name?.[0]}
          </Avatar>
          <Box>
            <Typography variant="body2" fontWeight="medium">
              {params.row.full_name || 'Unknown User'}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {params.row.email}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: 'role',
      headerName: 'Role',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color={getRoleColor(params.value) as any}
          size="small"
          variant="outlined"
        />
      ),
    },
    {
      field: 'department',
      headerName: 'Department',
      width: 150,
      renderCell: (params) => (
        <Typography variant="body2" color="text.secondary">
          {params.row.department || 'N/A'}
        </Typography>
      ),
    },
    {
      field: 'joined_at',
      headerName: 'Joined',
      width: 120,
      renderCell: (params) => (
        <Typography variant="body2" color="text.secondary">
          {new Date(params.value).toLocaleDateString()}
        </Typography>
      ),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 100,
      getActions: (params) => [
        <GridActionsCellItem
          key="edit"
          icon={<EditIcon />}
          label="Edit Role"
          onClick={() => {
            // TODO: Open role edit dialog
          }}
        />,
        <GridActionsCellItem
          key="remove"
          icon={<RemoveIcon />}
          label="Remove"
          onClick={() => handleRemoveMember(params.row.id)}
          sx={{ color: 'error.main' }}
        />,
        <GridActionsCellItem
          key="more"
          icon={<MoreVertIcon />}
          label="More"
          onClick={(event) => handleMenuOpen(event, params.row)}
        />,
      ],
    },
  ]

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          Group Members ({members.length})
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            size="small"
          >
            Export
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setAddDialogOpen(true)}
          >
            Add Members
          </Button>
        </Box>
      </Box>

      {/* Filters */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} md={8}>
          <TextField
            fullWidth
            placeholder="Search members by name or email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
            }}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <FormControl fullWidth>
            <InputLabel>Filter by Role</InputLabel>
            <Select
              value={roleFilter}
              label="Filter by Role"
              onChange={(e) => setRoleFilter(e.target.value)}
            >
              <MenuItem value="all">All Roles</MenuItem>
              <MenuItem value="admin">Admin</MenuItem>
              <MenuItem value="moderator">Moderator</MenuItem>
              <MenuItem value="member">Member</MenuItem>
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      {/* Members List */}
      {filteredMembers.length === 0 ? (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 8 }}>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No members found
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              {searchTerm || roleFilter !== 'all' 
                ? 'Try adjusting your filters'
                : 'Add members to get started'
              }
            </Typography>
            {!searchTerm && roleFilter === 'all' && (
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setAddDialogOpen(true)}
              >
                Add Members
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <Card>
          <DataGrid
            rows={filteredMembers}
            columns={columns}
            autoHeight
            checkboxSelection
            disableRowSelectionOnClick
            pageSizeOptions={[10, 25, 50]}
            initialState={{
              pagination: { paginationModel: { pageSize: 25 } },
            }}
            sx={{
              border: 'none',
              '& .MuiDataGrid-cell:focus': {
                outline: 'none',
              },
            }}
          />
        </Card>
      )}

      {/* Add Members Dialog */}
      <AddMembersDialog
        open={addDialogOpen}
        onClose={() => setAddDialogOpen(false)}
        onAdd={handleAddMembers}
        loading={loading}
      />

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => {
          // TODO: View member profile
          handleMenuClose()
        }}>
          View Profile
        </MenuItem>
        <MenuItem onClick={() => {
          // TODO: View member progress
          handleMenuClose()
        }}>
          View Progress
        </MenuItem>
        <MenuItem onClick={() => {
          // TODO: Send message
          handleMenuClose()
        }}>
          Send Message
        </MenuItem>
        <MenuItem 
          onClick={() => {
            if (selectedMember) {
              handleRemoveMember(selectedMember.id)
            }
            handleMenuClose()
          }}
          sx={{ color: 'error.main' }}
        >
          Remove from Group
        </MenuItem>
      </Menu>
    </Box>
  )
}
