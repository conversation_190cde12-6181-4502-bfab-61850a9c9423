'use client'

import React, { useState } from 'react'
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Switch,
  FormControlLabel,
  Alert,
  IconButton,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider
} from '@mui/material'
import {
  Close as CloseIcon,
  School as SchoolIcon,
  Person as PersonIcon
} from '@mui/icons-material'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'

import { useLearningPaths } from '@/lib/hooks/useLearningPaths'
import { useLearners, useAssignPath } from '@/lib/hooks/useLearners'
import { AssignPathData } from '@/lib/types/learners'

const assignPathSchema = z.object({
  path_id: z.number().min(1, 'Please select a learning path'),
  due_date: z.date().optional(),
  notes: z.string().optional(),
  send_notification: z.boolean().optional()
})

type AssignPathFormData = z.infer<typeof assignPathSchema>

interface AssignPathModalProps {
  open: boolean
  onClose: () => void
  learnerIds: string[]
}

const AssignPathModal: React.FC<AssignPathModalProps> = ({
  open,
  onClose,
  learnerIds
}) => {
  const { data: pathsData } = useLearningPaths()
  const { data: learnersData } = useLearners()
  const assignMutation = useAssignPath()

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting }
  } = useForm<AssignPathFormData>({
    resolver: zodResolver(assignPathSchema),
    defaultValues: {
      path_id: 0,
      due_date: undefined,
      notes: '',
      send_notification: true
    }
  })

  const paths = pathsData?.data || []
  const allLearners = learnersData?.data || []
  const selectedLearners = allLearners.filter(learner => learnerIds.includes(learner.id))

  const onSubmit = async (data: AssignPathFormData) => {
    try {
      const assignmentData: AssignPathData = {
        path_id: data.path_id,
        learner_ids: learnerIds,
        due_date: data.due_date?.toISOString(),
        notes: data.notes,
        send_notification: data.send_notification
      }

      await assignMutation.mutateAsync(assignmentData)
      onClose()
      reset()
    } catch (error) {
      console.error('Error assigning path:', error)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
      reset()
    }
  }

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{ sx: { borderRadius: 2 } }}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6" fontWeight="bold">
            Assign Learning Path
          </Typography>
          <IconButton onClick={handleClose} disabled={isSubmitting}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <form onSubmit={handleSubmit(onSubmit)}>
        <DialogContent dividers>
          {/* Selected Learners */}
          <Box mb={3}>
            <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
              Selected Learners ({selectedLearners.length})
            </Typography>
            <Box
              sx={{
                maxHeight: 200,
                overflow: 'auto',
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 1
              }}
            >
              <List dense>
                {selectedLearners.map((learner, index) => (
                  <React.Fragment key={learner.id}>
                    <ListItem>
                      <ListItemAvatar>
                        <Avatar src={learner.avatar_url} sx={{ width: 32, height: 32 }}>
                          <PersonIcon />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={learner.full_name}
                        secondary={learner.email}
                      />
                    </ListItem>
                    {index < selectedLearners.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </Box>
          </Box>

          {/* Learning Path Selection */}
          <Box mb={3}>
            <Controller
              name="path_id"
              control={control}
              render={({ field }) => (
                <FormControl fullWidth error={!!errors.path_id}>
                  <InputLabel>Learning Path</InputLabel>
                  <Select
                    {...field}
                    label="Learning Path"
                    renderValue={(value) => {
                      const path = paths.find(p => p.id === value)
                      return path ? (
                        <Box display="flex" alignItems="center" gap={1}>
                          <SchoolIcon fontSize="small" />
                          {path.title}
                        </Box>
                      ) : ''
                    }}
                  >
                    {paths.map((path) => (
                      <MenuItem key={path.id} value={path.id}>
                        <Box>
                          <Typography variant="body1">{path.title}</Typography>
                          <Box display="flex" gap={1} mt={0.5}>
                            <Chip
                              label={path.difficulty_level}
                              size="small"
                              variant="outlined"
                            />
                            <Chip
                              label={`${path.estimated_duration} weeks`}
                              size="small"
                              variant="outlined"
                            />
                          </Box>
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.path_id && (
                    <Typography variant="caption" color="error">
                      {errors.path_id.message}
                    </Typography>
                  )}
                </FormControl>
              )}
            />
          </Box>

          {/* Due Date */}
          <Box mb={3}>
            <Controller
              name="due_date"
              control={control}
              render={({ field }) => (
                <DatePicker
                  label="Due Date (Optional)"
                  value={field.value || null}
                  onChange={field.onChange}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      helperText: 'Leave empty for no due date'
                    }
                  }}
                />
              )}
            />
          </Box>

          {/* Notes */}
          <Box mb={3}>
            <Controller
              name="notes"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label="Notes (Optional)"
                  multiline
                  rows={3}
                  placeholder="Add any additional notes or instructions for the learners..."
                />
              )}
            />
          </Box>

          {/* Send Notification */}
          <Box mb={2}>
            <Controller
              name="send_notification"
              control={control}
              render={({ field }) => (
                <FormControlLabel
                  control={
                    <Switch
                      checked={field.value}
                      onChange={field.onChange}
                    />
                  }
                  label="Send notification to learners"
                />
              )}
            />
          </Box>

          {assignMutation.error && (
            <Alert severity="error">
              {(assignMutation.error as any)?.message || 'Failed to assign learning path'}
            </Alert>
          )}
        </DialogContent>

        <DialogActions sx={{ p: 3 }}>
          <Button onClick={handleClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Assigning...' : 'Assign Path'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  )
}

export default AssignPathModal
