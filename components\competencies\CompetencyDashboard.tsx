'use client'

import { useState, useMemo, useCallback } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Alert,
  Skeleton,
  useTheme,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  CircularProgress,
  Avatar,
  Stack,
  Fade,
  Zoom,
  Collapse,
  Checkbox,
} from '@mui/material'
import {
  Search as SearchIcon,
  MoreVert as MoreIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Map as MapIcon,
  TrendingUp as TrendingIcon,
  School as SchoolIcon,
  Psychology as PsychologyIcon,
  CheckCircle as ActiveIcon,
  Cancel as InactiveIcon,
  Notes as DraftIcon,
  KeyboardArrowDown as ExpandIcon,
  KeyboardArrowUp as CollapseIcon,
  Launch as LaunchIcon,
} from '@mui/icons-material'
import { DataGrid, GridColDef, GridRowSelectionModel, GridActionsCellItem, GridRowParams } from '@mui/x-data-grid'
import { motion, AnimatePresence } from 'framer-motion'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import toast from 'react-hot-toast'

import { supabase } from '@/lib/supabase'
import { useAuthStore } from '@/lib/store'

interface Competency {
  id: number
  name: string
  description: string
  category: string
  status: 'active' | 'inactive' | 'draft'
  tags: string[]
  created_at: string
  updated_at: string
  is_public: boolean
  metadata: any
  created_by: string
  mapped_paths_count?: number
  learners_count?: number
  completion_rate?: number
  trend_direction?: 'up' | 'down' | 'stable'
  trend_percentage?: number
}

interface CompetencyDashboardProps {
  onEditCompetency: (competency: Competency) => void
}

interface DetailDialogProps {
  open: boolean
  competency: Competency | null
  onClose: () => void
  onEdit: (competency: Competency) => void
}

interface MappingDialogProps {
  open: boolean
  competency: Competency | null
  onClose: () => void
}

interface TrendsDialogProps {
  open: boolean
  competency: Competency | null
  onClose: () => void
}

export default function CompetencyDashboard({ onEditCompetency }: CompetencyDashboardProps) {
  const theme = useTheme()
  const { user } = useAuthStore()
  const queryClient = useQueryClient()

  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [selectedStatus, setSelectedStatus] = useState<string>('')
  const [selectedRows, setSelectedRows] = useState<GridRowSelectionModel>([])
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [selectedCompetency, setSelectedCompetency] = useState<Competency | null>(null)

  // Dialog states
  const [detailDialogOpen, setDetailDialogOpen] = useState(false)
  const [mappingDialogOpen, setMappingDialogOpen] = useState(false)
  const [trendsDialogOpen, setTrendsDialogOpen] = useState(false)
  const [expandedRows, setExpandedRows] = useState<Set<string | number>>(new Set())

  // Loading states
  const [isDeleting, setIsDeleting] = useState(false)
  const [isBulkDeleting, setIsBulkDeleting] = useState(false)

  // Fetch competencies with enhanced data
  const { data: competencies = [], isLoading, error, refetch } = useQuery({
    queryKey: ['competencies', user?.tenant_id, searchTerm, selectedCategory, selectedStatus],
    queryFn: async (): Promise<Competency[]> => {
      if (!user?.tenant_id) return []

      let query = supabase
        .from('competencies')
        .select(`
          *,
          path_competencies(count),
          user_competencies(count),
          creator:users!competencies_created_by_fkey(full_name, email)
        `)
        .eq('tenant_id', user.tenant_id)
        .order('updated_at', { ascending: false })

      if (searchTerm) {
        query = query.or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%,category.ilike.%${searchTerm}%`)
      }

      if (selectedCategory) {
        query = query.eq('category', selectedCategory)
      }

      if (selectedStatus) {
        query = query.eq('status', selectedStatus)
      }

      const { data, error } = await query

      if (error) throw error

      return data?.map(comp => {
        // Calculate mock completion rate and trends (in real app, this would come from analytics)
        const completionRate = Math.round((Math.random() * 40 + 60) * 100) / 100
        const trendPercentage = Math.round((Math.random() * 20 - 10) * 100) / 100
        const trendDirection = trendPercentage > 5 ? 'up' : trendPercentage < -5 ? 'down' : 'stable'

        return {
          ...comp,
          mapped_paths_count: comp.path_competencies?.length || 0,
          learners_count: comp.user_competencies?.length || 0,
          completion_rate: completionRate,
          trend_direction: trendDirection,
          trend_percentage: Math.abs(trendPercentage),
          created_by: comp.creator?.full_name || comp.creator?.email || 'Unknown User',
        }
      }) || []
    },
    enabled: !!user?.tenant_id,
    refetchInterval: 30000, // Refresh every 30 seconds
  })

  // Fetch categories for filtering
  const { data: categories = [] } = useQuery({
    queryKey: ['competency-categories', user?.tenant_id],
    queryFn: async () => {
      if (!user?.tenant_id) return []

      const { data } = await supabase
        .from('competencies')
        .select('category')
        .eq('tenant_id', user.tenant_id)
        .not('category', 'is', null)

      const uniqueCategories = [...new Set(data?.map(item => item.category).filter(Boolean))]
      return uniqueCategories
    },
    enabled: !!user?.tenant_id,
  })

  // Delete competency mutation
  const deleteMutation = useMutation({
    mutationFn: async (competencyId: number) => {
      const { error } = await supabase
        .from('competencies')
        .delete()
        .eq('id', competencyId)
        .eq('tenant_id', user?.tenant_id)

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['competencies'] })
      toast.success('Competency deleted successfully')
    },
    onError: (error) => {
      console.error('Delete error:', error)
      toast.error('Failed to delete competency')
    },
  })

  // Bulk delete mutation
  const bulkDeleteMutation = useMutation({
    mutationFn: async (competencyIds: number[]) => {
      const { error } = await supabase
        .from('competencies')
        .delete()
        .in('id', competencyIds)
        .eq('tenant_id', user?.tenant_id)

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['competencies'] })
      setSelectedRows([])
      toast.success('Competencies deleted successfully')
    },
    onError: (error) => {
      console.error('Bulk delete error:', error)
      toast.error('Failed to delete competencies')
    },
  })

  // Enhanced event handlers
  const handleMenuOpen = useCallback((event: React.MouseEvent<HTMLElement>, competency: Competency) => {
    event.stopPropagation()
    setAnchorEl(event.currentTarget)
    setSelectedCompetency(competency)
  }, [])

  const handleMenuClose = useCallback(() => {
    setAnchorEl(null)
    setSelectedCompetency(null)
  }, [])

  const handleRowClick = useCallback((params: GridRowParams) => {
    setSelectedCompetency(params.row as Competency)
    setDetailDialogOpen(true)
  }, [])

  const handleEdit = useCallback(() => {
    if (selectedCompetency) {
      onEditCompetency(selectedCompetency)
    }
    handleMenuClose()
  }, [selectedCompetency, onEditCompetency, handleMenuClose])

  const handleViewDetails = useCallback(() => {
    if (selectedCompetency) {
      setDetailDialogOpen(true)
    }
    handleMenuClose()
  }, [selectedCompetency, handleMenuClose])

  const handleMapToPaths = useCallback(() => {
    if (selectedCompetency) {
      setMappingDialogOpen(true)
    }
    handleMenuClose()
  }, [selectedCompetency, handleMenuClose])

  const handleViewTrends = useCallback(() => {
    if (selectedCompetency) {
      setTrendsDialogOpen(true)
    }
    handleMenuClose()
  }, [selectedCompetency, handleMenuClose])

  const handleDelete = useCallback(async () => {
    if (selectedCompetency) {
      if (window.confirm(`Are you sure you want to delete "${selectedCompetency.name}"?`)) {
        setIsDeleting(true)
        try {
          await deleteMutation.mutateAsync(selectedCompetency.id)
        } finally {
          setIsDeleting(false)
        }
      }
    }
    handleMenuClose()
  }, [selectedCompetency, deleteMutation, handleMenuClose])

  const handleBulkDelete = useCallback(async () => {
    if (selectedRows.length > 0) {
      if (window.confirm(`Are you sure you want to delete ${selectedRows.length} competencies?`)) {
        setIsBulkDeleting(true)
        try {
          await bulkDeleteMutation.mutateAsync(selectedRows as number[])
        } finally {
          setIsBulkDeleting(false)
        }
      }
    }
  }, [selectedRows, bulkDeleteMutation])

  const toggleRowExpansion = useCallback((competencyId: string | number) => {
    setExpandedRows(prev => {
      const newSet = new Set(prev)
      if (newSet.has(competencyId)) {
        newSet.delete(competencyId)
      } else {
        newSet.add(competencyId)
      }
      return newSet
    })
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success'
      case 'inactive': return 'error'
      case 'draft': return 'warning'
      default: return 'default'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <ActiveIcon sx={{ fontSize: 16 }} />
      case 'inactive': return <InactiveIcon sx={{ fontSize: 16 }} />
      case 'draft': return <DraftIcon sx={{ fontSize: 16 }} />
      default: return null
    }
  }

  const getTrendIcon = (direction: string) => {
    switch (direction) {
      case 'up': return <TrendingIcon sx={{ fontSize: 16, color: 'success.main' }} />
      case 'down': return <TrendingIcon sx={{ fontSize: 16, color: 'error.main', transform: 'rotate(180deg)' }} />
      default: return <TrendingIcon sx={{ fontSize: 16, color: 'text.secondary', transform: 'rotate(90deg)' }} />
    }
  }

  const columns: GridColDef[] = [
    {
      field: 'expand',
      headerName: '',
      width: 50,
      sortable: false,
      renderCell: (params) => (
        <Tooltip title={expandedRows.has(params.row.id) ? 'Collapse' : 'Expand details'}>
          <IconButton
            size="small"
            onClick={(e) => {
              e.stopPropagation()
              toggleRowExpansion(params.row.id)
            }}
          >
            {expandedRows.has(params.row.id) ? <CollapseIcon /> : <ExpandIcon />}
          </IconButton>
        </Tooltip>
      ),
    },
    {
      field: 'name',
      headerName: 'Competency',
      flex: 1,
      minWidth: 250,
      renderCell: (params) => (
        <Box sx={{ py: 1, width: '100%' }}>
          <Box display="flex" alignItems="center" gap={1} mb={0.5}>
            <Avatar sx={{ width: 24, height: 24, bgcolor: 'primary.main' }}>
              <PsychologyIcon sx={{ fontSize: 14 }} />
            </Avatar>
            <Tooltip title={params.value}>
              <Typography
                variant="subtitle2"
                sx={{
                  fontWeight: 600,
                  cursor: 'pointer',
                  '&:hover': { color: 'primary.main' }
                }}
                noWrap
              >
                {params.value}
              </Typography>
            </Tooltip>
          </Box>

          <Tooltip title={params.row.description || 'No description'}>
            <Typography
              variant="caption"
              color="textSecondary"
              sx={{
                display: '-webkit-box',
                WebkitLineClamp: expandedRows.has(params.row.id) ? 'none' : 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
                lineHeight: 1.2,
              }}
            >
              {params.row.description || 'No description available'}
            </Typography>
          </Tooltip>

          {expandedRows.has(params.row.id) && (
            <Collapse in={expandedRows.has(params.row.id)}>
              <Box mt={1}>
                <Typography variant="caption" color="textSecondary">
                  Created by: {params.row.created_by}
                </Typography>
                {params.row.tags && params.row.tags.length > 0 && (
                  <Box display="flex" gap={0.5} mt={0.5} flexWrap="wrap">
                    {params.row.tags.slice(0, 3).map((tag: string, index: number) => (
                      <Chip
                        key={index}
                        label={tag}
                        size="small"
                        variant="outlined"
                        sx={{ fontSize: '0.7rem', height: 20 }}
                      />
                    ))}
                    {params.row.tags.length > 3 && (
                      <Chip
                        label={`+${params.row.tags.length - 3}`}
                        size="small"
                        variant="outlined"
                        sx={{ fontSize: '0.7rem', height: 20 }}
                      />
                    )}
                  </Box>
                )}
              </Box>
            </Collapse>
          )}
        </Box>
      ),
    },
    {
      field: 'category',
      headerName: 'Category',
      width: 150,
      renderCell: (params) => (
        params.value ? (
          <Tooltip title={`Category: ${params.value}`}>
            <Chip
              label={params.value}
              size="small"
              variant="outlined"
              color="primary"
              sx={{ maxWidth: '100%' }}
            />
          </Tooltip>
        ) : (
          <Typography variant="body2" color="textSecondary">
            Uncategorized
          </Typography>
        )
      ),
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: (params) => (
        <Box display="flex" alignItems="center" gap={0.5}>
          {getStatusIcon(params.value)}
          <Chip
            label={params.value}
            size="small"
            color={getStatusColor(params.value) as any}
            variant="filled"
            sx={{ textTransform: 'capitalize' }}
          />
        </Box>
      ),
    },
    {
      field: 'metrics',
      headerName: 'Metrics',
      width: 140,
      sortable: false,
      renderCell: (params) => (
        <Box>
          <Box display="flex" alignItems="center" gap={1} mb={0.5}>
            <SchoolIcon sx={{ fontSize: 14, color: 'text.secondary' }} />
            <Typography variant="caption">
              {params.row.mapped_paths_count || 0} paths
            </Typography>
          </Box>
          <Box display="flex" alignItems="center" gap={1}>
            <PsychologyIcon sx={{ fontSize: 14, color: 'text.secondary' }} />
            <Typography variant="caption">
              {params.row.learners_count || 0} learners
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: 'completion_rate',
      headerName: 'Progress',
      width: 120,
      renderCell: (params) => (
        <Box sx={{ width: '100%' }}>
          <Box display="flex" alignItems="center" gap={1} mb={0.5}>
            <Typography variant="caption" fontWeight="medium">
              {params.value}%
            </Typography>
            {getTrendIcon(params.row.trend_direction)}
          </Box>
          <LinearProgress
            variant="determinate"
            value={params.value}
            sx={{
              height: 4,
              borderRadius: 2,
              backgroundColor: 'grey.200',
              '& .MuiLinearProgress-bar': {
                borderRadius: 2,
                backgroundColor: params.value >= 80 ? 'success.main' :
                                params.value >= 60 ? 'warning.main' : 'error.main'
              }
            }}
          />
          {params.row.trend_percentage > 0 && (
            <Typography variant="caption" color="textSecondary">
              {params.row.trend_direction === 'up' ? '+' : params.row.trend_direction === 'down' ? '-' : ''}
              {params.row.trend_percentage}%
            </Typography>
          )}
        </Box>
      ),
    },
    {
      field: 'updated_at',
      headerName: 'Last Updated',
      width: 130,
      renderCell: (params) => (
        <Tooltip title={new Date(params.value).toLocaleString()}>
          <Typography variant="body2">
            {new Date(params.value).toLocaleDateString()}
          </Typography>
        </Tooltip>
      ),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 80,
      getActions: (params) => [
        <GridActionsCellItem
          key="menu"
          icon={
            <Tooltip title="More actions">
              <MoreIcon />
            </Tooltip>
          }
          label="More"
          onClick={(e) => handleMenuOpen(e as any, params.row)}
        />,
      ],
    },
  ]

  const filteredCompetencies = useMemo(() => {
    return competencies.filter(comp => {
      const matchesSearch = !searchTerm || 
        comp.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        comp.description?.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesCategory = !selectedCategory || comp.category === selectedCategory
      const matchesStatus = !selectedStatus || comp.status === selectedStatus
      
      return matchesSearch && matchesCategory && matchesStatus
    })
  }, [competencies, searchTerm, selectedCategory, selectedStatus])

  if (error) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Alert
          severity="error"
          action={
            <Button color="inherit" size="small" onClick={() => refetch()}>
              Retry
            </Button>
          }
        >
          Failed to load competencies. Please try again.
        </Alert>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card elevation={2}>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Box>
              <Typography variant="h6" fontWeight="bold">
                Competencies Overview
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {isLoading ? 'Loading...' : `${filteredCompetencies.length} competencies found`}
              </Typography>
            </Box>

            <AnimatePresence>
              {selectedRows.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.2 }}
                >
                  <Button
                    variant="outlined"
                    color="error"
                    size="small"
                    onClick={handleBulkDelete}
                    disabled={isBulkDeleting}
                    startIcon={isDeleting ? <CircularProgress size={16} /> : <DeleteIcon />}
                  >
                    Delete Selected ({selectedRows.length})
                  </Button>
                </motion.div>
              )}
            </AnimatePresence>
          </Box>

        {/* Filters */}
        <Box display="flex" gap={2} mb={3} flexWrap="wrap">
          <TextField
            placeholder="Search competencies..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            size="small"
            InputProps={{
              startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
            }}
            sx={{ minWidth: 250 }}
          />
          
          <TextField
            select
            label="Category"
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            size="small"
            sx={{ minWidth: 150 }}
          >
            <MenuItem value="">All Categories</MenuItem>
            {categories.map((category) => (
              <MenuItem key={category} value={category}>
                {category}
              </MenuItem>
            ))}
          </TextField>

          <TextField
            select
            label="Status"
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            size="small"
            sx={{ minWidth: 120 }}
          >
            <MenuItem value="">All Status</MenuItem>
            <MenuItem value="active">Active</MenuItem>
            <MenuItem value="inactive">Inactive</MenuItem>
            <MenuItem value="draft">Draft</MenuItem>
          </TextField>
        </Box>

        {/* Enhanced Data Grid */}
        <Box sx={{ height: 700, width: '100%' }}>
          <DataGrid
            rows={filteredCompetencies}
            columns={columns}
            loading={isLoading}
            checkboxSelection
            disableRowSelectionOnClick
            rowSelectionModel={selectedRows}
            onRowSelectionModelChange={setSelectedRows}
            onRowClick={handleRowClick}
            pageSizeOptions={[10, 25, 50, 100]}
            initialState={{
              pagination: { paginationModel: { pageSize: 25 } },
            }}
            getRowHeight={(params) => {
              return expandedRows.has(params.id) ? 'auto' : 80
            }}
            sx={{
              '& .MuiDataGrid-root': {
                border: 'none',
              },
              '& .MuiDataGrid-cell': {
                borderBottom: `1px solid ${theme.palette.divider}`,
                '&:focus': {
                  outline: 'none',
                },
              },
              '& .MuiDataGrid-row': {
                cursor: 'pointer',
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  backgroundColor: theme.palette.action.hover,
                  transform: 'translateY(-1px)',
                  boxShadow: theme.shadows[2],
                },
                '&.Mui-selected': {
                  backgroundColor: `${theme.palette.primary.main}08`,
                  '&:hover': {
                    backgroundColor: `${theme.palette.primary.main}12`,
                  },
                },
              },
              '& .MuiDataGrid-columnHeaders': {
                backgroundColor: theme.palette.grey[50],
                borderBottom: `2px solid ${theme.palette.primary.main}`,
                '& .MuiDataGrid-columnHeader': {
                  fontWeight: 600,
                  fontSize: '0.875rem',
                },
              },
              '& .MuiDataGrid-footerContainer': {
                borderTop: `1px solid ${theme.palette.divider}`,
                backgroundColor: theme.palette.grey[50],
              },
              '& .MuiDataGrid-virtualScroller': {
                '&::-webkit-scrollbar': {
                  width: 8,
                  height: 8,
                },
                '&::-webkit-scrollbar-track': {
                  backgroundColor: theme.palette.grey[100],
                  borderRadius: 4,
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: theme.palette.grey[400],
                  borderRadius: 4,
                  '&:hover': {
                    backgroundColor: theme.palette.grey[600],
                  },
                },
              },
            }}
            slots={{
              noRowsOverlay: () => (
                <Box
                  display="flex"
                  flexDirection="column"
                  alignItems="center"
                  justifyContent="center"
                  height="100%"
                  gap={2}
                >
                  <PsychologyIcon sx={{ fontSize: 64, color: 'text.secondary' }} />
                  <Typography variant="h6" color="textSecondary">
                    No competencies found
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Try adjusting your search criteria or create a new competency
                  </Typography>
                </Box>
              ),
              loadingOverlay: () => (
                <Box
                  display="flex"
                  flexDirection="column"
                  alignItems="center"
                  justifyContent="center"
                  height="100%"
                  gap={2}
                >
                  <LinearProgress sx={{ width: 200 }} />
                  <Typography variant="body2" color="textSecondary">
                    Loading competencies...
                  </Typography>
                </Box>
              ),
            }}
          />
        </Box>

        {/* Enhanced Action Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          PaperProps={{
            elevation: 8,
            sx: {
              mt: 1,
              minWidth: 200,
              '& .MuiMenuItem-root': {
                px: 2,
                py: 1,
                borderRadius: 1,
                mx: 1,
                my: 0.5,
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  backgroundColor: 'primary.light',
                  color: 'primary.contrastText',
                  transform: 'translateX(4px)',
                },
              },
            },
          }}
        >
          <MenuItem onClick={handleViewDetails}>
            <ViewIcon sx={{ mr: 2, fontSize: 20 }} />
            <Box>
              <Typography variant="body2" fontWeight="medium">
                View Details
              </Typography>
              <Typography variant="caption" color="textSecondary">
                See full competency information
              </Typography>
            </Box>
          </MenuItem>

          <MenuItem onClick={handleEdit}>
            <EditIcon sx={{ mr: 2, fontSize: 20 }} />
            <Box>
              <Typography variant="body2" fontWeight="medium">
                Edit
              </Typography>
              <Typography variant="caption" color="textSecondary">
                Modify competency details
              </Typography>
            </Box>
          </MenuItem>

          <MenuItem onClick={handleMapToPaths}>
            <MapIcon sx={{ mr: 2, fontSize: 20 }} />
            <Box>
              <Typography variant="body2" fontWeight="medium">
                Map to Paths
              </Typography>
              <Typography variant="caption" color="textSecondary">
                Link to learning paths
              </Typography>
            </Box>
          </MenuItem>

          <MenuItem onClick={handleViewTrends}>
            <TrendingIcon sx={{ mr: 2, fontSize: 20 }} />
            <Box>
              <Typography variant="body2" fontWeight="medium">
                View Trends
              </Typography>
              <Typography variant="caption" color="textSecondary">
                Analytics and insights
              </Typography>
            </Box>
          </MenuItem>

          <MenuItem
            onClick={handleDelete}
            sx={{
              color: 'error.main',
              '&:hover': {
                backgroundColor: 'error.light',
                color: 'error.contrastText',
              }
            }}
            disabled={isDeleting}
          >
            <DeleteIcon sx={{ mr: 2, fontSize: 20 }} />
            <Box>
              <Typography variant="body2" fontWeight="medium">
                Delete
              </Typography>
              <Typography variant="caption" color="textSecondary">
                Remove competency permanently
              </Typography>
            </Box>
          </MenuItem>
        </Menu>

        {/* Detail Dialog */}
        <CompetencyDetailDialog
          open={detailDialogOpen}
          competency={selectedCompetency}
          onClose={() => setDetailDialogOpen(false)}
          onEdit={onEditCompetency}
        />

        {/* Mapping Dialog */}
        <CompetencyMappingDialog
          open={mappingDialogOpen}
          competency={selectedCompetency}
          onClose={() => setMappingDialogOpen(false)}
        />

        {/* Trends Dialog */}
        <CompetencyTrendsDialog
          open={trendsDialogOpen}
          competency={selectedCompetency}
          onClose={() => setTrendsDialogOpen(false)}
        />
      </CardContent>
    </Card>
    </motion.div>
  )
}

// Competency Detail Dialog Component
function CompetencyDetailDialog({ open, competency, onClose, onEdit }: DetailDialogProps) {
  if (!competency) return null

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          minHeight: '60vh',
        }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box display="flex" alignItems="center" gap={2}>
          <Avatar sx={{ bgcolor: 'primary.main' }}>
            <PsychologyIcon />
          </Avatar>
          <Box flex={1}>
            <Typography variant="h6" fontWeight="bold">
              {competency.name}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              {competency.category || 'Uncategorized'}
            </Typography>
          </Box>
          <Chip
            label={competency.status}
            color={competency.status === 'active' ? 'success' : competency.status === 'draft' ? 'warning' : 'error'}
            variant="filled"
          />
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        <Stack spacing={3}>
          {/* Description */}
          <Box>
            <Typography variant="subtitle2" gutterBottom fontWeight="bold">
              Description
            </Typography>
            <Typography variant="body2" color="textSecondary" sx={{ lineHeight: 1.6 }}>
              {competency.description || 'No description available'}
            </Typography>
          </Box>

          {/* Tags */}
          {competency.tags && competency.tags.length > 0 && (
            <Box>
              <Typography variant="subtitle2" gutterBottom fontWeight="bold">
                Tags
              </Typography>
              <Box display="flex" gap={1} flexWrap="wrap">
                {competency.tags.map((tag, index) => (
                  <Chip
                    key={index}
                    label={tag}
                    size="small"
                    variant="outlined"
                    color="primary"
                  />
                ))}
              </Box>
            </Box>
          )}

          {/* Metrics */}
          <Box>
            <Typography variant="subtitle2" gutterBottom fontWeight="bold">
              Metrics
            </Typography>
            <Box display="flex" gap={4}>
              <Box textAlign="center">
                <Typography variant="h4" color="primary.main" fontWeight="bold">
                  {competency.mapped_paths_count || 0}
                </Typography>
                <Typography variant="caption" color="textSecondary">
                  Learning Paths
                </Typography>
              </Box>
              <Box textAlign="center">
                <Typography variant="h4" color="success.main" fontWeight="bold">
                  {competency.learners_count || 0}
                </Typography>
                <Typography variant="caption" color="textSecondary">
                  Learners
                </Typography>
              </Box>
              <Box textAlign="center">
                <Typography variant="h4" color="info.main" fontWeight="bold">
                  {competency.completion_rate || 0}%
                </Typography>
                <Typography variant="caption" color="textSecondary">
                  Completion Rate
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* Level Definitions */}
          {competency.metadata?.level_definitions && (
            <Box>
              <Typography variant="subtitle2" gutterBottom fontWeight="bold">
                Proficiency Levels
              </Typography>
              <Stack spacing={1}>
                {Object.entries(competency.metadata.level_definitions).map(([level, description]) => (
                  <Box key={level} display="flex" alignItems="center" gap={2}>
                    <Chip
                      label={`Level ${level}`}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                    <Typography variant="body2" color="textSecondary">
                      {description as string}
                    </Typography>
                  </Box>
                ))}
              </Stack>
            </Box>
          )}

          {/* Metadata */}
          <Box>
            <Typography variant="subtitle2" gutterBottom fontWeight="bold">
              Details
            </Typography>
            <Stack spacing={1}>
              <Box display="flex" justifyContent="space-between">
                <Typography variant="body2" color="textSecondary">Created:</Typography>
                <Typography variant="body2">{new Date(competency.created_at).toLocaleDateString()}</Typography>
              </Box>
              <Box display="flex" justifyContent="space-between">
                <Typography variant="body2" color="textSecondary">Last Updated:</Typography>
                <Typography variant="body2">{new Date(competency.updated_at).toLocaleDateString()}</Typography>
              </Box>
              <Box display="flex" justifyContent="space-between">
                <Typography variant="body2" color="textSecondary">Created By:</Typography>
                <Typography variant="body2">{competency.created_by}</Typography>
              </Box>
              <Box display="flex" justifyContent="space-between">
                <Typography variant="body2" color="textSecondary">Visibility:</Typography>
                <Typography variant="body2">{competency.is_public ? 'Public' : 'Private'}</Typography>
              </Box>
            </Stack>
          </Box>
        </Stack>
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        <Button onClick={onClose}>
          Close
        </Button>
        <Button
          variant="contained"
          onClick={() => {
            onEdit(competency)
            onClose()
          }}
          startIcon={<EditIcon />}
        >
          Edit Competency
        </Button>
      </DialogActions>
    </Dialog>
  )
}

// Competency Mapping Dialog Component
function CompetencyMappingDialog({ open, competency, onClose }: MappingDialogProps) {
  const { user } = useAuthStore()
  const [selectedPaths, setSelectedPaths] = useState<number[]>([])
  const [isMapping, setIsMapping] = useState(false)

  // Fetch available learning paths
  const { data: learningPaths = [], isLoading } = useQuery({
    queryKey: ['learning-paths-for-mapping', user?.tenant_id],
    queryFn: async () => {
      if (!user?.tenant_id) return []

      const { data, error } = await supabase
        .from('learning_paths')
        .select('id, title, description, category')
        .eq('tenant_id', user.tenant_id)
        .eq('is_live', true)

      if (error) throw error
      return data || []
    },
    enabled: !!user?.tenant_id && open,
  })

  // Fetch existing mappings
  const { data: existingMappings = [] } = useQuery({
    queryKey: ['competency-mappings', competency?.id],
    queryFn: async () => {
      if (!competency?.id) return []

      const { data, error } = await supabase
        .from('path_competencies')
        .select('path_id')
        .eq('competency_id', competency.id)

      if (error) throw error
      return data?.map(m => m.path_id) || []
    },
    enabled: !!competency?.id && open,
  })

  const handleMapping = async () => {
    if (!competency || selectedPaths.length === 0) return

    setIsMapping(true)
    try {
      // Remove existing mappings
      await supabase
        .from('path_competencies')
        .delete()
        .eq('competency_id', competency.id)

      // Add new mappings
      const mappings = selectedPaths.map(pathId => ({
        competency_id: competency.id,
        path_id: pathId,
        level_gained: 2, // Default level
      }))

      const { error } = await supabase
        .from('path_competencies')
        .insert(mappings)

      if (error) throw error

      toast.success('Competency mappings updated successfully')
      onClose()
    } catch (error) {
      console.error('Mapping error:', error)
      toast.error('Failed to update mappings')
    } finally {
      setIsMapping(false)
    }
  }

  if (!competency) return null

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2, minHeight: '60vh' }
      }}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={2}>
          <MapIcon color="primary" />
          <Box>
            <Typography variant="h6" fontWeight="bold">
              Map to Learning Paths
            </Typography>
            <Typography variant="body2" color="textSecondary">
              {competency.name}
            </Typography>
          </Box>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        {isLoading ? (
          <Box display="flex" justifyContent="center" py={4}>
            <LinearProgress sx={{ width: 200 }} />
          </Box>
        ) : (
          <Stack spacing={2}>
            <Typography variant="body2" color="textSecondary">
              Select learning paths that help develop this competency:
            </Typography>

            {learningPaths.map((path) => (
              <Box
                key={path.id}
                sx={{
                  p: 2,
                  border: 1,
                  borderColor: selectedPaths.includes(path.id) ? 'primary.main' : 'divider',
                  borderRadius: 2,
                  cursor: 'pointer',
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    borderColor: 'primary.main',
                    backgroundColor: 'primary.light',
                    transform: 'translateY(-2px)',
                  },
                }}
                onClick={() => {
                  setSelectedPaths(prev =>
                    prev.includes(path.id)
                      ? prev.filter(id => id !== path.id)
                      : [...prev, path.id]
                  )
                }}
              >
                <Box display="flex" alignItems="center" gap={2}>
                  <Checkbox
                    checked={selectedPaths.includes(path.id) || existingMappings.includes(path.id)}
                    onChange={() => {}}
                    color="primary"
                  />
                  <Box flex={1}>
                    <Typography variant="subtitle2" fontWeight="bold">
                      {path.title}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      {path.description}
                    </Typography>
                    {path.category && (
                      <Chip
                        label={path.category}
                        size="small"
                        variant="outlined"
                        sx={{ mt: 1 }}
                      />
                    )}
                  </Box>
                  {existingMappings.includes(path.id) && (
                    <Chip
                      label="Currently Mapped"
                      size="small"
                      color="success"
                      variant="filled"
                    />
                  )}
                </Box>
              </Box>
            ))}

            {learningPaths.length === 0 && (
              <Box textAlign="center" py={4}>
                <SchoolIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                <Typography variant="body1" color="textSecondary">
                  No learning paths available for mapping
                </Typography>
              </Box>
            )}
          </Stack>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        <Button onClick={onClose}>
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={handleMapping}
          disabled={isMapping || selectedPaths.length === 0}
          startIcon={isMapping ? <CircularProgress size={16} /> : <MapIcon />}
        >
          {isMapping ? 'Updating...' : `Map ${selectedPaths.length} Paths`}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

// Competency Trends Dialog Component
function CompetencyTrendsDialog({ open, competency, onClose }: TrendsDialogProps) {
  const [timeRange, setTimeRange] = useState('30d')

  // Mock trend data - in real implementation, this would come from analytics
  const trendData = useMemo(() => {
    if (!competency) return null

    const labels = ['Week 1', 'Week 2', 'Week 3', 'Week 4']
    const baseValue = competency.learners_count || 10
    const growth = competency.trend_direction === 'up' ? 5 : competency.trend_direction === 'down' ? -3 : 1

    return {
      labels,
      datasets: [
        {
          label: 'Learners Enrolled',
          data: labels.map((_, index) => baseValue + (growth * index)),
          borderColor: '#1976d2',
          backgroundColor: 'rgba(25, 118, 210, 0.1)',
          fill: true,
          tension: 0.4,
        },
        {
          label: 'Completion Rate',
          data: labels.map(() => Math.round((Math.random() * 20 + 70))),
          borderColor: '#2e7d32',
          backgroundColor: 'rgba(46, 125, 50, 0.1)',
          fill: true,
          tension: 0.4,
        },
      ],
    }
  }, [competency, timeRange])

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: 'Time Period',
        },
      },
      y: {
        display: true,
        title: {
          display: true,
          text: 'Count / Percentage',
        },
        beginAtZero: true,
      },
    },
  }

  if (!competency) return null

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2, minHeight: '70vh' }
      }}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={2}>
          <TrendingIcon color="primary" />
          <Box flex={1}>
            <Typography variant="h6" fontWeight="bold">
              Competency Analytics
            </Typography>
            <Typography variant="body2" color="textSecondary">
              {competency.name}
            </Typography>
          </Box>
          <TextField
            select
            size="small"
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            sx={{ minWidth: 120 }}
          >
            <MenuItem value="7d">Last 7 days</MenuItem>
            <MenuItem value="30d">Last 30 days</MenuItem>
            <MenuItem value="90d">Last 90 days</MenuItem>
            <MenuItem value="1y">Last year</MenuItem>
          </TextField>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        <Stack spacing={3}>
          {/* Key Metrics */}
          <Box>
            <Typography variant="h6" gutterBottom fontWeight="bold">
              Key Metrics
            </Typography>
            <Box display="flex" gap={3}>
              <Card sx={{ p: 2, flex: 1, textAlign: 'center' }}>
                <Typography variant="h4" color="primary.main" fontWeight="bold">
                  {competency.learners_count || 0}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Total Learners
                </Typography>
                <Box display="flex" alignItems="center" justifyContent="center" gap={0.5} mt={1}>
                  {competency.trend_direction === 'up' && (
                    <>
                      <TrendingIcon sx={{ fontSize: 16, color: 'success.main' }} />
                      <Typography variant="caption" color="success.main">
                        +{competency.trend_percentage}%
                      </Typography>
                    </>
                  )}
                </Box>
              </Card>

              <Card sx={{ p: 2, flex: 1, textAlign: 'center' }}>
                <Typography variant="h4" color="success.main" fontWeight="bold">
                  {competency.completion_rate || 0}%
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Completion Rate
                </Typography>
              </Card>

              <Card sx={{ p: 2, flex: 1, textAlign: 'center' }}>
                <Typography variant="h4" color="info.main" fontWeight="bold">
                  {competency.mapped_paths_count || 0}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Learning Paths
                </Typography>
              </Card>
            </Box>
          </Box>

          {/* Trend Chart */}
          <Box>
            <Typography variant="h6" gutterBottom fontWeight="bold">
              Trend Analysis
            </Typography>
            <Card sx={{ p: 2, height: 300 }}>
              {/* Chart would be rendered here with Chart.js */}
              <Box
                display="flex"
                alignItems="center"
                justifyContent="center"
                height="100%"
                bgcolor="grey.50"
                borderRadius={1}
              >
                <Typography variant="body1" color="textSecondary">
                  📊 Trend chart would be displayed here
                </Typography>
              </Box>
            </Card>
          </Box>

          {/* Insights */}
          <Box>
            <Typography variant="h6" gutterBottom fontWeight="bold">
              AI Insights
            </Typography>
            <Stack spacing={2}>
              <Alert severity="info">
                <Typography variant="body2">
                  This competency shows {competency.trend_direction === 'up' ? 'positive growth' :
                  competency.trend_direction === 'down' ? 'declining interest' : 'stable engagement'}
                  over the selected time period.
                </Typography>
              </Alert>

              {competency.completion_rate && competency.completion_rate < 70 && (
                <Alert severity="warning">
                  <Typography variant="body2">
                    Completion rate is below average. Consider reviewing the mapped learning paths
                    or providing additional support materials.
                  </Typography>
                </Alert>
              )}

              {competency.mapped_paths_count === 0 && (
                <Alert severity="error">
                  <Typography variant="body2">
                    No learning paths are mapped to this competency. Consider adding relevant
                    learning content to help learners develop this skill.
                  </Typography>
                </Alert>
              )}
            </Stack>
          </Box>
        </Stack>
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        <Button onClick={onClose}>
          Close
        </Button>
        <Button
          variant="outlined"
          startIcon={<LaunchIcon />}
          onClick={() => {
            // Navigate to detailed analytics page
            console.log('Navigate to detailed analytics')
          }}
        >
          View Detailed Analytics
        </Button>
      </DialogActions>
    </Dialog>
  )
}
