'use client'

import { useState, useEffect, useCallback } from 'react'
import './dashboard.css'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  IconButton,
  Paper,
  useTheme,
  Fab,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon,
  Avatar,
  Chip,
  LinearProgress,
  Tooltip,
  Badge,
} from '@mui/material'
import {
  People as PeopleIcon,
  School as SchoolIcon,
  TrendingUp as TrendingUpIcon,
  AccessTime as TimeIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
  Psychology as AIIcon,
  Mood as MoodIcon,
  RecordVoiceOver as VoiceIcon,
  EmojiEvents as TrophyIcon,
  Leaderboard as RankIcon,
  ViewInAr as ARIcon,
  Settings as SettingsIcon,
  Analytics as AnalyticsIcon,
  Group as GroupIcon,
  LibraryBooks as LibraryIcon,
  Assessment as ReportsIcon,
  Notifications as NotificationsIcon,
  Star as StarIcon,
  Celebration as CelebrationIcon,
} from '@mui/icons-material'
import { motion, AnimatePresence } from 'framer-motion'
import { Responsive, WidthProvider } from 'react-grid-layout'

import { useDashboardStore, useAuthStore } from '@/lib/store'
import MetricCard from '@/components/dashboard/MetricCard'
import ChartCard from '@/components/dashboard/ChartCard'
import RecentActivity from '@/components/dashboard/RecentActivity'
import QuickActions from '@/components/dashboard/QuickActions'

// Enhanced Dashboard Components
import AIAssistantWidget from '@/components/dashboard/AIAssistantWidget'
import MoodTrackerWidget from '@/components/dashboard/MoodTrackerWidget'
import GamificationWidget from '@/components/dashboard/GamificationWidget'
import VoiceCommandWidget from '@/components/dashboard/VoiceCommandWidget'
import PeerComparisonWidget from '@/components/dashboard/PeerComparisonWidget'
import ARPreviewWidget from '@/components/dashboard/ARPreviewWidget'
import PersonalizedWelcome from '@/components/dashboard/PersonalizedWelcome'
import AchievementNotification from '@/components/dashboard/AchievementNotification'

const ResponsiveGridLayout = WidthProvider(Responsive)

export default function DashboardPage() {
  const theme = useTheme()
  const { user, tenant } = useAuthStore()
  const {
    widgets,
    customization,
    gamification,
    currentMood,
    isVoiceActive,
    notifications,
    updateWidget,
    setWidgets,
    setCustomization,
    updateMood,
    setVoiceActive,
    addNotification,
    addAchievement
  } = useDashboardStore()

  const [isLoading, setIsLoading] = useState(true)
  const [showAchievement, setShowAchievement] = useState(false)
  const [currentAchievement, setCurrentAchievement] = useState(null)
  const [speedDialOpen, setSpeedDialOpen] = useState(false)
  const [dashboardData, setDashboardData] = useState({
    activeLearners: 1247,
    activeLearnersTrend: 12.5,
    completionRate: 78.3,
    completionRateTrend: 5.2,
    avgTimeSpent: 45.2,
    avgTimeSpentTrend: -2.1,
    engagementScore: 85.7,
    engagementScoreTrend: 8.3,
    completionTrends: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      data: [65, 72, 68, 75, 78, 82],
    },
    popularPaths: [
      { name: 'JavaScript Fundamentals', learners: 234, completion: 85 },
      { name: 'React Development', learners: 189, completion: 72 },
      { name: 'Python Basics', learners: 156, completion: 91 },
      { name: 'Data Science Intro', learners: 143, completion: 68 },
    ],
  })

  // Time-based greeting
  const getTimeBasedGreeting = useCallback(() => {
    const hour = new Date().getHours()
    if (hour < 12) return 'Good morning'
    if (hour < 17) return 'Good afternoon'
    return 'Good evening'
  }, [])

  // Mood-based theme adaptation
  useEffect(() => {
    if (currentMood && customization.theme === 'mood-adaptive') {
      const moodThemes = {
        '😊': 'light',
        '😴': 'dark',
        '🔥': 'energetic',
        '🧘': 'calm',
      }
      // Apply mood-based theme changes
    }
  }, [currentMood, customization.theme])

  const refreshData = async () => {
    setIsLoading(true)
    // Simulate API call with real-time updates
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Update widgets with new data
    updateWidget('active-learners', {
      data: { value: dashboardData.activeLearners, change: dashboardData.activeLearnersTrend }
    })
    updateWidget('completion-rate', {
      data: { value: dashboardData.completionRate, change: dashboardData.completionRateTrend }
    })

    setIsLoading(false)
    addNotification({
      id: `refresh-${Date.now()}`,
      title: 'Dashboard Updated',
      message: 'Your dashboard data has been refreshed successfully',
      type: 'success',
      priority: 'low',
      isRead: false,
      createdAt: new Date().toISOString(),
    })
  }

  // Voice command handler
  const handleVoiceCommand = useCallback((command: string) => {
    const lowerCommand = command.toLowerCase()

    if (lowerCommand.includes('show analytics')) {
      window.location.href = '/dashboard/reports'
    } else if (lowerCommand.includes('add user')) {
      window.location.href = '/dashboard/learners'
    } else if (lowerCommand.includes('create course')) {
      window.location.href = '/dashboard/learning-paths/create'
    }
  }, [])

  useEffect(() => {
    // Simulate loading dashboard data with enhanced features
    const timer = setTimeout(() => {
      setIsLoading(false)

      // Trigger welcome achievement for first-time users
      if (gamification.level === 1 && gamification.experience === 0) {
        const welcomeAchievement = {
          id: 'welcome-admin',
          title: 'Welcome, Admin!',
          description: 'You\'ve successfully accessed your dashboard',
          icon: '🎉',
          progress: 1,
          maxProgress: 1,
          isCompleted: true,
          reward: { type: 'points', value: 50 },
          unlockedAt: new Date().toISOString(),
        }

        addAchievement(welcomeAchievement)
        setCurrentAchievement(welcomeAchievement)
        setShowAchievement(true)
      }
    }, 1500)

    return () => clearTimeout(timer)
  }, [])

  // Layout change handler
  const handleLayoutChange = (layout: any, layouts: any) => {
    // Update widget positions in store
    const updatedWidgets = widgets.map(widget => {
      const layoutItem = layout.find((item: any) => item.i === widget.id)
      if (layoutItem) {
        return {
          ...widget,
          position: {
            x: layoutItem.x,
            y: layoutItem.y,
            w: layoutItem.w,
            h: layoutItem.h,
          }
        }
      }
      return widget
    })

    // Update the store with new positions
    setWidgets(updatedWidgets)
  }

  // Speed dial actions
  const speedDialActions = [
    { icon: <PeopleIcon />, name: 'Manage Users', onClick: () => window.location.href = '/dashboard/learners' },
    { icon: <SchoolIcon />, name: 'Learning Paths', onClick: () => window.location.href = '/dashboard/learning-paths' },
    { icon: <LibraryIcon />, name: 'Content Library', onClick: () => window.location.href = '/dashboard/content' },
    { icon: <ReportsIcon />, name: 'Reports', onClick: () => window.location.href = '/dashboard/reports' },
    { icon: <SettingsIcon />, name: 'Settings', onClick: () => window.location.href = '/dashboard/settings' },
  ]

  return (
    <Box
      className="dashboard-container"
      sx={{
        position: 'relative',
        minHeight: '100vh',
        p: { xs: 1, sm: 2, md: 3 },
        overflow: 'hidden'
      }}
    >
      <AnimatePresence>
        {showAchievement && currentAchievement && (
          <AchievementNotification
            achievement={currentAchievement}
            onClose={() => setShowAchievement(false)}
          />
        )}
      </AnimatePresence>

      {/* Enhanced Personalized Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <PersonalizedWelcome
          user={user}
          greeting={getTimeBasedGreeting()}
          gamification={gamification}
          currentMood={currentMood}
          onMoodChange={updateMood}
        />
      </motion.div>

      {/* Gamification Progress Bar */}
      <motion.div
        initial={{ opacity: 0, scaleX: 0 }}
        animate={{ opacity: 1, scaleX: 1 }}
        transition={{ duration: 0.8, delay: 0.2 }}
      >
        <Box sx={{ mb: 3 }}>
          <Box display="flex" alignItems="center" gap={2} mb={1}>
            <Avatar sx={{ bgcolor: 'primary.main', width: 32, height: 32 }}>
              <TrophyIcon fontSize="small" />
            </Avatar>
            <Typography variant="h6" fontWeight="bold">
              Level {gamification.level} - {gamification.rank}
            </Typography>
            <Chip
              label={`${gamification.experience}/${gamification.experienceToNext} XP`}
              color="primary"
              size="small"
            />
          </Box>
          <LinearProgress
            variant="determinate"
            value={(gamification.experience / gamification.experienceToNext) * 100}
            sx={{
              height: 8,
              borderRadius: 4,
              '& .MuiLinearProgress-bar': {
                borderRadius: 4,
                background: 'linear-gradient(45deg, #FE6B8B 30%, #FF8E53 90%)',
              }
            }}
          />
        </Box>
      </motion.div>

      {/* Action Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box display="flex" alignItems="center" gap={2}>
          <Badge badgeContent={notifications.filter(n => !n.isRead).length} color="error">
            <IconButton color="primary">
              <NotificationsIcon />
            </IconButton>
          </Badge>
          {currentMood && (
            <Chip
              label={`Feeling ${currentMood}`}
              size="small"
              sx={{
                bgcolor: theme.palette.mode === 'light' ? 'rgba(0,0,0,0.05)' : 'rgba(255,255,255,0.05)',
                fontSize: '1.2em'
              }}
            />
          )}
        </Box>
        <Box display="flex" gap={1}>
          <Tooltip title="Voice Commands">
            <IconButton
              onClick={() => setVoiceActive(!isVoiceActive)}
              color={isVoiceActive ? 'primary' : 'default'}
            >
              <VoiceIcon />
            </IconButton>
          </Tooltip>
          <IconButton
            onClick={refreshData}
            disabled={isLoading}
            color="primary"
          >
            <RefreshIcon />
          </IconButton>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            href="/dashboard/learning-paths/create"
            sx={{
              background: 'linear-gradient(45deg, #FE6B8B 30%, #FF8E53 90%)',
              '&:hover': {
                background: 'linear-gradient(45deg, #FE6B8B 60%, #FF8E53 100%)',
              }
            }}
          >
            Create Learning Path
          </Button>
        </Box>
      </Box>

      {/* Enhanced Widget Grid with Drag & Drop */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        sx={{
          '& .react-grid-layout': {
            position: 'relative',
          },
          '& .react-grid-item': {
            transition: 'all 200ms ease',
            '&.react-grid-item.resizing': {
              zIndex: 1,
            },
            '&.react-grid-item.react-draggable-dragging': {
              transition: 'none',
              zIndex: 3,
            },
          },
          '& .react-grid-item > .react-resizable-handle': {
            position: 'absolute',
            width: '20px',
            height: '20px',
            bottom: 0,
            right: 0,
            background: 'url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNiIgaGVpZ2h0PSI2IiB2aWV3Qm94PSIwIDAgNiA2IiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxnIGZpbGw9IiM0NDQiIGZpbGwtb3BhY2l0eT0iLjMiPjxwYXRoIGQ9Im0gNiw2IDAgLTYgLTYsNiB6Ii8+PC9nPjwvc3ZnPg==") no-repeat',
            backgroundPosition: 'bottom right',
            padding: '0 3px 3px 0',
            backgroundRepeat: 'no-repeat',
            backgroundOrigin: 'content-box',
            boxSizing: 'border-box',
            cursor: 'se-resize',
          }
        }}
      >
        <ResponsiveGridLayout
          className="layout"
          layouts={{
            lg: widgets.map(w => ({ i: w.id, ...w.position })),
            md: widgets.map(w => ({
              i: w.id,
              x: Math.floor(w.position.x * 0.8),
              y: w.position.y,
              w: Math.min(w.position.w + 1, 8),
              h: w.position.h
            })),
            sm: widgets.map(w => ({
              i: w.id,
              x: Math.floor(w.position.x * 0.5),
              y: w.position.y,
              w: Math.min(w.position.w + 2, 6),
              h: w.position.h
            })),
            xs: widgets.map(w => ({
              i: w.id,
              x: 0,
              y: w.position.y,
              w: 4,
              h: w.position.h
            })),
            xxs: widgets.map(w => ({
              i: w.id,
              x: 0,
              y: w.position.y,
              w: 2,
              h: w.position.h
            }))
          }}
          breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
          cols={{ lg: 12, md: 8, sm: 6, xs: 4, xxs: 2 }}
          rowHeight={80}
          isDraggable={true}
          isResizable={true}
          margin={[20, 20]}
          containerPadding={[20, 20]}
          onLayoutChange={handleLayoutChange}
          useCSSTransforms={true}
          preventCollision={false}
          compactType="vertical"
        >
          {widgets.filter(w => w.isVisible).map((widget) => (
            <div
              key={widget.id}
              className="dashboard-widget"
            >
              {widget.type === 'metric' && (
                <motion.div
                  whileHover={{ scale: 1.02, y: -2 }}
                  transition={{ type: "spring", stiffness: 300 }}
                  style={{ height: '100%' }}
                >
                  <MetricCard
                    title={widget.title}
                    value={widget.data?.value || 0}
                    change={widget.data?.change || 0}
                    icon={getWidgetIcon(widget.id)}
                    color={getWidgetColor(widget.id)}
                    isLoading={isLoading}
                  />
                </motion.div>
              )}

              {widget.type === 'ai-assistant' && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.6 }}
                  style={{ height: '100%' }}
                >
                  <AIAssistantWidget />
                </motion.div>
              )}

              {widget.type === 'mood-tracker' && (
                <motion.div
                  initial={{ opacity: 0, rotate: -10 }}
                  animate={{ opacity: 1, rotate: 0 }}
                  transition={{ delay: 0.8 }}
                  style={{ height: '100%' }}
                >
                  <MoodTrackerWidget
                    currentMood={currentMood}
                    onMoodChange={updateMood}
                  />
                </motion.div>
              )}

              {widget.type === 'gamification' && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 1.0 }}
                  style={{ height: '100%' }}
                >
                  <GamificationWidget
                    gamification={gamification}
                    achievements={[]}
                    dailyChallenges={[]}
                  />
                </motion.div>
              )}

              {widget.type === 'voice-command' && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.2 }}
                  style={{ height: '100%' }}
                >
                  <VoiceCommandWidget
                    isActive={isVoiceActive}
                    onToggle={setVoiceActive}
                    onCommand={handleVoiceCommand}
                  />
                </motion.div>
              )}

              {widget.type === 'peer-comparison' && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 1.4 }}
                  style={{ height: '100%' }}
                >
                  <PeerComparisonWidget />
                </motion.div>
              )}

              {widget.type === 'ar-preview' && (
                <motion.div
                  initial={{ opacity: 0, rotateY: 90 }}
                  animate={{ opacity: 1, rotateY: 0 }}
                  transition={{ delay: 1.6 }}
                  style={{ height: '100%' }}
                >
                  <ARPreviewWidget />
                </motion.div>
              )}

              {widget.type === 'chart' && (
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  style={{ height: '100%' }}
                >
                  <ChartCard
                    title={widget.title}
                    subtitle="Real-time analytics with AI insights"
                    data={dashboardData.completionTrends}
                    type="line"
                    isLoading={isLoading}
                  />
                </motion.div>
              )}
            </div>
          ))}
        </ResponsiveGridLayout>
      </motion.div>

      {/* Floating Speed Dial for Quick Actions */}
      <SpeedDial
        ariaLabel="Quick Actions"
        sx={{ position: 'fixed', bottom: 24, right: 24 }}
        icon={<SpeedDialIcon />}
        open={speedDialOpen}
        onClose={() => setSpeedDialOpen(false)}
        onOpen={() => setSpeedDialOpen(true)}
      >
        {speedDialActions.map((action) => (
          <SpeedDialAction
            key={action.name}
            icon={action.icon}
            tooltipTitle={action.name}
            onClick={action.onClick}
          />
        ))}
      </SpeedDial>

      {/* Enhanced Bottom Section with Real-time Updates */}
      <motion.div
        initial={{ opacity: 0, y: 40 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 1.8 }}
      >
        <Grid container spacing={3} sx={{ mt: 2 }}>
          <Grid item xs={12} md={8}>
            <Card sx={{
              height: '100%',
              background: theme.palette.mode === 'dark'
                ? 'linear-gradient(135deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0.02) 100%)'
                : 'linear-gradient(135deg, rgba(0,0,0,0.02) 0%, rgba(0,0,0,0.05) 100%)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255,255,255,0.1)',
            }}>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2} mb={2}>
                  <Avatar sx={{ bgcolor: 'info.main' }}>
                    <TrendingUpIcon />
                  </Avatar>
                  <Typography variant="h6" fontWeight="bold">
                    Real-time Activity Feed
                  </Typography>
                  <Chip label="Live" color="success" size="small" />
                </Box>
                <RecentActivity />
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card sx={{
              height: '100%',
              background: theme.palette.mode === 'dark'
                ? 'linear-gradient(135deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0.02) 100%)'
                : 'linear-gradient(135deg, rgba(0,0,0,0.02) 0%, rgba(0,0,0,0.05) 100%)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255,255,255,0.1)',
            }}>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2} mb={2}>
                  <Avatar sx={{ bgcolor: 'warning.main' }}>
                    <StarIcon />
                  </Avatar>
                  <Typography variant="h6" fontWeight="bold">
                    Quick Actions
                  </Typography>
                </Box>
                <QuickActions />
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </motion.div>

      {/* Celebration Effects Container */}
      <Box
        id="celebration-container"
        sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
          zIndex: 9999,
        }}
      />
    </Box>
  )
}

// Helper functions for widget rendering
function getWidgetIcon(widgetId: string) {
  const iconMap = {
    'active-learners': <PeopleIcon />,
    'completion-rate': <SchoolIcon />,
    'time-spent': <TimeIcon />,
    'engagement-score': <TrendingUpIcon />,
  }
  return iconMap[widgetId as keyof typeof iconMap] || <TrendingUpIcon />
}

function getWidgetColor(widgetId: string): 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info' {
  const colorMap = {
    'active-learners': 'primary',
    'completion-rate': 'success',
    'time-spent': 'info',
    'engagement-score': 'warning',
  }
  return colorMap[widgetId as keyof typeof colorMap] as any || 'primary'
}
