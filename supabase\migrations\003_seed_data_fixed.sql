-- Fixed seed data for ZenithLearn AI with proper error handling and transaction management
-- This version includes existence checks and proper dependency management

BEGIN;

-- Check if we already have data and exit early if so
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM tenants WHERE slug = 'demo-org') THEN
        RAISE NOTICE 'Seed data already exists, skipping insertion';
        RETURN;
    END IF;
END $$;

-- Insert default tenant (only if it doesn't exist)
INSERT INTO tenants (name, slug, settings) 
SELECT 'Demo Organization', 'demo-org', '{"theme": "light", "features": ["ai_chat", "gamification", "reports"]}'::jsonb
WHERE NOT EXISTS (SELECT 1 FROM tenants WHERE slug = 'demo-org');

-- Verify tenant was created and get its ID
DO $$
DECLARE
    tenant_count INTEGER;
    tenant_id_var INTEGER;
BEGIN
    SELECT COUNT(*), MAX(id) INTO tenant_count, tenant_id_var 
    FROM tenants WHERE slug = 'demo-org';
    
    IF tenant_count = 0 THEN
        RAISE EXCEPTION 'Failed to create or find demo tenant';
    END IF;
    
    RAISE NOTICE 'Demo tenant exists with ID: %', tenant_id_var;
END $$;

-- Insert default roles (only if they don't exist for this tenant)
INSERT INTO roles (tenant_id, name, permissions, is_default) 
SELECT 
    t.id,
    role_data.name,
    role_data.permissions::TEXT[],
    role_data.is_default
FROM tenants t,
(VALUES 
    ('Super Admin', ARRAY['admin'], false),
    ('Admin', ARRAY['view_dashboard', 'view_learning_paths', 'create_learning_paths', 'edit_learning_paths', 'delete_learning_paths', 'assign_learning_paths', 'view_learners', 'create_learners', 'edit_learners', 'view_content', 'upload_content', 'edit_content', 'view_reports', 'export_reports', 'view_settings', 'edit_settings'], false),
    ('Instructor', ARRAY['view_dashboard', 'view_learning_paths', 'create_learning_paths', 'edit_learning_paths', 'assign_learning_paths', 'view_learners', 'view_content', 'upload_content', 'view_reports'], false),
    ('Learner', ARRAY['view_dashboard'], true)
) AS role_data(name, permissions, is_default)
WHERE t.slug = 'demo-org'
AND NOT EXISTS (
    SELECT 1 FROM roles r 
    WHERE r.tenant_id = t.id AND r.name = role_data.name
);

-- Verify roles were created
DO $$
DECLARE
    role_count INTEGER;
    tenant_id_var INTEGER;
BEGIN
    SELECT id INTO tenant_id_var FROM tenants WHERE slug = 'demo-org';
    SELECT COUNT(*) INTO role_count FROM roles WHERE tenant_id = tenant_id_var;
    
    IF role_count < 4 THEN
        RAISE EXCEPTION 'Failed to create all required roles. Found % roles, expected 4', role_count;
    END IF;
    
    RAISE NOTICE 'Created % roles for tenant %', role_count, tenant_id_var;
END $$;

-- Insert default competencies (only if they don't exist)
INSERT INTO competencies (tenant_id, name, description, category, level_definitions) 
SELECT 
    t.id,
    comp_data.name,
    comp_data.description,
    comp_data.category,
    comp_data.level_definitions::jsonb
FROM tenants t,
(VALUES 
    ('JavaScript Programming', 'Proficiency in JavaScript programming language', 'Technical', '{"1": "Basic syntax and concepts", "2": "Functions and objects", "3": "Advanced concepts and frameworks", "4": "Expert level with optimization", "5": "Thought leader and mentor"}'),
    ('React Development', 'Building user interfaces with React', 'Technical', '{"1": "Basic components", "2": "State and props", "3": "Hooks and context", "4": "Performance optimization", "5": "Architecture and patterns"}'),
    ('Project Management', 'Managing projects and teams effectively', 'Soft Skills', '{"1": "Basic planning", "2": "Team coordination", "3": "Risk management", "4": "Strategic planning", "5": "Organizational leadership"}'),
    ('Communication', 'Effective verbal and written communication', 'Soft Skills', '{"1": "Basic communication", "2": "Clear presentation", "3": "Persuasive communication", "4": "Leadership communication", "5": "Inspirational speaking"}')
) AS comp_data(name, description, category, level_definitions)
WHERE t.slug = 'demo-org'
AND NOT EXISTS (
    SELECT 1 FROM competencies c 
    WHERE c.tenant_id = t.id AND c.name = comp_data.name
);

-- Insert sample learning paths (only if they don't exist)
INSERT INTO learning_paths (tenant_id, title, description, objectives, prerequisites, difficulty_level, estimated_duration, is_live, is_featured, category, tags, created_by) 
SELECT 
    t.id,
    path_data.title,
    path_data.description,
    path_data.objectives::TEXT[],
    path_data.prerequisites::TEXT[],
    path_data.difficulty_level,
    path_data.estimated_duration,
    path_data.is_live,
    path_data.is_featured,
    path_data.category,
    path_data.tags::TEXT[]
FROM tenants t,
(VALUES 
    ('JavaScript Fundamentals', 'Learn the basics of JavaScript programming', 
     ARRAY['Understand variables and data types', 'Master functions and scope', 'Work with objects and arrays'], 
     ARRAY['Basic computer literacy'], 
     'beginner', 480, true, true, 'Programming', 
     ARRAY['javascript', 'programming', 'web-development']),
    ('React Development Bootcamp', 'Comprehensive React.js development course', 
     ARRAY['Build React components', 'Manage state effectively', 'Create full applications'], 
     ARRAY['JavaScript Fundamentals'], 
     'intermediate', 720, true, true, 'Programming', 
     ARRAY['react', 'javascript', 'frontend']),
    ('Advanced Python Programming', 'Deep dive into Python for experienced developers', 
     ARRAY['Master advanced Python concepts', 'Build scalable applications', 'Optimize performance'], 
     ARRAY['Basic Python knowledge'], 
     'advanced', 600, true, false, 'Programming', 
     ARRAY['python', 'advanced', 'backend']),
    ('Project Management Essentials', 'Core project management skills and methodologies', 
     ARRAY['Plan and execute projects', 'Manage teams effectively', 'Handle project risks'], 
     ARRAY[]::TEXT[], 
     'beginner', 360, true, true, 'Management', 
     ARRAY['project-management', 'leadership', 'soft-skills'])
) AS path_data(title, description, objectives, prerequisites, difficulty_level, estimated_duration, is_live, is_featured, category, tags)
WHERE t.slug = 'demo-org'
AND NOT EXISTS (
    SELECT 1 FROM learning_paths lp 
    WHERE lp.tenant_id = t.id AND lp.title = path_data.title
);

-- Insert modules for JavaScript Fundamentals (only if the path exists and modules don't exist)
INSERT INTO modules (path_id, title, description, order_index) 
SELECT 
    lp.id,
    module_data.title,
    module_data.description,
    module_data.order_index
FROM learning_paths lp
JOIN tenants t ON lp.tenant_id = t.id,
(VALUES 
    ('Introduction to JavaScript', 'Getting started with JavaScript basics', 1),
    ('Variables and Data Types', 'Understanding JavaScript data types and variables', 2),
    ('Functions and Scope', 'Working with functions and understanding scope', 3),
    ('Objects and Arrays', 'Manipulating objects and arrays in JavaScript', 4),
    ('DOM Manipulation', 'Interacting with the Document Object Model', 5)
) AS module_data(title, description, order_index)
WHERE t.slug = 'demo-org' 
AND lp.title = 'JavaScript Fundamentals'
AND NOT EXISTS (
    SELECT 1 FROM modules m 
    WHERE m.path_id = lp.id AND m.title = module_data.title
);

-- Insert sample lessons (only if modules exist and lessons don't exist)
INSERT INTO lessons (module_id, title, description, type, content_text, order_index, estimated_duration) 
SELECT 
    m.id,
    lesson_data.title,
    lesson_data.description,
    lesson_data.type,
    lesson_data.content_text,
    lesson_data.order_index,
    lesson_data.estimated_duration
FROM modules m
JOIN learning_paths lp ON m.path_id = lp.id
JOIN tenants t ON lp.tenant_id = t.id,
(VALUES 
    ('Introduction to JavaScript', 'What is JavaScript?', 'Introduction to JavaScript and its uses', 'text', 'JavaScript is a versatile programming language primarily used for web development. It allows you to create interactive and dynamic web pages...', 1, 15),
    ('Introduction to JavaScript', 'Setting up Development Environment', 'How to set up your coding environment', 'video', NULL, 2, 20),
    ('Introduction to JavaScript', 'Your First JavaScript Program', 'Writing and running your first JavaScript code', 'interactive', NULL, 3, 25),
    ('Variables and Data Types', 'Understanding Variables', 'Learn about var, let, and const', 'text', 'Variables are containers for storing data values. In JavaScript, you can declare variables using var, let, or const keywords...', 1, 20)
) AS lesson_data(module_title, title, description, type, content_text, order_index, estimated_duration)
WHERE t.slug = 'demo-org' 
AND lp.title = 'JavaScript Fundamentals'
AND m.title = lesson_data.module_title
AND NOT EXISTS (
    SELECT 1 FROM lessons l 
    WHERE l.module_id = m.id AND l.title = lesson_data.title
);

-- Insert sample content library items (only if they don't exist)
INSERT INTO content_library (tenant_id, title, description, type, file_url, tags, uploaded_by, is_public) 
SELECT 
    t.id,
    content_data.title,
    content_data.description,
    content_data.type,
    content_data.file_url,
    content_data.tags::TEXT[],
    NULL,
    content_data.is_public
FROM tenants t,
(VALUES 
    ('JavaScript Cheat Sheet', 'Quick reference for JavaScript syntax', 'pdf', '/content/javascript-cheat-sheet.pdf', ARRAY['javascript', 'reference'], true),
    ('React Component Patterns', 'Common patterns for React components', 'video', '/content/react-patterns.mp4', ARRAY['react', 'patterns'], true),
    ('Project Planning Template', 'Template for project planning documents', 'document', '/content/project-template.docx', ARRAY['project-management', 'template'], true)
) AS content_data(title, description, type, file_url, tags, is_public)
WHERE t.slug = 'demo-org'
AND NOT EXISTS (
    SELECT 1 FROM content_library cl 
    WHERE cl.tenant_id = t.id AND cl.title = content_data.title
);

-- Create utility functions (only if they don't exist)
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert user into users table with default role
    INSERT INTO users (id, tenant_id, role_id, email, full_name)
    VALUES (
        NEW.id,
        1, -- Default tenant for demo
        (SELECT id FROM roles WHERE tenant_id = 1 AND is_default = true LIMIT 1),
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1))
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user registration (drop first if exists)
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Final verification
DO $$
DECLARE
    tenant_count INTEGER;
    role_count INTEGER;
    competency_count INTEGER;
    path_count INTEGER;
    tenant_id_var INTEGER;
BEGIN
    SELECT id INTO tenant_id_var FROM tenants WHERE slug = 'demo-org';
    
    SELECT COUNT(*) INTO tenant_count FROM tenants WHERE slug = 'demo-org';
    SELECT COUNT(*) INTO role_count FROM roles WHERE tenant_id = tenant_id_var;
    SELECT COUNT(*) INTO competency_count FROM competencies WHERE tenant_id = tenant_id_var;
    SELECT COUNT(*) INTO path_count FROM learning_paths WHERE tenant_id = tenant_id_var;
    
    RAISE NOTICE 'Seed data verification:';
    RAISE NOTICE '- Tenants: %', tenant_count;
    RAISE NOTICE '- Roles: %', role_count;
    RAISE NOTICE '- Competencies: %', competency_count;
    RAISE NOTICE '- Learning Paths: %', path_count;
    
    IF tenant_count = 0 OR role_count < 4 OR competency_count < 4 OR path_count < 4 THEN
        RAISE EXCEPTION 'Seed data verification failed. Check the counts above.';
    END IF;
    
    RAISE NOTICE 'Seed data successfully created!';
END $$;

COMMIT;
