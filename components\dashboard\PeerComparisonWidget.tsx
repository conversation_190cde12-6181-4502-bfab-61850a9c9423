'use client'

import { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  Typography,
  Box,
  LinearProgress,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  useTheme,
  Tooltip,
} from '@mui/material'
import {
  Leaderboard as LeaderboardIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Remove as NeutralIcon,
  EmojiEvents as TrophyIcon,
  Star as StarIcon,
  Speed as SpeedIcon,
  People as PeopleIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'

interface PeerMetric {
  label: string
  value: number
  rank: number
  percentile: number
  trend: 'up' | 'down' | 'neutral'
  icon: React.ReactNode
  color: string
}

const mockPeerData: PeerMetric[] = [
  {
    label: 'Users Managed',
    value: 247,
    rank: 12,
    percentile: 85,
    trend: 'up',
    icon: <PeopleIcon />,
    color: '#2196F3'
  },
  {
    label: 'Courses Created',
    value: 15,
    rank: 8,
    percentile: 92,
    trend: 'up',
    icon: <StarIcon />,
    color: '#4CAF50'
  },
  {
    label: 'Engagement Rate',
    value: 78.5,
    rank: 25,
    percentile: 68,
    trend: 'neutral',
    icon: <TrendingUpIcon />,
    color: '#FF9800'
  },
  {
    label: 'Response Time',
    value: 2.3,
    rank: 45,
    percentile: 45,
    trend: 'down',
    icon: <SpeedIcon />,
    color: '#F44336'
  }
]

const achievements = [
  { title: 'Top 10% Creator', description: 'Course creation excellence', icon: '🏆', color: '#FFD700' },
  { title: 'User Champion', description: 'Outstanding user management', icon: '👥', color: '#4CAF50' },
  { title: 'Rising Star', description: 'Fastest improvement this month', icon: '⭐', color: '#2196F3' },
]

export default function PeerComparisonWidget() {
  const theme = useTheme()
  const [selectedMetric, setSelectedMetric] = useState<PeerMetric | null>(null)
  const [overallRank, setOverallRank] = useState(18)
  const [totalAdmins, setTotalAdmins] = useState(156)

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUpIcon color="success" fontSize="small" />
      case 'down': return <TrendingDownIcon color="error" fontSize="small" />
      default: return <NeutralIcon color="disabled" fontSize="small" />
    }
  }

  const getPercentileColor = (percentile: number) => {
    if (percentile >= 90) return '#4CAF50'
    if (percentile >= 75) return '#8BC34A'
    if (percentile >= 50) return '#FF9800'
    if (percentile >= 25) return '#FF5722'
    return '#F44336'
  }

  const getPercentileLabel = (percentile: number) => {
    if (percentile >= 90) return 'Excellent'
    if (percentile >= 75) return 'Good'
    if (percentile >= 50) return 'Average'
    if (percentile >= 25) return 'Below Average'
    return 'Needs Improvement'
  }

  const getRankSuffix = (rank: number) => {
    if (rank % 10 === 1 && rank % 100 !== 11) return 'st'
    if (rank % 10 === 2 && rank % 100 !== 12) return 'nd'
    if (rank % 10 === 3 && rank % 100 !== 13) return 'rd'
    return 'th'
  }

  return (
    <Card sx={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      <CardContent sx={{
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
        <Box display="flex" alignItems="center" gap={2} mb={2}>
          <Avatar sx={{ bgcolor: 'info.main', width: 32, height: 32 }}>
            <LeaderboardIcon fontSize="small" />
          </Avatar>
          <Typography variant="h6" fontWeight="bold">
            Performance Benchmark
          </Typography>
        </Box>

        {/* Overall Ranking */}
        <Box mb={3} textAlign="center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", stiffness: 200 }}
          >
            <Typography variant="h3" fontWeight="bold" color="primary">
              #{overallRank}
            </Typography>
          </motion.div>
          <Typography variant="body2" color="text.secondary">
            out of {totalAdmins} admins
          </Typography>
          <Chip
            label={`Top ${Math.round((1 - overallRank / totalAdmins) * 100)}%`}
            color="primary"
            size="small"
            sx={{ mt: 1 }}
          />
        </Box>

        {/* Metrics List */}
        <List dense>
          {mockPeerData.map((metric, index) => (
            <motion.div
              key={metric.label}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <ListItem 
                sx={{ 
                  px: 0, 
                  py: 1,
                  cursor: 'pointer',
                  borderRadius: 1,
                  '&:hover': {
                    bgcolor: 'action.hover'
                  }
                }}
                onClick={() => setSelectedMetric(selectedMetric?.label === metric.label ? null : metric)}
              >
                <ListItemAvatar>
                  <Avatar sx={{ 
                    width: 32, 
                    height: 32, 
                    bgcolor: metric.color + '20',
                    color: metric.color
                  }}>
                    {metric.icon}
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography variant="body2" fontWeight="medium">
                        {metric.label}
                      </Typography>
                      <Box display="flex" alignItems="center" gap={0.5}>
                        {getTrendIcon(metric.trend)}
                        <Typography variant="body2" fontWeight="bold">
                          #{metric.rank}
                        </Typography>
                      </Box>
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={0.5}>
                        <Typography variant="caption" color="text.secondary">
                          {metric.value}{metric.label.includes('Rate') ? '%' : metric.label.includes('Time') ? 'h avg' : ''}
                        </Typography>
                        <Typography 
                          variant="caption" 
                          sx={{ color: getPercentileColor(metric.percentile) }}
                          fontWeight="bold"
                        >
                          {getPercentileLabel(metric.percentile)}
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={metric.percentile}
                        sx={{
                          height: 4,
                          borderRadius: 2,
                          bgcolor: 'rgba(0,0,0,0.1)',
                          '& .MuiLinearProgress-bar': {
                            borderRadius: 2,
                            bgcolor: getPercentileColor(metric.percentile),
                          }
                        }}
                      />
                    </Box>
                  }
                />
              </ListItem>

              {/* Expanded Details */}
              {selectedMetric?.label === metric.label && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <Box sx={{ 
                    ml: 5, 
                    p: 2, 
                    bgcolor: 'background.default', 
                    borderRadius: 1,
                    border: 1,
                    borderColor: 'divider'
                  }}>
                    <Typography variant="body2" fontWeight="medium" mb={1}>
                      Detailed Breakdown
                    </Typography>
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="caption">Your Score:</Typography>
                      <Typography variant="caption" fontWeight="bold">
                        {metric.value}{metric.label.includes('Rate') ? '%' : ''}
                      </Typography>
                    </Box>
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="caption">Percentile:</Typography>
                      <Typography variant="caption" fontWeight="bold">
                        {metric.percentile}{getRankSuffix(metric.percentile)}
                      </Typography>
                    </Box>
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="caption">Trend:</Typography>
                      <Box display="flex" alignItems="center" gap={0.5}>
                        {getTrendIcon(metric.trend)}
                        <Typography variant="caption" fontWeight="bold">
                          {metric.trend === 'up' ? 'Improving' : metric.trend === 'down' ? 'Declining' : 'Stable'}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </motion.div>
              )}
            </motion.div>
          ))}
        </List>

        {/* Achievements */}
        <Box mt={2}>
          <Typography variant="body2" fontWeight="medium" mb={1}>
            Recent Achievements
          </Typography>
          <Box display="flex" flexDirection="column" gap={1}>
            {achievements.map((achievement, index) => (
              <motion.div
                key={achievement.title}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.5 + index * 0.1 }}
              >
                <Tooltip title={achievement.description}>
                  <Chip
                    icon={<span style={{ fontSize: '0.8rem' }}>{achievement.icon}</span>}
                    label={achievement.title}
                    size="small"
                    sx={{
                      bgcolor: achievement.color + '20',
                      color: achievement.color,
                      fontWeight: 'bold',
                      '& .MuiChip-icon': {
                        color: achievement.color
                      }
                    }}
                  />
                </Tooltip>
              </motion.div>
            ))}
          </Box>
        </Box>

        {/* Motivational Message */}
        <Box mt={2} p={1.5} sx={{ 
          bgcolor: 'primary.main', 
          color: 'primary.contrastText',
          borderRadius: 1,
          textAlign: 'center'
        }}>
          <Typography variant="body2" fontWeight="medium">
            🎯 You're in the top 20%!
          </Typography>
          <Typography variant="caption">
            Keep up the excellent work!
          </Typography>
        </Box>
      </CardContent>
    </Card>
  )
}
