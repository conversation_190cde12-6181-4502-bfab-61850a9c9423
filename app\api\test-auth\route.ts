import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing authentication flow...')

    const { email, password } = await request.json()

    if (!email || !password) {
      return NextResponse.json({
        success: false,
        error: 'Email and password are required'
      }, { status: 400 })
    }

    // Test the authentication flow
    console.log('Attempting to sign in with:', email)
    
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) {
      console.log('❌ Authentication error:', error.message)
      return NextResponse.json({
        success: false,
        error: error.message,
        details: {
          code: error.status,
          name: error.name
        }
      }, { status: 401 })
    }

    if (data.user) {
      console.log('✅ Authentication successful for user:', data.user.id)
      
      // Test fetching user profile
      const { data: userProfile, error: profileError } = await supabase
        .from('users')
        .select('*')
        .eq('id', data.user.id)
        .single()

      if (profileError) {
        console.log('⚠️ Profile fetch error:', profileError.message)
      } else {
        console.log('✅ User profile fetched successfully')
      }

      return NextResponse.json({
        success: true,
        message: 'Authentication successful',
        user: {
          id: data.user.id,
          email: data.user.email,
          confirmed_at: data.user.confirmed_at,
          profile: userProfile
        }
      })
    }

    return NextResponse.json({
      success: false,
      error: 'Authentication failed - no user returned'
    }, { status: 401 })

  } catch (error) {
    console.error('❌ Test failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Authentication test endpoint. Use POST with email and password to test login.',
    usage: {
      method: 'POST',
      body: {
        email: '<EMAIL>',
        password: 'password123'
      }
    }
  })
}
