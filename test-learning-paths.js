// Test script to verify Learning Paths functionality
// Run with: node test-learning-paths.js

const { createClient } = require('@supabase/supabase-js')

// Supabase configuration
const supabaseUrl = 'https://ebugbzdstyztfvkhpqbs.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVidWdiemRzdHl6dGZ2a2hwcWJzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1NjA3MzAsImV4cCI6MjA2NTEzNjczMH0.aMHZiuBjytE86YQfbFRQOx4nlKwKgPkvXnBfz0lBS2E'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testLearningPathsCRUD() {
  console.log('🧪 Testing Learning Paths CRUD Operations...\n')

  try {
    // Test 1: Fetch all learning paths
    console.log('1️⃣ Testing: Fetch all learning paths')
    const { data: paths, error: fetchError } = await supabase
      .from('learning_paths')
      .select(`
        *,
        modules:modules(
          *,
          lessons:lessons(*)
        ),
        learner_assignments:learner_assignments(*)
      `)

    if (fetchError) {
      console.error('❌ Error fetching paths:', fetchError)
      return
    }

    console.log(`✅ Successfully fetched ${paths.length} learning paths`)
    paths.forEach(path => {
      console.log(`   - ${path.title} (${path.difficulty_level}, ${path.is_live ? 'Published' : 'Draft'})`)
    })
    console.log()

    // Test 2: Create a new learning path
    console.log('2️⃣ Testing: Create new learning path')
    const newPathData = {
      title: 'Test Path - JavaScript Fundamentals',
      description: 'Learn JavaScript from scratch with hands-on examples',
      objectives: ['Understand JavaScript syntax', 'Learn DOM manipulation', 'Build interactive web pages'],
      prerequisites: ['Basic HTML knowledge'],
      difficulty_level: 'beginner',
      estimated_duration: 4,
      category: 'Technology',
      tags: ['JavaScript', 'Web Development', 'Programming'],
      is_live: false,
      is_featured: false,
      tenant_id: 3,
      created_by: '5cd09c93-eb00-470f-a605-c6d0d057bdd6'
    }

    const { data: newPath, error: createError } = await supabase
      .from('learning_paths')
      .insert(newPathData)
      .select()
      .single()

    if (createError) {
      console.error('❌ Error creating path:', createError)
      return
    }

    console.log(`✅ Successfully created path: ${newPath.title} (ID: ${newPath.id})`)
    console.log()

    // Test 3: Add modules to the path
    console.log('3️⃣ Testing: Add modules to learning path')
    const moduleData = [
      {
        path_id: newPath.id,
        title: 'JavaScript Basics',
        description: 'Introduction to JavaScript syntax and concepts',
        order_index: 0,
        is_optional: false,
        unlock_conditions: {}
      },
      {
        path_id: newPath.id,
        title: 'DOM Manipulation',
        description: 'Learn to interact with HTML elements using JavaScript',
        order_index: 1,
        is_optional: false,
        unlock_conditions: {}
      }
    ]

    const { data: modules, error: moduleError } = await supabase
      .from('modules')
      .insert(moduleData)
      .select()

    if (moduleError) {
      console.error('❌ Error creating modules:', moduleError)
      return
    }

    console.log(`✅ Successfully created ${modules.length} modules`)
    modules.forEach(module => {
      console.log(`   - ${module.title} (Order: ${module.order_index})`)
    })
    console.log()

    // Test 4: Add lessons to the first module
    console.log('4️⃣ Testing: Add lessons to module')
    const lessonData = [
      {
        module_id: modules[0].id,
        title: 'Variables and Data Types',
        description: 'Learn about JavaScript variables and different data types',
        type: 'video',
        content_url: 'https://example.com/js-variables-video',
        content_metadata: { duration: 1200 },
        order_index: 0,
        estimated_duration: 20,
        is_mandatory: true,
        max_attempts: 3
      },
      {
        module_id: modules[0].id,
        title: 'Functions and Scope',
        description: 'Understanding JavaScript functions and variable scope',
        type: 'pdf',
        content_url: 'https://example.com/js-functions.pdf',
        content_metadata: { pages: 15 },
        order_index: 1,
        estimated_duration: 30,
        is_mandatory: true,
        max_attempts: 3
      },
      {
        module_id: modules[0].id,
        title: 'JavaScript Basics Quiz',
        description: 'Test your understanding of JavaScript fundamentals',
        type: 'quiz',
        content_metadata: { questions: 10, time_limit: 15 },
        order_index: 2,
        estimated_duration: 15,
        is_mandatory: true,
        passing_score: 80,
        max_attempts: 3
      }
    ]

    const { data: lessons, error: lessonError } = await supabase
      .from('lessons')
      .insert(lessonData)
      .select()

    if (lessonError) {
      console.error('❌ Error creating lessons:', lessonError)
      return
    }

    console.log(`✅ Successfully created ${lessons.length} lessons`)
    lessons.forEach(lesson => {
      console.log(`   - ${lesson.title} (${lesson.type}, ${lesson.estimated_duration}min)`)
    })
    console.log()

    // Test 5: Create learner assignment
    console.log('5️⃣ Testing: Create learner assignment')
    const assignmentData = {
      path_id: newPath.id,
      learner_id: '5cd09c93-eb00-470f-a605-c6d0d057bdd6',
      assigned_by: '5cd09c93-eb00-470f-a605-c6d0d057bdd6',
      due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
      status: 'assigned',
      completion_percentage: 0
    }

    const { data: assignment, error: assignmentError } = await supabase
      .from('learner_assignments')
      .insert(assignmentData)
      .select()
      .single()

    if (assignmentError) {
      console.error('❌ Error creating assignment:', assignmentError)
      return
    }

    console.log(`✅ Successfully created assignment (ID: ${assignment.id})`)
    console.log(`   - Due: ${new Date(assignment.due_date).toLocaleDateString()}`)
    console.log()

    // Test 6: Update learning path (publish it)
    console.log('6️⃣ Testing: Update learning path (publish)')
    const { data: updatedPath, error: updateError } = await supabase
      .from('learning_paths')
      .update({ 
        is_live: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', newPath.id)
      .select()
      .single()

    if (updateError) {
      console.error('❌ Error updating path:', updateError)
      return
    }

    console.log(`✅ Successfully published path: ${updatedPath.title}`)
    console.log()

    // Test 7: Fetch the complete path with all relationships
    console.log('7️⃣ Testing: Fetch complete path with relationships')
    const { data: completePath, error: completeError } = await supabase
      .from('learning_paths')
      .select(`
        *,
        modules:modules(
          *,
          lessons:lessons(*)
        ),
        learner_assignments:learner_assignments!learner_assignments_path_id_fkey(*),
        created_by_user:users!learning_paths_created_by_fkey(id, full_name, email)
      `)
      .eq('id', newPath.id)
      .single()

    if (completeError) {
      console.error('❌ Error fetching complete path:', completeError)
      return
    }

    console.log(`✅ Successfully fetched complete path: ${completePath.title}`)
    console.log(`   - Modules: ${completePath.modules.length}`)
    console.log(`   - Total Lessons: ${completePath.modules.reduce((total, module) => total + module.lessons.length, 0)}`)
    console.log(`   - Assignments: ${completePath.learner_assignments.length}`)
    console.log(`   - Created by: ${completePath.created_by_user.full_name}`)
    console.log()

    // Test 8: Test filtering and search
    console.log('8️⃣ Testing: Filter and search functionality')
    const { data: filteredPaths, error: filterError } = await supabase
      .from('learning_paths')
      .select('*')
      .eq('difficulty_level', 'beginner')
      .eq('is_live', true)

    if (filterError) {
      console.error('❌ Error filtering paths:', filterError)
      return
    }

    console.log(`✅ Successfully filtered paths: ${filteredPaths.length} beginner-level published paths`)
    console.log()

    // Test 9: Clean up - Delete the test data
    console.log('9️⃣ Testing: Clean up test data')
    
    // Delete lessons first (due to foreign key constraints)
    await supabase.from('lessons').delete().eq('module_id', modules[0].id)
    await supabase.from('lessons').delete().eq('module_id', modules[1].id)
    
    // Delete modules
    await supabase.from('modules').delete().eq('path_id', newPath.id)
    
    // Delete assignments
    await supabase.from('learner_assignments').delete().eq('path_id', newPath.id)
    
    // Delete the path
    const { error: deleteError } = await supabase
      .from('learning_paths')
      .delete()
      .eq('id', newPath.id)

    if (deleteError) {
      console.error('❌ Error deleting test path:', deleteError)
      return
    }

    console.log(`✅ Successfully cleaned up test data`)
    console.log()

    console.log('🎉 All tests completed successfully!')
    console.log('✅ Database schema is working correctly')
    console.log('✅ CRUD operations are functional')
    console.log('✅ Foreign key relationships are properly configured')
    console.log('✅ Data integrity is maintained')

  } catch (error) {
    console.error('💥 Unexpected error during testing:', error)
  }
}

// Run the tests
testLearningPathsCRUD()
