'use client'

import { useState } from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  IconButton,
  Chip,
  TextField,
  InputAdornment,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Avatar,
  useTheme,
} from '@mui/material'
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  MoreVert as MoreVertIcon,
  Preview as PreviewIcon,
  Edit as EditIcon,
  ContentCopy as CopyIcon,
  Delete as DeleteIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  School as SchoolIcon,
  Assessment as AssessmentIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'

interface ReportTemplate {
  id: number
  name: string
  description: string
  category: string
  metrics: string[]
  visualizations: string[]
  isPreBuilt: boolean
  isFavorite: boolean
  usageCount: number
  lastUsed: string
  createdBy: string
  tags: string[]
  estimatedTime: string
}

export default function ReportTemplates() {
  const theme = useTheme()
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null)
  const [previewOpen, setPreviewOpen] = useState(false)

  // Mock data for report templates
  const templates: ReportTemplate[] = [
    {
      id: 1,
      name: 'Learner Progress Report',
      description: 'Comprehensive overview of learner progress across all courses and learning paths',
      category: 'Progress',
      metrics: ['Completion Rate', 'Average Score', 'Time Spent', 'Active Learners'],
      visualizations: ['Bar Chart', 'Line Chart', 'Progress Table'],
      isPreBuilt: true,
      isFavorite: true,
      usageCount: 89,
      lastUsed: '2024-01-15T10:30:00Z',
      createdBy: 'System',
      tags: ['progress', 'completion', 'overview'],
      estimatedTime: '2-3 minutes',
    },
    {
      id: 2,
      name: 'Engagement Analytics Dashboard',
      description: 'Deep dive into learner engagement patterns and activity trends',
      category: 'Engagement',
      metrics: ['Login Frequency', 'Session Duration', 'Content Interactions', 'Discussion Participation'],
      visualizations: ['Heatmap', 'Time Series', 'Engagement Score'],
      isPreBuilt: true,
      isFavorite: false,
      usageCount: 76,
      lastUsed: '2024-01-14T15:45:00Z',
      createdBy: 'System',
      tags: ['engagement', 'activity', 'trends'],
      estimatedTime: '3-4 minutes',
    },
    {
      id: 3,
      name: 'Competency Gap Analysis',
      description: 'Identify skill gaps and competency development opportunities',
      category: 'Competency',
      metrics: ['Skill Mastery Rate', 'Gap Identification', 'Learning Recommendations'],
      visualizations: ['Radar Chart', 'Gap Matrix', 'Skill Tree'],
      isPreBuilt: true,
      isFavorite: true,
      usageCount: 64,
      lastUsed: '2024-01-13T09:15:00Z',
      createdBy: 'System',
      tags: ['competency', 'skills', 'gaps'],
      estimatedTime: '4-5 minutes',
    },
    {
      id: 4,
      name: 'Monthly Training Summary',
      description: 'Executive summary of training activities and outcomes for the month',
      category: 'Summary',
      metrics: ['Total Enrollments', 'Completion Rate', 'Training Hours', 'Cost per Learner'],
      visualizations: ['Executive Dashboard', 'KPI Cards', 'Trend Analysis'],
      isPreBuilt: true,
      isFavorite: false,
      usageCount: 52,
      lastUsed: '2024-01-12T14:20:00Z',
      createdBy: 'System',
      tags: ['summary', 'executive', 'monthly'],
      estimatedTime: '1-2 minutes',
    },
    {
      id: 5,
      name: 'Custom Performance Report',
      description: 'User-created report focusing on specific performance metrics',
      category: 'Performance',
      metrics: ['Quiz Scores', 'Assignment Grades', 'Peer Reviews'],
      visualizations: ['Performance Grid', 'Score Distribution'],
      isPreBuilt: false,
      isFavorite: false,
      usageCount: 23,
      lastUsed: '2024-01-11T11:30:00Z',
      createdBy: 'John Doe',
      tags: ['performance', 'custom', 'scores'],
      estimatedTime: '2-3 minutes',
    },
    {
      id: 6,
      name: 'Department Comparison',
      description: 'Compare training metrics across different departments',
      category: 'Comparison',
      metrics: ['Department Completion', 'Cross-Department Analysis', 'Resource Utilization'],
      visualizations: ['Comparison Charts', 'Department Matrix'],
      isPreBuilt: false,
      isFavorite: true,
      usageCount: 31,
      lastUsed: '2024-01-10T16:45:00Z',
      createdBy: 'Jane Smith',
      tags: ['department', 'comparison', 'analysis'],
      estimatedTime: '3-4 minutes',
    },
  ]

  const categories = [
    { value: 'all', label: 'All Categories', count: templates.length },
    { value: 'Progress', label: 'Progress', count: templates.filter(t => t.category === 'Progress').length },
    { value: 'Engagement', label: 'Engagement', count: templates.filter(t => t.category === 'Engagement').length },
    { value: 'Competency', label: 'Competency', count: templates.filter(t => t.category === 'Competency').length },
    { value: 'Summary', label: 'Summary', count: templates.filter(t => t.category === 'Summary').length },
    { value: 'Performance', label: 'Performance', count: templates.filter(t => t.category === 'Performance').length },
    { value: 'Comparison', label: 'Comparison', count: templates.filter(t => t.category === 'Comparison').length },
  ]

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Progress':
        return <TrendingUpIcon />
      case 'Engagement':
        return <PeopleIcon />
      case 'Competency':
        return <SchoolIcon />
      case 'Summary':
        return <AssessmentIcon />
      case 'Performance':
        return <TrendingUpIcon />
      case 'Comparison':
        return <AssessmentIcon />
      default:
        return <AssessmentIcon />
    }
  }

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, template: ReportTemplate) => {
    setAnchorEl(event.currentTarget)
    setSelectedTemplate(template)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
    setSelectedTemplate(null)
  }

  const handlePreview = (template: ReportTemplate) => {
    setSelectedTemplate(template)
    setPreviewOpen(true)
    handleMenuClose()
  }

  const toggleFavorite = (templateId: number) => {
    // In a real app, this would update the backend
    console.log('Toggle favorite for template:', templateId)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    })
  }

  return (
    <Box>
      {/* Header and Search */}
      <Box mb={3}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6" fontWeight="bold">
            Report Templates
          </Typography>
          <Button variant="contained" startIcon={<EditIcon />}>
            Create Template
          </Button>
        </Box>

        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search templates..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Box display="flex" gap={1} flexWrap="wrap">
              {categories.map((category) => (
                <Chip
                  key={category.value}
                  label={`${category.label} (${category.count})`}
                  variant={selectedCategory === category.value ? 'filled' : 'outlined'}
                  color={selectedCategory === category.value ? 'primary' : 'default'}
                  onClick={() => setSelectedCategory(category.value)}
                  size="small"
                />
              ))}
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Templates Grid */}
      <Grid container spacing={3}>
        {filteredTemplates.map((template, index) => (
          <Grid item xs={12} md={6} lg={4} key={template.id}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  '&:hover': {
                    boxShadow: 4,
                  },
                }}
              >
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
                    <Box display="flex" alignItems="center">
                      <Avatar
                        sx={{
                          bgcolor: template.isPreBuilt ? theme.palette.primary.main : theme.palette.secondary.main,
                          width: 32,
                          height: 32,
                          mr: 1,
                        }}
                      >
                        {getCategoryIcon(template.category)}
                      </Avatar>
                      <Box>
                        <Typography variant="h6" fontWeight="bold" sx={{ fontSize: '1rem' }}>
                          {template.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {template.category} • {template.estimatedTime}
                        </Typography>
                      </Box>
                    </Box>
                    <Box display="flex" alignItems="center">
                      <IconButton
                        size="small"
                        onClick={() => toggleFavorite(template.id)}
                        color={template.isFavorite ? 'warning' : 'default'}
                      >
                        {template.isFavorite ? <StarIcon /> : <StarBorderIcon />}
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={(e) => handleMenuOpen(e, template)}
                      >
                        <MoreVertIcon />
                      </IconButton>
                    </Box>
                  </Box>

                  <Typography variant="body2" color="text.secondary" mb={2}>
                    {template.description}
                  </Typography>

                  <Box mb={2}>
                    <Typography variant="caption" color="text.secondary" gutterBottom>
                      Metrics ({template.metrics.length})
                    </Typography>
                    <Box display="flex" flexWrap="wrap" gap={0.5}>
                      {template.metrics.slice(0, 3).map((metric) => (
                        <Chip
                          key={metric}
                          label={metric}
                          size="small"
                          variant="outlined"
                          sx={{ fontSize: '0.7rem' }}
                        />
                      ))}
                      {template.metrics.length > 3 && (
                        <Chip
                          label={`+${template.metrics.length - 3} more`}
                          size="small"
                          variant="outlined"
                          sx={{ fontSize: '0.7rem' }}
                        />
                      )}
                    </Box>
                  </Box>

                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Used {template.usageCount} times
                      </Typography>
                      <br />
                      <Typography variant="caption" color="text.secondary">
                        Last used {formatDate(template.lastUsed)}
                      </Typography>
                    </Box>
                    {template.isPreBuilt && (
                      <Chip
                        label="Pre-built"
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                    )}
                  </Box>
                </CardContent>

                <CardActions>
                  <Button
                    size="small"
                    startIcon={<PreviewIcon />}
                    onClick={() => handlePreview(template)}
                  >
                    Preview
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={() => console.log('Use template:', template.id)}
                  >
                    Use Template
                  </Button>
                </CardActions>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handlePreview(selectedTemplate!)}>
          <PreviewIcon sx={{ mr: 1 }} />
          Preview
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <EditIcon sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <CopyIcon sx={{ mr: 1 }} />
          Duplicate
        </MenuItem>
        {!selectedTemplate?.isPreBuilt && (
          <MenuItem onClick={handleMenuClose} sx={{ color: 'error.main' }}>
            <DeleteIcon sx={{ mr: 1 }} />
            Delete
          </MenuItem>
        )}
      </Menu>

      {/* Preview Dialog */}
      <Dialog
        open={previewOpen}
        onClose={() => setPreviewOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" alignItems="center">
            {selectedTemplate && getCategoryIcon(selectedTemplate.category)}
            <Typography variant="h6" ml={1}>
              {selectedTemplate?.name}
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            {selectedTemplate?.description}
          </Typography>
          
          <Box mt={2}>
            <Typography variant="subtitle2" gutterBottom>
              Metrics ({selectedTemplate?.metrics.length})
            </Typography>
            <Box display="flex" flexWrap="wrap" gap={1} mb={2}>
              {selectedTemplate?.metrics.map((metric) => (
                <Chip key={metric} label={metric} variant="outlined" />
              ))}
            </Box>
          </Box>

          <Box>
            <Typography variant="subtitle2" gutterBottom>
              Visualizations ({selectedTemplate?.visualizations.length})
            </Typography>
            <Box display="flex" flexWrap="wrap" gap={1}>
              {selectedTemplate?.visualizations.map((viz) => (
                <Chip key={viz} label={viz} variant="outlined" color="secondary" />
              ))}
            </Box>
          </Box>

          <Box mt={2}>
            <Typography variant="body2" color="text.secondary">
              <strong>Category:</strong> {selectedTemplate?.category}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              <strong>Estimated Generation Time:</strong> {selectedTemplate?.estimatedTime}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              <strong>Created by:</strong> {selectedTemplate?.createdBy}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewOpen(false)}>
            Close
          </Button>
          <Button variant="contained">
            Use This Template
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}
