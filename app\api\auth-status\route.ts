import { NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

export async function GET() {
  try {
    console.log('🔍 Checking auth table status...')

    // Check for NULL values in all problematic columns
    const { data: nullCheck, error: nullError } = await supabaseAdmin
      .from('auth.users')
      .select(`
        COUNT(*) as total_users,
        COUNT(confirmation_token) as non_null_confirmation_token,
        COUNT(email_change) as non_null_email_change,
        COUNT(email_change_token_new) as non_null_email_change_token_new,
        COUNT(recovery_token) as non_null_recovery_token
      `)
      .single()

    if (nullError) {
      console.log('❌ Error checking NULL values:', nullError.message)
      return NextResponse.json({
        success: false,
        error: 'Failed to check auth table status',
        details: nullError.message
      }, { status: 500 })
    }

    // Check trigger status
    const { data: triggers, error: triggerError } = await supabaseAdmin
      .rpc('sql', {
        query: `
          SELECT trigger_name, event_manipulation, action_statement 
          FROM information_schema.triggers 
          WHERE event_object_schema = 'auth' 
          AND event_object_table = 'users' 
          AND trigger_name = 'ensure_confirmation_token_not_null'
          ORDER BY trigger_name;
        `
      })

    if (triggerError) {
      console.log('⚠️ Could not check trigger status:', triggerError.message)
    }

    // Calculate NULL counts
    const totalUsers = (nullCheck as any)?.total_users || 0
    const nullCounts = {
      confirmation_token: totalUsers - ((nullCheck as any)?.non_null_confirmation_token || 0),
      email_change: totalUsers - ((nullCheck as any)?.non_null_email_change || 0),
      email_change_token_new: totalUsers - ((nullCheck as any)?.non_null_email_change_token_new || 0),
      recovery_token: totalUsers - ((nullCheck as any)?.non_null_recovery_token || 0)
    }

    const hasNullValues = Object.values(nullCounts).some(count => count > 0)

    console.log('✅ Auth table status check completed')

    return NextResponse.json({
      success: true,
      message: 'Auth table status check completed',
      status: {
        total_users: totalUsers,
        null_values_found: hasNullValues,
        null_counts: nullCounts,
        triggers_active: triggers ? triggers.length > 0 : 'unknown',
        all_columns_fixed: !hasNullValues
      },
      recommendations: hasNullValues ? [
        'NULL values detected in auth columns',
        'Run the fix script to convert NULL values to empty strings',
        'Ensure triggers are properly configured'
      ] : [
        'All auth columns are properly configured',
        'No NULL values detected',
        'Authentication should work without scanning errors'
      ]
    })

  } catch (error) {
    console.error('❌ Status check failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 })
  }
}
