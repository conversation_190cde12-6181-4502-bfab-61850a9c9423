import { supabase } from './supabase'
import { useAuthStore } from './store'

export interface User {
  id: string
  email: string
  full_name?: string
  avatar_url?: string
  tenant_id: number
  role_id: number
}

export interface Tenant {
  id: number
  name: string
  created_at: string
}

export class AuthService {
  static async signIn(email: string, password: string) {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) throw error

      if (data.user) {
        // Fetch user profile and tenant information
        const userProfile = await this.getUserProfile(data.user.id)
        useAuthStore.getState().setUser(userProfile)
        
        if (userProfile?.tenant_id) {
          const tenant = await this.getTenant(userProfile.tenant_id)
          useAuthStore.getState().setTenant(tenant)
        }
      }

      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }

  static async signUp(email: string, password: string, fullName: string, tenantId: number) {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            tenant_id: tenantId,
          },
        },
      })

      if (error) throw error

      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }

  static async signOut() {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error

      useAuthStore.getState().logout()
      return { error: null }
    } catch (error) {
      return { error }
    }
  }

  static async getCurrentUser() {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()
      
      if (error) throw error
      
      if (user) {
        const userProfile = await this.getUserProfile(user.id)
        return userProfile
      }
      
      return null
    } catch (error) {
      console.error('Error getting current user:', error)
      return null
    }
  }

  static async getUserProfile(userId: string): Promise<User | null> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error fetching user profile:', error)
      return null
    }
  }

  static async updateUserProfile(userId: string, updates: Partial<User>) {
    try {
      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', userId)
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }

  static async getTenant(tenantId: number): Promise<Tenant | null> {
    try {
      const { data, error } = await supabase
        .from('tenants')
        .select('*')
        .eq('id', tenantId)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error fetching tenant:', error)
      return null
    }
  }

  static async resetPassword(email: string) {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      })

      if (error) throw error
      return { error: null }
    } catch (error) {
      return { error }
    }
  }

  static async updatePassword(newPassword: string) {
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      })

      if (error) throw error
      return { error: null }
    } catch (error) {
      return { error }
    }
  }

  static async uploadAvatar(userId: string, file: File) {
    try {
      const fileExt = file.name.split('.').pop()
      const fileName = `${userId}.${fileExt}`
      const filePath = `avatars/${fileName}`

      const { error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(filePath, file, { upsert: true })

      if (uploadError) throw uploadError

      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(filePath)

      // Update user profile with new avatar URL
      await this.updateUserProfile(userId, { avatar_url: publicUrl })

      return { url: publicUrl, error: null }
    } catch (error) {
      return { url: null, error }
    }
  }

  static onAuthStateChange(callback: (user: User | null) => void) {
    return supabase.auth.onAuthStateChange(async (event, session) => {
      if (session?.user) {
        const userProfile = await this.getUserProfile(session.user.id)
        callback(userProfile)
      } else {
        callback(null)
      }
    })
  }
}

// Permission checking utilities
export const hasPermission = (userPermissions: string[], requiredPermission: string): boolean => {
  return userPermissions.includes(requiredPermission) || userPermissions.includes('admin')
}

export const checkPermissions = (requiredPermissions: string[], userPermissions: string[]): boolean => {
  return requiredPermissions.every(permission => hasPermission(userPermissions, permission))
}

// Common permissions
export const PERMISSIONS = {
  // Dashboard
  VIEW_DASHBOARD: 'view_dashboard',
  
  // Learning Paths
  VIEW_LEARNING_PATHS: 'view_learning_paths',
  CREATE_LEARNING_PATHS: 'create_learning_paths',
  EDIT_LEARNING_PATHS: 'edit_learning_paths',
  DELETE_LEARNING_PATHS: 'delete_learning_paths',
  ASSIGN_LEARNING_PATHS: 'assign_learning_paths',
  
  // Learners
  VIEW_LEARNERS: 'view_learners',
  CREATE_LEARNERS: 'create_learners',
  EDIT_LEARNERS: 'edit_learners',
  DELETE_LEARNERS: 'delete_learners',
  
  // Content
  VIEW_CONTENT: 'view_content',
  UPLOAD_CONTENT: 'upload_content',
  EDIT_CONTENT: 'edit_content',
  DELETE_CONTENT: 'delete_content',
  
  // Reports
  VIEW_REPORTS: 'view_reports',
  EXPORT_REPORTS: 'export_reports',
  
  // Settings
  VIEW_SETTINGS: 'view_settings',
  EDIT_SETTINGS: 'edit_settings',
  MANAGE_ROLES: 'manage_roles',
  
  // Admin
  ADMIN: 'admin',
} as const
