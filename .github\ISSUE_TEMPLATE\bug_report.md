---
name: Bug report
about: Create a report to help us improve
title: '[BUG] '
labels: 'bug'
assignees: ''
---

## 🐛 Bug Description
A clear and concise description of what the bug is.

## 🔄 Steps to Reproduce
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## ✅ Expected Behavior
A clear and concise description of what you expected to happen.

## ❌ Actual Behavior
A clear and concise description of what actually happened.

## 📸 Screenshots
If applicable, add screenshots to help explain your problem.

## 🖥️ Environment
- **OS**: [e.g. Windows 11, macOS 13, Ubuntu 22.04]
- **Browser**: [e.g. Chrome 118, Firefox 119, Safari 17]
- **Node.js Version**: [e.g. 18.17.0]
- **Next.js Version**: [e.g. 14.2.15]
- **Device**: [e.g. Desktop, Mobile, Tablet]

## 📋 Additional Context
Add any other context about the problem here.

## 🔍 Console Errors
If applicable, paste any console errors or logs here:

```
Paste console errors here
```

## 🧪 Reproducible Demo
If possible, provide a link to a minimal reproduction of the issue:
- CodeSandbox link
- GitHub repository
- Live demo URL

## 📝 Possible Solution
If you have ideas on how to fix this bug, please describe them here.

## 🏷️ Related Issues
Link any related issues here using #issue_number
