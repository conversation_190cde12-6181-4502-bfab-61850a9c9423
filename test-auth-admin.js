// Test script to verify Auth Admin API directly
// Run with: node test-auth-admin.js

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://ebugbzdstyztfvkhpqbs.supabase.co'
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVidWdiemRzdHl6dGZ2a2hwcWJzIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTU2MDczMCwiZXhwIjoyMDY1MTM2NzMwfQ.lVkWAUL6ID5_ziqggFfac3XyQqGcWnjKh9ONo_f1c9Q'

const supabaseAdmin = createClient(supabaseUrl, serviceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Generate a temporary password
function generateTemporaryPassword() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
  let password = ''
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return password
}

async function testAuthAdmin() {
  console.log('🧪 Testing Auth Admin API directly...\n')

  const testEmail = `test-${Date.now()}@example.com`
  const testName = 'Test User'
  const tempPassword = generateTemporaryPassword()

  try {
    console.log('1. Testing Auth Admin API...')
    console.log('   Email:', testEmail)
    console.log('   Password:', tempPassword)

    // Test the Auth Admin API
    const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email: testEmail,
      password: tempPassword,
      email_confirm: true,
      user_metadata: {
        full_name: testName
      }
    })

    if (authError) {
      console.log('❌ Auth user creation failed:')
      console.log('   Message:', authError.message)
      console.log('   Code:', authError.code)
      console.log('   Status:', authError.status)
      console.log('   Full error:', JSON.stringify(authError, null, 2))
      return
    }

    if (!authUser.user) {
      console.log('❌ No user returned from auth creation')
      return
    }

    console.log('✅ Auth user created successfully')
    console.log('   User ID:', authUser.user.id)
    console.log('   Email:', authUser.user.email)

    // Wait for trigger to create profile
    console.log('\n2. Waiting for trigger to create profile...')
    await new Promise(resolve => setTimeout(resolve, 2000))

    // Check if profile was created
    console.log('3. Checking profile creation...')
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', authUser.user.id)
      .single()

    if (profileError) {
      console.log('❌ Profile check failed:', profileError.message)
      console.log('   Code:', profileError.code)
      console.log('   Details:', profileError.details)
      console.log('   Hint:', profileError.hint)
    } else {
      console.log('✅ Profile created successfully')
      console.log('   Profile ID:', profile.id)
      console.log('   Tenant ID:', profile.tenant_id)
      console.log('   Role ID:', profile.role_id)
      console.log('   Email:', profile.email)
      console.log('   Full Name:', profile.full_name)
    }

    // Clean up test user
    console.log('\n4. Cleaning up test user...')
    await supabaseAdmin.auth.admin.deleteUser(authUser.user.id)
    console.log('✅ Test user cleaned up')

    console.log('\n🎉 Test completed successfully!')

  } catch (error) {
    console.error('❌ Test failed with error:', error.message)
    console.error('   Stack:', error.stack)
  }
}

// Run the test
testAuthAdmin()
