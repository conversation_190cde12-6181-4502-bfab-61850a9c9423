# Troubleshooting Guide

## 🚨 Common Issues & Solutions

This guide covers the most frequently encountered issues during development and deployment of the ZenithLearn AI Learner Dashboard.

## 🔧 Development Issues

### 1. Environment Setup Problems

#### Issue: "Module not found" errors
```bash
Error: Cannot resolve module '@/app/components/dashboard/PersonalizedWelcome'
```

**Solution:**
```bash
# Check TypeScript configuration
cat tsconfig.json

# Verify path mapping
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./app/*"]
    }
  }
}

# Clear Next.js cache
rm -rf .next
npm run dev
```

#### Issue: Supabase connection errors
```bash
Error: Invalid API key or URL
```

**Solution:**
```bash
# Verify environment variables
echo $NEXT_PUBLIC_SUPABASE_URL
echo $NEXT_PUBLIC_SUPABASE_ANON_KEY

# Check .env.local file
cat .env.local

# Test Supabase connection
npx supabase status
```

### 2. Material-UI Icon Issues

#### Issue: "LocalFire is not defined" or similar icon errors
```bash
ReferenceError: LocalFire is not defined
```

**Solution:**
```typescript
// Check icon imports - some icons don't exist
// ❌ Wrong
import { LocalFire } from '@mui/icons-material';

// ✅ Correct
import { Whatshot as FireIcon } from '@mui/icons-material';

// Verify icon exists in Material-UI documentation
// https://mui.com/material-ui/material-icons/
```

### 3. TypeScript Errors

#### Issue: Type errors in dashboard components
```bash
Type 'undefined' is not assignable to type 'DashboardData'
```

**Solution:**
```typescript
// Add proper type guards
interface DashboardComponentProps {
  dashboardData?: DashboardData; // Make optional
}

const Component = ({ dashboardData }: DashboardComponentProps) => {
  if (!dashboardData) {
    return <LoadingSkeleton />;
  }
  
  // Safe to use dashboardData here
  return <div>{dashboardData.enrolledCourses}</div>;
};
```

## 🗄️ Database Issues

### 1. Migration Failures

#### Issue: RLS policy conflicts
```sql
ERROR: policy "Users can view their own data" already exists
```

**Solution:**
```sql
-- Drop existing policies first
DROP POLICY IF EXISTS "Users can view their own data" ON ai_insights;

-- Then create new policy
CREATE POLICY "Users can view their own insights" ON ai_insights
  FOR SELECT USING (auth.uid() = user_id);
```

#### Issue: Foreign key constraint violations
```sql
ERROR: insert or update on table violates foreign key constraint
```

**Solution:**
```sql
-- Check if referenced user exists
SELECT id FROM auth.users WHERE id = 'user-uuid';

-- Verify tenant_id is correct
SELECT tenant_id FROM user_profiles WHERE user_id = 'user-uuid';

-- Use proper user context in queries
INSERT INTO ai_insights (user_id, tenant_id, ...)
VALUES (auth.uid(), (SELECT tenant_id FROM user_profiles WHERE user_id = auth.uid()), ...);
```

### 2. Real-time Subscription Issues

#### Issue: Real-time updates not working
```typescript
// Subscriptions not receiving updates
```

**Solution:**
```typescript
// Check RLS policies allow real-time access
// Verify subscription setup
const subscription = supabase
  .channel('dashboard_updates')
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'daily_challenges',
    filter: `user_id=eq.${user.id}` // Ensure filter is correct
  }, (payload) => {
    console.log('Received update:', payload);
  })
  .subscribe((status) => {
    console.log('Subscription status:', status);
  });

// Clean up subscription
return () => subscription.unsubscribe();
```

## 🚀 Deployment Issues

### 1. Vercel Build Failures

#### Issue: Build timeout or memory errors
```bash
Error: Command "npm run build" exited with 137
```

**Solution:**
```json
// vercel.json
{
  "functions": {
    "app/**/*.ts": {
      "maxDuration": 60
    }
  },
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/node",
      "config": {
        "maxLambdaSize": "50mb"
      }
    }
  ]
}
```

#### Issue: Environment variables not available
```bash
Error: NEXT_PUBLIC_SUPABASE_URL is not defined
```

**Solution:**
```bash
# Add environment variables in Vercel dashboard
# Settings > Environment Variables

# Or use Vercel CLI
vercel env add NEXT_PUBLIC_SUPABASE_URL
vercel env add NEXT_PUBLIC_SUPABASE_ANON_KEY
vercel env add SUPABASE_SERVICE_ROLE_KEY

# Redeploy after adding variables
vercel --prod
```

### 2. API Route Issues

#### Issue: API routes returning 500 errors
```bash
Internal Server Error
```

**Solution:**
```typescript
// Add proper error handling
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    
    if (!userId) {
      return Response.json(
        { error: 'User ID is required' }, 
        { status: 400 }
      );
    }
    
    // Your logic here
    
  } catch (error) {
    console.error('API Error:', error);
    return Response.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}
```

### 3. Authentication Issues

#### Issue: Users can't log in after deployment
```bash
Error: Invalid redirect URL
```

**Solution:**
```sql
-- Update Supabase Auth settings
UPDATE auth.config SET 
  site_url = 'https://your-domain.com',
  additional_redirect_urls = 'https://your-domain.com/auth/callback';

-- Add redirect URLs in Supabase dashboard
-- Authentication > URL Configuration
-- Site URL: https://your-domain.com
-- Redirect URLs: https://your-domain.com/auth/callback
```

## 🎨 UI/UX Issues

### 1. Responsive Design Problems

#### Issue: Components not responsive on mobile
```css
/* Components overflow on small screens */
```

**Solution:**
```typescript
// Use Material-UI breakpoints
const theme = useTheme();
const isMobile = useMediaQuery(theme.breakpoints.down('md'));

return (
  <Grid container spacing={isMobile ? 1 : 3}>
    <Grid item xs={12} md={6}>
      <Component />
    </Grid>
  </Grid>
);
```

### 2. Performance Issues

#### Issue: Slow dashboard loading
```bash
Dashboard takes >3 seconds to load
```

**Solution:**
```typescript
// Implement lazy loading
const GamifiedJourney = lazy(() => import('./creative/GamifiedJourney'));

// Add loading states
if (isLoading) {
  return <DashboardSkeleton />;
}

// Optimize queries
const { data } = useQuery({
  queryKey: ['dashboard', user?.id],
  queryFn: fetchDashboardData,
  staleTime: 5 * 60 * 1000, // 5 minutes
  select: (data) => ({
    // Only select needed fields
    enrolledCourses: data.enrolledCourses,
    progress: data.progress,
  }),
});
```

## 🔍 Debugging Tools

### 1. Development Debugging

#### React Developer Tools
```bash
# Install React DevTools browser extension
# Check component props and state
# Monitor re-renders and performance
```

#### Supabase Debugging
```typescript
// Enable debug mode
const supabase = createClient(url, key, {
  auth: {
    debug: process.env.NODE_ENV === 'development',
  },
});

// Log SQL queries
const { data, error } = await supabase
  .from('ai_insights')
  .select('*')
  .explain({ analyze: true, verbose: true });
```

### 2. Production Debugging

#### Vercel Function Logs
```bash
# View function logs
vercel logs

# Real-time logs
vercel logs --follow

# Filter by function
vercel logs --filter="api/dashboard"
```

#### Error Monitoring
```typescript
// Add error boundary
class ErrorBoundary extends React.Component {
  componentDidCatch(error, errorInfo) {
    // Send to error tracking service
    console.error('Dashboard error:', error, errorInfo);
  }
}

// Wrap dashboard in error boundary
<ErrorBoundary>
  <Dashboard />
</ErrorBoundary>
```

## 📊 Performance Monitoring

### 1. Core Web Vitals

#### Monitor Performance
```typescript
// Add performance monitoring
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

getCLS(console.log);
getFID(console.log);
getFCP(console.log);
getLCP(console.log);
getTTFB(console.log);
```

#### Optimize Bundle Size
```bash
# Analyze bundle
npm run build
npx @next/bundle-analyzer

# Check for large dependencies
npm ls --depth=0 --long
```

### 2. Database Performance

#### Query Optimization
```sql
-- Add indexes for common queries
CREATE INDEX CONCURRENTLY idx_ai_insights_user_created 
ON ai_insights(user_id, created_at DESC);

-- Analyze slow queries
EXPLAIN ANALYZE SELECT * FROM daily_challenges 
WHERE user_id = $1 AND status = 'active';
```

## 🆘 Getting Help

### 1. Community Resources
- **GitHub Issues** - Report bugs and feature requests
- **Discord Community** - Real-time help and discussions
- **Stack Overflow** - Technical questions with `zenithlearn` tag

### 2. Support Channels
- **Documentation** - Check all documentation sections
- **Email Support** - <EMAIL>
- **Video Calls** - Schedule with development team

### 3. Escalation Process
1. **Check Documentation** - Review relevant guides
2. **Search Issues** - Look for similar problems
3. **Create Minimal Reproduction** - Isolate the issue
4. **Contact Support** - Provide detailed information

## 📋 Issue Reporting Template

When reporting issues, please include:

```markdown
## Issue Description
Brief description of the problem

## Steps to Reproduce
1. Step one
2. Step two
3. Step three

## Expected Behavior
What should happen

## Actual Behavior
What actually happens

## Environment
- OS: [e.g., macOS 12.0]
- Browser: [e.g., Chrome 96.0]
- Node.js: [e.g., 18.17.0]
- Next.js: [e.g., 14.0.0]

## Additional Context
Screenshots, error messages, logs, etc.
```

---

**Need More Help?** Contact our support team or check the [Developer Guide](../developer-guide.md) for advanced troubleshooting techniques.
