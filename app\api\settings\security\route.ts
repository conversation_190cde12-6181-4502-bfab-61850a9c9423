import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { useAuthStore } from '@/lib/store'
import { SecuritySettings, SettingsResponse } from '@/lib/types/settings'

// Helper function to get current user context
const getCurrentUserContext = () => {
  const { user, tenant } = useAuthStore.getState()

  // Development fallback - remove in production
  if (!user || !tenant) {
    console.warn('No authenticated user found, using development fallback')
    return {
      user: {
        id: '5cd09c93-eb00-470f-a605-c6d0d057bdd6',
        email: '<EMAIL>',
        tenant_id: 3,
        role_id: 1
      },
      tenant: {
        id: 3,
        name: 'Development Tenant'
      }
    }
  }

  // Ensure tenant ID is a number
  const tenantId = typeof tenant.id === 'string' ? parseInt(tenant.id, 10) : tenant.id

  if (isNaN(tenantId)) {
    throw new Error('Invalid tenant ID: must be a valid integer')
  }

  return {
    user: {
      ...user,
      tenant_id: tenantId
    },
    tenant: {
      ...tenant,
      id: tenantId
    }
  }
}

// GET /api/settings/security
export async function GET(request: NextRequest) {
  try {
    const { tenant } = getCurrentUserContext()

    const { data, error } = await supabaseAdmin
      .from('tenant_settings')
      .select('*')
      .eq('tenant_id', tenant.id)
      .eq('category', 'security')
      .single()

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching security settings:', error)
      throw error
    }

    const settings = data?.settings || {
      auth_methods: [
        { provider: 'email', enabled: true },
        { provider: 'google', enabled: false },
        { provider: 'azure', enabled: false }
      ],
      mfa_required: false,
      session_timeout: 480, // 8 hours in minutes
      password_policy: {
        min_length: 8,
        require_uppercase: true,
        require_lowercase: true,
        require_numbers: true,
        require_symbols: false,
        max_age_days: 90
      },
      ip_whitelist: [],
      log_retention: 90,
      data_retention: 2555 // 7 years in days
    }

    return NextResponse.json({
      success: true,
      data: settings
    })

  } catch (error) {
    console.error('Failed to get security settings:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 })
  }
}

// PUT /api/settings/security
export async function PUT(request: NextRequest) {
  try {
    const { user, tenant } = getCurrentUserContext()
    const settings: Partial<SecuritySettings> = await request.json()

    const { data, error } = await supabaseAdmin
      .from('tenant_settings')
      .upsert({
        tenant_id: tenant.id,
        category: 'security',
        settings,
        updated_by: user.id,
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error('Error updating security settings:', error)
      throw error
    }

    // Log the change
    await supabaseAdmin
      .from('settings_audit')
      .insert({
        tenant_id: tenant.id,
        category: 'security',
        action: 'update',
        old_value: null,
        new_value: settings,
        admin_id: user.id,
        timestamp: new Date().toISOString()
      })

    const response: SettingsResponse<SecuritySettings> = {
      data: data.settings,
      success: true,
      message: 'Security settings updated successfully'
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Failed to update security settings:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 })
  }
}
