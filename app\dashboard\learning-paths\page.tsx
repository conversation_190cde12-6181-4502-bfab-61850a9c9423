'use client'

import React, { useState } from 'react'
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Grid,
  Card,
  CardContent,
  CardActions,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Fab,
  Skeleton,
  Alert
} from '@mui/material'
import {
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ContentCopy as CopyIcon,
  Publish as PublishIcon,
  Visibility as ViewIcon,
  FilterList as FilterIcon,
  Search as SearchIcon
} from '@mui/icons-material'
import { useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'

import { 
  useLearningPaths, 
  useDeleteLearningPath, 
  useDuplicateLearningPath,
  usePublishLearningPath 
} from '@/lib/hooks/useLearningPaths'
import { useLearningPathsStore } from '@/lib/store'
import { LearningPath } from '@/lib/types/learning-paths'
import PathFilters from '@/components/learning-paths/PathFilters'
import PathCard from '@/components/learning-paths/PathCard'
import OptimizedPathList from '@/components/learning-paths/OptimizedPathList'

export default function LearningPathsPage() {
  const router = useRouter()
  const { filters, setFilters } = useLearningPathsStore()
  
  const [page, setPage] = useState(1)
  const [showFilters, setShowFilters] = useState(false)
  const [selectedPath, setSelectedPath] = useState<LearningPath | null>(null)
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [duplicateDialogOpen, setDuplicateDialogOpen] = useState(false)
  const [duplicateTitle, setDuplicateTitle] = useState('')

  // API hooks
  const { data: pathsData, isLoading, error } = useLearningPaths(filters, undefined, page, 12)
  const deleteMutation = useDeleteLearningPath()
  const duplicateMutation = useDuplicateLearningPath()
  const publishMutation = usePublishLearningPath()

  const handleCreatePath = () => {
    router.push('/dashboard/learning-paths/create')
  }

  const handleViewPath = (path: LearningPath) => {
    router.push(`/dashboard/learning-paths/${path.id}`)
  }

  const handleEditPath = (path: LearningPath) => {
    router.push(`/dashboard/learning-paths/${path.id}/edit`)
  }

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, path: LearningPath) => {
    event.stopPropagation()
    setMenuAnchor(event.currentTarget)
    setSelectedPath(path)
  }

  const handleMenuClose = () => {
    setMenuAnchor(null)
    setSelectedPath(null)
  }

  const handleDeleteClick = () => {
    setDeleteDialogOpen(true)
    handleMenuClose()
  }

  const handleDeleteConfirm = () => {
    if (selectedPath) {
      deleteMutation.mutate(selectedPath.id.toString())
      setDeleteDialogOpen(false)
      setSelectedPath(null)
    }
  }

  const handleDuplicateClick = () => {
    if (selectedPath) {
      setDuplicateTitle(`${selectedPath.title} (Copy)`)
      setDuplicateDialogOpen(true)
    }
    handleMenuClose()
  }

  const handleDuplicateConfirm = () => {
    if (selectedPath && duplicateTitle.trim()) {
      duplicateMutation.mutate({
        id: selectedPath.id.toString(),
        newTitle: duplicateTitle.trim()
      })
      setDuplicateDialogOpen(false)
      setDuplicateTitle('')
      setSelectedPath(null)
    }
  }

  const handlePublishClick = () => {
    if (selectedPath) {
      console.log('Publishing path:', selectedPath.id, selectedPath.title)
      publishMutation.mutate(selectedPath.id.toString())
    }
    handleMenuClose()
  }

  const handleSearchChange = (search: string) => {
    setFilters({ search })
    setPage(1)
  }

  if (error) {
    return (
      <Box p={3}>
        <Alert severity="error">
          Failed to load learning paths. Please try again.
        </Alert>
      </Box>
    )
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" fontWeight="bold" gutterBottom>
            Learning Paths
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Create and manage AI-powered learning experiences
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreatePath}
          size="large"
        >
          Create Path
        </Button>
      </Box>

      {/* Search and Filters */}
      <Box mb={3}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search learning paths..."
              value={filters.search}
              onChange={(e) => handleSearchChange(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Box display="flex" justifyContent="flex-end">
              <Button
                startIcon={<FilterIcon />}
                onClick={() => setShowFilters(!showFilters)}
                variant={showFilters ? 'contained' : 'outlined'}
              >
                Filters
              </Button>
            </Box>
          </Grid>
        </Grid>

        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Box mt={2}>
                <PathFilters />
              </Box>
            </motion.div>
          )}
        </AnimatePresence>
      </Box>

      {/* Learning Paths Grid */}
      <Grid container spacing={3}>
        {isLoading ? (
          // Loading skeletons
          Array.from({ length: 6 }).map((_, index) => (
            <Grid item xs={12} sm={6} lg={4} key={index}>
              <Card>
                <CardContent>
                  <Skeleton variant="text" width="60%" height={32} />
                  <Skeleton variant="text" width="100%" height={20} sx={{ mt: 1 }} />
                  <Skeleton variant="text" width="80%" height={20} />
                  <Box display="flex" gap={1} mt={2}>
                    <Skeleton variant="rectangular" width={60} height={24} />
                    <Skeleton variant="rectangular" width={80} height={24} />
                  </Box>
                </CardContent>
                <CardActions>
                  <Skeleton variant="rectangular" width={80} height={36} />
                  <Skeleton variant="rectangular" width={80} height={36} />
                </CardActions>
              </Card>
            </Grid>
          ))
        ) : pathsData?.data.length === 0 ? (
          // Empty state
          <Grid item xs={12}>
            <Box
              display="flex"
              flexDirection="column"
              alignItems="center"
              justifyContent="center"
              py={8}
              textAlign="center"
            >
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No learning paths found
              </Typography>
              <Typography variant="body2" color="text.secondary" mb={3}>
                {filters.search || filters.status !== 'all' || filters.category !== 'all'
                  ? 'Try adjusting your filters or search terms'
                  : 'Get started by creating your first learning path'}
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleCreatePath}
              >
                Create Your First Path
              </Button>
            </Box>
          </Grid>
        ) : (
          // Learning paths
          pathsData?.data.map((path) => (
            <Grid item xs={12} sm={6} lg={4} key={path.id}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <PathCard
                  path={path}
                  onView={() => handleViewPath(path)}
                  onEdit={() => handleEditPath(path)}
                  onMenuClick={(event) => handleMenuOpen(event, path)}
                />
              </motion.div>
            </Grid>
          ))
        )}
      </Grid>

      {/* Floating Action Button for Mobile */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
          display: { xs: 'flex', md: 'none' }
        }}
        onClick={handleCreatePath}
      >
        <AddIcon />
      </Fab>

      {/* Context Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => selectedPath && handleViewPath(selectedPath)}>
          <ViewIcon sx={{ mr: 1 }} />
          View
        </MenuItem>
        <MenuItem onClick={() => selectedPath && handleEditPath(selectedPath)}>
          <EditIcon sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem onClick={handleDuplicateClick}>
          <CopyIcon sx={{ mr: 1 }} />
          Duplicate
        </MenuItem>
        {selectedPath?.status === 'draft' && (
          <MenuItem onClick={handlePublishClick}>
            <PublishIcon sx={{ mr: 1 }} />
            Publish
          </MenuItem>
        )}
        <MenuItem onClick={handleDeleteClick} sx={{ color: 'error.main' }}>
          <DeleteIcon sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete Learning Path</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete "{selectedPath?.title}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleDeleteConfirm} 
            color="error" 
            variant="contained"
            disabled={deleteMutation.isPending}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Duplicate Dialog */}
      <Dialog open={duplicateDialogOpen} onClose={() => setDuplicateDialogOpen(false)}>
        <DialogTitle>Duplicate Learning Path</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="New Path Title"
            fullWidth
            variant="outlined"
            value={duplicateTitle}
            onChange={(e) => setDuplicateTitle(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDuplicateDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleDuplicateConfirm} 
            variant="contained"
            disabled={!duplicateTitle.trim() || duplicateMutation.isPending}
          >
            Duplicate
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}
