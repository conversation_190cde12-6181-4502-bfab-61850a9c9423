// Settings Types for ZenithLearn AI Admin Settings Page

// General Settings
export interface GeneralSettings {
  platform_name: string
  tagline: string
  timezone: string
  language: string
  currency: string
  maintenance_mode: boolean
  maintenance_message: string
  scheduled_start?: string
  scheduled_end?: string
}

// Branding Settings
export interface BrandingSettings {
  logo_url?: string
  primary_color: string
  secondary_color: string
  font_family: string
  theme_name: string
  custom_css?: string
  email_templates: EmailTemplate[]
}

export interface EmailTemplate {
  id: string
  type: 'welcome' | 'reset_password' | 'notification' | 'custom'
  name: string
  subject: string
  template_html: string
  variables: string[]
}

// Security Settings
export interface SecuritySettings {
  auth_methods: AuthMethod[]
  mfa_required: boolean
  session_timeout: number
  password_policy: PasswordPolicy
  ip_whitelist: string[]
  log_retention: number
  data_retention: number
}

export interface AuthMethod {
  provider: 'email' | 'google' | 'azure' | 'github' | 'saml'
  enabled: boolean
  config?: Record<string, any>
}

export interface PasswordPolicy {
  min_length: number
  require_uppercase: boolean
  require_lowercase: boolean
  require_numbers: boolean
  require_symbols: boolean
  max_age_days: number
}

// Role Management
export interface Role {
  id: string
  name: string
  description: string
  permissions: Permission[]
  user_count: number
  is_default: boolean
  created_at: string
  updated_at: string
}

export interface Permission {
  key: string
  name: string
  description: string
  category: string
}

// Feature Toggles
export interface FeatureFlag {
  key: string
  name: string
  description: string
  enabled: boolean
  config?: Record<string, any>
  category: string
}

// AI & Automation Settings
export interface AISettings {
  model: string
  confidence_threshold: number
  auto_tagging: boolean
  content_moderation: boolean
  recommendation_engine: boolean
  chat_assistant: boolean
}

export interface AutomationRule {
  id: string
  name: string
  description: string
  trigger: AutomationTrigger
  conditions: AutomationCondition[]
  actions: AutomationAction[]
  enabled: boolean
  created_at: string
}

export interface AutomationTrigger {
  type: 'user_signup' | 'course_completion' | 'inactivity' | 'schedule'
  config: Record<string, any>
}

export interface AutomationCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains'
  value: any
}

export interface AutomationAction {
  type: 'send_email' | 'assign_path' | 'update_status' | 'webhook'
  config: Record<string, any>
}

// Integration Settings
export interface APIKey {
  id: string
  name: string
  key: string
  permissions: string[]
  last_used?: string
  created_at: string
  expires_at?: string
}

export interface Webhook {
  id: string
  name: string
  url: string
  events: string[]
  secret?: string
  enabled: boolean
  last_triggered?: string
  created_at: string
}

// Notification Settings
export interface NotificationSettings {
  channels: NotificationChannel[]
  templates: NotificationTemplate[]
  escalation_policies: EscalationPolicy[]
}

export interface NotificationChannel {
  type: 'email' | 'sms' | 'push' | 'webhook'
  enabled: boolean
  config: Record<string, any>
}

export interface NotificationTemplate {
  id: string
  name: string
  channel: string
  template: string
  variables: string[]
}

export interface EscalationPolicy {
  id: string
  name: string
  steps: EscalationStep[]
  enabled: boolean
}

export interface EscalationStep {
  delay_minutes: number
  recipients: string[]
  action: 'email' | 'sms' | 'call'
}

// Analytics Settings
export interface AnalyticsSettings {
  retention_days: number
  export_formats: string[]
  scheduled_reports: ScheduledReport[]
  custom_metrics: CustomMetric[]
}

export interface ScheduledReport {
  id: string
  name: string
  type: string
  schedule: string
  recipients: string[]
  enabled: boolean
}

export interface CustomMetric {
  id: string
  name: string
  query: string
  chart_type: string
  enabled: boolean
}

// Compliance Settings
export interface ComplianceSettings {
  gdpr_enabled: boolean
  data_processing_agreement: string
  privacy_policy_url: string
  terms_of_service_url: string
  cookie_consent: boolean
  audit_logging: boolean
  data_export_enabled: boolean
  data_deletion_enabled: boolean
}

// Combined Settings Interface
export interface TenantSettings {
  tenant_id: string
  general: GeneralSettings
  branding: BrandingSettings
  security: SecuritySettings
  features: FeatureFlag[]
  ai: AISettings
  notifications: NotificationSettings
  analytics: AnalyticsSettings
  compliance: ComplianceSettings
  updated_at: string
  updated_by: string
}

// API Response Types
export interface SettingsResponse<T> {
  data: T
  success: boolean
  message?: string
}

export interface SettingsUpdateRequest<T> {
  settings: Partial<T>
  validate_only?: boolean
}

// Form Types
export interface SettingsFormData {
  [key: string]: any
}

// Audit Log
export interface SettingsAuditLog {
  id: string
  tenant_id: string
  admin_id: string
  admin_name: string
  setting_key: string
  old_value: any
  new_value: any
  action: 'create' | 'update' | 'delete'
  timestamp: string
  ip_address: string
}

// Feature Flag Configuration
export interface FeatureFlagConfig {
  enable_multi_language: boolean
  restrict_timezone: boolean
  allow_maintenance_scheduling: boolean
  enable_custom_fonts: boolean
  restrict_color_palette: boolean
  allow_template_editing: boolean
  enable_mfa: boolean
  restrict_sso: boolean
  enable_retention_customization: boolean
  enable_custom_roles: boolean
  restrict_permission_scope: boolean
  enable_feature_management: boolean
  enable_sub_tenants: boolean
  enable_custom_workflows: boolean
  enable_ai_themes: boolean
  enable_custom_widgets: boolean
  enable_compliance_dashboard: boolean
  enable_ip_whitelisting: boolean
  enable_activity_monitor: boolean
  enable_temp_tokens: boolean
  enable_gamification: boolean
  enable_moderation: boolean
  enable_ai_customization: boolean
  enable_automation: boolean
  enable_api_keys: boolean
  enable_webhooks: boolean
  enable_sms_notifications: boolean
  enable_escalations: boolean
  enable_usage_analytics: boolean
  enable_audit_viewer: boolean
}
