import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // Get the authenticated user
    const {
      data: { user },
      error: userError,
    } = await supabaseClient.auth.getUser()

    if (userError || !user) {
      throw new Error('Unauthorized')
    }

    // Get user's tenant_id
    const { data: userData, error: userDataError } = await supabaseClient
      .from('users')
      .select('tenant_id')
      .eq('id', user.id)
      .single()

    if (userDataError || !userData) {
      throw new Error('User data not found')
    }

    const tenantId = userData.tenant_id

    // Get request body
    const { competency_id, path_id, batch_mode } = await req.json()

    if (competency_id && path_id) {
      // Analyze specific competency-path mapping
      const suggestion = await analyzeMappingSuggestion(supabaseClient, tenantId, competency_id, path_id)
      return new Response(
        JSON.stringify({ suggestion }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Run batch analysis for all unmapped combinations
    const suggestions = await generateBatchSuggestions(supabaseClient, tenantId)

    return new Response(
      JSON.stringify({ 
        suggestions,
        total_suggestions: suggestions.length,
        high_confidence: suggestions.filter(s => s.confidence_score >= 0.8).length,
        medium_confidence: suggestions.filter(s => s.confidence_score >= 0.6 && s.confidence_score < 0.8).length
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error in suggest-competency-mappings function:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})

async function analyzeMappingSuggestion(supabaseClient: any, tenantId: number, competencyId: number, pathId: number) {
  // Get competency details
  const { data: competency } = await supabaseClient
    .from('competencies')
    .select('name, description, category, tags')
    .eq('id', competencyId)
    .eq('tenant_id', tenantId)
    .single()

  // Get learning path details
  const { data: path } = await supabaseClient
    .from('learning_paths')
    .select('title, description, category, tags, objectives')
    .eq('id', pathId)
    .eq('tenant_id', tenantId)
    .single()

  if (!competency || !path) {
    throw new Error('Competency or learning path not found')
  }

  // Calculate similarity score based on various factors
  const similarity = calculateSimilarity(competency, path)
  
  // Generate reasoning
  const reasoning = generateReasoning(competency, path, similarity)

  const suggestion = {
    competency_id: competencyId,
    path_id: pathId,
    competency_name: competency.name,
    path_title: path.title,
    confidence_score: similarity.overall,
    reasoning: reasoning,
    similarity_factors: similarity.factors,
    status: 'pending'
  }

  // Save suggestion to database
  await supabaseClient
    .from('mapping_suggestions')
    .upsert({
      competency_id: competencyId,
      path_id: pathId,
      confidence_score: similarity.overall,
      reasoning: reasoning,
      status: 'pending',
      suggested_at: new Date().toISOString()
    })

  return suggestion
}

async function generateBatchSuggestions(supabaseClient: any, tenantId: number) {
  // Get all competencies
  const { data: competencies } = await supabaseClient
    .from('competencies')
    .select('id, name, description, category, tags')
    .eq('tenant_id', tenantId)
    .eq('status', 'active')

  // Get all learning paths
  const { data: paths } = await supabaseClient
    .from('learning_paths')
    .select('id, title, description, category, tags, objectives')
    .eq('tenant_id', tenantId)
    .eq('is_live', true)

  // Get existing mappings to avoid duplicates
  const { data: existingMappings } = await supabaseClient
    .from('path_competencies')
    .select('competency_id, path_id')

  const existingMappingSet = new Set(
    existingMappings?.map(m => `${m.competency_id}-${m.path_id}`) || []
  )

  const suggestions = []

  // Analyze each competency-path combination
  for (const competency of competencies || []) {
    for (const path of paths || []) {
      const mappingKey = `${competency.id}-${path.id}`
      
      // Skip if mapping already exists
      if (existingMappingSet.has(mappingKey)) continue

      // Calculate similarity
      const similarity = calculateSimilarity(competency, path)
      
      // Only suggest if confidence is above threshold
      if (similarity.overall >= 0.6) {
        const reasoning = generateReasoning(competency, path, similarity)
        
        suggestions.push({
          competency_id: competency.id,
          path_id: path.id,
          competency_name: competency.name,
          path_title: path.title,
          confidence_score: similarity.overall,
          reasoning: reasoning,
          similarity_factors: similarity.factors,
          status: 'pending'
        })

        // Save to database
        await supabaseClient
          .from('mapping_suggestions')
          .upsert({
            competency_id: competency.id,
            path_id: path.id,
            confidence_score: similarity.overall,
            reasoning: reasoning,
            status: 'pending',
            suggested_at: new Date().toISOString()
          })
      }
    }
  }

  return suggestions.sort((a, b) => b.confidence_score - a.confidence_score)
}

function calculateSimilarity(competency: any, path: any) {
  const factors = {
    category_match: 0,
    tag_overlap: 0,
    keyword_similarity: 0,
    title_description_match: 0
  }

  // Category matching
  if (competency.category && path.category) {
    factors.category_match = competency.category.toLowerCase() === path.category.toLowerCase() ? 1 : 0
  }

  // Tag overlap
  const competencyTags = competency.tags || []
  const pathTags = path.tags || []
  if (competencyTags.length > 0 && pathTags.length > 0) {
    const intersection = competencyTags.filter(tag => 
      pathTags.some(pathTag => pathTag.toLowerCase().includes(tag.toLowerCase()) || 
                              tag.toLowerCase().includes(pathTag.toLowerCase()))
    )
    factors.tag_overlap = intersection.length / Math.max(competencyTags.length, pathTags.length)
  }

  // Keyword similarity in descriptions
  const competencyText = `${competency.name} ${competency.description || ''}`.toLowerCase()
  const pathText = `${path.title} ${path.description || ''}`.toLowerCase()
  
  const competencyWords = competencyText.split(/\s+/).filter(word => word.length > 3)
  const pathWords = pathText.split(/\s+/).filter(word => word.length > 3)
  
  const commonWords = competencyWords.filter(word => pathWords.includes(word))
  factors.keyword_similarity = commonWords.length / Math.max(competencyWords.length, pathWords.length)

  // Title-description matching
  const competencyName = competency.name.toLowerCase()
  const pathTitle = path.title.toLowerCase()
  factors.title_description_match = pathTitle.includes(competencyName) || competencyName.includes(pathTitle) ? 0.8 : 0

  // Calculate weighted overall score
  const weights = {
    category_match: 0.3,
    tag_overlap: 0.25,
    keyword_similarity: 0.25,
    title_description_match: 0.2
  }

  const overall = Object.keys(factors).reduce((sum, factor) => {
    return sum + (factors[factor] * weights[factor])
  }, 0)

  return {
    overall: Math.round(overall * 100) / 100,
    factors
  }
}

function generateReasoning(competency: any, path: any, similarity: any) {
  const reasons = []

  if (similarity.factors.category_match > 0) {
    reasons.push(`Both belong to the "${competency.category}" category`)
  }

  if (similarity.factors.tag_overlap > 0.3) {
    reasons.push(`High tag overlap indicating related skills`)
  }

  if (similarity.factors.keyword_similarity > 0.2) {
    reasons.push(`Similar keywords found in descriptions`)
  }

  if (similarity.factors.title_description_match > 0) {
    reasons.push(`Direct name/title correlation detected`)
  }

  if (reasons.length === 0) {
    reasons.push(`AI-detected correlation based on content analysis`)
  }

  return reasons.join('; ')
}
