'use client'

import { useState, useCallback } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Autocomplete,
  Switch,
  FormControlLabel,
  LinearProgress,
  Alert,
  Paper,
  IconButton,
  Stepper,
  Step,
  StepLabel
} from '@mui/material'
import {
  CloudUpload as UploadIcon,
  Close as CloseIcon,
  InsertDriveFile as FileIcon,
  AutoAwesome as AIIcon,
  Visibility as PreviewIcon
} from '@mui/icons-material'
import { useDropzone } from 'react-dropzone'
import { motion, AnimatePresence } from 'framer-motion'
import toast from 'react-hot-toast'

import type { ContentCategory, ContentType, ContentUploadRequest } from '@/lib/types/content'
import { ContentService } from '@/lib/services/contentService'

interface ContentUploadDialogProps {
  open: boolean
  onClose: () => void
  onUpload: () => void
  categories: ContentCategory[]
}

const CONTENT_TYPES: { value: ContentType; label: string; accept: string[] }[] = [
  { value: 'video', label: 'Video', accept: ['video/*'] },
  { value: 'pdf', label: 'PDF Document', accept: ['application/pdf'] },
  { value: 'document', label: 'Document', accept: ['.doc,.docx,.txt,.rtf'] },
  { value: 'presentation', label: 'Presentation', accept: ['.ppt,.pptx'] },
  { value: 'image', label: 'Image', accept: ['image/*'] },
  { value: 'audio', label: 'Audio', accept: ['audio/*'] },
  { value: 'scorm', label: 'SCORM Package', accept: ['.zip'] },
  { value: 'quiz', label: 'Quiz', accept: ['.json'] }
]

const UPLOAD_STEPS = ['Select File', 'Content Details', 'AI Enhancement', 'Review & Upload']

const ContentUploadDialog = ({ open, onClose, onUpload, categories }: ContentUploadDialogProps) => {
  const [activeStep, setActiveStep] = useState(0)
  const [uploadData, setUploadData] = useState<Partial<ContentUploadRequest>>({
    auto_tag: true,
    auto_categorize: true,
    extract_text: true,
    generate_thumbnail: true,
    is_public: false
  })
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [errors, setErrors] = useState<string[]>([])
  const [aiSuggestions, setAiSuggestions] = useState<any>(null)

  const handleClose = useCallback(() => {
    if (!uploading) {
      setActiveStep(0)
      setUploadData({
        auto_tag: true,
        auto_categorize: true,
        extract_text: true,
        generate_thumbnail: true,
        is_public: false
      })
      setSelectedFile(null)
      setErrors([])
      setAiSuggestions(null)
      onClose()
    }
  }, [uploading, onClose])

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (file) {
      setSelectedFile(file)
      
      // Auto-detect content type
      const detectedType = detectContentType(file)
      setUploadData(prev => ({
        ...prev,
        type: detectedType,
        title: file.name.replace(/\.[^/.]+$/, '') // Remove extension
      }))
      
      setActiveStep(1)
      setErrors([])
    }
  }, [])

  const detectContentType = (file: File): ContentType => {
    const extension = file.name.split('.').pop()?.toLowerCase()
    const mimeType = file.type.toLowerCase()

    if (mimeType.startsWith('video/')) return 'video'
    if (mimeType === 'application/pdf') return 'pdf'
    if (mimeType.startsWith('image/')) return 'image'
    if (mimeType.startsWith('audio/')) return 'audio'
    if (extension === 'zip') return 'scorm'
    if (extension === 'json') return 'quiz'
    if (['ppt', 'pptx'].includes(extension || '')) return 'presentation'
    return 'document'
  }

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'video/*': ['.mp4', '.webm', '.ogg', '.avi', '.mov'],
      'application/pdf': ['.pdf'],
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp'],
      'audio/*': ['.mp3', '.wav', '.ogg', '.aac'],
      'application/zip': ['.zip'],
      'application/json': ['.json'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.ms-powerpoint': ['.ppt'],
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx']
    },
    maxFiles: 1,
    maxSize: 200 * 1024 * 1024 // 200MB
  })

  const handleNext = useCallback(() => {
    setActiveStep(prev => Math.min(prev + 1, UPLOAD_STEPS.length - 1))
  }, [])

  const handleBack = useCallback(() => {
    setActiveStep(prev => Math.max(prev - 1, 0))
  }, [])

  const handleUpload = useCallback(async () => {
    if (!selectedFile || !uploadData.title || !uploadData.type) {
      setErrors(['Please fill in all required fields'])
      return
    }

    try {
      setUploading(true)
      setUploadProgress(0)

      const uploadRequest: ContentUploadRequest = {
        title: uploadData.title,
        description: uploadData.description,
        type: uploadData.type,
        category_id: uploadData.category_id,
        tags: uploadData.tags,
        is_public: uploadData.is_public || false,
        file: selectedFile,
        auto_tag: uploadData.auto_tag,
        auto_categorize: uploadData.auto_categorize,
        extract_text: uploadData.extract_text,
        generate_thumbnail: uploadData.generate_thumbnail
      }

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90))
      }, 200)

      const result = await ContentService.uploadContent(uploadRequest)
      
      clearInterval(progressInterval)
      setUploadProgress(100)

      toast.success('Content uploaded successfully!')
      onUpload()
      handleClose()
    } catch (error) {
      console.error('Upload error:', error)
      setErrors([error instanceof Error ? error.message : 'Upload failed'])
    } finally {
      setUploading(false)
      setUploadProgress(0)
    }
  }, [selectedFile, uploadData, onUpload, handleClose])

  const generateAISuggestions = useCallback(async () => {
    if (!selectedFile) return

    try {
      // Mock AI suggestions - in real implementation, this would call an API
      const suggestions = {
        tags: ['tutorial', 'beginner', 'educational'],
        category: 'Training Materials',
        difficulty_level: 'beginner',
        estimated_duration: 15
      }
      
      setAiSuggestions(suggestions)
      setUploadData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), ...suggestions.tags]
      }))
    } catch (error) {
      console.error('AI suggestions error:', error)
    }
  }, [selectedFile])

  const renderStepContent = () => {
    switch (activeStep) {
      case 0: // File Selection
        return (
          <Box>
            <Paper
              {...getRootProps()}
              sx={{
                p: 4,
                textAlign: 'center',
                border: '2px dashed',
                borderColor: isDragActive ? 'primary.main' : 'grey.300',
                bgcolor: isDragActive ? 'primary.light' : 'grey.50',
                cursor: 'pointer',
                transition: 'all 0.2s',
                '&:hover': {
                  borderColor: 'primary.main',
                  bgcolor: 'primary.light'
                }
              }}
            >
              <input {...getInputProps()} />
              <UploadIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                {isDragActive ? 'Drop your file here' : 'Drag & drop a file here'}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                or click to browse files
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Supported formats: Video, PDF, Images, Audio, Documents, SCORM packages
                <br />
                Maximum file size: 200MB
              </Typography>
            </Paper>

            {selectedFile && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Box mt={2} p={2} bgcolor="success.light" borderRadius={1}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <FileIcon color="success" />
                    <Typography variant="body2" fontWeight="bold">
                      {selectedFile.name}
                    </Typography>
                    <Chip
                      label={`${(selectedFile.size / (1024 * 1024)).toFixed(1)} MB`}
                      size="small"
                      color="success"
                    />
                  </Box>
                </Box>
              </motion.div>
            )}
          </Box>
        )

      case 1: // Content Details
        return (
          <Box display="flex" flexDirection="column" gap={2}>
            <TextField
              fullWidth
              label="Title"
              value={uploadData.title || ''}
              onChange={(e) => setUploadData(prev => ({ ...prev, title: e.target.value }))}
              required
            />

            <TextField
              fullWidth
              label="Description"
              multiline
              rows={3}
              value={uploadData.description || ''}
              onChange={(e) => setUploadData(prev => ({ ...prev, description: e.target.value }))}
            />

            <FormControl fullWidth>
              <InputLabel>Content Type</InputLabel>
              <Select
                value={uploadData.type || ''}
                label="Content Type"
                onChange={(e) => setUploadData(prev => ({ ...prev, type: e.target.value as ContentType }))}
                required
              >
                {CONTENT_TYPES.map((type) => (
                  <MenuItem key={type.value} value={type.value}>
                    {type.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <InputLabel>Category</InputLabel>
              <Select
                value={uploadData.category_id || ''}
                label="Category"
                onChange={(e) => setUploadData(prev => ({ ...prev, category_id: e.target.value as number }))}
              >
                <MenuItem value="">
                  <em>No Category</em>
                </MenuItem>
                {categories.map((category) => (
                  <MenuItem key={category.id} value={category.id}>
                    {category.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <Autocomplete
              multiple
              freeSolo
              options={[]}
              value={uploadData.tags || []}
              onChange={(_, newValue) => setUploadData(prev => ({ ...prev, tags: newValue }))}
              renderTags={(value, getTagProps) =>
                value.map((option, index) => (
                  <Chip
                    variant="outlined"
                    label={option}
                    {...getTagProps({ index })}
                    key={option}
                  />
                ))
              }
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Tags"
                  placeholder="Add tags..."
                />
              )}
            />

            <FormControlLabel
              control={
                <Switch
                  checked={uploadData.is_public || false}
                  onChange={(e) => setUploadData(prev => ({ ...prev, is_public: e.target.checked }))}
                />
              }
              label="Make this content public"
            />
          </Box>
        )

      case 2: // AI Enhancement
        return (
          <Box display="flex" flexDirection="column" gap={2}>
            <Typography variant="h6" gutterBottom>
              AI Enhancement Options
            </Typography>

            <FormControlLabel
              control={
                <Switch
                  checked={uploadData.auto_tag || false}
                  onChange={(e) => setUploadData(prev => ({ ...prev, auto_tag: e.target.checked }))}
                />
              }
              label="Auto-generate tags"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={uploadData.auto_categorize || false}
                  onChange={(e) => setUploadData(prev => ({ ...prev, auto_categorize: e.target.checked }))}
                />
              }
              label="Auto-categorize content"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={uploadData.extract_text || false}
                  onChange={(e) => setUploadData(prev => ({ ...prev, extract_text: e.target.checked }))}
                />
              }
              label="Extract text content"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={uploadData.generate_thumbnail || false}
                  onChange={(e) => setUploadData(prev => ({ ...prev, generate_thumbnail: e.target.checked }))}
                />
              }
              label="Generate thumbnail"
            />

            <Button
              variant="outlined"
              startIcon={<AIIcon />}
              onClick={generateAISuggestions}
              sx={{ mt: 2 }}
            >
              Generate AI Suggestions
            </Button>

            {aiSuggestions && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Alert severity="info" sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    AI Suggestions:
                  </Typography>
                  <Box display="flex" flexWrap="wrap" gap={0.5}>
                    {aiSuggestions.tags.map((tag: string) => (
                      <Chip key={tag} label={tag} size="small" color="primary" />
                    ))}
                  </Box>
                </Alert>
              </motion.div>
            )}
          </Box>
        )

      case 3: // Review & Upload
        return (
          <Box display="flex" flexDirection="column" gap={2}>
            <Typography variant="h6" gutterBottom>
              Review Your Upload
            </Typography>

            <Box p={2} bgcolor="grey.50" borderRadius={1}>
              <Typography variant="subtitle2" gutterBottom>
                File: {selectedFile?.name}
              </Typography>
              <Typography variant="body2" gutterBottom>
                Title: {uploadData.title}
              </Typography>
              <Typography variant="body2" gutterBottom>
                Type: {uploadData.type}
              </Typography>
              <Typography variant="body2" gutterBottom>
                Category: {categories.find(c => c.id === uploadData.category_id)?.name || 'None'}
              </Typography>
              {uploadData.tags && uploadData.tags.length > 0 && (
                <Box mt={1}>
                  <Typography variant="body2" gutterBottom>
                    Tags:
                  </Typography>
                  <Box display="flex" flexWrap="wrap" gap={0.5}>
                    {uploadData.tags.map((tag) => (
                      <Chip key={tag} label={tag} size="small" />
                    ))}
                  </Box>
                </Box>
              )}
            </Box>

            {uploading && (
              <Box>
                <LinearProgress variant="determinate" value={uploadProgress} />
                <Typography variant="body2" color="text.secondary" mt={1}>
                  Uploading... {uploadProgress}%
                </Typography>
              </Box>
            )}
          </Box>
        )

      default:
        return null
    }
  }

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          Upload Content
          <IconButton onClick={handleClose} disabled={uploading}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box mb={3}>
          <Stepper activeStep={activeStep} alternativeLabel>
            {UPLOAD_STEPS.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>

        {errors.length > 0 && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {errors.map((error, index) => (
              <Typography key={index} variant="body2">
                {error}
              </Typography>
            ))}
          </Alert>
        )}

        <AnimatePresence mode="wait">
          <motion.div
            key={activeStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            {renderStepContent()}
          </motion.div>
        </AnimatePresence>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={uploading}>
          Cancel
        </Button>
        
        {activeStep > 0 && (
          <Button onClick={handleBack} disabled={uploading}>
            Back
          </Button>
        )}
        
        {activeStep < UPLOAD_STEPS.length - 1 ? (
          <Button
            onClick={handleNext}
            variant="contained"
            disabled={activeStep === 0 && !selectedFile}
          >
            Next
          </Button>
        ) : (
          <Button
            onClick={handleUpload}
            variant="contained"
            disabled={uploading || !uploadData.title || !uploadData.type}
          >
            {uploading ? 'Uploading...' : 'Upload'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  )
}

export default ContentUploadDialog
