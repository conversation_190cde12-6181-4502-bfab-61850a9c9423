import { supabase } from '@/lib/supabase'
import { 
  Group, 
  GroupMember, 
  GroupAssignment, 
  GroupProgress, 
  GroupMessage,
  GroupMilestone,
  AssignmentTemplate,
  GroupFilters,
  GroupsResponse,
  GroupMembersResponse,
  GroupProgressResponse,
  SmartGroupingSuggestion,
  AutoAssignmentRule
} from '@/lib/types/groups'

export class GroupsService {
  // Groups CRUD Operations
  static async getGroups(filters: Partial<GroupFilters> = {}): Promise<GroupsResponse> {
    let query = supabase
      .from('groups')
      .select(`
        *,
        group_members(count),
        group_assignments(count),
        group_progress(completion_rate)
      `)

    // Apply filters
    if (filters.search) {
      query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`)
    }
    
    if (filters.status && filters.status !== 'all') {
      query = query.eq('status', filters.status)
    }
    
    if (filters.tags && filters.tags.length > 0) {
      query = query.contains('tags', filters.tags)
    }
    
    if (filters.created_by) {
      query = query.eq('created_by', filters.created_by)
    }
    
    if (filters.date_range?.start) {
      query = query.gte('created_at', filters.date_range.start)
    }
    
    if (filters.date_range?.end) {
      query = query.lte('created_at', filters.date_range.end)
    }
    
    if (filters.parent_group_id) {
      query = query.eq('parent_group_id', filters.parent_group_id)
    }

    const { data, error, count } = await query
      .order('created_at', { ascending: false })

    if (error) throw error

    return {
      data: data || [],
      count: count || 0,
      page: 1,
      limit: 50
    }
  }

  static async getGroup(id: number): Promise<Group> {
    const { data, error } = await supabase
      .from('groups')
      .select(`
        *,
        group_members(*,
          users(full_name, email, avatar_url)
        ),
        group_assignments(*,
          learning_paths(title, description, estimated_duration, difficulty_level)
        ),
        group_progress(*),
        children:groups!parent_group_id(*)
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  }

  static async createGroup(groupData: Partial<Group>): Promise<Group> {
    const { data, error } = await supabase
      .from('groups')
      .insert([groupData])
      .select()
      .single()

    if (error) throw error
    return data
  }

  static async updateGroup(id: number, updates: Partial<Group>): Promise<Group> {
    const { data, error } = await supabase
      .from('groups')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  static async deleteGroup(id: number): Promise<void> {
    const { error } = await supabase
      .from('groups')
      .delete()
      .eq('id', id)

    if (error) throw error
  }

  static async duplicateGroup(id: number, newName: string): Promise<Group> {
    const originalGroup = await this.getGroup(id)
    
    const { data: newGroup, error } = await supabase
      .from('groups')
      .insert([{
        ...originalGroup,
        id: undefined,
        name: newName,
        status: 'draft',
        created_at: undefined,
        updated_at: undefined
      }])
      .select()
      .single()

    if (error) throw error
    return newGroup
  }

  // Group Members Operations
  static async getGroupMembers(groupId: number): Promise<GroupMembersResponse> {
    const { data, error, count } = await supabase
      .from('group_members')
      .select(`
        *,
        users(full_name, email, avatar_url, department)
      `)
      .eq('group_id', groupId)

    if (error) throw error

    return {
      data: data || [],
      count: count || 0
    }
  }

  static async addGroupMember(groupId: number, learnerId: string, role: string = 'member'): Promise<GroupMember> {
    const { data, error } = await supabase
      .from('group_members')
      .insert([{
        group_id: groupId,
        learner_id: learnerId,
        role,
        added_by: 'current_user' // TODO: Get from auth context
      }])
      .select()
      .single()

    if (error) throw error
    return data
  }

  static async addGroupMembers(groupId: number, learnerIds: string[], role: string = 'member'): Promise<GroupMember[]> {
    const members = learnerIds.map(learnerId => ({
      group_id: groupId,
      learner_id: learnerId,
      role,
      added_by: 'current_user' // TODO: Get from auth context
    }))

    const { data, error } = await supabase
      .from('group_members')
      .insert(members)
      .select()

    if (error) throw error
    return data || []
  }

  static async removeGroupMember(memberId: number): Promise<void> {
    const { error } = await supabase
      .from('group_members')
      .delete()
      .eq('id', memberId)

    if (error) throw error
  }

  static async updateMemberRole(memberId: number, role: string): Promise<GroupMember> {
    const { data, error } = await supabase
      .from('group_members')
      .update({ role })
      .eq('id', memberId)
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Group Assignments Operations
  static async getGroupAssignments(groupId: number): Promise<GroupAssignment[]> {
    const { data, error } = await supabase
      .from('group_assignments')
      .select(`
        *,
        learning_paths(title, description, estimated_duration, difficulty_level)
      `)
      .eq('group_id', groupId)

    if (error) throw error
    return data || []
  }

  static async assignPathToGroup(groupId: number, pathId: number, settings: any = {}): Promise<GroupAssignment> {
    const { data, error } = await supabase
      .from('group_assignments')
      .insert([{
        group_id: groupId,
        path_id: pathId,
        assigned_by: 'current_user', // TODO: Get from auth context
        ...settings
      }])
      .select()
      .single()

    if (error) throw error
    return data
  }

  static async removePathFromGroup(assignmentId: number): Promise<void> {
    const { error } = await supabase
      .from('group_assignments')
      .delete()
      .eq('id', assignmentId)

    if (error) throw error
  }

  static async bulkAssignPaths(groupIds: number[], pathIds: number[], settings: any = {}): Promise<GroupAssignment[]> {
    const assignments = []
    for (const groupId of groupIds) {
      for (const pathId of pathIds) {
        assignments.push({
          group_id: groupId,
          path_id: pathId,
          assigned_by: 'current_user', // TODO: Get from auth context
          ...settings
        })
      }
    }

    const { data, error } = await supabase
      .from('group_assignments')
      .insert(assignments)
      .select()

    if (error) throw error
    return data || []
  }

  // Group Progress Operations
  static async getGroupProgress(groupId: number): Promise<GroupProgressResponse> {
    const { data, error } = await supabase
      .from('group_progress')
      .select(`
        *,
        learning_paths(title)
      `)
      .eq('group_id', groupId)

    if (error) throw error

    // Calculate summary statistics
    const summary = {
      total_groups: 1,
      active_groups: 1,
      average_completion_rate: data?.reduce((acc, p) => acc + p.completion_rate, 0) / (data?.length || 1) || 0,
      total_learners: data?.[0]?.total_members || 0
    }

    return {
      data: data || [],
      summary
    }
  }

  static async refreshGroupProgress(groupId: number): Promise<void> {
    // This would typically trigger a database function or edge function
    // to recalculate progress statistics
    const { error } = await supabase.rpc('refresh_group_progress', { group_id: groupId })
    if (error) throw error
  }

  // Communication Operations
  static async sendGroupMessage(groupId: number, message: Partial<GroupMessage>): Promise<GroupMessage> {
    const { data, error } = await supabase
      .from('group_messages')
      .insert([{
        group_id: groupId,
        sender_id: 'current_user', // TODO: Get from auth context
        ...message
      }])
      .select()
      .single()

    if (error) throw error
    return data
  }

  static async getGroupMessages(groupId: number, limit: number = 50): Promise<GroupMessage[]> {
    const { data, error } = await supabase
      .from('group_messages')
      .select(`
        *,
        users(full_name, avatar_url)
      `)
      .eq('group_id', groupId)
      .order('sent_at', { ascending: false })
      .limit(limit)

    if (error) throw error
    return data || []
  }

  // Milestones Operations
  static async getGroupMilestones(groupId: number): Promise<GroupMilestone[]> {
    const { data, error } = await supabase
      .from('group_milestones')
      .select('*')
      .eq('group_id', groupId)
      .order('due_date', { ascending: true })

    if (error) throw error
    return data || []
  }

  static async createMilestone(milestone: Partial<GroupMilestone>): Promise<GroupMilestone> {
    const { data, error } = await supabase
      .from('group_milestones')
      .insert([{
        ...milestone,
        created_by: 'current_user' // TODO: Get from auth context
      }])
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Templates Operations
  static async getAssignmentTemplates(): Promise<AssignmentTemplate[]> {
    const { data, error } = await supabase
      .from('assignment_templates')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  }

  static async createAssignmentTemplate(template: Partial<AssignmentTemplate>): Promise<AssignmentTemplate> {
    const { data, error } = await supabase
      .from('assignment_templates')
      .insert([{
        ...template,
        created_by: 'current_user' // TODO: Get from auth context
      }])
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Statistics and Analytics
  static async getGroupStats(): Promise<any> {
    const { data: groupsData, error: groupsError } = await supabase
      .from('groups')
      .select('status')

    if (groupsError) throw groupsError

    const { data: membersData, error: membersError } = await supabase
      .from('group_members')
      .select('id')

    if (membersError) throw membersError

    const { data: progressData, error: progressError } = await supabase
      .from('group_progress')
      .select('completion_rate')

    if (progressError) throw progressError

    return {
      totalGroups: groupsData?.length || 0,
      activeGroups: groupsData?.filter(g => g.status === 'active').length || 0,
      totalMembers: membersData?.length || 0,
      averageCompletion: progressData?.reduce((acc, p) => acc + p.completion_rate, 0) / (progressData?.length || 1) || 0
    }
  }

  // AI and Smart Features
  static async generateSmartGroupingSuggestions(criteria: any): Promise<SmartGroupingSuggestion[]> {
    // This would call an Edge Function that uses OpenAI
    const { data, error } = await supabase.functions.invoke('generate-group-suggestions', {
      body: { criteria }
    })

    if (error) throw error
    return data?.suggestions || []
  }

  static async getAutoAssignmentRules(groupId: number): Promise<AutoAssignmentRule[]> {
    // Implementation for auto-assignment rules
    // This would be stored in a separate table
    return []
  }
}
