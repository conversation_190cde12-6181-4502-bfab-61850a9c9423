'use client'

import { useState } from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Chip,
  Avatar,
  useTheme,
  Skeleton,
  Paper,
  Divider,
} from '@mui/material'
import {
  MoreVert as MoreVertIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Schedule as ScheduleIcon,
  Visibility as ViewIcon,
  Download as DownloadIcon,
  Share as ShareIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  ArcElement,
} from 'chart.js'
import { Line, Bar, Doughnut } from 'react-chartjs-2'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  ArcElement
)

interface ReportsStats {
  totalReports: number
  activeReports: number
  scheduledReports: number
  sharedReports: number
  totalViews: number
  avgGenerationTime: number
  popularTemplates: Array<{
    id: number
    name: string
    usage: number
  }>
}

interface ReportsDashboardProps {
  stats?: ReportsStats
  isLoading: boolean
}

export default function ReportsDashboard({ stats, isLoading }: ReportsDashboardProps) {
  const theme = useTheme()

  // Mock data for recent reports
  const recentReports = [
    {
      id: 1,
      name: 'Q4 Learning Progress Report',
      type: 'Progress',
      lastGenerated: '2024-01-15T10:30:00Z',
      views: 45,
      status: 'completed',
      generatedBy: 'John Doe',
    },
    {
      id: 2,
      name: 'Engagement Analytics Dashboard',
      type: 'Engagement',
      lastGenerated: '2024-01-14T15:45:00Z',
      views: 32,
      status: 'scheduled',
      generatedBy: 'Jane Smith',
    },
    {
      id: 3,
      name: 'Competency Gap Analysis',
      type: 'Competency',
      lastGenerated: '2024-01-13T09:15:00Z',
      views: 28,
      status: 'completed',
      generatedBy: 'Mike Johnson',
    },
    {
      id: 4,
      name: 'Monthly Training Summary',
      type: 'Summary',
      lastGenerated: '2024-01-12T14:20:00Z',
      views: 67,
      status: 'completed',
      generatedBy: 'Sarah Wilson',
    },
  ]

  // Chart data for report generation trends
  const reportTrendsData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Reports Generated',
        data: [12, 19, 15, 25, 22, 30],
        borderColor: theme.palette.primary.main,
        backgroundColor: `${theme.palette.primary.main}20`,
        fill: true,
        tension: 0.4,
      },
    ],
  }

  // Chart data for report types distribution
  const reportTypesData = {
    labels: ['Progress Reports', 'Engagement Analytics', 'Competency Analysis', 'Summary Reports'],
    datasets: [
      {
        data: [35, 25, 20, 20],
        backgroundColor: [
          theme.palette.primary.main,
          theme.palette.secondary.main,
          theme.palette.success.main,
          theme.palette.warning.main,
        ],
        borderWidth: 0,
      },
    ],
  }

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  }

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
      },
    },
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success'
      case 'scheduled':
        return 'warning'
      case 'failed':
        return 'error'
      default:
        return 'default'
    }
  }

  return (
    <Box>
      <Grid container spacing={3}>
        {/* Performance Metrics */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Report Generation Trends
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Monthly report generation activity
              </Typography>
              <Box height={300} mt={2}>
                {isLoading ? (
                  <Skeleton variant="rectangular" height="100%" />
                ) : (
                  <Line data={reportTrendsData} options={chartOptions} />
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Report Types Distribution
              </Typography>
              <Box height={300} mt={2}>
                {isLoading ? (
                  <Skeleton variant="circular" height="100%" />
                ) : (
                  <Doughnut data={reportTypesData} options={doughnutOptions} />
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Reports */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6" fontWeight="bold">
                  Recent Reports
                </Typography>
                <Button size="small" color="primary">
                  View All
                </Button>
              </Box>
              <List>
                {recentReports.map((report, index) => (
                  <motion.div
                    key={report.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <ListItem
                      sx={{
                        border: 1,
                        borderColor: 'divider',
                        borderRadius: 1,
                        mb: 1,
                        '&:hover': {
                          backgroundColor: 'action.hover',
                        },
                      }}
                    >
                      <Avatar
                        sx={{
                          bgcolor: theme.palette.primary.main,
                          mr: 2,
                          width: 40,
                          height: 40,
                        }}
                      >
                        {report.type[0]}
                      </Avatar>
                      <ListItemText
                        primary={
                          <Typography variant="subtitle1" fontWeight="medium">
                            {report.name}
                          </Typography>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              Generated by {report.generatedBy} • {formatDate(report.lastGenerated)}
                            </Typography>
                            <Box display="flex" alignItems="center" gap={1} mt={0.5}>
                              <Chip
                                label={report.status}
                                size="small"
                                color={getStatusColor(report.status) as any}
                                variant="outlined"
                              />
                              <Typography variant="caption" color="text.secondary">
                                {report.views} views
                              </Typography>
                            </Box>
                          </Box>
                        }
                      />
                      <ListItemSecondaryAction>
                        <IconButton size="small">
                          <MoreVertIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                  </motion.div>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Popular Templates */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Popular Templates
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Most used report templates
              </Typography>
              <List>
                {isLoading ? (
                  Array.from({ length: 3 }).map((_, index) => (
                    <ListItem key={index}>
                      <Skeleton variant="text" width="100%" height={40} />
                    </ListItem>
                  ))
                ) : (
                  stats?.popularTemplates.map((template, index) => (
                    <ListItem key={template.id} sx={{ px: 0 }}>
                      <ListItemText
                        primary={template.name}
                        secondary={`${template.usage} uses`}
                      />
                      <Box display="flex" alignItems="center">
                        <TrendingUpIcon
                          color="success"
                          sx={{ fontSize: 16, mr: 0.5 }}
                        />
                        <Typography variant="caption" color="success.main">
                          +{Math.floor(Math.random() * 20)}%
                        </Typography>
                      </Box>
                    </ListItem>
                  ))
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              Quick Actions
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  variant="outlined"
                  fullWidth
                  startIcon={<TrendingUpIcon />}
                  sx={{ py: 1.5 }}
                >
                  Generate Progress Report
                </Button>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  variant="outlined"
                  fullWidth
                  startIcon={<ScheduleIcon />}
                  sx={{ py: 1.5 }}
                >
                  Schedule Weekly Report
                </Button>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  variant="outlined"
                  fullWidth
                  startIcon={<ShareIcon />}
                  sx={{ py: 1.5 }}
                >
                  Share Dashboard
                </Button>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  variant="outlined"
                  fullWidth
                  startIcon={<DownloadIcon />}
                  sx={{ py: 1.5 }}
                >
                  Export All Data
                </Button>
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  )
}
