import { NextRequest, NextResponse } from 'next/server'
import { supabase, supabaseAdmin } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing Supabase API authentication...')

    // Test environment variables
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

    console.log('Environment variables:')
    console.log('- SUPABASE_URL:', supabaseUrl ? '✅ Set' : '❌ Missing')
    console.log('- ANON_KEY:', anonKey ? '✅ Set' : '❌ Missing')
    console.log('- SERVICE_KEY:', serviceKey ? '✅ Set' : '❌ Missing')

    // Log actual values (first 20 chars for security)
    console.log('- SUPABASE_URL value:', supabaseUrl?.substring(0, 30) + '...')
    console.log('- ANON_KEY value:', anonKey?.substring(0, 20) + '...')
    console.log('- SERVICE_KEY value:', serviceKey?.substring(0, 20) + '...')

    const results: {
      environment: {
        supabaseUrl: string
        anonKey: string
        serviceKey: string
        supabaseUrlValue: string
        anonKeyValue: string
        serviceKeyValue: string
      }
      tests: {
        anonClient?: { success: boolean; error?: string; data?: any }
        adminClient?: { success: boolean; error?: string; data?: any }
        settingsQuery?: { success: boolean; error?: string; data?: any }
        rlsCheck?: { success: boolean; error?: string; recordCount?: number }
      }
    } = {
      environment: {
        supabaseUrl: supabaseUrl ? 'Set' : 'Missing',
        anonKey: anonKey ? 'Set' : 'Missing',
        serviceKey: serviceKey ? 'Set' : 'Missing',
        supabaseUrlValue: supabaseUrl?.substring(0, 30) + '...',
        anonKeyValue: anonKey?.substring(0, 20) + '...',
        serviceKeyValue: serviceKey?.substring(0, 20) + '...'
      },
      tests: {}
    }
    
    // Test 1: Basic connection with anon client
    console.log('\n🧪 Test 1: Anon client connection...')
    try {
      const { data: anonData, error: anonError } = await supabase
        .from('tenants')
        .select('id, name')
        .limit(1)
      
      if (anonError) {
        console.log('❌ Anon client error:', anonError.message)
        results.tests.anonClient = { success: false, error: anonError.message }
      } else {
        console.log('✅ Anon client working')
        results.tests.anonClient = { success: true, data: anonData }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      console.log('❌ Anon client exception:', errorMessage)
      results.tests.anonClient = { success: false, error: errorMessage }
    }
    
    // Test 2: Service role client connection
    console.log('\n🧪 Test 2: Service role client connection...')
    try {
      const { data: adminData, error: adminError } = await supabaseAdmin
        .from('tenants')
        .select('id, name')
        .limit(1)
      
      if (adminError) {
        console.log('❌ Admin client error:', adminError.message)
        results.tests.adminClient = { success: false, error: adminError.message }
      } else {
        console.log('✅ Admin client working')
        results.tests.adminClient = { success: true, data: adminData }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      console.log('❌ Admin client exception:', errorMessage)
      results.tests.adminClient = { success: false, error: errorMessage }
    }
    
    // Test 3: Specific tenant_settings query
    console.log('\n🧪 Test 3: Tenant settings query...')
    try {
      const { data: settingsData, error: settingsError } = await supabaseAdmin
        .from('tenant_settings')
        .select('*')
        .eq('tenant_id', 3)
        .eq('category', 'general')
        .single()
      
      if (settingsError) {
        console.log('❌ Settings query error:', settingsError.message)
        results.tests.settingsQuery = { success: false, error: settingsError.message }
      } else {
        console.log('✅ Settings query working')
        results.tests.settingsQuery = { success: true, data: settingsData }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      console.log('❌ Settings query exception:', errorMessage)
      results.tests.settingsQuery = { success: false, error: errorMessage }
    }
    
    // Test 4: Check RLS policies
    console.log('\n🧪 Test 4: RLS policy check...')
    try {
      const { data: rlsData, error: rlsError } = await supabaseAdmin
        .from('tenant_settings')
        .select('*')
        .limit(1)
      
      if (rlsError) {
        console.log('❌ RLS check error:', rlsError.message)
        results.tests.rlsCheck = { success: false, error: rlsError.message }
      } else {
        console.log('✅ RLS bypassed successfully')
        results.tests.rlsCheck = { success: true, recordCount: rlsData.length }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      console.log('❌ RLS check exception:', errorMessage)
      results.tests.rlsCheck = { success: false, error: errorMessage }
    }
    
    console.log('\n✅ Test completed!')
    
    return NextResponse.json({
      success: true,
      message: 'Supabase API test completed',
      results
    })
    
  } catch (error) {
    console.error('❌ Test failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 })
  }
}
