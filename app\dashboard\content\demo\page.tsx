'use client'

import { useState } from 'react'
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Button,
  Alert,
  Grid,
  Chip,
  Paper
} from '@mui/material'
import {
  CloudUpload as UploadIcon,
  Folder as FolderIcon,
  Description as DocumentIcon,
  VideoLibrary as VideoIcon,
  Image as ImageIcon
} from '@mui/icons-material'

import ContentGrid from '@/components/content/ContentGrid'
import ContentFilters from '@/components/content/ContentFilters'
import ContentUploadDialog from '@/components/content/ContentUploadDialog'
import ContentPreviewDialog from '@/components/content/ContentPreviewDialog'
import ContentCategories from '@/components/content/ContentCategories'

import type { ContentItem, ContentCategory, ContentFilter } from '@/lib/types/content'

// Mock data for demo
const mockCategories: ContentCategory[] = [
  {
    id: 1,
    tenant_id: 1,
    name: 'Training Materials',
    description: 'General training and educational content',
    color: '#1976d2',
    sort_order: 1,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    content_count: 15
  },
  {
    id: 2,
    tenant_id: 1,
    name: 'Documentation',
    description: 'Technical documentation and guides',
    color: '#388e3c',
    sort_order: 2,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    content_count: 8
  },
  {
    id: 3,
    tenant_id: 1,
    name: 'Videos',
    description: 'Video content and tutorials',
    color: '#d32f2f',
    sort_order: 3,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    content_count: 12
  }
]

const mockContent: ContentItem[] = [
  {
    id: 1,
    tenant_id: 1,
    title: 'Introduction to React',
    description: 'A comprehensive guide to getting started with React development',
    type: 'video',
    file_url: 'https://example.com/react-intro.mp4',
    file_size: 52428800, // 50MB
    mime_type: 'video/mp4',
    duration: 1800, // 30 minutes
    tags: ['react', 'javascript', 'frontend', 'tutorial'],
    metadata: {
      ai_difficulty_level: 'beginner',
      ai_estimated_duration: 30,
      ai_tags: ['react', 'components', 'jsx'],
      processing_status: 'completed'
    },
    is_public: true,
    created_at: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
    updated_at: new Date(Date.now() - 86400000).toISOString(),
    category: mockCategories[2]
  },
  {
    id: 2,
    tenant_id: 1,
    title: 'JavaScript Best Practices',
    description: 'Essential best practices for writing clean, maintainable JavaScript code',
    type: 'pdf',
    file_url: 'https://example.com/js-best-practices.pdf',
    file_size: 2097152, // 2MB
    mime_type: 'application/pdf',
    tags: ['javascript', 'best-practices', 'coding-standards'],
    metadata: {
      pdf_pages: 45,
      ai_difficulty_level: 'intermediate',
      ai_estimated_duration: 60,
      processing_status: 'completed'
    },
    is_public: false,
    created_at: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
    updated_at: new Date(Date.now() - 172800000).toISOString(),
    category: mockCategories[1]
  },
  {
    id: 3,
    tenant_id: 1,
    title: 'UI Design Principles',
    description: 'Fundamental principles of user interface design',
    type: 'presentation',
    file_url: 'https://example.com/ui-design.pptx',
    file_size: 15728640, // 15MB
    mime_type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    tags: ['design', 'ui', 'principles', 'presentation'],
    metadata: {
      ai_difficulty_level: 'beginner',
      ai_estimated_duration: 45,
      processing_status: 'completed'
    },
    is_public: true,
    created_at: new Date(Date.now() - 259200000).toISOString(), // 3 days ago
    updated_at: new Date(Date.now() - 259200000).toISOString(),
    category: mockCategories[0]
  }
]

export default function ContentLibraryDemoPage() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [selectedCategory, setSelectedCategory] = useState<number | undefined>()
  const [selectedItems, setSelectedItems] = useState<number[]>([])
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false)
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false)
  const [selectedContent, setSelectedContent] = useState<ContentItem | null>(null)
  
  const [filter, setFilter] = useState<ContentFilter>({
    page: 1,
    limit: 20,
    sort_by: 'created_at',
    sort_order: 'desc'
  })

  // Filter content based on selected category
  const filteredContent = selectedCategory 
    ? mockContent.filter(item => item.category?.id === selectedCategory)
    : mockContent

  const handleContentSelect = (content: ContentItem) => {
    setSelectedContent(content)
    setPreviewDialogOpen(true)
  }

  const handleFilterChange = (newFilter: Partial<ContentFilter>) => {
    setFilter(prev => ({ ...prev, ...newFilter }))
  }

  const handleCategorySelect = (categoryId: number | null) => {
    setSelectedCategory(categoryId || undefined)
  }

  const handleUpload = () => {
    console.log('Upload completed')
    setUploadDialogOpen(false)
  }

  const handleBulkAction = (action: string) => {
    console.log('Bulk action:', action, 'on items:', selectedItems)
  }

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Demo Header */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Content Library Demo
        </Typography>
        <Typography variant="body2">
          This is a demonstration of the Content Library page with mock data. 
          In the full application, this would connect to Supabase for real data management.
        </Typography>
      </Alert>

      {/* Header */}
      <Box mb={3}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box>
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              Content Library
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Manage and organize your learning content
            </Typography>
          </Box>
          
          <Button
            variant="contained"
            startIcon={<UploadIcon />}
            onClick={() => setUploadDialogOpen(true)}
          >
            Upload Content
          </Button>
        </Box>

        {/* Stats Cards */}
        <Grid container spacing={2} mb={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" color="primary">
                  {mockContent.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Items
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" color="primary">
                  {mockCategories.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Categories
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" color="primary">
                  {selectedItems.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Selected
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" color="success.main">
                  Active
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Status
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>

      {/* Main Content Area */}
      <Box sx={{ display: 'flex', flex: 1, gap: 2, overflow: 'hidden' }}>
        {/* Categories Sidebar */}
        <Box sx={{ width: 280 }}>
          <ContentCategories
            categories={mockCategories}
            selectedCategory={selectedCategory}
            onCategorySelect={handleCategorySelect}
          />
        </Box>

        {/* Content Area */}
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
          {/* Filters */}
          <ContentFilters
            filter={filter}
            onFilterChange={handleFilterChange}
            categories={mockCategories}
          />

          {/* Content Grid */}
          <Box sx={{ flex: 1, overflow: 'auto' }}>
            <ContentGrid
              content={filteredContent}
              viewMode={viewMode}
              selectedItems={selectedItems}
              onItemSelect={handleContentSelect}
              onSelectionChange={setSelectedItems}
              onBulkAction={handleBulkAction}
              loading={false}
            />
          </Box>
        </Box>
      </Box>

      {/* Dialogs */}
      <ContentUploadDialog
        open={uploadDialogOpen}
        onClose={() => setUploadDialogOpen(false)}
        onUpload={handleUpload}
        categories={mockCategories}
      />

      <ContentPreviewDialog
        open={previewDialogOpen}
        onClose={() => setPreviewDialogOpen(false)}
        content={selectedContent}
      />

      {/* Demo Features Info */}
      <Paper sx={{ mt: 2, p: 2, bgcolor: 'grey.50' }}>
        <Typography variant="h6" gutterBottom>
          Demo Features Included:
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <Box display="flex" alignItems="center" gap={1} mb={1}>
              <FolderIcon color="primary" />
              <Typography variant="body2">Category-based organization</Typography>
            </Box>
            <Box display="flex" alignItems="center" gap={1} mb={1}>
              <DocumentIcon color="primary" />
              <Typography variant="body2">Multi-format content support</Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box display="flex" alignItems="center" gap={1} mb={1}>
              <VideoIcon color="primary" />
              <Typography variant="body2">Rich preview functionality</Typography>
            </Box>
            <Box display="flex" alignItems="center" gap={1} mb={1}>
              <ImageIcon color="primary" />
              <Typography variant="body2">Drag-and-drop upload interface</Typography>
            </Box>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  )
}
