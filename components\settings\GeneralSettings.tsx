'use client'

import React, { useState, useEffect } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Switch,
  FormControlLabel,
  Button,
  Grid,
  Divider,
  Alert,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress
} from '@mui/material'
import {
  Schedule as ScheduleIcon,
  Info as InfoIcon,
  Refresh as RefreshIcon,
  Preview as PreviewIcon
} from '@mui/icons-material'
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { useForm, Controller } from 'react-hook-form'
import { motion } from 'framer-motion'

import { useGeneralSettings, useUpdateGeneralSettings } from '@/lib/hooks/useSettings'
import { useSettingsStore } from '@/lib/store'
import { GeneralSettings as GeneralSettingsType } from '@/lib/types/settings'

// Timezone options (subset for demo)
const timezones = [
  { value: 'UTC', label: 'UTC (Coordinated Universal Time)' },
  { value: 'America/New_York', label: 'Eastern Time (ET)' },
  { value: 'America/Chicago', label: 'Central Time (CT)' },
  { value: 'America/Denver', label: 'Mountain Time (MT)' },
  { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
  { value: 'Europe/London', label: 'Greenwich Mean Time (GMT)' },
  { value: 'Europe/Paris', label: 'Central European Time (CET)' },
  { value: 'Asia/Tokyo', label: 'Japan Standard Time (JST)' },
  { value: 'Asia/Shanghai', label: 'China Standard Time (CST)' },
  { value: 'Asia/Kolkata', label: 'India Standard Time (IST)' },
  { value: 'Australia/Sydney', label: 'Australian Eastern Time (AET)' }
]

// Language options
const languages = [
  { value: 'en', label: 'English' },
  { value: 'es', label: 'Spanish' },
  { value: 'fr', label: 'French' },
  { value: 'de', label: 'German' },
  { value: 'it', label: 'Italian' },
  { value: 'pt', label: 'Portuguese' },
  { value: 'zh', label: 'Chinese' },
  { value: 'ja', label: 'Japanese' },
  { value: 'ko', label: 'Korean' },
  { value: 'ar', label: 'Arabic' }
]

// Currency options
const currencies = [
  { value: 'USD', label: 'US Dollar ($)' },
  { value: 'EUR', label: 'Euro (€)' },
  { value: 'GBP', label: 'British Pound (£)' },
  { value: 'JPY', label: 'Japanese Yen (¥)' },
  { value: 'CAD', label: 'Canadian Dollar (C$)' },
  { value: 'AUD', label: 'Australian Dollar (A$)' },
  { value: 'CHF', label: 'Swiss Franc (CHF)' },
  { value: 'CNY', label: 'Chinese Yuan (¥)' },
  { value: 'INR', label: 'Indian Rupee (₹)' }
]

export default function GeneralSettings() {
  const { setUnsavedChanges } = useSettingsStore()
  const { data: settings, isLoading } = useGeneralSettings()
  const updateSettings = useUpdateGeneralSettings()
  
  const [previewMessage, setPreviewMessage] = useState(false)
  
  const { control, handleSubmit, watch, reset, formState: { isDirty } } = useForm<GeneralSettingsType>({
    defaultValues: settings || {
      platform_name: 'ZenithLearn AI',
      tagline: 'AI-Powered Learning Management',
      timezone: 'UTC',
      language: 'en',
      currency: 'USD',
      maintenance_mode: false,
      maintenance_message: 'System is under maintenance. Please check back later.'
    }
  })

  // Update form when settings data loads
  useEffect(() => {
    if (settings) {
      reset(settings)
    }
  }, [settings, reset])

  // Track unsaved changes
  useEffect(() => {
    setUnsavedChanges(isDirty)
  }, [isDirty, setUnsavedChanges])

  const watchedValues = watch()

  const onSubmit = async (data: GeneralSettingsType) => {
    try {
      await updateSettings.mutateAsync(data)
      reset(data) // Reset form state to mark as saved
    } catch (error) {
      console.error('Failed to save general settings:', error)
    }
  }

  const handlePreviewMessage = () => {
    setPreviewMessage(!previewMessage)
  }

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" py={4}>
        <CircularProgress />
      </Box>
    )
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid container spacing={3}>
          {/* Platform Configuration */}
          <Grid item xs={12}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" mb={3}>
                    <Typography variant="h6" fontWeight="bold">
                      Platform Configuration
                    </Typography>
                    <Tooltip title="Configure core platform settings">
                      <IconButton size="small" sx={{ ml: 1 }}>
                        <InfoIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Box>

                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Controller
                        name="platform_name"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="Platform Name"
                            fullWidth
                            helperText="The name displayed in the header and emails"
                          />
                        )}
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <Controller
                        name="tagline"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="Tagline"
                            fullWidth
                            helperText="A brief description of your platform"
                          />
                        )}
                      />
                    </Grid>

                    <Grid item xs={12} md={4}>
                      <Controller
                        name="timezone"
                        control={control}
                        render={({ field }) => (
                          <FormControl fullWidth>
                            <InputLabel>Timezone</InputLabel>
                            <Select {...field} label="Timezone">
                              {timezones.map((tz) => (
                                <MenuItem key={tz.value} value={tz.value}>
                                  {tz.label}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        )}
                      />
                    </Grid>

                    <Grid item xs={12} md={4}>
                      <Controller
                        name="language"
                        control={control}
                        render={({ field }) => (
                          <FormControl fullWidth>
                            <InputLabel>Default Language</InputLabel>
                            <Select {...field} label="Default Language">
                              {languages.map((lang) => (
                                <MenuItem key={lang.value} value={lang.value}>
                                  {lang.label}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        )}
                      />
                    </Grid>

                    <Grid item xs={12} md={4}>
                      <Controller
                        name="currency"
                        control={control}
                        render={({ field }) => (
                          <FormControl fullWidth>
                            <InputLabel>Currency</InputLabel>
                            <Select {...field} label="Currency">
                              {currencies.map((curr) => (
                                <MenuItem key={curr.value} value={curr.value}>
                                  {curr.label}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        )}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>

          {/* Maintenance Mode */}
          <Grid item xs={12}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" mb={3}>
                    <Typography variant="h6" fontWeight="bold">
                      Maintenance Mode
                    </Typography>
                    <Chip 
                      label={watchedValues.maintenance_mode ? 'ACTIVE' : 'INACTIVE'}
                      color={watchedValues.maintenance_mode ? 'error' : 'success'}
                      size="small"
                      sx={{ ml: 2 }}
                    />
                  </Box>

                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <Controller
                        name="maintenance_mode"
                        control={control}
                        render={({ field }) => (
                          <FormControlLabel
                            control={
                              <Switch
                                checked={field.value}
                                onChange={field.onChange}
                                color="warning"
                              />
                            }
                            label="Enable Maintenance Mode"
                          />
                        )}
                      />
                    </Grid>

                    {watchedValues.maintenance_mode && (
                      <>
                        <Grid item xs={12}>
                          <Controller
                            name="maintenance_message"
                            control={control}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                label="Maintenance Message"
                                fullWidth
                                multiline
                                rows={3}
                                helperText="Message displayed to users during maintenance"
                                InputProps={{
                                  endAdornment: (
                                    <IconButton onClick={handlePreviewMessage}>
                                      <PreviewIcon />
                                    </IconButton>
                                  )
                                }}
                              />
                            )}
                          />
                        </Grid>

                        <Grid item xs={12} md={6}>
                          <Controller
                            name="scheduled_start"
                            control={control}
                            render={({ field }) => (
                              <DateTimePicker
                                label="Scheduled Start"
                                value={field.value ? new Date(field.value) : null}
                                onChange={(date) => field.onChange(date?.toISOString())}
                                slotProps={{
                                  textField: {
                                    fullWidth: true,
                                    helperText: "Optional: Schedule maintenance start time"
                                  }
                                }}
                              />
                            )}
                          />
                        </Grid>

                        <Grid item xs={12} md={6}>
                          <Controller
                            name="scheduled_end"
                            control={control}
                            render={({ field }) => (
                              <DateTimePicker
                                label="Scheduled End"
                                value={field.value ? new Date(field.value) : null}
                                onChange={(date) => field.onChange(date?.toISOString())}
                                slotProps={{
                                  textField: {
                                    fullWidth: true,
                                    helperText: "Optional: Schedule maintenance end time"
                                  }
                                }}
                              />
                            )}
                          />
                        </Grid>
                      </>
                    )}
                  </Grid>

                  {/* Preview Message */}
                  {previewMessage && watchedValues.maintenance_message && (
                    <Box mt={3}>
                      <Alert severity="warning" icon={<ScheduleIcon />}>
                        <Typography variant="subtitle2" gutterBottom>
                          Maintenance Message Preview:
                        </Typography>
                        <Typography variant="body2">
                          {watchedValues.maintenance_message}
                        </Typography>
                      </Alert>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          </Grid>

          {/* Save Button */}
          <Grid item xs={12}>
            <Box display="flex" justifyContent="flex-end" gap={2}>
              <Button
                variant="outlined"
                onClick={() => reset()}
                disabled={!isDirty}
              >
                Reset Changes
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={!isDirty || updateSettings.isPending}
                startIcon={updateSettings.isPending ? <CircularProgress size={20} /> : undefined}
              >
                Save General Settings
              </Button>
            </Box>
          </Grid>
        </Grid>
      </form>
    </LocalizationProvider>
  )
}
