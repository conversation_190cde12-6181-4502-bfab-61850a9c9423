version: 2
updates:
  # Enable version updates for npm
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 10
    reviewers:
      - "chiragbiradar"
    assignees:
      - "chiragbiradar"
    commit-message:
      prefix: "chore"
      prefix-development: "chore"
      include: "scope"
    labels:
      - "dependencies"
      - "automated"
    ignore:
      # Ignore major version updates for stable dependencies
      - dependency-name: "react"
        update-types: ["version-update:semver-major"]
      - dependency-name: "react-dom"
        update-types: ["version-update:semver-major"]
      - dependency-name: "next"
        update-types: ["version-update:semver-major"]
    groups:
      # Group React ecosystem updates
      react-ecosystem:
        patterns:
          - "react*"
          - "@types/react*"
      # Group Material-UI updates
      mui-ecosystem:
        patterns:
          - "@mui/*"
          - "@emotion/*"
      # Group testing dependencies
      testing-dependencies:
        patterns:
          - "*jest*"
          - "@testing-library/*"
          - "@playwright/*"
          - "@axe-core/*"
      # Group TypeScript ecosystem
      typescript-ecosystem:
        patterns:
          - "typescript"
          - "@types/*"
          - "@typescript-eslint/*"
      # Group Supabase dependencies
      supabase-ecosystem:
        patterns:
          - "@supabase/*"
          - "supabase"
      # Group development tools
      dev-tools:
        patterns:
          - "eslint*"
          - "prettier"
          - "@next/eslint-plugin-next"

  # Enable version updates for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 5
    reviewers:
      - "chiragbiradar"
    assignees:
      - "chiragbiradar"
    commit-message:
      prefix: "ci"
      include: "scope"
    labels:
      - "github-actions"
      - "automated"
