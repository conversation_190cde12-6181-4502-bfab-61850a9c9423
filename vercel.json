{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm install", "devCommand": "npm run dev", "build": {"env": {"NEXT_TELEMETRY_DISABLED": "1"}}, "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "rewrites": [{"source": "/api/health", "destination": "/api/health"}], "redirects": [{"source": "/admin", "destination": "/dashboard", "permanent": true}], "trailingSlash": false, "cleanUrls": true, "github": {"enabled": true, "autoAlias": true}}