'use client'

import React, { useState, useMemo } from 'react'
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Fab,
  Alert,
  Skeleton,
  useTheme,
  useMediaQuery
} from '@mui/material'
import {
  Add as AddIcon,
  FileUpload as ImportIcon,
  Download as ExportIcon,
  MoreVert as MoreIcon,
  Group as GroupIcon,
  Message as MessageIcon,
  Assignment as AssignIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material'
import { motion, AnimatePresence } from 'framer-motion'

import { 
  useLearners, 
  useLearnerStats,
  useDeleteLearner,
  useAssignPath,
  useSendMessage
} from '@/lib/hooks/useLearners'
import { useLearnersStore } from '@/lib/store'
import { Learner, LearnerFilters, LearnerSortOptions } from '@/lib/types/learners'

// Components
import LearnersTable from '@/components/learners/LearnersTable'
import LearnerModal from '@/components/learners/LearnerModal'
import BulkActions from '@/components/learners/BulkActions'
import LearnerFiltersComponent from '@/components/learners/LearnerFilters'
import StatsCards from '@/components/learners/StatsCards'
import ImportModal from '@/components/learners/ImportModal'
import AssignPathModal from '@/components/learners/AssignPathModal'
import MessageModal from '@/components/learners/MessageModal'

export default function LearnersPage() {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))

  // Local state
  const [page, setPage] = useState(1)
  const [showFilters, setShowFilters] = useState(false)
  const [selectedLearners, setSelectedLearners] = useState<string[]>([])
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null)
  const [selectedLearner, setSelectedLearner] = useState<Learner | null>(null)

  // Modal states
  const [showLearnerModal, setShowLearnerModal] = useState(false)
  const [showImportModal, setShowImportModal] = useState(false)
  const [showAssignModal, setShowAssignModal] = useState(false)
  const [showMessageModal, setShowMessageModal] = useState(false)
  const [editingLearner, setEditingLearner] = useState<Learner | null>(null)

  // Store state
  const { filters, setFilters } = useLearnersStore()

  // API hooks
  const { data: learnersData, isLoading, error, refetch } = useLearners(filters, undefined, page, 20)
  const { data: statsData, isLoading: statsLoading } = useLearnerStats()
  const deleteMutation = useDeleteLearner()
  const assignMutation = useAssignPath()
  const messageMutation = useSendMessage()

  // Computed values
  const learners = learnersData?.data || []
  const totalLearners = learnersData?.total || 0
  const hasMore = learnersData?.has_more || false

  const handleCreateLearner = () => {
    setEditingLearner(null)
    setShowLearnerModal(true)
  }

  const handleEditLearner = (learner: Learner) => {
    setEditingLearner(learner)
    setShowLearnerModal(true)
  }

  const handleDeleteLearner = async (learner: Learner) => {
    if (window.confirm(`Are you sure you want to deactivate ${learner.full_name}?`)) {
      await deleteMutation.mutateAsync(learner.id)
      setMenuAnchor(null)
    }
  }

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, learner: Learner) => {
    event.stopPropagation()
    setMenuAnchor(event.currentTarget)
    setSelectedLearner(learner)
  }

  const handleMenuClose = () => {
    setMenuAnchor(null)
    setSelectedLearner(null)
  }

  const handleBulkAssign = () => {
    if (selectedLearners.length === 0) {
      return
    }
    setShowAssignModal(true)
  }

  const handleBulkMessage = () => {
    if (selectedLearners.length === 0) {
      return
    }
    setShowMessageModal(true)
  }

  const handleExport = async () => {
    // TODO: Implement CSV export
    console.log('Exporting learners...')
  }

  const handleRefresh = () => {
    refetch()
  }

  const handleFilterChange = (newFilters: Partial<LearnerFilters>) => {
    setFilters(newFilters)
    setPage(1) // Reset to first page when filters change
  }

  const handlePageChange = (newPage: number) => {
    setPage(newPage)
  }

  const handleSelectionChange = (selectedIds: string[]) => {
    setSelectedLearners(selectedIds)
  }

  if (error) {
    return (
      <Box p={3}>
        <Alert severity="error">
          Failed to load learners. Please try again.
        </Alert>
      </Box>
    )
  }

  return (
    <Box>
      {/* Header */}
      <Box mb={3} display="flex" justifyContent="space-between" alignItems="center" flexWrap="wrap" gap={2}>
        <Box>
          <Typography variant="h4" fontWeight="bold" gutterBottom>
            Learners
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage learners, track progress, and analyze engagement
          </Typography>
        </Box>

        <Box display="flex" gap={1} flexWrap="wrap">
          <Button
            variant="outlined"
            startIcon={<FilterIcon />}
            onClick={() => setShowFilters(!showFilters)}
            sx={{ display: { xs: 'none', sm: 'flex' } }}
          >
            Filters
          </Button>
          
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            disabled={isLoading}
          >
            Refresh
          </Button>

          <Button
            variant="outlined"
            startIcon={<ExportIcon />}
            onClick={handleExport}
            sx={{ display: { xs: 'none', md: 'flex' } }}
          >
            Export
          </Button>

          <Button
            variant="outlined"
            startIcon={<ImportIcon />}
            onClick={() => setShowImportModal(true)}
            sx={{ display: { xs: 'none', md: 'flex' } }}
          >
            Import
          </Button>

          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateLearner}
            sx={{ display: { xs: 'none', sm: 'flex' } }}
          >
            Add Learner
          </Button>
        </Box>
      </Box>

      {/* Stats Cards */}
      <Box mb={3}>
        <StatsCards data={statsData?.analytics} loading={statsLoading} />
      </Box>

      {/* Filters */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Box mb={3}>
              <LearnerFiltersComponent
                filters={filters}
                onFiltersChange={handleFilterChange}
              />
            </Box>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Bulk Actions */}
      {selectedLearners.length > 0 && (
        <Box mb={2}>
          <BulkActions
            selectedCount={selectedLearners.length}
            onAssign={handleBulkAssign}
            onMessage={handleBulkMessage}
            onClearSelection={() => setSelectedLearners([])}
          />
        </Box>
      )}

      {/* Learners Table */}
      <Card>
        <LearnersTable
          learners={learners}
          loading={isLoading}
          selectedLearners={selectedLearners}
          onSelectionChange={handleSelectionChange}
          onEdit={handleEditLearner}
          onDelete={handleDeleteLearner}
          onMenuClick={handleMenuOpen}
          page={page}
          totalCount={totalLearners}
          onPageChange={handlePageChange}
        />
      </Card>

      {/* Floating Action Button for Mobile */}
      <Fab
        color="primary"
        aria-label="add learner"
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
          display: { xs: 'flex', sm: 'none' }
        }}
        onClick={handleCreateLearner}
      >
        <AddIcon />
      </Fab>

      {/* Context Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => selectedLearner && handleEditLearner(selectedLearner)}>
          Edit Profile
        </MenuItem>
        <MenuItem onClick={() => setShowAssignModal(true)}>
          <AssignIcon sx={{ mr: 1 }} />
          Assign Path
        </MenuItem>
        <MenuItem onClick={() => setShowMessageModal(true)}>
          <MessageIcon sx={{ mr: 1 }} />
          Send Message
        </MenuItem>
        <MenuItem 
          onClick={() => selectedLearner && handleDeleteLearner(selectedLearner)}
          sx={{ color: 'error.main' }}
        >
          Deactivate
        </MenuItem>
      </Menu>

      {/* Modals */}
      <LearnerModal
        open={showLearnerModal}
        onClose={() => setShowLearnerModal(false)}
        learner={editingLearner}
      />

      <ImportModal
        open={showImportModal}
        onClose={() => setShowImportModal(false)}
      />

      <AssignPathModal
        open={showAssignModal}
        onClose={() => setShowAssignModal(false)}
        learnerIds={selectedLearners.length > 0 ? selectedLearners : selectedLearner ? [selectedLearner.id] : []}
      />

      <MessageModal
        open={showMessageModal}
        onClose={() => setShowMessageModal(false)}
        recipientIds={selectedLearners.length > 0 ? selectedLearners : selectedLearner ? [selectedLearner.id] : []}
      />
    </Box>
  )
}
