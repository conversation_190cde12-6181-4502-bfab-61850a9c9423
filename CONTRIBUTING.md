# Contributing to ZenithLearn AI

Thank you for your interest in contributing to ZenithLearn AI! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites

- Node.js 18.x or higher
- npm or yarn package manager
- Git
- Supabase account (for backend integration)
- OpenAI API key (for AI features)

### Development Setup

1. **Fork and Clone**
   ```bash
   git clone https://github.com/your-username/zenithlearn-ai-admin.git
   cd zenithlearn-ai-admin
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.local.example .env.local
   # Fill in your environment variables
   ```

4. **Start Development Server**
   ```bash
   npm run dev
   ```

## 📋 Development Guidelines

### Code Style

- **TypeScript**: All new code must be written in TypeScript
- **ESLint**: Follow the configured ESLint rules (`npm run lint`)
- **Prettier**: Code formatting is handled by Prettier
- **Naming Conventions**:
  - Components: PascalCase (`UserProfile.tsx`)
  - Files: kebab-case (`user-profile.utils.ts`)
  - Variables/Functions: camelCase (`getUserData`)
  - Constants: UPPER_SNAKE_CASE (`MAX_FILE_SIZE`)

### Component Guidelines

- Use functional components with hooks
- Implement proper TypeScript interfaces
- Follow Material-UI design patterns
- Ensure accessibility (WCAG 2.1 AA compliance)
- Add proper error boundaries

### Testing Requirements

- **Unit Tests**: Required for all new components and utilities
- **Integration Tests**: Required for API interactions
- **E2E Tests**: Required for critical user flows
- **Accessibility Tests**: Use @axe-core/react for a11y testing

```bash
# Run all tests
npm test

# Run E2E tests
npm run test:e2e

# Run type checking
npm run type-check
```

### Database Changes

- All database changes must be done through Supabase migrations
- Include both up and down migration scripts
- Test migrations on a separate Supabase project first
- Document schema changes in commit messages

### AI Integration Guidelines

- Use OpenAI API responsibly with rate limiting
- Cache AI responses when possible
- Implement fallback mechanisms for AI failures
- Follow OpenAI usage policies

## 🔄 Contribution Workflow

### 1. Issue Creation

- Check existing issues before creating new ones
- Use issue templates for bug reports and feature requests
- Provide detailed descriptions and reproduction steps
- Add appropriate labels

### 2. Branch Strategy

- Create feature branches from `main`
- Use descriptive branch names: `feature/user-management`, `fix/auth-redirect`
- Keep branches focused on single features/fixes

### 3. Commit Guidelines

Follow [Conventional Commits](https://www.conventionalcommits.org/):

```
type(scope): description

feat(auth): add multi-factor authentication
fix(dashboard): resolve chart rendering issue
docs(readme): update installation instructions
test(components): add unit tests for UserCard
```

**Types:**
- `feat`: New features
- `fix`: Bug fixes
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

### 4. Pull Request Process

1. **Before Submitting**
   - Ensure all tests pass
   - Run linting and type checking
   - Update documentation if needed
   - Add/update tests for new functionality

2. **PR Description**
   - Use the PR template
   - Link related issues
   - Describe changes and rationale
   - Include screenshots for UI changes

3. **Review Process**
   - At least one code review required
   - Address all feedback
   - Maintain clean commit history
   - Squash commits if requested

## 🏗️ Architecture Guidelines

### Project Structure

```
├── app/                    # Next.js App Router pages
├── components/            # Reusable UI components
├── lib/                   # Utility libraries and configurations
├── supabase/             # Database migrations and functions
├── __tests__/            # Unit tests
├── tests/e2e/            # End-to-end tests
└── public/               # Static assets
```

### State Management

- Use Zustand for global state
- Use React Query for server state
- Keep component state local when possible
- Follow immutable update patterns

### Performance Guidelines

- Implement proper code splitting
- Use Next.js Image optimization
- Minimize bundle size
- Implement proper caching strategies
- Monitor Core Web Vitals

## 🔒 Security Guidelines

- Never commit sensitive data (API keys, passwords)
- Use environment variables for configuration
- Implement proper input validation
- Follow OWASP security guidelines
- Use Supabase RLS policies for data access

## 📚 Documentation

- Update README.md for significant changes
- Document new components with JSDoc
- Update API documentation
- Include inline comments for complex logic

## 🐛 Bug Reports

When reporting bugs, include:

- Clear description of the issue
- Steps to reproduce
- Expected vs actual behavior
- Environment details (OS, browser, Node.js version)
- Screenshots or videos if applicable
- Console errors or logs

## 💡 Feature Requests

For new features:

- Describe the problem you're solving
- Propose a solution
- Consider alternative approaches
- Discuss impact on existing functionality
- Provide mockups or examples if applicable

## 📞 Getting Help

- **GitHub Issues**: For bugs and feature requests
- **GitHub Discussions**: For questions and general discussion
- **Code Review**: Tag maintainers for urgent reviews

## 🏆 Recognition

Contributors will be recognized in:

- README.md contributors section
- Release notes for significant contributions
- GitHub contributor graphs

## 📄 License

By contributing to ZenithLearn AI, you agree that your contributions will be licensed under the MIT License.

---

Thank you for contributing to ZenithLearn AI! 🚀
