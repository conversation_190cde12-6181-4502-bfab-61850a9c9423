'use client'

import { useState, useCallback } from 'react'
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  Fab,
  useTheme,
  useMediaQuery
} from '@mui/material'
import {
  Add as AddIcon,
  Upload as UploadIcon,
  ViewModule as GridViewIcon,
  ViewList as ListViewIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { motion, AnimatePresence } from 'framer-motion'
import toast from 'react-hot-toast'

import { ContentService } from '@/lib/services/contentService'
import { useAuthStore } from '@/lib/store'
import type { ContentFilter, ContentItem } from '@/lib/types/content'

// Import components (to be created)
import ContentGrid from '@/components/content/ContentGrid'
import ContentFilters from '@/components/content/ContentFilters'
import ContentUploadDialog from '@/components/content/ContentUploadDialog'
import ContentPreviewDialog from '@/components/content/ContentPreviewDialog'
import ContentCategories from '@/components/content/ContentCategories'

export default function ContentLibraryPage() {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const { user } = useAuthStore()
  const queryClient = useQueryClient()

  // State management
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showFilters, setShowFilters] = useState(false)
  const [showCategories, setShowCategories] = useState(!isMobile)
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false)
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false)
  const [selectedContent, setSelectedContent] = useState<ContentItem | null>(null)
  const [selectedItems, setSelectedItems] = useState<number[]>([])
  
  const [filter, setFilter] = useState<ContentFilter>({
    page: 1,
    limit: 20,
    sort_by: 'created_at',
    sort_order: 'desc'
  })

  // Fetch content data
  const {
    data: contentData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['content', filter],
    queryFn: () => ContentService.getContent(filter),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1, // Only retry once to avoid infinite loops during development
  })

  // Fetch categories
  const { data: categories } = useQuery({
    queryKey: ['content-categories'],
    queryFn: () => ContentService.getCategories(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  })

  // Event handlers
  const handleFilterChange = useCallback((newFilter: Partial<ContentFilter>) => {
    setFilter(prev => ({
      ...prev,
      ...newFilter,
      page: 1 // Reset to first page when filter changes
    }))
  }, [])

  const handleContentSelect = useCallback((content: ContentItem) => {
    setSelectedContent(content)
    setPreviewDialogOpen(true)
  }, [])

  const handleContentUpload = useCallback(async () => {
    // Refresh content list after upload
    await queryClient.invalidateQueries({ queryKey: ['content'] })
    toast.success('Content uploaded successfully!')
  }, [queryClient])

  const handleRefresh = useCallback(() => {
    refetch()
    toast.success('Content refreshed!')
  }, [refetch])

  const handleBulkAction = useCallback(async (action: string) => {
    if (selectedItems.length === 0) {
      toast.error('Please select items first')
      return
    }

    try {
      // Handle bulk actions here
      switch (action) {
        case 'delete':
          // Implement bulk delete
          break
        case 'download':
          // Implement bulk download
          break
        default:
          break
      }
      
      setSelectedItems([])
      await queryClient.invalidateQueries({ queryKey: ['content'] })
    } catch (error) {
      toast.error('Bulk action failed')
    }
  }, [selectedItems, queryClient])

  // Loading state
  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography>Loading content...</Typography>
      </Box>
    )
  }

  // Error state
  if (error) {
    return (
      <Box p={3}>
        <Alert severity="error">
          Failed to load content library. Please try again.
        </Alert>
      </Box>
    )
  }

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box mb={3}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box>
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              Content Library
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Manage and organize your learning content
            </Typography>
          </Box>
          
          <Box display="flex" gap={1} alignItems="center">
            {!isMobile && (
              <>
                <Tooltip title="Refresh">
                  <IconButton onClick={handleRefresh}>
                    <RefreshIcon />
                  </IconButton>
                </Tooltip>
                
                <Tooltip title="Toggle Filters">
                  <IconButton 
                    onClick={() => setShowFilters(!showFilters)}
                    color={showFilters ? 'primary' : 'default'}
                  >
                    <FilterIcon />
                  </IconButton>
                </Tooltip>
                
                <Tooltip title={`Switch to ${viewMode === 'grid' ? 'list' : 'grid'} view`}>
                  <IconButton onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}>
                    {viewMode === 'grid' ? <ListViewIcon /> : <GridViewIcon />}
                  </IconButton>
                </Tooltip>
              </>
            )}
            
            <Button
              variant="contained"
              startIcon={<UploadIcon />}
              onClick={() => setUploadDialogOpen(true)}
              sx={{ ml: 1 }}
            >
              Upload Content
            </Button>
          </Box>
        </Box>

        {/* Stats Cards */}
        <Grid container spacing={2} mb={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" color="primary">
                  {contentData?.total_count || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Items
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" color="primary">
                  {categories?.length || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Categories
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" color="primary">
                  {selectedItems.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Selected
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" color="success.main">
                  Active
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Status
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>

      {/* Main Content Area */}
      <Box sx={{ display: 'flex', flex: 1, gap: 2, overflow: 'hidden' }}>
        {/* Categories Sidebar */}
        <AnimatePresence>
          {showCategories && (
            <motion.div
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: 280, opacity: 1 }}
              exit={{ width: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
              style={{ overflow: 'hidden' }}
            >
              <ContentCategories
                categories={categories || []}
                selectedCategory={filter.category_id?.[0]}
                onCategorySelect={(categoryId) => 
                  handleFilterChange({ category_id: categoryId ? [categoryId] : undefined })
                }
              />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Content Area */}
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
          {/* Filters */}
          <AnimatePresence>
            {showFilters && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                style={{ overflow: 'hidden' }}
              >
                <ContentFilters
                  filter={filter}
                  onFilterChange={handleFilterChange}
                  categories={categories || []}
                />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Content Grid */}
          <Box sx={{ flex: 1, overflow: 'auto' }}>
            <ContentGrid
              content={contentData?.items || []}
              viewMode={viewMode}
              selectedItems={selectedItems}
              onItemSelect={handleContentSelect}
              onSelectionChange={setSelectedItems}
              onBulkAction={handleBulkAction}
              loading={isLoading}
            />
          </Box>
        </Box>
      </Box>

      {/* Floating Action Button for Mobile */}
      {isMobile && (
        <Fab
          color="primary"
          aria-label="upload content"
          sx={{ position: 'fixed', bottom: 16, right: 16 }}
          onClick={() => setUploadDialogOpen(true)}
        >
          <AddIcon />
        </Fab>
      )}

      {/* Dialogs */}
      <ContentUploadDialog
        open={uploadDialogOpen}
        onClose={() => setUploadDialogOpen(false)}
        onUpload={handleContentUpload}
        categories={categories || []}
      />

      <ContentPreviewDialog
        open={previewDialogOpen}
        onClose={() => setPreviewDialogOpen(false)}
        content={selectedContent}
      />
    </Box>
  )
}
