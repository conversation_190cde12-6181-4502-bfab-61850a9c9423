'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import {
  Box,
  Typography,
  Button,
  Tabs,
  Tab,
  Card,
  CardContent,
  Grid,
  Chip,
  Avatar,
  IconButton,
  Breadcrumbs,
  Link,
  <PERSON>ert,
  Skeleton,
} from '@mui/material'
import {
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  Settings as SettingsIcon,
  People as PeopleIcon,
  School as SchoolIcon,
  Assessment as ProgressIcon,
  Chat as ChatIcon,
  Timeline as MilestonesIcon,
  Notifications as NotificationsIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'

import { useGroupsStore } from '@/lib/store'
import { GroupsService } from '@/lib/services/groups'
import { Group } from '@/lib/types/groups'
import GroupForm from '@/components/groups/GroupForm'
import MemberManagement from '@/components/groups/MemberManagement'
import GroupAssignments from '@/components/groups/GroupAssignments'
import GroupProgress from '@/components/groups/GroupProgress'
import GroupCommunication from '@/components/groups/GroupCommunication'
import GroupMilestones from '@/components/groups/GroupMilestones'

interface TabPanelProps {
  children?: React.ReactNode
  index: number
  value: number
}

function TabPanel({ children, value, index, ...other }: TabPanelProps) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`group-tabpanel-${index}`}
      aria-labelledby={`group-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  )
}

export default function GroupDetailPage() {
  const params = useParams()
  const router = useRouter()
  const groupId = parseInt(params.id as string)

  const {
    selectedGroup,
    members,
    assignments,
    progress,
    loading,
    setSelectedGroup,
    setMembers,
    setAssignments,
    setProgress,
    setLoading,
    updateGroup,
  } = useGroupsStore()

  const [activeTab, setActiveTab] = useState(0)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (groupId) {
      loadGroupDetails()
    }
  }, [groupId])

  const loadGroupDetails = async () => {
    try {
      setLoading(true)
      setError(null)

      // Load group details
      const group = await GroupsService.getGroup(groupId)
      setSelectedGroup(group)

      // Load members
      const membersResponse = await GroupsService.getGroupMembers(groupId)
      setMembers(membersResponse.data)

      // Load assignments
      const assignmentsData = await GroupsService.getGroupAssignments(groupId)
      setAssignments(assignmentsData)

      // Load progress
      const progressResponse = await GroupsService.getGroupProgress(groupId)
      setProgress(progressResponse.data)
    } catch (error) {
      console.error('Failed to load group details:', error)
      setError('Failed to load group details. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleEditGroup = async (groupData: Partial<Group>) => {
    try {
      const updatedGroup = await GroupsService.updateGroup(groupId, groupData)
      updateGroup(groupId, updatedGroup)
      setSelectedGroup(updatedGroup)
      setEditModalOpen(false)
    } catch (error) {
      console.error('Failed to update group:', error)
    }
  }

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success'
      case 'inactive':
        return 'warning'
      case 'archived':
        return 'default'
      case 'draft':
        return 'info'
      default:
        return 'default'
    }
  }

  if (loading && !selectedGroup) {
    return (
      <Box sx={{ p: 3 }}>
        <Skeleton variant="text" width={200} height={40} />
        <Skeleton variant="rectangular" width="100%" height={200} sx={{ mt: 2 }} />
        <Skeleton variant="rectangular" width="100%" height={400} sx={{ mt: 2 }} />
      </Box>
    )
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button variant="outlined" onClick={() => router.back()}>
          Go Back
        </Button>
      </Box>
    )
  }

  if (!selectedGroup) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning" sx={{ mb: 2 }}>
          Group not found
        </Alert>
        <Button variant="outlined" onClick={() => router.back()}>
          Go Back
        </Button>
      </Box>
    )
  }

  const tabs = [
    { label: 'Overview', icon: <PeopleIcon />, value: 0 },
    { label: 'Members', icon: <PeopleIcon />, value: 1 },
    { label: 'Assignments', icon: <SchoolIcon />, value: 2 },
    { label: 'Progress', icon: <ProgressIcon />, value: 3 },
    { label: 'Communication', icon: <ChatIcon />, value: 4 },
    { label: 'Milestones', icon: <MilestonesIcon />, value: 5 },
  ]

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <IconButton onClick={() => router.back()} sx={{ mr: 1 }}>
              <ArrowBackIcon />
            </IconButton>
            <Breadcrumbs>
              <Link
                color="inherit"
                href="/dashboard/groups"
                sx={{ textDecoration: 'none', '&:hover': { textDecoration: 'underline' } }}
              >
                Groups & Batches
              </Link>
              <Typography color="text.primary">{selectedGroup.name}</Typography>
            </Breadcrumbs>
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Avatar sx={{ width: 64, height: 64, bgcolor: 'primary.main', fontSize: '1.5rem' }}>
                {selectedGroup.name[0]}
              </Avatar>
              <Box>
                <Typography variant="h4" component="h1" fontWeight="bold">
                  {selectedGroup.name}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                  <Chip
                    label={selectedGroup.status}
                    color={getStatusColor(selectedGroup.status) as any}
                    size="small"
                    variant="outlined"
                  />
                  {selectedGroup.tags && selectedGroup.tags.length > 0 && (
                    <>
                      {selectedGroup.tags.slice(0, 3).map((tag) => (
                        <Chip key={tag} label={tag} size="small" variant="outlined" />
                      ))}
                      {selectedGroup.tags.length > 3 && (
                        <Chip label={`+${selectedGroup.tags.length - 3}`} size="small" variant="outlined" />
                      )}
                    </>
                  )}
                </Box>
                {selectedGroup.description && (
                  <Typography variant="body1" color="text.secondary" sx={{ mt: 1, maxWidth: 600 }}>
                    {selectedGroup.description}
                  </Typography>
                )}
              </Box>
            </Box>

            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="outlined"
                startIcon={<EditIcon />}
                onClick={() => setEditModalOpen(true)}
              >
                Edit Group
              </Button>
              <IconButton>
                <SettingsIcon />
              </IconButton>
            </Box>
          </Box>
        </Box>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: 'primary.light' }}>
                    <PeopleIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h5" fontWeight="bold">
                      {selectedGroup.member_count || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Members
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: 'success.light' }}>
                    <SchoolIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h5" fontWeight="bold">
                      {selectedGroup.assigned_paths_count || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Learning Paths
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: 'info.light' }}>
                    <ProgressIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h5" fontWeight="bold">
                      {selectedGroup.completion_rate || 0}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Completion Rate
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: 'warning.light' }}>
                    <NotificationsIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h5" fontWeight="bold">
                      {/* TODO: Calculate active notifications */}
                      0
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Active Notifications
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Tabs */}
        <Card>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{ px: 2 }}
            >
              {tabs.map((tab) => (
                <Tab
                  key={tab.value}
                  icon={tab.icon}
                  label={tab.label}
                  iconPosition="start"
                  sx={{ minHeight: 64 }}
                />
              ))}
            </Tabs>
          </Box>

          <TabPanel value={activeTab} index={0}>
            {/* Overview content will be added */}
            <Typography variant="h6">Group Overview</Typography>
            <Typography variant="body2" color="text.secondary">
              Overview content coming soon...
            </Typography>
          </TabPanel>

          <TabPanel value={activeTab} index={1}>
            <MemberManagement
              group={selectedGroup}
              members={members}
              onRefresh={loadGroupDetails}
            />
          </TabPanel>

          <TabPanel value={activeTab} index={2}>
            <GroupAssignments
              group={selectedGroup}
              assignments={assignments}
              onRefresh={loadGroupDetails}
            />
          </TabPanel>

          <TabPanel value={activeTab} index={3}>
            <GroupProgress
              group={selectedGroup}
              progress={progress}
              onRefresh={loadGroupDetails}
            />
          </TabPanel>

          <TabPanel value={activeTab} index={4}>
            <GroupCommunication
              group={selectedGroup}
              onRefresh={loadGroupDetails}
            />
          </TabPanel>

          <TabPanel value={activeTab} index={5}>
            <GroupMilestones
              group={selectedGroup}
              onRefresh={loadGroupDetails}
            />
          </TabPanel>
        </Card>

        {/* Edit Group Modal */}
        <GroupForm
          open={editModalOpen}
          onClose={() => setEditModalOpen(false)}
          onSubmit={handleEditGroup}
          group={selectedGroup}
          title="Edit Group"
        />
      </Box>
    </motion.div>
  )
}
