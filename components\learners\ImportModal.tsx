'use client'

import React, { useState, useCallback } from 'react'
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Box,
  Typography,
  Stepper,
  Step,
  StepLabel,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Alert,
  IconButton,
  Chip
} from '@mui/material'
import {
  CloudUpload as UploadIcon,
  Close as CloseIcon,
  Download as DownloadIcon
} from '@mui/icons-material'
import { useDropzone } from 'react-dropzone'
import Papa from 'papaparse'

import { useBulkImportLearners } from '@/lib/hooks/useLearners'

interface ImportModalProps {
  open: boolean
  onClose: () => void
}

interface CSVData {
  headers: string[]
  rows: string[][]
}

const ImportModal: React.FC<ImportModalProps> = ({ open, onClose }) => {
  const [activeStep, setActiveStep] = useState(0)
  const [csvFile, setCsvFile] = useState<File | null>(null)
  const [csvData, setCsvData] = useState<CSVData | null>(null)
  const [mapping, setMapping] = useState<Record<string, string>>({})
  const [sendWelcomeEmails, setSendWelcomeEmails] = useState(true)
  const [defaultRoleId, setDefaultRoleId] = useState(2)

  const importMutation = useBulkImportLearners()

  const steps = ['Upload CSV', 'Map Fields', 'Review & Import']

  const requiredFields = [
    { key: 'email', label: 'Email Address', required: true },
    { key: 'full_name', label: 'Full Name', required: true },
    { key: 'department', label: 'Department', required: false },
    { key: 'position', label: 'Position', required: false },
    { key: 'phone', label: 'Phone', required: false },
    { key: 'location', label: 'Location', required: false }
  ]

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (file) {
      setCsvFile(file)
      
      Papa.parse(file, {
        header: false,
        complete: (results) => {
          const data = results.data as string[][]
          if (data.length > 0) {
            setCsvData({
              headers: data[0],
              rows: data.slice(1, 6) // Show first 5 rows for preview
            })
            setActiveStep(1)
          }
        },
        error: (error) => {
          console.error('CSV parsing error:', error)
        }
      })
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.ms-excel': ['.csv']
    },
    maxFiles: 1
  })

  const handleMappingChange = (fieldKey: string, csvHeader: string) => {
    setMapping(prev => ({
      ...prev,
      [fieldKey]: csvHeader
    }))
  }

  const handleNext = () => {
    setActiveStep(prev => prev + 1)
  }

  const handleBack = () => {
    setActiveStep(prev => prev - 1)
  }

  const handleImport = async () => {
    if (!csvFile) return

    try {
      await importMutation.mutateAsync({
        file: csvFile,
        mapping,
        send_welcome_emails: sendWelcomeEmails,
        default_role_id: defaultRoleId
      })
      onClose()
      handleReset()
    } catch (error) {
      console.error('Import error:', error)
    }
  }

  const handleReset = () => {
    setActiveStep(0)
    setCsvFile(null)
    setCsvData(null)
    setMapping({})
    setSendWelcomeEmails(true)
    setDefaultRoleId(2)
  }

  const handleClose = () => {
    if (!importMutation.isPending) {
      onClose()
      handleReset()
    }
  }

  const downloadTemplate = () => {
    const headers = ['email', 'full_name', 'department', 'position', 'phone', 'location']
    const csvContent = headers.join(',') + '\n' + 
      '<EMAIL>,John Doe,Engineering,Software Engineer,+1234567890,New York\n' +
      '<EMAIL>,Jane Smith,Marketing,Marketing Manager,+1234567891,San Francisco'
    
    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'learners_template.csv'
    a.click()
    window.URL.revokeObjectURL(url)
  }

  const canProceed = () => {
    if (activeStep === 1) {
      return requiredFields
        .filter(field => field.required)
        .every(field => mapping[field.key])
    }
    return true
  }

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{ sx: { borderRadius: 2 } }}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6" fontWeight="bold">
            Import Learners
          </Typography>
          <IconButton onClick={handleClose} disabled={importMutation.isPending}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {activeStep === 0 && (
          <Box>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
              <Typography variant="h6">Upload CSV File</Typography>
              <Button
                startIcon={<DownloadIcon />}
                onClick={downloadTemplate}
                variant="outlined"
                size="small"
              >
                Download Template
              </Button>
            </Box>

            <Paper
              {...getRootProps()}
              sx={{
                p: 4,
                textAlign: 'center',
                border: '2px dashed',
                borderColor: isDragActive ? 'primary.main' : 'grey.300',
                backgroundColor: isDragActive ? 'action.hover' : 'background.paper',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
            >
              <input {...getInputProps()} />
              <UploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                {isDragActive ? 'Drop the CSV file here' : 'Drag & drop a CSV file here'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                or click to select a file
              </Typography>
            </Paper>

            {csvFile && (
              <Alert severity="success" sx={{ mt: 2 }}>
                File "{csvFile.name}" uploaded successfully. {csvData?.rows.length} rows detected.
              </Alert>
            )}
          </Box>
        )}

        {activeStep === 1 && csvData && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Map CSV Columns to Fields
            </Typography>
            <Typography variant="body2" color="text.secondary" mb={3}>
              Map your CSV columns to the corresponding learner fields. Required fields must be mapped.
            </Typography>

            <Box mb={3}>
              {requiredFields.map((field) => (
                <Box key={field.key} display="flex" alignItems="center" gap={2} mb={2}>
                  <Box minWidth={150}>
                    <Typography variant="body2" fontWeight="medium">
                      {field.label}
                      {field.required && <Chip label="Required" size="small" color="error" sx={{ ml: 1 }} />}
                    </Typography>
                  </Box>
                  <FormControl size="small" sx={{ minWidth: 200 }}>
                    <InputLabel>Select CSV Column</InputLabel>
                    <Select
                      value={mapping[field.key] || ''}
                      label="Select CSV Column"
                      onChange={(e) => handleMappingChange(field.key, e.target.value)}
                    >
                      <MenuItem value="">None</MenuItem>
                      {csvData.headers.map((header, index) => (
                        <MenuItem key={index} value={header}>
                          {header}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>
              ))}
            </Box>

            <Typography variant="subtitle2" gutterBottom>
              Preview (First 5 rows)
            </Typography>
            <TableContainer component={Paper} variant="outlined">
              <Table size="small">
                <TableHead>
                  <TableRow>
                    {csvData.headers.map((header, index) => (
                      <TableCell key={index}>{header}</TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {csvData.rows.map((row, index) => (
                    <TableRow key={index}>
                      {row.map((cell, cellIndex) => (
                        <TableCell key={cellIndex}>{cell}</TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        )}

        {activeStep === 2 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Review & Import
            </Typography>
            
            <Box mb={3}>
              <FormControlLabel
                control={
                  <Switch
                    checked={sendWelcomeEmails}
                    onChange={(e) => setSendWelcomeEmails(e.target.checked)}
                  />
                }
                label="Send welcome emails to new learners"
              />
            </Box>

            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>Default Role</InputLabel>
              <Select
                value={defaultRoleId}
                label="Default Role"
                onChange={(e) => setDefaultRoleId(Number(e.target.value))}
              >
                <MenuItem value={2}>Learner</MenuItem>
                <MenuItem value={3}>Manager</MenuItem>
                <MenuItem value={4}>Instructor</MenuItem>
              </Select>
            </FormControl>

            <Alert severity="info">
              Ready to import {csvData?.rows.length} learners. This action cannot be undone.
            </Alert>

            {importMutation.error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {(importMutation.error as any)?.message || 'Import failed'}
              </Alert>
            )}
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button onClick={handleClose} disabled={importMutation.isPending}>
          Cancel
        </Button>
        
        {activeStep > 0 && (
          <Button onClick={handleBack} disabled={importMutation.isPending}>
            Back
          </Button>
        )}
        
        {activeStep < steps.length - 1 ? (
          <Button
            onClick={handleNext}
            variant="contained"
            disabled={!canProceed()}
          >
            Next
          </Button>
        ) : (
          <Button
            onClick={handleImport}
            variant="contained"
            disabled={!canProceed() || importMutation.isPending}
          >
            {importMutation.isPending ? 'Importing...' : 'Import Learners'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  )
}

export default ImportModal
