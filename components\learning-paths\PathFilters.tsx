'use client'

import React from 'react'
import {
  Box,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  OutlinedInput,
  TextField,
  Button,
  Paper,
  Typography,
  Autocomplete
} from '@mui/material'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { Clear as ClearIcon } from '@mui/icons-material'

import { useLearningPathsStore } from '@/lib/store'

const PathFilters: React.FC = () => {
  const { filters, setFilters } = useLearningPathsStore()

  // Mock data - in real app, these would come from API
  const categories = [
    'Technology',
    'Leadership',
    'Sales',
    'Marketing',
    'HR',
    'Finance',
    'Operations',
    'Compliance',
    'Soft Skills'
  ]

  const availableTags = [
    'AI/ML',
    'Data Science',
    'Web Development',
    'Mobile Development',
    'Cloud Computing',
    'Cybersecurity',
    'Project Management',
    'Communication',
    'Team Building',
    'Customer Service',
    'Analytics',
    'Strategy',
    'Innovation',
    'Digital Transformation'
  ]

  const creators = [
    { id: '1', name: '<PERSON>', email: '<EMAIL>' },
    { id: '2', name: '<PERSON>', email: '<EMAIL>' },
    { id: '3', name: 'Mike Chen', email: '<EMAIL>' },
    { id: '4', name: 'Emily Davis', email: '<EMAIL>' }
  ]

  const handleFilterChange = (key: string, value: any) => {
    setFilters({ [key]: value })
  }

  const handleTagsChange = (newTags: string[]) => {
    setFilters({ tags: newTags })
  }

  const handleDateRangeChange = (field: 'start' | 'end', date: Date | null) => {
    const currentRange = filters.date_range || { start: '', end: '' }
    setFilters({
      date_range: {
        ...currentRange,
        [field]: date ? date.toISOString() : ''
      }
    })
  }

  const clearAllFilters = () => {
    setFilters({
      search: '',
      status: 'all',
      category: 'all',
      difficulty: 'all',
      tags: [],
      created_by: '',
      date_range: { start: '', end: '' }
    })
  }

  const hasActiveFilters = 
    filters.status !== 'all' ||
    filters.category !== 'all' ||
    filters.difficulty !== 'all' ||
    (filters.tags && filters.tags.length > 0) ||
    filters.created_by ||
    (filters.date_range && (filters.date_range.start || filters.date_range.end))

  return (
    <Paper sx={{ p: 3, mt: 2 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h6" fontWeight="bold">
          Filters
        </Typography>
        {hasActiveFilters && (
          <Button
            startIcon={<ClearIcon />}
            onClick={clearAllFilters}
            size="small"
            variant="outlined"
          >
            Clear All
          </Button>
        )}
      </Box>

      <Grid container spacing={3}>
        {/* Status Filter */}
        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth>
            <InputLabel>Status</InputLabel>
            <Select
              value={filters.status || 'all'}
              label="Status"
              onChange={(e) => handleFilterChange('status', e.target.value)}
            >
              <MenuItem value="all">All Statuses</MenuItem>
              <MenuItem value="draft">Draft</MenuItem>
              <MenuItem value="published">Published</MenuItem>
              <MenuItem value="archived">Archived</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        {/* Category Filter */}
        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth>
            <InputLabel>Category</InputLabel>
            <Select
              value={filters.category || 'all'}
              label="Category"
              onChange={(e) => handleFilterChange('category', e.target.value)}
            >
              <MenuItem value="all">All Categories</MenuItem>
              {categories.map((category) => (
                <MenuItem key={category} value={category}>
                  {category}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Difficulty Filter */}
        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth>
            <InputLabel>Difficulty</InputLabel>
            <Select
              value={filters.difficulty || 'all'}
              label="Difficulty"
              onChange={(e) => handleFilterChange('difficulty', e.target.value)}
            >
              <MenuItem value="all">All Levels</MenuItem>
              <MenuItem value="beginner">Beginner</MenuItem>
              <MenuItem value="intermediate">Intermediate</MenuItem>
              <MenuItem value="advanced">Advanced</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        {/* Created By Filter */}
        <Grid item xs={12} sm={6} md={3}>
          <Autocomplete
            options={creators}
            getOptionLabel={(option) => option.name}
            value={creators.find(c => c.id === filters.created_by) || null}
            onChange={(_, newValue) => handleFilterChange('created_by', newValue?.id || '')}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Created By"
                placeholder="Select creator"
              />
            )}
          />
        </Grid>

        {/* Tags Filter */}
        <Grid item xs={12} md={6}>
          <Autocomplete
            multiple
            options={availableTags}
            value={filters.tags || []}
            onChange={(_, newValue) => handleTagsChange(newValue)}
            renderTags={(value, getTagProps) =>
              value.map((option, index) => (
                <Chip
                  variant="outlined"
                  label={option}
                  {...getTagProps({ index })}
                  key={option}
                />
              ))
            }
            renderInput={(params) => (
              <TextField
                {...params}
                label="Tags"
                placeholder="Select tags"
              />
            )}
          />
        </Grid>

        {/* Date Range Filter */}
        <Grid item xs={12} md={6}>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <DatePicker
                  label="Created From"
                  value={filters.date_range?.start ? new Date(filters.date_range.start) : null}
                  onChange={(date) => handleDateRangeChange('start', date)}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      size: 'medium'
                    }
                  }}
                />
              </Grid>
              <Grid item xs={6}>
                <DatePicker
                  label="Created To"
                  value={filters.date_range?.end ? new Date(filters.date_range.end) : null}
                  onChange={(date) => handleDateRangeChange('end', date)}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      size: 'medium'
                    }
                  }}
                />
              </Grid>
            </Grid>
          </LocalizationProvider>
        </Grid>
      </Grid>

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <Box mt={3}>
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            Active Filters:
          </Typography>
          <Box display="flex" gap={1} flexWrap="wrap">
            {filters.status !== 'all' && (
              <Chip
                label={`Status: ${filters.status}`}
                onDelete={() => handleFilterChange('status', 'all')}
                size="small"
                variant="outlined"
              />
            )}
            {filters.category !== 'all' && (
              <Chip
                label={`Category: ${filters.category}`}
                onDelete={() => handleFilterChange('category', 'all')}
                size="small"
                variant="outlined"
              />
            )}
            {filters.difficulty !== 'all' && (
              <Chip
                label={`Difficulty: ${filters.difficulty}`}
                onDelete={() => handleFilterChange('difficulty', 'all')}
                size="small"
                variant="outlined"
              />
            )}
            {filters.created_by && (
              <Chip
                label={`Creator: ${creators.find(c => c.id === filters.created_by)?.name}`}
                onDelete={() => handleFilterChange('created_by', '')}
                size="small"
                variant="outlined"
              />
            )}
            {filters.tags && filters.tags.map((tag) => (
              <Chip
                key={tag}
                label={`Tag: ${tag}`}
                onDelete={() => handleTagsChange(filters.tags!.filter(t => t !== tag))}
                size="small"
                variant="outlined"
              />
            ))}
            {filters.date_range?.start && (
              <Chip
                label={`From: ${new Date(filters.date_range.start).toLocaleDateString()}`}
                onDelete={() => handleDateRangeChange('start', null)}
                size="small"
                variant="outlined"
              />
            )}
            {filters.date_range?.end && (
              <Chip
                label={`To: ${new Date(filters.date_range.end).toLocaleDateString()}`}
                onDelete={() => handleDateRangeChange('end', null)}
                size="small"
                variant="outlined"
              />
            )}
          </Box>
        </Box>
      )}
    </Paper>
  )
}

export default PathFilters
