-- Row Level Security Policies for Enhanced Competencies
-- Ensures tenant isolation and proper access control

-- Enable RLS on all new competency tables
ALTER TABLE competency_frameworks ENABLE ROW LEVEL SECURITY;
ALTER TABLE framework_competencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE competency_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE competency_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE competency_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE competency_audit ENABLE ROW LEVEL SECURITY;
ALTER TABLE skill_gaps ENABLE ROW LEVEL SECURITY;
ALTER TABLE competency_badges ENABLE ROW LEVEL SECURITY;
ALTER TABLE learner_badges ENABLE ROW LEVEL SECURITY;
ALTER TABLE mapping_suggestions ENABLE ROW LEVEL SECURITY;
ALTER TABLE skill_journeys ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE competency_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE external_competencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE competency_goals ENABLE ROW LEVEL SECURITY;
ALTER TABLE learner_points ENABLE ROW LEVEL SECURITY;
ALTER TABLE report_templates ENABLE ROW LEVEL SECURITY;

-- Helper function to get user's tenant_id from JWT
CREATE OR REPLACE FUNCTION get_user_tenant_id()
RETURNS INTEGER AS $$
BEGIN
  RETURN COALESCE(
    (auth.jwt() ->> 'tenant_id')::INTEGER,
    (SELECT tenant_id FROM users WHERE id = auth.uid())
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user has admin role
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users u
    JOIN roles r ON u.role_id = r.id
    WHERE u.id = auth.uid()
    AND r.name IN ('admin', 'super_admin')
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user has specific permission
CREATE OR REPLACE FUNCTION has_permission(permission_name TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users u
    JOIN roles r ON u.role_id = r.id
    WHERE u.id = auth.uid()
    AND permission_name = ANY(r.permissions)
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Competency Frameworks Policies
CREATE POLICY "Users can view competency frameworks in their tenant"
  ON competency_frameworks FOR SELECT
  USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Admins can manage competency frameworks in their tenant"
  ON competency_frameworks FOR ALL
  USING (tenant_id = get_user_tenant_id() AND is_admin());

-- Framework Competencies Policies
CREATE POLICY "Users can view framework competencies in their tenant"
  ON framework_competencies FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM competency_frameworks cf
      WHERE cf.id = framework_competencies.framework_id
      AND cf.tenant_id = get_user_tenant_id()
    )
  );

CREATE POLICY "Admins can manage framework competencies in their tenant"
  ON framework_competencies FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM competency_frameworks cf
      WHERE cf.id = framework_competencies.framework_id
      AND cf.tenant_id = get_user_tenant_id()
      AND is_admin()
    )
  );

-- Competency Categories Policies
CREATE POLICY "Users can view competency categories in their tenant"
  ON competency_categories FOR SELECT
  USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Admins can manage competency categories in their tenant"
  ON competency_categories FOR ALL
  USING (tenant_id = get_user_tenant_id() AND is_admin());

-- Competency Tags Policies
CREATE POLICY "Users can view competency tags in their tenant"
  ON competency_tags FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM competencies c
      WHERE c.id = competency_tags.competency_id
      AND c.tenant_id = get_user_tenant_id()
    )
  );

CREATE POLICY "Admins can manage competency tags in their tenant"
  ON competency_tags FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM competencies c
      WHERE c.id = competency_tags.competency_id
      AND c.tenant_id = get_user_tenant_id()
      AND is_admin()
    )
  );

-- Competency Versions Policies
CREATE POLICY "Users can view competency versions in their tenant"
  ON competency_versions FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM competencies c
      WHERE c.id = competency_versions.competency_id
      AND c.tenant_id = get_user_tenant_id()
    )
  );

CREATE POLICY "Admins can manage competency versions in their tenant"
  ON competency_versions FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM competencies c
      WHERE c.id = competency_versions.competency_id
      AND c.tenant_id = get_user_tenant_id()
      AND is_admin()
    )
  );

-- Competency Audit Policies
CREATE POLICY "Admins can view competency audit logs in their tenant"
  ON competency_audit FOR SELECT
  USING (
    is_admin() AND
    EXISTS (
      SELECT 1 FROM competencies c
      WHERE c.id = competency_audit.competency_id
      AND c.tenant_id = get_user_tenant_id()
    )
  );

CREATE POLICY "System can insert competency audit logs"
  ON competency_audit FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM competencies c
      WHERE c.id = competency_audit.competency_id
      AND c.tenant_id = get_user_tenant_id()
    )
  );

-- Skill Gaps Policies
CREATE POLICY "Users can view their own skill gaps"
  ON skill_gaps FOR SELECT
  USING (learner_id = auth.uid());

CREATE POLICY "Admins can view all skill gaps in their tenant"
  ON skill_gaps FOR SELECT
  USING (
    is_admin() AND
    EXISTS (
      SELECT 1 FROM users u
      WHERE u.id = skill_gaps.learner_id
      AND u.tenant_id = get_user_tenant_id()
    )
  );

CREATE POLICY "System can manage skill gaps"
  ON skill_gaps FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM users u
      WHERE u.id = skill_gaps.learner_id
      AND u.tenant_id = get_user_tenant_id()
    )
  );

-- Competency Badges Policies
CREATE POLICY "Users can view competency badges in their tenant"
  ON competency_badges FOR SELECT
  USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Admins can manage competency badges in their tenant"
  ON competency_badges FOR ALL
  USING (tenant_id = get_user_tenant_id() AND is_admin());

-- Learner Badges Policies
CREATE POLICY "Users can view their own badges"
  ON learner_badges FOR SELECT
  USING (learner_id = auth.uid());

CREATE POLICY "Admins can view all learner badges in their tenant"
  ON learner_badges FOR SELECT
  USING (
    is_admin() AND
    EXISTS (
      SELECT 1 FROM users u
      WHERE u.id = learner_badges.learner_id
      AND u.tenant_id = get_user_tenant_id()
    )
  );

CREATE POLICY "System can award badges"
  ON learner_badges FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM users u
      WHERE u.id = learner_badges.learner_id
      AND u.tenant_id = get_user_tenant_id()
    )
  );

-- Mapping Suggestions Policies
CREATE POLICY "Admins can view mapping suggestions in their tenant"
  ON mapping_suggestions FOR SELECT
  USING (
    is_admin() AND
    EXISTS (
      SELECT 1 FROM competencies c
      WHERE c.id = mapping_suggestions.competency_id
      AND c.tenant_id = get_user_tenant_id()
    )
  );

CREATE POLICY "Admins can manage mapping suggestions in their tenant"
  ON mapping_suggestions FOR ALL
  USING (
    is_admin() AND
    EXISTS (
      SELECT 1 FROM competencies c
      WHERE c.id = mapping_suggestions.competency_id
      AND c.tenant_id = get_user_tenant_id()
    )
  );

-- Skill Journeys Policies
CREATE POLICY "Users can view their own skill journeys"
  ON skill_journeys FOR SELECT
  USING (learner_id = auth.uid());

CREATE POLICY "Admins can view all skill journeys in their tenant"
  ON skill_journeys FOR SELECT
  USING (
    is_admin() AND
    EXISTS (
      SELECT 1 FROM users u
      WHERE u.id = skill_journeys.learner_id
      AND u.tenant_id = get_user_tenant_id()
    )
  );

CREATE POLICY "System can manage skill journeys"
  ON skill_journeys FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM users u
      WHERE u.id = skill_journeys.learner_id
      AND u.tenant_id = get_user_tenant_id()
    )
  );

-- Learning Plans Policies
CREATE POLICY "Users can view their own learning plans"
  ON learning_plans FOR SELECT
  USING (learner_id = auth.uid());

CREATE POLICY "Admins can view all learning plans in their tenant"
  ON learning_plans FOR SELECT
  USING (
    is_admin() AND
    EXISTS (
      SELECT 1 FROM users u
      WHERE u.id = learning_plans.learner_id
      AND u.tenant_id = get_user_tenant_id()
    )
  );

CREATE POLICY "Admins can manage learning plans in their tenant"
  ON learning_plans FOR ALL
  USING (
    is_admin() AND
    EXISTS (
      SELECT 1 FROM users u
      WHERE u.id = learning_plans.learner_id
      AND u.tenant_id = get_user_tenant_id()
    )
  );

-- Competency Reviews Policies
CREATE POLICY "Users can view competency reviews in their tenant"
  ON competency_reviews FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM competencies c
      WHERE c.id = competency_reviews.competency_id
      AND c.tenant_id = get_user_tenant_id()
    )
  );

CREATE POLICY "Admins can manage competency reviews in their tenant"
  ON competency_reviews FOR ALL
  USING (
    is_admin() AND
    EXISTS (
      SELECT 1 FROM competencies c
      WHERE c.id = competency_reviews.competency_id
      AND c.tenant_id = get_user_tenant_id()
    )
  );

-- External Competencies Policies
CREATE POLICY "Admins can view external competencies in their tenant"
  ON external_competencies FOR SELECT
  USING (
    is_admin() AND
    EXISTS (
      SELECT 1 FROM competencies c
      WHERE c.id = external_competencies.competency_id
      AND c.tenant_id = get_user_tenant_id()
    )
  );

CREATE POLICY "System can manage external competencies"
  ON external_competencies FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM competencies c
      WHERE c.id = external_competencies.competency_id
      AND c.tenant_id = get_user_tenant_id()
    )
  );

-- Competency Goals Policies
CREATE POLICY "Users can view competency goals in their tenant"
  ON competency_goals FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM competencies c
      WHERE c.id = competency_goals.competency_id
      AND c.tenant_id = get_user_tenant_id()
    )
  );

CREATE POLICY "Admins can manage competency goals in their tenant"
  ON competency_goals FOR ALL
  USING (
    is_admin() AND
    EXISTS (
      SELECT 1 FROM competencies c
      WHERE c.id = competency_goals.competency_id
      AND c.tenant_id = get_user_tenant_id()
    )
  );

-- Learner Points Policies
CREATE POLICY "Users can view their own points"
  ON learner_points FOR SELECT
  USING (learner_id = auth.uid());

CREATE POLICY "Admins can view all learner points in their tenant"
  ON learner_points FOR SELECT
  USING (
    is_admin() AND
    EXISTS (
      SELECT 1 FROM users u
      WHERE u.id = learner_points.learner_id
      AND u.tenant_id = get_user_tenant_id()
    )
  );

CREATE POLICY "System can award points"
  ON learner_points FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM users u
      WHERE u.id = learner_points.learner_id
      AND u.tenant_id = get_user_tenant_id()
    )
  );

-- Report Templates Policies
CREATE POLICY "Users can view public report templates in their tenant"
  ON report_templates FOR SELECT
  USING (tenant_id = get_user_tenant_id() AND is_public = true);

CREATE POLICY "Users can view their own report templates"
  ON report_templates FOR SELECT
  USING (admin_id = auth.uid());

CREATE POLICY "Admins can manage report templates in their tenant"
  ON report_templates FOR ALL
  USING (tenant_id = get_user_tenant_id() AND is_admin());
