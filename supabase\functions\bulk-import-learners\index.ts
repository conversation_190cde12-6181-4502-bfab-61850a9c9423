import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ImportResult {
  success: number
  errors: string[]
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
    )

    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization header required' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get current user from auth
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(
      authHeader.replace('Bearer ', '')
    )

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid authorization' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get user profile to get tenant_id
    const { data: userProfile, error: profileError } = await supabaseClient
      .from('users')
      .select('tenant_id')
      .eq('id', user.id)
      .single()

    if (profileError || !userProfile) {
      return new Response(
        JSON.stringify({ error: 'User profile not found' }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Parse form data
    const formData = await req.formData()
    const file = formData.get('file') as File
    const mapping = JSON.parse(formData.get('mapping') as string)
    const sendWelcomeEmails = formData.get('send_welcome_emails') === 'true'
    const defaultRoleId = parseInt(formData.get('default_role_id') as string)

    if (!file) {
      return new Response(
        JSON.stringify({ error: 'CSV file is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Read and parse CSV file
    const csvText = await file.text()
    const lines = csvText.split('\n').filter(line => line.trim())
    
    if (lines.length < 2) {
      return new Response(
        JSON.stringify({ error: 'CSV file must contain at least a header and one data row' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
    const dataRows = lines.slice(1)

    const result: ImportResult = {
      success: 0,
      errors: []
    }

    // Process each row
    for (let i = 0; i < dataRows.length; i++) {
      try {
        const row = dataRows[i].split(',').map(cell => cell.trim().replace(/"/g, ''))
        
        // Map CSV columns to user fields
        const userData: any = {
          tenant_id: userProfile.tenant_id,
          role_id: defaultRoleId,
          status: 'active'
        }

        // Apply field mapping
        Object.entries(mapping).forEach(([fieldKey, csvHeader]) => {
          const columnIndex = headers.indexOf(csvHeader as string)
          if (columnIndex !== -1 && row[columnIndex]) {
            userData[fieldKey] = row[columnIndex]
          }
        })

        // Validate required fields
        if (!userData.email || !userData.full_name) {
          result.errors.push(`Row ${i + 2}: Missing required fields (email or full_name)`)
          continue
        }

        // Check if user already exists
        const { data: existingUser } = await supabaseClient
          .from('users')
          .select('id')
          .eq('email', userData.email)
          .eq('tenant_id', userProfile.tenant_id)
          .single()

        if (existingUser) {
          result.errors.push(`Row ${i + 2}: User with email ${userData.email} already exists`)
          continue
        }

        // Create user
        const { error: insertError } = await supabaseClient
          .from('users')
          .insert(userData)

        if (insertError) {
          result.errors.push(`Row ${i + 2}: ${insertError.message}`)
          continue
        }

        result.success++

        // TODO: Send welcome email if requested
        if (sendWelcomeEmails) {
          // This would typically call another edge function or email service
          console.log(`Would send welcome email to ${userData.email}`)
        }

      } catch (error) {
        result.errors.push(`Row ${i + 2}: ${error.message}`)
      }
    }

    return new Response(
      JSON.stringify(result),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in bulk-import-learners:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
