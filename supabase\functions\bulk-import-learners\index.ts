import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Generate a temporary password for new users
function generateTemporaryPassword(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
  let password = ''
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return password
}

interface ImportResult {
  success: number
  errors: string[]
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
    )

    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization header required' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get current user from auth
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(
      authHeader.replace('Bearer ', '')
    )

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid authorization' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get user profile to get tenant_id
    const { data: userProfile, error: profileError } = await supabaseClient
      .from('users')
      .select('tenant_id')
      .eq('id', user.id)
      .single()

    if (profileError || !userProfile) {
      return new Response(
        JSON.stringify({ error: 'User profile not found' }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Parse form data
    const formData = await req.formData()
    const file = formData.get('file') as File
    const mapping = JSON.parse(formData.get('mapping') as string)
    const sendWelcomeEmails = formData.get('send_welcome_emails') === 'true'
    const defaultRoleId = parseInt(formData.get('default_role_id') as string)

    if (!file) {
      return new Response(
        JSON.stringify({ error: 'CSV file is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Read and parse CSV file
    const csvText = await file.text()
    const lines = csvText.split('\n').filter(line => line.trim())
    
    if (lines.length < 2) {
      return new Response(
        JSON.stringify({ error: 'CSV file must contain at least a header and one data row' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
    const dataRows = lines.slice(1)

    const result: ImportResult = {
      success: 0,
      errors: []
    }

    // Process each row
    for (let i = 0; i < dataRows.length; i++) {
      try {
        const row = dataRows[i].split(',').map(cell => cell.trim().replace(/"/g, ''))
        
        // Map CSV columns to user fields
        const userData: any = {
          tenant_id: userProfile.tenant_id,
          role_id: defaultRoleId,
          status: 'active'
        }

        // Apply field mapping
        Object.entries(mapping).forEach(([fieldKey, csvHeader]) => {
          const columnIndex = headers.indexOf(csvHeader as string)
          if (columnIndex !== -1 && row[columnIndex]) {
            userData[fieldKey] = row[columnIndex]
          }
        })

        // Validate required fields
        if (!userData.email || !userData.full_name) {
          result.errors.push(`Row ${i + 2}: Missing required fields (email or full_name)`)
          continue
        }

        // Check if user already exists in auth.users by email
        const { data: existingUsers } = await supabaseClient.auth.admin.listUsers()
        const existingAuthUser = existingUsers.users?.find(user => user.email === userData.email)

        if (existingAuthUser) {
          result.errors.push(`Row ${i + 2}: User with email ${userData.email} already exists`)
          continue
        }

        try {
          // Create user in Supabase Auth using Admin API
          const { data: authUser, error: authError } = await supabaseClient.auth.admin.createUser({
            email: userData.email,
            password: generateTemporaryPassword(),
            email_confirm: true,
            user_metadata: {
              full_name: userData.full_name,
              department: userData.department,
              position: userData.position,
              phone: userData.phone,
              location: userData.location
            }
          })

          if (authError) {
            result.errors.push(`Row ${i + 2}: Failed to create auth user - ${authError.message}`)
            continue
          }

          if (!authUser.user) {
            result.errors.push(`Row ${i + 2}: Failed to create auth user - No user returned`)
            continue
          }

          // Wait for trigger to create profile
          await new Promise(resolve => setTimeout(resolve, 500))

          // Update the user profile with additional data
          const { error: updateError } = await supabaseClient
            .from('users')
            .update({
              role_id: userData.role_id,
              department: userData.department,
              position: userData.position,
              phone: userData.phone,
              location: userData.location,
              status: 'active'
            })
            .eq('id', authUser.user.id)
            .eq('tenant_id', userProfile.tenant_id)

          if (updateError) {
            result.errors.push(`Row ${i + 2}: Failed to update profile - ${updateError.message}`)
            // Clean up auth user
            await supabaseClient.auth.admin.deleteUser(authUser.user.id)
            continue
          }

          result.success++

          // Send welcome email if requested
          if (sendWelcomeEmails) {
            console.log(`Would send welcome email to ${userData.email}`)
          }
        } catch (error) {
          result.errors.push(`Row ${i + 2}: ${error.message}`)
          continue
        }

      } catch (error) {
        result.errors.push(`Row ${i + 2}: ${error.message}`)
      }
    }

    return new Response(
      JSON.stringify(result),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in bulk-import-learners:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
