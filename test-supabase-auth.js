// Test script to verify Supabase API authentication
// Run this with: node test-supabase-auth.js

const fetch = require('node-fetch');

// Test configuration
const SUPABASE_URL = 'https://ebugbzdstyztfvkhpqbs.supabase.co';
const ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVidWdiemRzdHl6dGZ2a2hwcWJzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1NjA3MzAsImV4cCI6MjA2NTEzNjczMH0.aMHZiuBjytE86YQfbFRQOx4nlKwKgPkvXnBfz0lBS2E';
const SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVidWdiemRzdHl6dGZ2a2hwcWJzIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTU2MDczMCwiZXhwIjoyMDY1MTM2NzMwfQ.lVkWAUL6ID5_ziqggFfac3XyQqGcWnjKh9ONo_f1c9Q';

async function testApiKey(keyName, apiKey) {
  console.log(`\n🧪 Testing ${keyName} key...`);
  
  try {
    const response = await fetch(`${SUPABASE_URL}/rest/v1/tenant_settings?select=*&tenant_id=eq.3&category=eq.general`, {
      method: 'GET',
      headers: {
        'apikey': apiKey,
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'Prefer': 'return=representation'
      }
    });

    console.log(`Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ SUCCESS: ${keyName} key is working`);
      console.log(`Data returned:`, JSON.stringify(data, null, 2));
    } else {
      const errorText = await response.text();
      console.log(`❌ FAILED: ${keyName} key failed`);
      console.log(`Error:`, errorText);
    }
  } catch (error) {
    console.log(`❌ ERROR: ${keyName} key test failed`);
    console.log(`Error:`, error.message);
  }
}

async function testTenantSettingsTable() {
  console.log('\n🔍 Testing tenant_settings table access...');
  
  try {
    // Test with service role key to check if table exists and has data
    const response = await fetch(`${SUPABASE_URL}/rest/v1/tenant_settings?select=*&limit=5`, {
      method: 'GET',
      headers: {
        'apikey': SERVICE_KEY,
        'Authorization': `Bearer ${SERVICE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Table exists and accessible`);
      console.log(`Records found: ${data.length}`);
      if (data.length > 0) {
        console.log(`Sample record:`, JSON.stringify(data[0], null, 2));
      }
    } else {
      const errorText = await response.text();
      console.log(`❌ Table access failed`);
      console.log(`Error:`, errorText);
    }
  } catch (error) {
    console.log(`❌ ERROR: Table test failed`);
    console.log(`Error:`, error.message);
  }
}

async function runTests() {
  console.log('🚀 Starting Supabase API Authentication Tests');
  console.log('==============================================');
  
  // Test anon key
  await testApiKey('ANON', ANON_KEY);
  
  // Test service role key
  await testApiKey('SERVICE_ROLE', SERVICE_KEY);
  
  // Test table access
  await testTenantSettingsTable();
  
  console.log('\n✅ Tests completed!');
}

runTests().catch(console.error);
