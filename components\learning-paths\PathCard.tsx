'use client'

import React from 'react'
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Chip,
  Box,
  IconButton,
  Avatar,
  LinearProgress,
  Tooltip
} from '@mui/material'
import {
  MoreVert as MoreVertIcon,
  Schedule as ScheduleIcon,
  People as PeopleIcon,
  TrendingUp as TrendingUpIcon,
  Star as StarIcon,
  EditNote as DraftIcon,
  Publish as PublishIcon,
  Archive as ArchiveIcon
} from '@mui/icons-material'
import { format } from 'date-fns'

import { LearningPath } from '@/lib/types/learning-paths'

interface PathCardProps {
  path: LearningPath
  onView: () => void
  onEdit: () => void
  onMenuClick: (event: React.MouseEvent<HTMLElement>) => void
}

const PathCard: React.FC<PathCardProps> = ({
  path,
  onView,
  onEdit,
  onMenuClick
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'success'
      case 'draft':
        return 'warning'
      case 'archived':
        return 'default'
      default:
        return 'default'
    }
  }

  const getStatusIcon = (status: string): React.ReactElement | undefined => {
    switch (status) {
      case 'published':
        return <PublishIcon fontSize="small" />
      case 'draft':
        return <DraftIcon fontSize="small" />
      case 'archived':
        return <ArchiveIcon fontSize="small" />
      default:
        return undefined
    }
  }

  // Compute status from is_live field
  const status = path.status || (path.is_live ? 'published' : 'draft')
  const difficulty = path.difficulty || path.difficulty_level
  const duration = path.duration || path.estimated_duration

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'success'
      case 'intermediate':
        return 'warning'
      case 'advanced':
        return 'error'
      default:
        return 'default'
    }
  }

  const moduleCount = path.modules?.length || 0
  const lessonCount = path.modules?.reduce((total, module) => total + (module.lessons?.length || 0), 0) || 0

  return (
    <Card
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        cursor: 'pointer',
        transition: 'all 0.2s ease-in-out',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: (theme) => theme.shadows[8],
        },
      }}
      onClick={onView}
    >
      <CardContent sx={{ flexGrow: 1, pb: 1 }}>
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
          <Box flexGrow={1}>
            <Typography variant="h6" fontWeight="bold" gutterBottom noWrap>
              {path.title}
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
                minHeight: '2.5em',
              }}
            >
              {path.description}
            </Typography>
          </Box>
          <IconButton
            size="small"
            onClick={onMenuClick}
            sx={{ ml: 1 }}
          >
            <MoreVertIcon />
          </IconButton>
        </Box>

        {/* Status and Difficulty */}
        <Box display="flex" gap={1} mb={2}>
          <Chip
            label={status}
            color={getStatusColor(status) as any}
            size="small"
            {...(getStatusIcon(status) && { icon: getStatusIcon(status) })}
            variant="outlined"
          />
          <Chip
            label={difficulty}
            color={getDifficultyColor(difficulty) as any}
            size="small"
            variant="outlined"
          />
        </Box>

        {/* Category and Tags */}
        <Box mb={2}>
          <Typography variant="caption" color="text.secondary" display="block">
            Category: {path.category}
          </Typography>
          {path.tags && path.tags.length > 0 && (
            <Box display="flex" gap={0.5} mt={0.5} flexWrap="wrap">
              {path.tags.slice(0, 3).map((tag, index) => (
                <Chip
                  key={index}
                  label={tag}
                  size="small"
                  variant="outlined"
                  sx={{ fontSize: '0.7rem', height: 20 }}
                />
              ))}
              {path.tags.length > 3 && (
                <Chip
                  label={`+${path.tags.length - 3}`}
                  size="small"
                  variant="outlined"
                  sx={{ fontSize: '0.7rem', height: 20 }}
                />
              )}
            </Box>
          )}
        </Box>

        {/* Metrics */}
        <Box display="flex" alignItems="center" gap={2} mb={2}>
          <Tooltip title="Duration">
            <Box display="flex" alignItems="center" gap={0.5}>
              <ScheduleIcon fontSize="small" color="action" />
              <Typography variant="caption">
                {duration} week{duration !== 1 ? 's' : ''}
              </Typography>
            </Box>
          </Tooltip>
          
          <Tooltip title="Total Learners">
            <Box display="flex" alignItems="center" gap={0.5}>
              <PeopleIcon fontSize="small" color="action" />
              <Typography variant="caption">
                {path.metadata?.total_learners || 0}
              </Typography>
            </Box>
          </Tooltip>

          {path.metadata?.average_rating && (
            <Tooltip title="Average Rating">
              <Box display="flex" alignItems="center" gap={0.5}>
                <StarIcon fontSize="small" color="action" />
                <Typography variant="caption">
                  {path.metadata.average_rating.toFixed(1)}
                </Typography>
              </Box>
            </Tooltip>
          )}
        </Box>

        {/* Progress Bar */}
        {path.metadata?.completion_rate !== undefined && (
          <Box mb={2}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={0.5}>
              <Typography variant="caption" color="text.secondary">
                Completion Rate
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {Math.round(path.metadata.completion_rate)}%
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={path.metadata.completion_rate}
              sx={{ height: 6, borderRadius: 3 }}
            />
          </Box>
        )}

        {/* Content Structure */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
          <Typography variant="caption" color="text.secondary">
            {moduleCount} module{moduleCount !== 1 ? 's' : ''} • {lessonCount} lesson{lessonCount !== 1 ? 's' : ''}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {path.metadata?.estimated_hours || 0}h
          </Typography>
        </Box>

        {/* Created Date */}
        <Typography variant="caption" color="text.secondary">
          Created {format(new Date(path.created_at), 'MMM d, yyyy')}
        </Typography>
      </CardContent>

      <CardActions sx={{ pt: 0, px: 2, pb: 2 }}>
        <Button
          size="small"
          onClick={(e) => {
            e.stopPropagation()
            onView()
          }}
        >
          View
        </Button>
        <Button
          size="small"
          onClick={(e) => {
            e.stopPropagation()
            onEdit()
          }}
        >
          Edit
        </Button>
        {path.status === 'published' && (
          <Box ml="auto">
            <Tooltip title="Published">
              <TrendingUpIcon fontSize="small" color="success" />
            </Tooltip>
          </Box>
        )}
      </CardActions>
    </Card>
  )
}

export default PathCard
