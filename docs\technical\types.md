# TypeScript Types Documentation

This document provides comprehensive documentation for all TypeScript types used in the Groups and Batches feature, including interfaces, enums, utility types, and type guards.

## 📋 Core Entity Types

### Group

The main group entity with all properties and computed fields.

```typescript
interface Group {
  // Database fields
  id: number
  tenant_id: number
  name: string
  description?: string
  start_date?: string
  end_date?: string
  status: 'active' | 'inactive' | 'archived' | 'draft'
  tags?: string[]
  parent_group_id?: number
  settings?: GroupSettings
  created_by: string
  created_at: string
  updated_at: string
  
  // Computed fields (from joins/aggregations)
  member_count?: number
  assigned_paths_count?: number
  completion_rate?: number
  members?: GroupMember[]
  assignments?: GroupAssignment[]
  progress?: GroupProgress[]
  children?: Group[]
}
```

### GroupMember

Represents a learner's membership in a group with role and audit information.

```typescript
interface GroupMember {
  // Database fields
  id: number
  group_id: number
  learner_id: string
  role: 'member' | 'moderator' | 'admin'
  joined_at: string
  added_by: string
  
  // Computed fields from user table
  full_name?: string
  email?: string
  avatar_url?: string
  department?: string
  progress?: LearnerGroupProgress[]
}
```

### GroupAssignment

Links groups to learning paths with scheduling and configuration.

```typescript
interface GroupAssignment {
  // Database fields
  id: number
  group_id: number
  path_id: number
  assigned_by: string
  assigned_at: string
  start_date?: string
  due_date?: string
  is_mandatory: boolean
  settings?: AssignmentSettings
  
  // Computed fields from learning_paths table
  path_title?: string
  path_description?: string
  estimated_duration?: number
  difficulty_level?: string
}
```

### GroupProgress

Real-time progress tracking and analytics for group-path combinations.

```typescript
interface GroupProgress {
  // Database fields
  id: number
  group_id: number
  path_id: number
  total_members: number
  completed_members: number
  in_progress_members: number
  not_started_members: number
  average_score?: number
  completion_rate: number
  updated_at: string
  
  // Computed fields
  path_title?: string
}
```

### GroupMessage

Multi-channel communication messages within groups.

```typescript
interface GroupMessage {
  // Database fields
  id: number
  group_id: number
  sender_id: string
  message: string
  message_type: 'announcement' | 'chat' | 'system'
  channels: string[]
  metadata?: MessageMetadata
  sent_at: string
  
  // Computed fields
  sender_name?: string
  sender_avatar?: string
}
```

### GroupMilestone

Timeline and achievement tracking for groups.

```typescript
interface GroupMilestone {
  // Database fields
  id: number
  group_id: number
  title: string
  description?: string
  due_date: string
  completion_criteria?: any
  is_completed: boolean
  completed_at?: string
  created_by: string
  created_at: string
  
  // Computed fields
  days_remaining?: number
  progress_percentage?: number
}
```

### AssignmentTemplate

Reusable assignment configurations for groups.

```typescript
interface AssignmentTemplate {
  // Database fields
  id: number
  tenant_id: number
  name: string
  description?: string
  template_data: TemplateData
  created_by: string
  created_at: string
  updated_at: string
}
```

## ⚙️ Configuration Types

### GroupSettings

Comprehensive group configuration options.

```typescript
interface GroupSettings {
  notifications?: {
    email_enabled: boolean
    sms_enabled: boolean
    in_app_enabled: boolean
    announcement_frequency: 'immediate' | 'daily' | 'weekly'
  }
  communication?: {
    chat_enabled: boolean
    file_sharing_enabled: boolean
    video_calls_enabled: boolean
  }
  progress?: {
    auto_progress_tracking: boolean
    milestone_reminders: boolean
    completion_certificates: boolean
  }
  access?: {
    self_enrollment: boolean
    member_invite_permissions: string[]
    content_access_level: 'full' | 'restricted'
  }
}
```

### AssignmentSettings

Configuration for learning path assignments.

```typescript
interface AssignmentSettings {
  auto_assign_new_members: boolean
  grace_period_days?: number
  reminder_schedule?: string[]
  completion_requirements?: {
    minimum_score?: number
    time_limit?: number
    attempts_allowed?: number
  }
}
```

### MessageMetadata

Extended metadata for group messages.

```typescript
interface MessageMetadata {
  priority?: 'low' | 'normal' | 'high' | 'urgent'
  read_receipts?: boolean
  delivery_status?: {
    email?: 'sent' | 'delivered' | 'failed'
    sms?: 'sent' | 'delivered' | 'failed'
    in_app?: 'sent' | 'read'
  }
  attachments?: {
    name: string
    url: string
    type: string
    size: number
  }[]
}
```

### TemplateData

Structure for assignment template data.

```typescript
interface TemplateData {
  paths: {
    path_id: number
    start_date?: string
    due_date?: string
    is_mandatory: boolean
    settings?: AssignmentSettings
  }[]
  default_settings?: GroupSettings
  milestones?: {
    title: string
    description?: string
    due_date_offset_days: number
    completion_criteria?: any
  }[]
}
```

## 🔍 Filter and Search Types

### GroupFilters

Comprehensive filtering options for groups.

```typescript
interface GroupFilters {
  search: string
  status: 'all' | 'active' | 'inactive' | 'archived' | 'draft'
  tags: string[]
  created_by: string
  date_range: {
    start: string
    end: string
  }
  member_count_range: {
    min: number
    max: number
  }
  completion_rate_range: {
    min: number
    max: number
  }
  has_overdue_assignments: boolean
  parent_group_id?: number
}
```

### GroupMemberFilters

Filtering options for group members.

```typescript
interface GroupMemberFilters {
  search: string
  role: 'all' | 'member' | 'moderator' | 'admin'
  department: string
  progress_status: 'all' | 'not_started' | 'in_progress' | 'completed' | 'overdue'
  engagement_level: 'all' | 'high' | 'medium' | 'low' | 'at_risk'
}
```

## 🤖 AI and Automation Types

### SmartGroupingSuggestion

AI-generated group formation suggestions.

```typescript
interface SmartGroupingSuggestion {
  id: string
  name: string
  description: string
  criteria: {
    type: 'skill_level' | 'department' | 'performance' | 'learning_style' | 'custom'
    parameters: any
  }
  suggested_members: string[]
  confidence_score: number
  reasoning: string
}
```

### AutoAssignmentRule

Automated assignment rules based on triggers.

```typescript
interface AutoAssignmentRule {
  id: number
  group_id: number
  name: string
  description?: string
  trigger_conditions: {
    type: 'progress_threshold' | 'time_based' | 'skill_gap' | 'performance_drop'
    parameters: any
  }
  actions: {
    type: 'assign_path' | 'reassign_group' | 'send_notification' | 'schedule_meeting'
    parameters: any
  }[]
  is_active: boolean
  created_by: string
  created_at: string
}
```

### LearnerGroupProgress

Individual learner progress within a group context.

```typescript
interface LearnerGroupProgress {
  learner_id: string
  path_id: number
  status: 'not_started' | 'in_progress' | 'completed'
  progress_percentage: number
  score?: number
  time_spent?: number
  last_activity?: string
  completed_at?: string
}
```

## 🎛️ Component Props Types

### GroupDashboardProps

Props for the main groups dashboard component.

```typescript
interface GroupDashboardProps {
  groups: Group[]
  loading: boolean
  onCreateGroup: () => void
  onEditGroup: (group: Group) => void
  onDeleteGroup: (groupId: number) => void
  onViewGroup: (group: Group) => void
}
```

### GroupFormProps

Props for the group creation/editing form.

```typescript
interface GroupFormProps {
  group?: Group
  open: boolean
  onClose: () => void
  onSubmit: (data: Partial<Group>) => void
  loading?: boolean
}
```

### MemberManagementProps

Props for the member management component.

```typescript
interface MemberManagementProps {
  group: Group
  members: GroupMember[]
  availableLearners: any[]
  onAddMembers: (learnerIds: string[]) => void
  onRemoveMember: (memberId: number) => void
  onUpdateMemberRole: (memberId: number, role: string) => void
}
```

### GroupProgressProps

Props for the progress tracking component.

```typescript
interface GroupProgressProps {
  group: Group
  progress: GroupProgress[]
  detailedProgress?: LearnerGroupProgress[]
  onViewDetails: (pathId: number) => void
}
```

## 📡 API Response Types

### GroupsResponse

Response format for groups list API.

```typescript
interface GroupsResponse {
  data: Group[]
  count: number
  page: number
  limit: number
}
```

### GroupMembersResponse

Response format for group members API.

```typescript
interface GroupMembersResponse {
  data: GroupMember[]
  count: number
}
```

### GroupProgressResponse

Response format for group progress API.

```typescript
interface GroupProgressResponse {
  data: GroupProgress[]
  summary: {
    total_groups: number
    active_groups: number
    average_completion_rate: number
    total_learners: number
  }
}
```

## 🚩 Feature Flags Type

### GroupFeatureFlags

Complete feature flags configuration.

```typescript
interface GroupFeatureFlags {
  // Core features
  enable_group_creation: boolean
  enable_group_archiving: boolean
  enable_group_tags: boolean
  enable_group_hierarchy: boolean
  enable_bulk_member_add: boolean
  enable_group_communication: boolean
  enable_group_chat: boolean
  enable_group_scheduling: boolean
  enable_ai_grouping: boolean
  enable_auto_reassignment: boolean
  enable_assignment_templates: boolean
  enable_group_customization: boolean
  enable_group_integrations: boolean
  enable_advanced_analytics: boolean
  enable_learner_drilldown: boolean
  enable_deadline_enforcement: boolean
  
  // Restrictions
  restrict_group_creation: boolean
  restrict_group_dates: boolean
  restrict_member_removal: boolean
  restrict_mandatory_paths: boolean
  restrict_sms_channel: boolean
}
```

## 🛡️ Type Guards

### Type Guard Functions

Utility functions for runtime type checking.

```typescript
// Group type guards
export function isGroup(obj: any): obj is Group {
  return obj && typeof obj.id === 'number' && typeof obj.name === 'string'
}

export function isGroupMember(obj: any): obj is GroupMember {
  return obj && typeof obj.group_id === 'number' && typeof obj.learner_id === 'string'
}

export function isGroupAssignment(obj: any): obj is GroupAssignment {
  return obj && typeof obj.group_id === 'number' && typeof obj.path_id === 'number'
}

// Status type guards
export function isValidGroupStatus(status: string): status is Group['status'] {
  return ['active', 'inactive', 'archived', 'draft'].includes(status)
}

export function isValidMemberRole(role: string): role is GroupMember['role'] {
  return ['member', 'moderator', 'admin'].includes(role)
}

export function isValidMessageType(type: string): type is GroupMessage['message_type'] {
  return ['announcement', 'chat', 'system'].includes(type)
}
```

## 🔧 Utility Types

### Utility Type Definitions

Helper types for common operations.

```typescript
// Partial types for updates
export type GroupUpdate = Partial<Omit<Group, 'id' | 'created_at' | 'updated_at'>>
export type MemberUpdate = Partial<Omit<GroupMember, 'id' | 'joined_at'>>
export type AssignmentUpdate = Partial<Omit<GroupAssignment, 'id' | 'assigned_at'>>

// Create types (without generated fields)
export type GroupCreate = Omit<Group, 'id' | 'created_at' | 'updated_at' | 'member_count' | 'assigned_paths_count' | 'completion_rate'>
export type MemberCreate = Omit<GroupMember, 'id' | 'joined_at' | 'full_name' | 'email' | 'avatar_url' | 'department'>
export type AssignmentCreate = Omit<GroupAssignment, 'id' | 'assigned_at' | 'path_title' | 'path_description'>

// Filter types
export type GroupSortField = keyof Pick<Group, 'name' | 'created_at' | 'updated_at' | 'member_count' | 'completion_rate'>
export type GroupSortOrder = 'asc' | 'desc'

// Pagination types
export interface PaginationParams {
  page: number
  limit: number
  sort_field?: GroupSortField
  sort_order?: GroupSortOrder
}

// Search types
export interface SearchParams {
  query: string
  fields: string[]
  exact_match?: boolean
}

// Bulk operation types
export interface BulkOperation<T> {
  operation: 'create' | 'update' | 'delete'
  items: T[]
  options?: {
    ignore_errors?: boolean
    batch_size?: number
  }
}

// Real-time event types
export interface RealtimeEvent<T> {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE'
  new?: T
  old?: T
  table: string
  schema: string
}

// Error types
export interface GroupsError {
  code: string
  message: string
  details?: any
  hint?: string
}

// Success response types
export interface SuccessResponse<T> {
  data: T
  message?: string
  metadata?: {
    total_count?: number
    page?: number
    limit?: number
  }
}
```

## 📝 Enum Types

### Status Enums

```typescript
export enum GroupStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ARCHIVED = 'archived',
  DRAFT = 'draft'
}

export enum MemberRole {
  MEMBER = 'member',
  MODERATOR = 'moderator',
  ADMIN = 'admin'
}

export enum MessageType {
  ANNOUNCEMENT = 'announcement',
  CHAT = 'chat',
  SYSTEM = 'system'
}

export enum ProgressStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed'
}

export enum NotificationChannel {
  IN_APP = 'in_app',
  EMAIL = 'email',
  SMS = 'sms'
}

export enum Priority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent'
}
```

## 🔄 State Management Types

### Store State Types

Types for Zustand store state management.

```typescript
// Groups store state
export interface GroupsState {
  // Data
  groups: Group[]
  selectedGroup: Group | null
  selectedGroups: number[]
  members: GroupMember[]
  assignments: GroupAssignment[]
  progress: GroupProgress[]
  
  // UI state
  loading: boolean
  viewMode: 'grid' | 'list'
  filters: GroupFilters
  memberFilters: GroupMemberFilters
  
  // Advanced features
  smartSuggestions: SmartGroupingSuggestion[]
  isCreating: boolean
  bulkOperations: {
    selectedMembers: string[]
    selectedAssignments: number[]
    isProcessing: boolean
  }
  communication: {
    messages: GroupMessage[]
    unreadCount: number
    chatOpen: boolean
  }
  milestones: GroupMilestone[]
  templates: AssignmentTemplate[]
}

// Store actions
export interface GroupsActions {
  // Data actions
  setGroups: (groups: Group[]) => void
  addGroup: (group: Group) => void
  updateGroup: (groupId: number, updates: Partial<Group>) => void
  deleteGroup: (groupId: number) => void
  
  // Member actions
  addMember: (groupId: number, member: GroupMember) => void
  removeMember: (groupId: number, memberId: number) => void
  updateMemberRole: (groupId: number, memberId: number, role: string) => void
  
  // Assignment actions
  addAssignment: (groupId: number, assignment: GroupAssignment) => void
  removeAssignment: (groupId: number, assignmentId: number) => void
  
  // UI actions
  setLoading: (loading: boolean) => void
  setViewMode: (mode: 'grid' | 'list') => void
  setFilters: (filters: Partial<GroupFilters>) => void
  
  // Utility actions
  resetFilters: () => void
  clearSelection: () => void
}

// Combined store type
export type GroupsStore = GroupsState & GroupsActions
```

This comprehensive type system provides type safety, IntelliSense support, and runtime validation for the entire Groups and Batches feature.
