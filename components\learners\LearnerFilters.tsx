'use client'

import React from 'react'
import {
  Card,
  CardContent,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Box,
  Typography,
  Switch,
  FormControlLabel,
  Slider,
  Autocomplete
} from '@mui/material'
import {
  Search as SearchIcon,
  Clear as ClearIcon
} from '@mui/icons-material'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'

import { LearnerFilters } from '@/lib/types/learners'
import { useGroups } from '@/lib/hooks/useLearners'

interface LearnerFiltersProps {
  filters: LearnerFilters
  onFiltersChange: (filters: Partial<LearnerFilters>) => void
}

const LearnerFiltersComponent: React.FC<LearnerFiltersProps> = ({
  filters,
  onFiltersChange
}) => {
  const { data: groups = [] } = useGroups()

  const handleFilterChange = (key: keyof LearnerFilters, value: any) => {
    onFiltersChange({ [key]: value })
  }

  const handleClearFilters = () => {
    onFiltersChange({
      search: '',
      status: 'all',
      role: 'all',
      group: 'all',
      department: 'all',
      engagement_risk: 'all',
      completion_rate: undefined,
      last_active: undefined,
      has_overdue: false
    })
  }

  const hasActiveFilters = Object.values(filters).some(value => {
    if (typeof value === 'string') return value !== '' && value !== 'all'
    if (typeof value === 'boolean') return value
    if (typeof value === 'object' && value !== null) return true
    return false
  })

  const statusOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'suspended', label: 'Suspended' }
  ]

  const roleOptions = [
    { value: 'all', label: 'All Roles' },
    { value: '2', label: 'Learner' },
    { value: '3', label: 'Manager' },
    { value: '4', label: 'Instructor' }
  ]

  const departmentOptions = [
    { value: 'all', label: 'All Departments' },
    { value: 'Engineering', label: 'Engineering' },
    { value: 'Sales', label: 'Sales' },
    { value: 'Marketing', label: 'Marketing' },
    { value: 'HR', label: 'Human Resources' },
    { value: 'Finance', label: 'Finance' },
    { value: 'Operations', label: 'Operations' }
  ]

  const engagementRiskOptions = [
    { value: 'all', label: 'All Risk Levels' },
    { value: 'low', label: 'Low Risk' },
    { value: 'medium', label: 'Medium Risk' },
    { value: 'high', label: 'High Risk' }
  ]

  const lastActiveOptions = [
    { value: 7, label: 'Last 7 days' },
    { value: 30, label: 'Last 30 days' },
    { value: 90, label: 'Last 90 days' }
  ]

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h6" fontWeight="medium">
            Filter Learners
          </Typography>
          {hasActiveFilters && (
            <Chip
              label="Clear All"
              variant="outlined"
              size="small"
              deleteIcon={<ClearIcon />}
              onDelete={handleClearFilters}
              onClick={handleClearFilters}
            />
          )}
        </Box>

        <Grid container spacing={3}>
          {/* Search */}
          <Grid item xs={12} md={6} lg={3}>
            <TextField
              fullWidth
              label="Search"
              placeholder="Name or email..."
              value={filters.search || ''}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
            />
          </Grid>

          {/* Status */}
          <Grid item xs={12} md={6} lg={3}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={filters.status || 'all'}
                label="Status"
                onChange={(e) => handleFilterChange('status', e.target.value)}
              >
                {statusOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Role */}
          <Grid item xs={12} md={6} lg={3}>
            <FormControl fullWidth>
              <InputLabel>Role</InputLabel>
              <Select
                value={filters.role || 'all'}
                label="Role"
                onChange={(e) => handleFilterChange('role', e.target.value)}
              >
                {roleOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Department */}
          <Grid item xs={12} md={6} lg={3}>
            <FormControl fullWidth>
              <InputLabel>Department</InputLabel>
              <Select
                value={filters.department || 'all'}
                label="Department"
                onChange={(e) => handleFilterChange('department', e.target.value)}
              >
                {departmentOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Group */}
          <Grid item xs={12} md={6} lg={3}>
            <FormControl fullWidth>
              <InputLabel>Group</InputLabel>
              <Select
                value={filters.group || 'all'}
                label="Group"
                onChange={(e) => handleFilterChange('group', e.target.value)}
              >
                <MenuItem value="all">All Groups</MenuItem>
                {groups.map((group) => (
                  <MenuItem key={group.id} value={group.id}>
                    {group.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Engagement Risk */}
          <Grid item xs={12} md={6} lg={3}>
            <FormControl fullWidth>
              <InputLabel>Engagement Risk</InputLabel>
              <Select
                value={filters.engagement_risk || 'all'}
                label="Engagement Risk"
                onChange={(e) => handleFilterChange('engagement_risk', e.target.value)}
              >
                {engagementRiskOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Last Active */}
          <Grid item xs={12} md={6} lg={3}>
            <Autocomplete
              options={lastActiveOptions}
              getOptionLabel={(option) => option.label}
              value={lastActiveOptions.find(opt => opt.value === filters.last_active?.days) || null}
              onChange={(_, value) => 
                handleFilterChange('last_active', value ? { days: value.value } : undefined)
              }
              renderInput={(params) => (
                <TextField {...params} label="Last Active" fullWidth />
              )}
            />
          </Grid>

          {/* Has Overdue */}
          <Grid item xs={12} md={6} lg={3}>
            <FormControlLabel
              control={
                <Switch
                  checked={filters.has_overdue || false}
                  onChange={(e) => handleFilterChange('has_overdue', e.target.checked)}
                />
              }
              label="Has Overdue Assignments"
            />
          </Grid>

          {/* Completion Rate Range */}
          <Grid item xs={12} md={6}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Completion Rate: {filters.completion_rate?.min || 0}% - {filters.completion_rate?.max || 100}%
            </Typography>
            <Slider
              value={[filters.completion_rate?.min || 0, filters.completion_rate?.max || 100]}
              onChange={(_, value) => {
                const [min, max] = value as number[]
                handleFilterChange('completion_rate', { min, max })
              }}
              valueLabelDisplay="auto"
              min={0}
              max={100}
              step={5}
              marks={[
                { value: 0, label: '0%' },
                { value: 25, label: '25%' },
                { value: 50, label: '50%' },
                { value: 75, label: '75%' },
                { value: 100, label: '100%' }
              ]}
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default LearnerFiltersComponent
