-- Settings Schema Migration for ZenithLearn AI
-- This migration creates tables for comprehensive settings management

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create tenant_settings table for storing all tenant-specific settings
CREATE TABLE IF NOT EXISTS tenant_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    category VARCHAR(50) NOT NULL, -- 'general', 'branding', 'security', etc.
    settings JSONB NOT NULL DEFAULT '{}',
    updated_by UUID, -- References auth.users
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, category)
);

-- Create tenant_features table for feature flags
CREATE TABLE IF NOT EXISTS tenant_features (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    feature_key VARCHAR(100) NOT NULL,
    enabled BOOLEAN DEFAULT FALSE,
    config_json JSONB DEFAULT '{}',
    updated_by UUID, -- References auth.users
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, feature_key)
);

-- Create permissions table for role-based access control
CREATE TABLE IF NOT EXISTS permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create role_permissions junction table
CREATE TABLE IF NOT EXISTS role_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    role_id INTEGER REFERENCES roles(id) ON DELETE CASCADE,
    permission_id UUID REFERENCES permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(role_id, permission_id)
);

-- Create settings_audit table for audit logging
CREATE TABLE IF NOT EXISTS settings_audit (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    admin_id UUID, -- References auth.users
    setting_key VARCHAR(255) NOT NULL,
    old_value JSONB,
    new_value JSONB,
    action VARCHAR(20) NOT NULL CHECK (action IN ('create', 'update', 'delete')),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT
);

-- Create api_keys table for API key management
CREATE TABLE IF NOT EXISTS api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    key_hash VARCHAR(255) NOT NULL, -- Hashed API key
    permissions TEXT[] DEFAULT '{}',
    last_used TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_by UUID, -- References auth.users
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);

-- Create webhooks table for webhook management
CREATE TABLE IF NOT EXISTS webhooks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    url TEXT NOT NULL,
    events TEXT[] NOT NULL,
    secret VARCHAR(255),
    enabled BOOLEAN DEFAULT TRUE,
    last_triggered TIMESTAMP WITH TIME ZONE,
    created_by UUID, -- References auth.users
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create tenant_templates table for email and UI templates
CREATE TABLE IF NOT EXISTS tenant_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL, -- 'email', 'welcome_page', 'login_page', etc.
    name VARCHAR(255) NOT NULL,
    template_html TEXT,
    template_variables TEXT[] DEFAULT '{}',
    is_default BOOLEAN DEFAULT FALSE,
    created_by UUID, -- References auth.users
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, type, name)
);

-- Create automation_rules table for automation settings
CREATE TABLE IF NOT EXISTS automation_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    trigger_type VARCHAR(50) NOT NULL,
    trigger_config JSONB DEFAULT '{}',
    conditions JSONB DEFAULT '[]',
    actions JSONB DEFAULT '[]',
    enabled BOOLEAN DEFAULT TRUE,
    created_by UUID, -- References auth.users
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create compliance_logs table for compliance monitoring
CREATE TABLE IF NOT EXISTS compliance_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    check_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('compliant', 'non_compliant', 'warning')),
    details JSONB DEFAULT '{}',
    checked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create usage_logs table for analytics
CREATE TABLE IF NOT EXISTS usage_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    metric VARCHAR(100) NOT NULL,
    value NUMERIC NOT NULL,
    metadata JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tenant_settings_tenant_category ON tenant_settings(tenant_id, category);
CREATE INDEX IF NOT EXISTS idx_tenant_features_tenant_key ON tenant_features(tenant_id, feature_key);
CREATE INDEX IF NOT EXISTS idx_settings_audit_tenant_timestamp ON settings_audit(tenant_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_api_keys_tenant_active ON api_keys(tenant_id, is_active);
CREATE INDEX IF NOT EXISTS idx_webhooks_tenant_enabled ON webhooks(tenant_id, enabled);
CREATE INDEX IF NOT EXISTS idx_usage_logs_tenant_metric_timestamp ON usage_logs(tenant_id, metric, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_compliance_logs_tenant_type_timestamp ON compliance_logs(tenant_id, check_type, checked_at DESC);

-- Insert default permissions
INSERT INTO permissions (key, name, description, category) VALUES
-- Administration
('user_management', 'User Management', 'Create, update, and delete users', 'Administration'),
('role_management', 'Role Management', 'Manage roles and permissions', 'Administration'),
('tenant_settings', 'Tenant Settings', 'Configure tenant-wide settings', 'Administration'),
('security_settings', 'Security Settings', 'Configure security and compliance settings', 'Administration'),

-- Content Management
('content_create', 'Content Creation', 'Create new learning content', 'Content'),
('content_edit', 'Content Editing', 'Edit existing content', 'Content'),
('content_delete', 'Content Deletion', 'Delete content', 'Content'),
('content_publish', 'Content Publishing', 'Publish content to learners', 'Content'),

-- Learning Management
('learning_path_create', 'Learning Path Creation', 'Create learning paths', 'Learning'),
('learning_path_edit', 'Learning Path Editing', 'Edit learning paths', 'Learning'),
('learning_path_assign', 'Learning Path Assignment', 'Assign paths to learners', 'Learning'),

-- Analytics & Reporting
('analytics_view', 'View Analytics', 'Access analytics dashboards', 'Analytics'),
('reports_create', 'Create Reports', 'Create custom reports', 'Analytics'),
('reports_export', 'Export Reports', 'Export report data', 'Analytics'),

-- API & Integrations
('api_access', 'API Access', 'Access REST API endpoints', 'Integration'),
('webhook_manage', 'Webhook Management', 'Manage webhook configurations', 'Integration'),

-- Basic Access
('dashboard_view', 'Dashboard Access', 'Access main dashboard', 'Basic'),
('profile_edit', 'Profile Editing', 'Edit own profile', 'Basic')
ON CONFLICT (key) DO NOTHING;

-- Insert default feature flags
INSERT INTO tenant_features (tenant_id, feature_key, enabled) 
SELECT 
    t.id,
    feature_key,
    enabled
FROM tenants t
CROSS JOIN (VALUES
    ('enable_multi_language', true),
    ('restrict_timezone', false),
    ('allow_maintenance_scheduling', true),
    ('enable_custom_fonts', true),
    ('restrict_color_palette', false),
    ('allow_template_editing', true),
    ('enable_mfa', true),
    ('restrict_sso', false),
    ('enable_retention_customization', true),
    ('enable_custom_roles', true),
    ('restrict_permission_scope', false),
    ('enable_feature_management', true),
    ('enable_ai_themes', false),
    ('enable_custom_widgets', false),
    ('enable_compliance_dashboard', true),
    ('enable_ip_whitelisting', false),
    ('enable_activity_monitor', true),
    ('enable_temp_tokens', false),
    ('enable_gamification', true),
    ('enable_moderation', true),
    ('enable_ai_customization', true),
    ('enable_automation', false),
    ('enable_api_keys', true),
    ('enable_webhooks', false),
    ('enable_sms_notifications', false),
    ('enable_escalations', false),
    ('enable_usage_analytics', true),
    ('enable_audit_viewer', true)
) AS features(feature_key, enabled)
ON CONFLICT (tenant_id, feature_key) DO NOTHING;

-- Create Row Level Security (RLS) policies
ALTER TABLE tenant_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenant_features ENABLE ROW LEVEL SECURITY;
ALTER TABLE role_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE settings_audit ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE webhooks ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenant_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE automation_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE compliance_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policies (placeholder - would need to be customized based on auth setup)
-- These policies ensure tenant isolation and proper access control

-- Tenant Settings Policies
CREATE POLICY "Users can view their tenant settings" ON tenant_settings
    FOR SELECT USING (tenant_id = (auth.jwt() ->> 'tenant_id')::integer);

CREATE POLICY "Admins can modify their tenant settings" ON tenant_settings
    FOR ALL USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::integer 
        AND auth.jwt() ->> 'role' = 'admin'
    );

-- Feature Flags Policies
CREATE POLICY "Users can view their tenant features" ON tenant_features
    FOR SELECT USING (tenant_id = (auth.jwt() ->> 'tenant_id')::integer);

CREATE POLICY "Admins can modify their tenant features" ON tenant_features
    FOR ALL USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::integer 
        AND auth.jwt() ->> 'role' = 'admin'
    );

-- Audit Logs Policies
CREATE POLICY "Admins can view their tenant audit logs" ON settings_audit
    FOR SELECT USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::integer 
        AND auth.jwt() ->> 'role' = 'admin'
    );

-- API Keys Policies
CREATE POLICY "Admins can manage their tenant API keys" ON api_keys
    FOR ALL USING (
        tenant_id = (auth.jwt() ->> 'tenant_id')::integer 
        AND auth.jwt() ->> 'role' = 'admin'
    );

-- Add updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for updated_at columns
CREATE TRIGGER update_tenant_settings_updated_at BEFORE UPDATE ON tenant_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tenant_features_updated_at BEFORE UPDATE ON tenant_features
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_webhooks_updated_at BEFORE UPDATE ON webhooks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tenant_templates_updated_at BEFORE UPDATE ON tenant_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_automation_rules_updated_at BEFORE UPDATE ON automation_rules
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
