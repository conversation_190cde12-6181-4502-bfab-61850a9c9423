'use client'

import React, { useState, useMemo } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Checkbox,
  Avatar,
  Chip,
  IconButton,
  Typography,
  Box,
  Skeleton,
  Tooltip,
  LinearProgress,
  useTheme
} from '@mui/material'
import {
  MoreVert as MoreIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Warning as WarningIcon
} from '@mui/icons-material'
import { format, formatDistanceToNow } from 'date-fns'

import { Learner } from '@/lib/types/learners'

interface LearnersTableProps {
  learners: Learner[]
  loading: boolean
  selectedLearners: string[]
  onSelectionChange: (selectedIds: string[]) => void
  onEdit: (learner: <PERSON>rner) => void
  onDelete: (learner: Learner) => void
  onMenuClick: (event: React.MouseEvent<HTMLElement>, learner: Learner) => void
  page: number
  totalCount: number
  onPageChange: (page: number) => void
}

const LearnersTable: React.FC<LearnersTableProps> = ({
  learners,
  loading,
  selectedLearners,
  onSelectionChange,
  onEdit,
  onDelete,
  onMenuClick,
  page,
  totalCount,
  onPageChange
}) => {
  const theme = useTheme()
  const [rowsPerPage, setRowsPerPage] = useState(20)

  const isSelected = (learnerId: string) => selectedLearners.includes(learnerId)
  const isAllSelected = learners.length > 0 && selectedLearners.length === learners.length

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      onSelectionChange(learners.map(learner => learner.id))
    } else {
      onSelectionChange([])
    }
  }

  const handleSelectOne = (learnerId: string) => {
    const selectedIndex = selectedLearners.indexOf(learnerId)
    let newSelected: string[] = []

    if (selectedIndex === -1) {
      newSelected = [...selectedLearners, learnerId]
    } else {
      newSelected = selectedLearners.filter(id => id !== learnerId)
    }

    onSelectionChange(newSelected)
  }

  const handleChangePage = (event: unknown, newPage: number) => {
    onPageChange(newPage + 1) // Convert to 1-based pagination
  }

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10))
    onPageChange(1)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success'
      case 'inactive':
        return 'default'
      case 'suspended':
        return 'error'
      default:
        return 'default'
    }
  }

  const getEngagementColor = (score?: number) => {
    if (!score) return 'default'
    if (score >= 80) return 'success'
    if (score >= 60) return 'warning'
    return 'error'
  }

  const formatLastActive = (lastActive?: string) => {
    if (!lastActive) return 'Never'
    try {
      return formatDistanceToNow(new Date(lastActive), { addSuffix: true })
    } catch {
      return 'Unknown'
    }
  }

  if (loading) {
    return (
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Skeleton variant="rectangular" width={24} height={24} />
              </TableCell>
              <TableCell>Learner</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Progress</TableCell>
              <TableCell>Engagement</TableCell>
              <TableCell>Last Active</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {Array.from({ length: 10 }).map((_, index) => (
              <TableRow key={index}>
                <TableCell padding="checkbox">
                  <Skeleton variant="rectangular" width={24} height={24} />
                </TableCell>
                <TableCell>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Skeleton variant="circular" width={40} height={40} />
                    <Box>
                      <Skeleton variant="text" width={120} />
                      <Skeleton variant="text" width={180} />
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>
                  <Skeleton variant="rectangular" width={80} height={24} />
                </TableCell>
                <TableCell>
                  <Skeleton variant="rectangular" width={100} height={8} />
                </TableCell>
                <TableCell>
                  <Skeleton variant="rectangular" width={60} height={24} />
                </TableCell>
                <TableCell>
                  <Skeleton variant="text" width={100} />
                </TableCell>
                <TableCell>
                  <Skeleton variant="circular" width={24} height={24} />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    )
  }

  return (
    <>
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  indeterminate={selectedLearners.length > 0 && selectedLearners.length < learners.length}
                  checked={isAllSelected}
                  onChange={handleSelectAll}
                />
              </TableCell>
              <TableCell>Learner</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Progress</TableCell>
              <TableCell>Engagement</TableCell>
              <TableCell>Last Active</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {learners.map((learner) => (
              <TableRow
                key={learner.id}
                hover
                selected={isSelected(learner.id)}
                sx={{ cursor: 'pointer' }}
                onClick={() => onEdit(learner)}
              >
                <TableCell padding="checkbox" onClick={(e) => e.stopPropagation()}>
                  <Checkbox
                    checked={isSelected(learner.id)}
                    onChange={() => handleSelectOne(learner.id)}
                  />
                </TableCell>

                <TableCell>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Avatar
                      src={learner.avatar_url}
                      alt={learner.full_name}
                      sx={{ width: 40, height: 40 }}
                    >
                      {learner.full_name.charAt(0).toUpperCase()}
                    </Avatar>
                    <Box>
                      <Typography variant="subtitle2" fontWeight="medium">
                        {learner.full_name}
                      </Typography>
                      <Box display="flex" alignItems="center" gap={1}>
                        <EmailIcon sx={{ fontSize: 14, color: 'text.secondary' }} />
                        <Typography variant="body2" color="text.secondary">
                          {learner.email}
                        </Typography>
                      </Box>
                      {learner.department && (
                        <Typography variant="caption" color="text.secondary">
                          {learner.department}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                </TableCell>

                <TableCell>
                  <Chip
                    label={learner.status}
                    color={getStatusColor(learner.status) as any}
                    size="small"
                    variant="outlined"
                  />
                  {learner.overdue_assignments && learner.overdue_assignments > 0 && (
                    <Tooltip title={`${learner.overdue_assignments} overdue assignments`}>
                      <WarningIcon 
                        sx={{ 
                          ml: 1, 
                          fontSize: 16, 
                          color: 'warning.main' 
                        }} 
                      />
                    </Tooltip>
                  )}
                </TableCell>

                <TableCell>
                  <Box sx={{ minWidth: 120 }}>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={0.5}>
                      <Typography variant="body2">
                        {learner.completion_rate || 0}%
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {learner.completed_paths || 0}/{learner.total_paths || 0}
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={learner.completion_rate || 0}
                      sx={{ height: 6, borderRadius: 3 }}
                    />
                  </Box>
                </TableCell>

                <TableCell>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Chip
                      label={learner.engagement_score || 0}
                      color={getEngagementColor(learner.engagement_score) as any}
                      size="small"
                      variant="filled"
                    />
                    {learner.engagement_score && learner.engagement_score >= 80 ? (
                      <TrendingUpIcon sx={{ fontSize: 16, color: 'success.main' }} />
                    ) : learner.engagement_score && learner.engagement_score < 60 ? (
                      <TrendingDownIcon sx={{ fontSize: 16, color: 'error.main' }} />
                    ) : null}
                  </Box>
                </TableCell>

                <TableCell>
                  <Typography variant="body2">
                    {formatLastActive(learner.last_active)}
                  </Typography>
                </TableCell>

                <TableCell align="right" onClick={(e) => e.stopPropagation()}>
                  <IconButton
                    size="small"
                    onClick={(e) => onMenuClick(e, learner)}
                  >
                    <MoreIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <TablePagination
        component="div"
        count={totalCount}
        page={page - 1} // Convert back to 0-based for MUI
        onPageChange={handleChangePage}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        rowsPerPageOptions={[10, 20, 50, 100]}
      />
    </>
  )
}

export default LearnersTable
