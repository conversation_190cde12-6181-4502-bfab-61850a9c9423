import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
    )

    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization header required' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get current user from auth
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(
      authHeader.replace('Bearer ', '')
    )

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid authorization' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get request body
    const { 
      recipient_ids, 
      subject, 
      content, 
      send_email, 
      send_notification,
      sender_id,
      tenant_id 
    } = await req.json()

    if (!recipient_ids || !Array.isArray(recipient_ids) || recipient_ids.length === 0) {
      return new Response(
        JSON.stringify({ error: 'recipient_ids array is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    if (!subject || !content) {
      return new Response(
        JSON.stringify({ error: 'subject and content are required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get sender information
    const { data: senderData, error: senderError } = await supabaseClient
      .from('users')
      .select('full_name, email')
      .eq('id', sender_id)
      .single()

    if (senderError || !senderData) {
      return new Response(
        JSON.stringify({ error: 'Sender not found' }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get recipient information
    const { data: recipients, error: recipientsError } = await supabaseClient
      .from('users')
      .select('id, full_name, email')
      .in('id', recipient_ids)
      .eq('tenant_id', tenant_id)

    if (recipientsError) {
      throw recipientsError
    }

    if (!recipients || recipients.length === 0) {
      return new Response(
        JSON.stringify({ error: 'No valid recipients found' }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Store messages in database
    const messages = recipients.map(recipient => ({
      sender_id,
      recipient_id: recipient.id,
      subject,
      content,
      created_at: new Date().toISOString(),
      read: false
    }))

    const { error: insertError } = await supabaseClient
      .from('messages')
      .insert(messages)

    if (insertError) {
      throw insertError
    }

    // Send in-app notifications if requested
    if (send_notification) {
      const notifications = recipients.map(recipient => ({
        user_id: recipient.id,
        type: 'message',
        title: `New message from ${senderData.full_name}`,
        content: subject,
        data: {
          sender_id,
          sender_name: senderData.full_name,
          message_subject: subject
        },
        created_at: new Date().toISOString(),
        read: false
      }))

      const { error: notificationError } = await supabaseClient
        .from('notifications')
        .insert(notifications)

      if (notificationError) {
        console.error('Error creating notifications:', notificationError)
        // Don't fail the entire request for notification errors
      }
    }

    // Send emails if requested
    if (send_email) {
      // In a real implementation, you would integrate with an email service
      // like SendGrid, AWS SES, or similar
      console.log('Would send emails to:', recipients.map(r => r.email))
      
      // Mock email sending
      for (const recipient of recipients) {
        console.log(`Sending email to ${recipient.email}:`)
        console.log(`Subject: ${subject}`)
        console.log(`From: ${senderData.full_name} <${senderData.email}>`)
        console.log(`Content: ${content}`)
      }
    }

    // Send real-time notifications via Supabase Realtime
    for (const recipient of recipients) {
      await supabaseClient
        .channel(`user:${recipient.id}`)
        .send({
          type: 'broadcast',
          event: 'new_message',
          payload: {
            sender_id,
            sender_name: senderData.full_name,
            subject,
            content: content.substring(0, 100) + (content.length > 100 ? '...' : ''),
            created_at: new Date().toISOString()
          }
        })
    }

    return new Response(
      JSON.stringify({ 
        success: true,
        messages_sent: recipients.length,
        recipients: recipients.map(r => ({ id: r.id, name: r.full_name, email: r.email }))
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in send-learner-message:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
