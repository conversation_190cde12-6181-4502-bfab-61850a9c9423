'use client'

import {
  <PERSON>,
  CardContent,
  Typography,
  Box,
  Avatar,
  Chip,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Button,
} from '@mui/material'
import {
  School as SchoolIcon,
  Person as PersonIcon,
  Upload as UploadIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material'

// Mock recent activity data
const recentActivities = [
  {
    id: 1,
    type: 'enrollment',
    user: '<PERSON>',
    action: 'enrolled in',
    target: 'Advanced JavaScript',
    time: '2 minutes ago',
    avatar: null,
  },
  {
    id: 2,
    type: 'completion',
    user: '<PERSON>',
    action: 'completed',
    target: 'React Fundamentals',
    time: '15 minutes ago',
    avatar: null,
  },
  {
    id: 3,
    type: 'upload',
    user: 'Admin',
    action: 'uploaded new content',
    target: 'Introduction to AI',
    time: '1 hour ago',
    avatar: null,
  },
  {
    id: 4,
    type: 'assignment',
    user: '<PERSON>',
    action: 'was assigned to',
    target: 'Python Basics',
    time: '2 hours ago',
    avatar: null,
  },
  {
    id: 5,
    type: 'completion',
    user: '<PERSON>',
    action: 'completed',
    target: 'Data Science Intro',
    time: '3 hours ago',
    avatar: null,
  },
]

const getActivityIcon = (type: string) => {
  switch (type) {
    case 'enrollment':
      return <PersonIcon />
    case 'completion':
      return <SchoolIcon />
    case 'upload':
      return <UploadIcon />
    case 'assignment':
      return <AssignmentIcon />
    default:
      return <PersonIcon />
  }
}

const getActivityColor = (type: string) => {
  switch (type) {
    case 'enrollment':
      return 'primary'
    case 'completion':
      return 'success'
    case 'upload':
      return 'info'
    case 'assignment':
      return 'warning'
    default:
      return 'default'
  }
}

export default function RecentActivity() {
  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6" fontWeight="bold">
            Recent Activity
          </Typography>
          <Button variant="text" size="small">
            View All
          </Button>
        </Box>

        <List sx={{ p: 0 }}>
          {recentActivities.map((activity, index) => (
            <ListItem
              key={activity.id}
              sx={{
                px: 0,
                py: 1.5,
                borderBottom: index < recentActivities.length - 1 ? 1 : 0,
                borderColor: 'divider',
              }}
            >
              <ListItemAvatar>
                <Avatar
                  sx={{
                    bgcolor: `${getActivityColor(activity.type)}.main`,
                    width: 40,
                    height: 40,
                  }}
                >
                  {getActivityIcon(activity.type)}
                </Avatar>
              </ListItemAvatar>
              
              <ListItemText
                primary={
                  <Box display="flex" alignItems="center" gap={1} flexWrap="wrap">
                    <Typography variant="body2" component="span" fontWeight="medium">
                      {activity.user}
                    </Typography>
                    <Typography variant="body2" component="span" color="text.secondary">
                      {activity.action}
                    </Typography>
                    <Chip
                      label={activity.target}
                      size="small"
                      variant="outlined"
                      sx={{ fontSize: '0.75rem', height: 20 }}
                    />
                  </Box>
                }
                secondary={
                  <Typography variant="caption" color="text.secondary">
                    {activity.time}
                  </Typography>
                }
              />
            </ListItem>
          ))}
        </List>
      </CardContent>
    </Card>
  )
}
