'use client'

import { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  IconButton,
  useTheme,
  Alert,
} from '@mui/material'
import {
  ViewInAr as ARIcon,
  CameraAlt as CameraIcon,
  PhoneAndroid as MobileIcon,
  Science as ScienceIcon,
  Architecture as ArchIcon,
  Psychology as BrainIcon,
  Launch as LaunchIcon,
  QrCode as QRIcon,
} from '@mui/icons-material'
import { motion, AnimatePresence } from 'framer-motion'

interface ARContent {
  id: string
  title: string
  type: 'model' | 'simulation' | 'interactive'
  category: string
  description: string
  icon: React.ReactNode
  color: string
  isAvailable: boolean
  previewUrl?: string
}

const mockARContent: ARContent[] = [
  {
    id: '1',
    title: 'Human Anatomy 3D',
    type: 'model',
    category: 'Medical',
    description: 'Interactive 3D human body model',
    icon: <BrainIcon />,
    color: '#E91E63',
    isAvailable: true,
    previewUrl: '/ar/anatomy'
  },
  {
    id: '2',
    title: 'Solar System',
    type: 'simulation',
    category: 'Science',
    description: 'Explore planets and their orbits',
    icon: <ScienceIcon />,
    color: '#3F51B5',
    isAvailable: true,
    previewUrl: '/ar/solar-system'
  },
  {
    id: '3',
    title: 'Architecture Basics',
    type: 'interactive',
    category: 'Engineering',
    description: 'Build and explore structures',
    icon: <ArchIcon />,
    color: '#FF9800',
    isAvailable: false
  },
  {
    id: '4',
    title: 'Chemical Reactions',
    type: 'simulation',
    category: 'Chemistry',
    description: 'Visualize molecular interactions',
    icon: <ScienceIcon />,
    color: '#4CAF50',
    isAvailable: true,
    previewUrl: '/ar/chemistry'
  }
]

export default function ARPreviewWidget() {
  const theme = useTheme()
  const [isARSupported, setIsARSupported] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [selectedContent, setSelectedContent] = useState<ARContent | null>(null)
  const [showQRCode, setShowQRCode] = useState(false)

  useEffect(() => {
    // Check if device supports AR
    const checkARSupport = () => {
      const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
      setIsMobile(isMobileDevice)
      
      // Check for WebXR support (simplified check)
      if ('xr' in navigator) {
        (navigator as any).xr.isSessionSupported('immersive-ar').then((supported: boolean) => {
          setIsARSupported(supported)
        }).catch(() => {
          setIsARSupported(false)
        })
      } else {
        // Fallback: assume AR is supported on mobile devices
        setIsARSupported(isMobileDevice)
      }
    }

    checkARSupport()
  }, [])

  const handleARPreview = (content: ARContent) => {
    if (!content.isAvailable) return

    if (isMobile) {
      // On mobile, show QR code or direct link
      setSelectedContent(content)
      setShowQRCode(true)
    } else {
      // On desktop, show instructions to use mobile
      setSelectedContent(content)
    }
  }

  const generateQRCode = (content: ARContent) => {
    // In a real app, this would generate an actual QR code
    // For demo purposes, we'll show a placeholder
    return `https://your-domain.com${content.previewUrl}?ar=true`
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'model': return '🏗️'
      case 'simulation': return '🔬'
      case 'interactive': return '🎮'
      default: return '📱'
    }
  }

  return (
    <Card sx={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      <CardContent sx={{
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
        <Box display="flex" alignItems="center" gap={2} mb={2}>
          <Avatar sx={{ bgcolor: 'secondary.main', width: 32, height: 32 }}>
            <ARIcon fontSize="small" />
          </Avatar>
          <Typography variant="h6" fontWeight="bold">
            AR Course Previews
          </Typography>
          <Chip 
            label={isARSupported ? 'AR Ready' : 'Limited'} 
            color={isARSupported ? 'success' : 'warning'}
            size="small"
          />
        </Box>

        {!isARSupported && (
          <Alert severity="info" sx={{ mb: 2, fontSize: '0.75rem' }}>
            <Typography variant="caption">
              AR features work best on mobile devices. Use your phone to scan QR codes for full AR experience.
            </Typography>
          </Alert>
        )}

        {/* AR Content List */}
        <Box sx={{
          flexGrow: 1,
          overflowY: 'auto',
          minHeight: 0,
          '&::-webkit-scrollbar': { width: 4 },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'rgba(0,0,0,0.2)',
            borderRadius: 2
          }
        }}>
          <List dense>
          {mockARContent.map((content, index) => (
            <motion.div
              key={content.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <ListItem 
                sx={{ 
                  px: 0, 
                  py: 1,
                  opacity: content.isAvailable ? 1 : 0.6,
                  cursor: content.isAvailable ? 'pointer' : 'default',
                  borderRadius: 1,
                  '&:hover': content.isAvailable ? {
                    bgcolor: 'action.hover'
                  } : {}
                }}
                onClick={() => content.isAvailable && handleARPreview(content)}
              >
                <ListItemAvatar>
                  <Avatar sx={{ 
                    width: 32, 
                    height: 32, 
                    bgcolor: content.color + '20',
                    color: content.color,
                    fontSize: '0.8rem'
                  }}>
                    {getTypeIcon(content.type)}
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography variant="body2" fontWeight="medium">
                        {content.title}
                      </Typography>
                      <Box display="flex" alignItems="center" gap={0.5}>
                        <Chip
                          label={content.category}
                          size="small"
                          sx={{
                            height: 16,
                            fontSize: '0.6rem',
                            bgcolor: content.color + '20',
                            color: content.color
                          }}
                        />
                        {content.isAvailable && (
                          <IconButton size="small" color="primary">
                            <ARIcon fontSize="small" />
                          </IconButton>
                        )}
                      </Box>
                    </Box>
                  }
                  secondary={
                    <Typography variant="caption" color="text.secondary">
                      {content.description}
                    </Typography>
                  }
                />
              </ListItem>
            </motion.div>
          ))}
        </List>
        </Box>

        {/* AR Instructions */}
        <Box mt={2} p={1.5} sx={{ 
          bgcolor: 'background.default',
          borderRadius: 1,
          border: 1,
          borderColor: 'divider'
        }}>
          <Typography variant="body2" fontWeight="medium" mb={1}>
            How to use AR:
          </Typography>
          <Box display="flex" flexDirection="column" gap={0.5}>
            <Box display="flex" alignItems="center" gap={1}>
              <MobileIcon fontSize="small" color="primary" />
              <Typography variant="caption">
                Use mobile device for best experience
              </Typography>
            </Box>
            <Box display="flex" alignItems="center" gap={1}>
              <CameraIcon fontSize="small" color="primary" />
              <Typography variant="caption">
                Allow camera access when prompted
              </Typography>
            </Box>
            <Box display="flex" alignItems="center" gap={1}>
              <QRIcon fontSize="small" color="primary" />
              <Typography variant="caption">
                Scan QR codes to launch AR content
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Quick AR Demo Button */}
        <Box mt={2} textAlign="center">
          <Button
            variant="outlined"
            startIcon={<ARIcon />}
            onClick={() => {
              // Demo AR functionality
              if (mockARContent[0].isAvailable) {
                handleARPreview(mockARContent[0])
              }
            }}
            disabled={!mockARContent[0].isAvailable}
            sx={{
              borderColor: 'secondary.main',
              color: 'secondary.main',
              '&:hover': {
                borderColor: 'secondary.dark',
                bgcolor: 'secondary.main',
                color: 'secondary.contrastText'
              }
            }}
          >
            Try AR Demo
          </Button>
        </Box>
      </CardContent>

      {/* AR Preview Modal/Dialog */}
      <AnimatePresence>
        {selectedContent && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0,0,0,0.8)',
              zIndex: 9999,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: 16,
            }}
            onClick={() => {
              setSelectedContent(null)
              setShowQRCode(false)
            }}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              style={{
                backgroundColor: theme.palette.background.paper,
                borderRadius: 16,
                padding: 24,
                maxWidth: 400,
                width: '100%',
                textAlign: 'center',
              }}
            >
              <Typography variant="h6" fontWeight="bold" mb={2}>
                {selectedContent.title}
              </Typography>
              
              {isMobile ? (
                <Box>
                  <Typography variant="body2" color="text.secondary" mb={2}>
                    Launching AR experience...
                  </Typography>
                  <Box sx={{
                    width: 200,
                    height: 200,
                    bgcolor: 'grey.100',
                    borderRadius: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mx: 'auto',
                    mb: 2
                  }}>
                    <QRIcon sx={{ fontSize: 80, color: 'grey.400' }} />
                  </Box>
                  <Typography variant="caption" color="text.secondary" mb={2} display="block">
                    QR Code: {generateQRCode(selectedContent)}
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<LaunchIcon />}
                    href={selectedContent.previewUrl}
                    target="_blank"
                    fullWidth
                  >
                    Launch AR Experience
                  </Button>
                </Box>
              ) : (
                <Box>
                  <Typography variant="body2" color="text.secondary" mb={2}>
                    For the best AR experience, please use your mobile device to scan this QR code:
                  </Typography>
                  <Box sx={{
                    width: 200,
                    height: 200,
                    bgcolor: 'grey.100',
                    borderRadius: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mx: 'auto',
                    mb: 2
                  }}>
                    <QRIcon sx={{ fontSize: 80, color: 'grey.400' }} />
                  </Box>
                  <Typography variant="caption" color="text.secondary">
                    Or visit: {generateQRCode(selectedContent)}
                  </Typography>
                </Box>
              )}
              
              <Button
                onClick={() => {
                  setSelectedContent(null)
                  setShowQRCode(false)
                }}
                sx={{ mt: 2 }}
              >
                Close
              </Button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </Card>
  )
}
