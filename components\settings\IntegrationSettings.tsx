'use client'

import React, { useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Tabs,
  Tab
} from '@mui/material'
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Key as KeyIcon,
  Webhook as WebhookIcon,
  ContentCopy as CopyIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon
} from '@mui/icons-material'
import { motion } from 'framer-motion'

interface TabPanelProps {
  children?: React.ReactNode
  index: number
  value: number
}

function TabPanel({ children, value, index }: TabPanelProps) {
  return (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  )
}

const mockAPIKeys = [
  {
    id: '1',
    name: 'Mobile App Integration',
    key: 'sk_live_51H7...',
    permissions: ['read', 'write'],
    lastUsed: '2024-01-15T10:30:00Z',
    created: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    name: 'Analytics Dashboard',
    key: 'sk_live_52K9...',
    permissions: ['read'],
    lastUsed: '2024-01-14T15:45:00Z',
    created: '2024-01-05T00:00:00Z'
  }
]

const mockWebhooks = [
  {
    id: '1',
    name: 'User Registration Webhook',
    url: 'https://api.example.com/webhooks/user-registration',
    events: ['user.created', 'user.updated'],
    enabled: true,
    lastTriggered: '2024-01-15T09:20:00Z'
  },
  {
    id: '2',
    name: 'Course Completion Webhook',
    url: 'https://api.example.com/webhooks/course-completion',
    events: ['course.completed'],
    enabled: false,
    lastTriggered: null
  }
]

export default function IntegrationSettings() {
  const [tabValue, setTabValue] = useState(0)
  const [apiKeyDialogOpen, setApiKeyDialogOpen] = useState(false)
  const [webhookDialogOpen, setWebhookDialogOpen] = useState(false)
  const [showApiKey, setShowApiKey] = useState<Record<string, boolean>>({})

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue)
  }

  const toggleApiKeyVisibility = (keyId: string) => {
    setShowApiKey(prev => ({
      ...prev,
      [keyId]: !prev[keyId]
    }))
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  return (
    <Box>
      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab icon={<KeyIcon />} label="API Keys" iconPosition="start" />
          <Tab icon={<WebhookIcon />} label="Webhooks" iconPosition="start" />
        </Tabs>
      </Box>

      {/* API Keys Tab */}
      <TabPanel value={tabValue} index={0}>
        <Grid container spacing={3}>
          {/* Header */}
          <Grid item xs={12}>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Typography variant="h6" fontWeight="bold">
                API Key Management
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setApiKeyDialogOpen(true)}
              >
                Generate API Key
              </Button>
            </Box>
          </Grid>

          {/* API Keys Table */}
          <Grid item xs={12}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Card>
                <CardContent>
                  <TableContainer component={Paper} variant="outlined">
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Name</TableCell>
                          <TableCell>API Key</TableCell>
                          <TableCell>Permissions</TableCell>
                          <TableCell>Last Used</TableCell>
                          <TableCell>Actions</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {mockAPIKeys.map((apiKey) => (
                          <TableRow key={apiKey.id}>
                            <TableCell>
                              <Typography fontWeight="medium">
                                {apiKey.name}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Box display="flex" alignItems="center" gap={1}>
                                <Typography variant="body2" fontFamily="monospace">
                                  {showApiKey[apiKey.id] ? apiKey.key : '••••••••••••••••'}
                                </Typography>
                                <IconButton 
                                  size="small"
                                  onClick={() => toggleApiKeyVisibility(apiKey.id)}
                                >
                                  {showApiKey[apiKey.id] ? <VisibilityOffIcon /> : <VisibilityIcon />}
                                </IconButton>
                                <IconButton 
                                  size="small"
                                  onClick={() => copyToClipboard(apiKey.key)}
                                >
                                  <CopyIcon />
                                </IconButton>
                              </Box>
                            </TableCell>
                            <TableCell>
                              <Box display="flex" gap={0.5}>
                                {apiKey.permissions.map((permission) => (
                                  <Chip 
                                    key={permission}
                                    label={permission}
                                    size="small"
                                    variant="outlined"
                                  />
                                ))}
                              </Box>
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2" color="text.secondary">
                                {new Date(apiKey.lastUsed).toLocaleDateString()}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <IconButton size="small">
                                <EditIcon />
                              </IconButton>
                              <IconButton size="small" color="error">
                                <DeleteIcon />
                              </IconButton>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>

          {/* API Documentation */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  API Documentation
                </Typography>
                <Alert severity="info" sx={{ mb: 2 }}>
                  Use these endpoints to integrate with external systems. Full documentation is available in our developer portal.
                </Alert>
                <Box component="pre" sx={{ 
                  backgroundColor: 'grey.100', 
                  p: 2, 
                  borderRadius: 1,
                  overflow: 'auto',
                  fontSize: '0.875rem'
                }}>
{`Base URL: https://api.zenithlearn.ai/v1

Authentication:
Authorization: Bearer YOUR_API_KEY

Endpoints:
GET    /users           - List users
POST   /users           - Create user
GET    /courses         - List courses
POST   /courses         - Create course
GET    /progress        - Get progress data`}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Webhooks Tab */}
      <TabPanel value={tabValue} index={1}>
        <Grid container spacing={3}>
          {/* Header */}
          <Grid item xs={12}>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Typography variant="h6" fontWeight="bold">
                Webhook Management
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setWebhookDialogOpen(true)}
              >
                Add Webhook
              </Button>
            </Box>
          </Grid>

          {/* Webhooks Table */}
          <Grid item xs={12}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Card>
                <CardContent>
                  <TableContainer component={Paper} variant="outlined">
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Name</TableCell>
                          <TableCell>URL</TableCell>
                          <TableCell>Events</TableCell>
                          <TableCell>Status</TableCell>
                          <TableCell>Last Triggered</TableCell>
                          <TableCell>Actions</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {mockWebhooks.map((webhook) => (
                          <TableRow key={webhook.id}>
                            <TableCell>
                              <Typography fontWeight="medium">
                                {webhook.name}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2" fontFamily="monospace">
                                {webhook.url}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Box display="flex" gap={0.5} flexWrap="wrap">
                                {webhook.events.map((event) => (
                                  <Chip 
                                    key={event}
                                    label={event}
                                    size="small"
                                    variant="outlined"
                                  />
                                ))}
                              </Box>
                            </TableCell>
                            <TableCell>
                              <Chip 
                                label={webhook.enabled ? 'Active' : 'Inactive'}
                                color={webhook.enabled ? 'success' : 'default'}
                                size="small"
                              />
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2" color="text.secondary">
                                {webhook.lastTriggered 
                                  ? new Date(webhook.lastTriggered).toLocaleDateString()
                                  : 'Never'
                                }
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <IconButton size="small">
                                <EditIcon />
                              </IconButton>
                              <IconButton size="small" color="error">
                                <DeleteIcon />
                              </IconButton>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>

          {/* Available Events */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Available Webhook Events
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      User Events
                    </Typography>
                    <Box display="flex" gap={1} flexWrap="wrap" mb={2}>
                      <Chip label="user.created" size="small" />
                      <Chip label="user.updated" size="small" />
                      <Chip label="user.deleted" size="small" />
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      Course Events
                    </Typography>
                    <Box display="flex" gap={1} flexWrap="wrap" mb={2}>
                      <Chip label="course.started" size="small" />
                      <Chip label="course.completed" size="small" />
                      <Chip label="course.progress" size="small" />
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* API Key Dialog */}
      <Dialog open={apiKeyDialogOpen} onClose={() => setApiKeyDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Generate New API Key</DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="API Key Name"
                placeholder="e.g., Mobile App Integration"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Permissions</InputLabel>
                <Select multiple defaultValue={['read']}>
                  <MenuItem value="read">Read</MenuItem>
                  <MenuItem value="write">Write</MenuItem>
                  <MenuItem value="delete">Delete</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setApiKeyDialogOpen(false)}>Cancel</Button>
          <Button variant="contained">Generate Key</Button>
        </DialogActions>
      </Dialog>

      {/* Webhook Dialog */}
      <Dialog open={webhookDialogOpen} onClose={() => setWebhookDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add New Webhook</DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Webhook Name"
                placeholder="e.g., User Registration Webhook"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Webhook URL"
                placeholder="https://api.example.com/webhooks/endpoint"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Events</InputLabel>
                <Select multiple defaultValue={[]}>
                  <MenuItem value="user.created">User Created</MenuItem>
                  <MenuItem value="user.updated">User Updated</MenuItem>
                  <MenuItem value="course.completed">Course Completed</MenuItem>
                  <MenuItem value="course.started">Course Started</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setWebhookDialogOpen(false)}>Cancel</Button>
          <Button variant="contained">Add Webhook</Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}
