'use client'

import { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  Typography,
  Box,
  LinearProgress,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  IconButton,
  Tooltip,
  useTheme,
} from '@mui/material'
import {
  EmojiEvents as TrophyIcon,
  Star as StarIcon,
  LocalFireDepartment as StreakIcon,
  Assignment as QuestIcon,
  Celebration as CelebrationIcon,
  KeyboardArrowRight as ArrowIcon,
} from '@mui/icons-material'
import { motion, AnimatePresence } from 'framer-motion'

interface GamificationWidgetProps {
  gamification: any
  achievements: any[]
  dailyChallenges: any[]
}

const mockChallenges = [
  {
    id: '1',
    title: 'User Onboarding Master',
    description: 'Add 5 new users today',
    progress: 3,
    target: 5,
    reward: { points: 100, badge: '👥' },
    type: 'user_management'
  },
  {
    id: '2',
    title: 'Course Creator',
    description: 'Create or update 2 learning paths',
    progress: 1,
    target: 2,
    reward: { points: 150, badge: '📚' },
    type: 'course_creation'
  },
  {
    id: '3',
    title: 'Engagement Booster',
    description: 'Achieve 85% completion rate',
    progress: 78,
    target: 85,
    reward: { points: 200, badge: '🚀' },
    type: 'engagement'
  }
]

const mockAchievements = [
  {
    id: '1',
    title: 'First Steps',
    description: 'Completed your first day as admin',
    icon: '🎯',
    rarity: 'common',
    unlockedAt: new Date().toISOString()
  },
  {
    id: '2',
    title: 'People Person',
    description: 'Managed 100+ users',
    icon: '👥',
    rarity: 'rare',
    unlockedAt: new Date().toISOString()
  },
  {
    id: '3',
    title: 'Course Architect',
    description: 'Created 10 learning paths',
    icon: '🏗️',
    rarity: 'epic',
    unlockedAt: null
  }
]

export default function GamificationWidget({ 
  gamification, 
  achievements = mockAchievements, 
  dailyChallenges = mockChallenges 
}: GamificationWidgetProps) {
  const theme = useTheme()
  const [showCelebration, setShowCelebration] = useState(false)
  const [selectedTab, setSelectedTab] = useState<'challenges' | 'achievements'>('challenges')

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return '#4CAF50'
      case 'rare': return '#2196F3'
      case 'epic': return '#9C27B0'
      case 'legendary': return '#FF9800'
      default: return theme.palette.primary.main
    }
  }

  const getProgressColor = (progress: number, target: number) => {
    const percentage = (progress / target) * 100
    if (percentage >= 100) return '#4CAF50'
    if (percentage >= 75) return '#FF9800'
    if (percentage >= 50) return '#2196F3'
    return '#9E9E9E'
  }

  const triggerCelebration = () => {
    setShowCelebration(true)
    setTimeout(() => setShowCelebration(false), 3000)
  }

  useEffect(() => {
    // Check for completed challenges
    const completedChallenges = dailyChallenges.filter(c => c.progress >= c.target)
    if (completedChallenges.length > 0) {
      // triggerCelebration()
    }
  }, [dailyChallenges])

  return (
    <Card sx={{
      height: '100%',
      position: 'relative',
      overflow: 'hidden',
      display: 'flex',
      flexDirection: 'column'
    }}>
      <CardContent sx={{
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
        <Box display="flex" alignItems="center" gap={2} mb={2}>
          <Avatar sx={{ bgcolor: 'warning.main', width: 32, height: 32 }}>
            <TrophyIcon fontSize="small" />
          </Avatar>
          <Typography variant="h6" fontWeight="bold">
            Admin Quest Hub
          </Typography>
        </Box>

        {/* Level & XP Display */}
        <Box mb={3}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
            <Box display="flex" alignItems="center" gap={1}>
              <Typography variant="h5" fontWeight="bold" color="primary">
                Level {gamification.level}
              </Typography>
              <Chip 
                label={gamification.rank} 
                size="small" 
                color="primary"
                sx={{ fontWeight: 'bold' }}
              />
            </Box>
            <Box display="flex" alignItems="center" gap={1}>
              <StreakIcon color="warning" fontSize="small" />
              <Typography variant="body2" fontWeight="bold">
                {gamification.streak} day streak
              </Typography>
            </Box>
          </Box>
          
          <Box mb={1}>
            <Typography variant="body2" color="text.secondary">
              {gamification.experience} / {gamification.experienceToNext} XP
            </Typography>
          </Box>
          
          <LinearProgress
            variant="determinate"
            value={(gamification.experience / gamification.experienceToNext) * 100}
            sx={{
              height: 8,
              borderRadius: 4,
              bgcolor: 'rgba(255, 193, 7, 0.2)',
              '& .MuiLinearProgress-bar': {
                borderRadius: 4,
                background: 'linear-gradient(45deg, #FFC107 30%, #FF8F00 90%)',
              }
            }}
          />
        </Box>

        {/* Tab Selection */}
        <Box display="flex" gap={1} mb={2}>
          <Chip
            label="Daily Quests"
            clickable
            color={selectedTab === 'challenges' ? 'primary' : 'default'}
            onClick={() => setSelectedTab('challenges')}
            icon={<QuestIcon />}
            size="small"
          />
          <Chip
            label="Achievements"
            clickable
            color={selectedTab === 'achievements' ? 'primary' : 'default'}
            onClick={() => setSelectedTab('achievements')}
            icon={<StarIcon />}
            size="small"
          />
        </Box>

        {/* Content Area */}
        <Box sx={{
          flexGrow: 1,
          overflowY: 'auto',
          minHeight: 0,
          '&::-webkit-scrollbar': { width: 4 },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'rgba(0,0,0,0.2)',
            borderRadius: 2
          }
        }}>
          <AnimatePresence mode="wait">
            {selectedTab === 'challenges' && (
              <motion.div
                key="challenges"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3 }}
              >
                <List dense>
                  {dailyChallenges.map((challenge, index) => (
                    <motion.div
                      key={challenge.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <ListItem sx={{ px: 0, py: 1 }}>
                        <ListItemAvatar>
                          <Avatar sx={{ 
                            width: 32, 
                            height: 32, 
                            bgcolor: getProgressColor(challenge.progress, challenge.target),
                            fontSize: '0.8rem'
                          }}>
                            {challenge.reward.badge}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Typography variant="body2" fontWeight="medium">
                              {challenge.title}
                            </Typography>
                          }
                          secondary={
                            <Box>
                              <Typography variant="caption" color="text.secondary">
                                {challenge.description}
                              </Typography>
                              <Box display="flex" alignItems="center" gap={1} mt={0.5}>
                                <LinearProgress
                                  variant="determinate"
                                  value={(challenge.progress / challenge.target) * 100}
                                  sx={{
                                    flexGrow: 1,
                                    height: 4,
                                    borderRadius: 2,
                                    bgcolor: 'rgba(0,0,0,0.1)',
                                    '& .MuiLinearProgress-bar': {
                                      borderRadius: 2,
                                      bgcolor: getProgressColor(challenge.progress, challenge.target),
                                    }
                                  }}
                                />
                                <Typography variant="caption" fontWeight="bold">
                                  {challenge.progress}/{challenge.target}
                                </Typography>
                              </Box>
                              <Chip
                                label={`+${challenge.reward.points} XP`}
                                size="small"
                                color="warning"
                                sx={{ mt: 0.5, height: 16, fontSize: '0.6rem' }}
                              />
                            </Box>
                          }
                        />
                        {challenge.progress >= challenge.target && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ type: "spring", stiffness: 500 }}
                          >
                            <IconButton size="small" color="success">
                              <CelebrationIcon fontSize="small" />
                            </IconButton>
                          </motion.div>
                        )}
                      </ListItem>
                    </motion.div>
                  ))}
                </List>
              </motion.div>
            )}

            {selectedTab === 'achievements' && (
              <motion.div
                key="achievements"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <List dense>
                  {achievements.map((achievement, index) => (
                    <motion.div
                      key={achievement.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <ListItem sx={{ px: 0, py: 1 }}>
                        <ListItemAvatar>
                          <Avatar sx={{ 
                            width: 32, 
                            height: 32,
                            bgcolor: achievement.unlockedAt ? getRarityColor(achievement.rarity) : 'grey.400',
                            opacity: achievement.unlockedAt ? 1 : 0.5,
                            fontSize: '0.8rem'
                          }}>
                            {achievement.icon}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Typography 
                              variant="body2" 
                              fontWeight="medium"
                              sx={{ 
                                opacity: achievement.unlockedAt ? 1 : 0.6,
                                textDecoration: achievement.unlockedAt ? 'none' : 'none'
                              }}
                            >
                              {achievement.title}
                            </Typography>
                          }
                          secondary={
                            <Box>
                              <Typography 
                                variant="caption" 
                                color="text.secondary"
                                sx={{ opacity: achievement.unlockedAt ? 1 : 0.6 }}
                              >
                                {achievement.description}
                              </Typography>
                              <Box display="flex" alignItems="center" gap={1} mt={0.5}>
                                <Chip
                                  label={achievement.rarity}
                                  size="small"
                                  sx={{
                                    height: 16,
                                    fontSize: '0.6rem',
                                    bgcolor: getRarityColor(achievement.rarity) + '20',
                                    color: getRarityColor(achievement.rarity),
                                    fontWeight: 'bold'
                                  }}
                                />
                                {achievement.unlockedAt && (
                                  <Typography variant="caption" color="success.main" fontWeight="bold">
                                    ✓ Unlocked
                                  </Typography>
                                )}
                              </Box>
                            </Box>
                          }
                        />
                      </ListItem>
                    </motion.div>
                  ))}
                </List>
              </motion.div>
            )}
          </AnimatePresence>
        </Box>
      </CardContent>

      {/* Celebration Effect */}
      <AnimatePresence>
        {showCelebration && (
          <motion.div
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0 }}
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              zIndex: 10,
              fontSize: '3rem',
            }}
          >
            🎉
          </motion.div>
        )}
      </AnimatePresence>
    </Card>
  )
}
