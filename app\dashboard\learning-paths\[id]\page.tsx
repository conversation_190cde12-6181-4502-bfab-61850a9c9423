'use client'

import React, { use } from 'react'
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Alert,
  Skeleton,
  IconButton,
  Menu,
  MenuItem
} from '@mui/material'
import {
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  MoreVert as MoreVertIcon,
  Publish as PublishIcon,
  ContentCopy as CopyIcon,
  Delete as DeleteIcon,
  PlayArrow as PlayIcon,
  Quiz as QuizIcon,
  PictureAsPdf as PdfIcon,
  Link as LinkIcon,
  Code as CodeIcon,
  Schedule as ScheduleIcon,
  People as PeopleIcon,
  School as SchoolIcon
} from '@mui/icons-material'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

import { useLearningPath } from '@/lib/hooks/useLearningPaths'

interface LearningPathViewProps {
  params: Promise<{
    id: string
  }>
}

export default function LearningPathViewPage({ params }: LearningPathViewProps) {
  const router = useRouter()
  const resolvedParams = use(params)
  const { data: path, isLoading, error } = useLearningPath(resolvedParams.id)
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null)

  const handleBack = () => {
    router.push('/dashboard/learning-paths')
  }

  const handleEdit = () => {
    router.push(`/dashboard/learning-paths/${resolvedParams.id}/edit`)
  }

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchor(event.currentTarget)
  }

  const handleMenuClose = () => {
    setMenuAnchor(null)
  }

  const getLessonIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <PlayIcon />
      case 'pdf':
        return <PdfIcon />
      case 'quiz':
        return <QuizIcon />
      case 'link':
        return <LinkIcon />
      case 'simulation':
        return <CodeIcon />
      default:
        return <PlayIcon />
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'success'
      case 'intermediate':
        return 'warning'
      case 'advanced':
        return 'error'
      default:
        return 'default'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'success'
      case 'draft':
        return 'warning'
      case 'archived':
        return 'default'
      default:
        return 'default'
    }
  }

  // Compute derived values from database fields
  const status = path?.status || (path?.is_live ? 'published' : 'draft')
  const difficulty = path?.difficulty || path?.difficulty_level || 'beginner'
  const duration = path?.duration || path?.estimated_duration

  if (error) {
    return (
      <Box p={3}>
        <Alert severity="error">
          Failed to load learning path. Please try again.
        </Alert>
      </Box>
    )
  }

  if (isLoading) {
    return (
      <Box p={3}>
        <Box display="flex" alignItems="center" mb={4}>
          <Skeleton variant="rectangular" width={100} height={36} sx={{ mr: 2 }} />
          <Skeleton variant="text" width={300} height={40} />
        </Box>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Skeleton variant="text" width="60%" height={32} />
                <Skeleton variant="text" width="100%" height={20} sx={{ mt: 1 }} />
                <Skeleton variant="text" width="80%" height={20} />
                <Box display="flex" gap={1} mt={2}>
                  <Skeleton variant="rectangular" width={60} height={24} />
                  <Skeleton variant="rectangular" width={80} height={24} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Skeleton variant="text" width="50%" height={24} />
                <Skeleton variant="text" width="100%" height={20} sx={{ mt: 1 }} />
                <Skeleton variant="text" width="70%" height={20} />
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    )
  }

  if (!path) {
    return (
      <Box p={3}>
        <Alert severity="warning">
          Learning path not found.
        </Alert>
      </Box>
    )
  }

  const totalLessons = path.modules?.reduce((total, module) => 
    total + (module.lessons?.length || 0), 0) || 0
  
  const totalDuration = path.modules?.reduce((total, module) => 
    total + (module.lessons?.reduce((lessonTotal: number, lesson: any) => 
      lessonTotal + (lesson.estimated_duration || 0), 0) || 0), 0) || 0

  const estimatedHours = Math.round(totalDuration / 60 * 10) / 10

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Box display="flex" alignItems="center">
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={handleBack}
            sx={{ mr: 2 }}
          >
            Back to Learning Paths
          </Button>
          <Box>
            <Typography variant="h4" fontWeight="bold">
              {path.title}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Learning Path Details
            </Typography>
          </Box>
        </Box>
        
        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            startIcon={<EditIcon />}
            onClick={handleEdit}
          >
            Edit
          </Button>
          <IconButton onClick={handleMenuOpen}>
            <MoreVertIcon />
          </IconButton>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Main Content */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Box display="flex" gap={1} mb={3} flexWrap="wrap">
                <Chip
                  label={status}
                  color={getStatusColor(status) as any}
                  size="small"
                />
                <Chip
                  label={difficulty}
                  color={getDifficultyColor(difficulty) as any}
                  size="small"
                />
                <Chip
                  label={path.category}
                  variant="outlined"
                  size="small"
                />
              </Box>

              <Typography variant="body1" paragraph>
                {path.description}
              </Typography>

              {path.tags && path.tags.length > 0 && (
                <Box mb={3}>
                  <Typography variant="subtitle2" gutterBottom>
                    Tags:
                  </Typography>
                  <Box display="flex" gap={0.5} flexWrap="wrap">
                    {path.tags.map((tag, index) => (
                      <Chip
                        key={index}
                        label={tag}
                        size="small"
                        variant="outlined"
                      />
                    ))}
                  </Box>
                </Box>
              )}

              {path.objectives && path.objectives.length > 0 && (
                <Box mb={3}>
                  <Typography variant="h6" gutterBottom>
                    Learning Objectives
                  </Typography>
                  <List dense>
                    {path.objectives.map((objective, index) => (
                      <ListItem key={index} sx={{ py: 0.5, pl: 0 }}>
                        <ListItemIcon sx={{ minWidth: 24 }}>
                          <SchoolIcon fontSize="small" color="primary" />
                        </ListItemIcon>
                        <ListItemText primary={objective} />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}

              <Divider sx={{ my: 3 }} />

              {/* Modules */}
              <Typography variant="h6" gutterBottom>
                Course Structure
              </Typography>
              
              {path.modules && path.modules.length > 0 ? (
                path.modules.map((module, index) => (
                  <Card key={module.id} variant="outlined" sx={{ mb: 2 }}>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Module {index + 1}: {module.title}
                      </Typography>
                      
                      {module.description && (
                        <Typography variant="body2" color="text.secondary" mb={2}>
                          {module.description}
                        </Typography>
                      )}

                      {module.lessons && module.lessons.length > 0 ? (
                        <List dense>
                          {module.lessons.map((lesson: any, lessonIndex: number) => (
                            <ListItem key={lesson.id} sx={{ py: 0.5, pl: 2 }}>
                              <ListItemIcon sx={{ minWidth: 32 }}>
                                {getLessonIcon(lesson.type)}
                              </ListItemIcon>
                              <ListItemText
                                primary={
                                  <Box display="flex" alignItems="center" gap={1}>
                                    <Typography variant="body2">
                                      {lesson.title}
                                    </Typography>
                                    <Chip 
                                      label={lesson.type} 
                                      size="small" 
                                      variant="outlined"
                                      sx={{ fontSize: '0.7rem', height: 20 }}
                                    />
                                    {lesson.is_optional && (
                                      <Chip 
                                        label="Optional" 
                                        size="small" 
                                        color="secondary"
                                        variant="outlined"
                                        sx={{ fontSize: '0.7rem', height: 20 }}
                                      />
                                    )}
                                  </Box>
                                }
                                secondary={`${lesson.estimated_duration || 0} minutes`}
                              />
                            </ListItem>
                          ))}
                        </List>
                      ) : (
                        <Typography variant="body2" color="text.secondary" style={{ fontStyle: 'italic' }}>
                          No lessons in this module
                        </Typography>
                      )}
                    </CardContent>
                  </Card>
                ))
              ) : (
                <Alert severity="info">
                  No modules have been created for this learning path yet.
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Path Information
              </Typography>
              
              <Box display="flex" flexDirection="column" gap={2}>
                <Box display="flex" alignItems="center" gap={1}>
                  <ScheduleIcon color="action" fontSize="small" />
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      Duration
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {duration} weeks ({estimatedHours}h estimated)
                    </Typography>
                  </Box>
                </Box>

                <Box display="flex" alignItems="center" gap={1}>
                  <SchoolIcon color="action" fontSize="small" />
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      Content
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {path.modules?.length || 0} modules, {totalLessons} lessons
                    </Typography>
                  </Box>
                </Box>

                <Box display="flex" alignItems="center" gap={1}>
                  <PeopleIcon color="action" fontSize="small" />
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      Learners
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {path.metadata?.total_learners || 0} assigned
                    </Typography>
                  </Box>
                </Box>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Typography variant="body2" color="text.secondary">
                Created: {new Date(path.created_at).toLocaleDateString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Updated: {new Date(path.updated_at).toLocaleDateString()}
              </Typography>
              {path.published_at && (
                <Typography variant="body2" color="text.secondary">
                  Published: {new Date(path.published_at).toLocaleDateString()}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Context Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => { handleEdit(); handleMenuClose(); }}>
          <EditIcon sx={{ mr: 1 }} />
          Edit Path
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <CopyIcon sx={{ mr: 1 }} />
          Duplicate
        </MenuItem>
        {status === 'draft' && (
          <MenuItem onClick={handleMenuClose}>
            <PublishIcon sx={{ mr: 1 }} />
            Publish
          </MenuItem>
        )}
        <MenuItem onClick={handleMenuClose} sx={{ color: 'error.main' }}>
          <DeleteIcon sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>
    </Box>
  )
}
