-- Create content_categories table
CREATE TABLE content_categories (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_id INTEGER REFERENCES content_categories(id) ON DELETE CASCADE,
    color VARCHAR(7) DEFAULT '#1976d2', -- Hex color code
    icon VARCHAR(50),
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, name, parent_id)
);

-- Add category_id to content_library table
ALTER TABLE content_library 
ADD COLUMN category_id INTEGER REFERENCES content_categories(id) ON DELETE SET NULL;

-- Create content_tags table for better tag management
CREATE TABLE content_tags (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    name VA<PERSON>HAR(100) NOT NULL,
    color VARCHAR(7) DEFAULT '#757575',
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, name)
);

-- Create content_tag_assignments table for many-to-many relationship
CREATE TABLE content_tag_assignments (
    id SERIAL PRIMARY KEY,
    content_id INTEGER REFERENCES content_library(id) ON DELETE CASCADE,
    tag_id INTEGER REFERENCES content_tags(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(content_id, tag_id)
);

-- Create content_versions table for version control
CREATE TABLE content_versions (
    id SERIAL PRIMARY KEY,
    content_id INTEGER REFERENCES content_library(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    file_url TEXT NOT NULL,
    file_size INTEGER,
    metadata JSONB DEFAULT '{}',
    change_notes TEXT,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(content_id, version_number)
);

-- Create content_reviews table for approval workflow
CREATE TABLE content_reviews (
    id SERIAL PRIMARY KEY,
    content_id INTEGER REFERENCES content_library(id) ON DELETE CASCADE,
    reviewer_id UUID REFERENCES users(id),
    status VARCHAR(20) CHECK (status IN ('pending', 'approved', 'rejected', 'needs_changes')) DEFAULT 'pending',
    comments TEXT,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create content_usage_stats table for analytics
CREATE TABLE content_usage_stats (
    id SERIAL PRIMARY KEY,
    content_id INTEGER REFERENCES content_library(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id),
    action VARCHAR(50) NOT NULL, -- 'view', 'download', 'complete', etc.
    session_id VARCHAR(255),
    duration INTEGER, -- in seconds
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create smart_folders table for dynamic content organization
CREATE TABLE smart_folders (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    rules JSONB NOT NULL DEFAULT '[]',
    auto_update BOOLEAN DEFAULT true,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, name)
);

-- Add indexes for better performance
CREATE INDEX idx_content_categories_tenant_id ON content_categories(tenant_id);
CREATE INDEX idx_content_categories_parent_id ON content_categories(parent_id);
CREATE INDEX idx_content_library_category_id ON content_library(category_id);
CREATE INDEX idx_content_tags_tenant_id ON content_tags(tenant_id);
CREATE INDEX idx_content_tag_assignments_content_id ON content_tag_assignments(content_id);
CREATE INDEX idx_content_tag_assignments_tag_id ON content_tag_assignments(tag_id);
CREATE INDEX idx_content_versions_content_id ON content_versions(content_id);
CREATE INDEX idx_content_reviews_content_id ON content_reviews(content_id);
CREATE INDEX idx_content_reviews_reviewer_id ON content_reviews(reviewer_id);
CREATE INDEX idx_content_usage_stats_content_id ON content_usage_stats(content_id);
CREATE INDEX idx_content_usage_stats_user_id ON content_usage_stats(user_id);
CREATE INDEX idx_content_usage_stats_created_at ON content_usage_stats(created_at);
CREATE INDEX idx_smart_folders_tenant_id ON smart_folders(tenant_id);

-- Add updated_at triggers
CREATE TRIGGER update_content_categories_updated_at 
    BEFORE UPDATE ON content_categories 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_smart_folders_updated_at 
    BEFORE UPDATE ON smart_folders 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS on new tables
ALTER TABLE content_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_tag_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_usage_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE smart_folders ENABLE ROW LEVEL SECURITY;

-- RLS Policies for content_categories
CREATE POLICY "Users can view categories in their tenant" ON content_categories
    FOR SELECT USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Users can manage categories in their tenant" ON content_categories
    FOR ALL USING (
        tenant_id = get_user_tenant_id() AND
        user_has_permission('manage_content')
    );

-- RLS Policies for content_tags
CREATE POLICY "Users can view tags in their tenant" ON content_tags
    FOR SELECT USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Users can manage tags in their tenant" ON content_tags
    FOR ALL USING (
        tenant_id = get_user_tenant_id() AND
        user_has_permission('manage_content')
    );

-- RLS Policies for content_tag_assignments
CREATE POLICY "Users can view tag assignments for content in their tenant" ON content_tag_assignments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM content_library cl 
            WHERE cl.id = content_tag_assignments.content_id 
            AND cl.tenant_id = get_user_tenant_id()
        )
    );

CREATE POLICY "Users can manage tag assignments for content in their tenant" ON content_tag_assignments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM content_library cl 
            WHERE cl.id = content_tag_assignments.content_id 
            AND cl.tenant_id = get_user_tenant_id()
            AND user_has_permission('manage_content')
        )
    );

-- RLS Policies for content_versions
CREATE POLICY "Users can view versions for content in their tenant" ON content_versions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM content_library cl 
            WHERE cl.id = content_versions.content_id 
            AND cl.tenant_id = get_user_tenant_id()
        )
    );

CREATE POLICY "Users can manage versions for content in their tenant" ON content_versions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM content_library cl 
            WHERE cl.id = content_versions.content_id 
            AND cl.tenant_id = get_user_tenant_id()
            AND (cl.uploaded_by = auth.uid() OR user_has_permission('manage_content'))
        )
    );

-- RLS Policies for content_reviews
CREATE POLICY "Users can view reviews for content in their tenant" ON content_reviews
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM content_library cl 
            WHERE cl.id = content_reviews.content_id 
            AND cl.tenant_id = get_user_tenant_id()
        )
    );

CREATE POLICY "Users can manage reviews for content in their tenant" ON content_reviews
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM content_library cl 
            WHERE cl.id = content_reviews.content_id 
            AND cl.tenant_id = get_user_tenant_id()
            AND (reviewer_id = auth.uid() OR user_has_permission('manage_content'))
        )
    );

-- RLS Policies for content_usage_stats
CREATE POLICY "Users can view usage stats for content in their tenant" ON content_usage_stats
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM content_library cl 
            WHERE cl.id = content_usage_stats.content_id 
            AND cl.tenant_id = get_user_tenant_id()
        )
    );

CREATE POLICY "Users can insert their own usage stats" ON content_usage_stats
    FOR INSERT WITH CHECK (
        user_id = auth.uid() AND
        EXISTS (
            SELECT 1 FROM content_library cl 
            WHERE cl.id = content_usage_stats.content_id 
            AND cl.tenant_id = get_user_tenant_id()
        )
    );

-- RLS Policies for smart_folders
CREATE POLICY "Users can view smart folders in their tenant" ON smart_folders
    FOR SELECT USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Users can manage smart folders in their tenant" ON smart_folders
    FOR ALL USING (
        tenant_id = get_user_tenant_id() AND
        user_has_permission('manage_content')
    );

-- Function to get content with category and tag information
CREATE OR REPLACE FUNCTION get_content_with_metadata(
    tenant_id_param INTEGER,
    limit_param INTEGER DEFAULT 20,
    offset_param INTEGER DEFAULT 0
)
RETURNS TABLE (
    id INTEGER,
    title VARCHAR(255),
    description TEXT,
    type VARCHAR(20),
    file_url TEXT,
    file_size INTEGER,
    mime_type VARCHAR(100),
    duration INTEGER,
    metadata JSONB,
    uploaded_by UUID,
    is_public BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    category_name VARCHAR(255),
    category_color VARCHAR(7),
    tags TEXT[]
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cl.id,
        cl.title,
        cl.description,
        cl.type,
        cl.file_url,
        cl.file_size,
        cl.mime_type,
        cl.duration,
        cl.metadata,
        cl.uploaded_by,
        cl.is_public,
        cl.created_at,
        cl.updated_at,
        cc.name as category_name,
        cc.color as category_color,
        COALESCE(
            ARRAY_AGG(ct.name) FILTER (WHERE ct.name IS NOT NULL),
            ARRAY[]::TEXT[]
        ) as tags
    FROM content_library cl
    LEFT JOIN content_categories cc ON cl.category_id = cc.id
    LEFT JOIN content_tag_assignments cta ON cl.id = cta.content_id
    LEFT JOIN content_tags ct ON cta.tag_id = ct.id
    WHERE cl.tenant_id = tenant_id_param
    GROUP BY cl.id, cc.name, cc.color
    ORDER BY cl.created_at DESC
    LIMIT limit_param
    OFFSET offset_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update tag usage counts
CREATE OR REPLACE FUNCTION update_tag_usage_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE content_tags 
        SET usage_count = usage_count + 1 
        WHERE id = NEW.tag_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE content_tags 
        SET usage_count = GREATEST(usage_count - 1, 0) 
        WHERE id = OLD.tag_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update tag usage counts
CREATE TRIGGER update_tag_usage_count_trigger
    AFTER INSERT OR DELETE ON content_tag_assignments
    FOR EACH ROW EXECUTE FUNCTION update_tag_usage_count();

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_content_with_metadata(INTEGER, INTEGER, INTEGER) TO authenticated;

-- Insert some default categories
INSERT INTO content_categories (tenant_id, name, description, color, sort_order) VALUES 
(1, 'Training Materials', 'General training and educational content', '#1976d2', 1),
(1, 'Documentation', 'Technical documentation and guides', '#388e3c', 2),
(1, 'Presentations', 'Slide decks and presentation materials', '#f57c00', 3),
(1, 'Videos', 'Video content and tutorials', '#d32f2f', 4),
(1, 'Assessments', 'Quizzes and evaluation materials', '#7b1fa2', 5)
ON CONFLICT (tenant_id, name, parent_id) DO NOTHING;
