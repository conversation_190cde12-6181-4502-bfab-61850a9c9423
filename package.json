{"name": "zeni<PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:e2e": "playwright test", "type-check": "tsc --noEmit"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@hookform/resolvers": "^3.9.0", "@mui/icons-material": "^5.16.7", "@mui/lab": "^5.0.0-alpha.176", "@mui/material": "^5.16.7", "@mui/x-data-grid": "^7.22.2", "@mui/x-date-pickers": "^8.5.1", "@supabase/supabase-js": "^2.45.6", "@tanstack/react-query": "^5.59.13", "@tanstack/react-query-devtools": "^5.80.6", "@tanstack/react-table": "^8.20.5", "@tanstack/react-virtual": "^3.13.10", "@tiptap/extension-image": "^2.6.6", "@tiptap/extension-link": "^2.6.6", "@tiptap/extension-youtube": "^2.6.6", "@tiptap/react": "^2.6.6", "@tiptap/starter-kit": "^2.6.6", "chart.js": "^4.4.5", "chartjs-adapter-date-fns": "^3.0.0", "critters": "^0.0.24", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "framer-motion": "^11.11.9", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "next": "15.3.3", "next-intl": "^3.23.2", "openai": "^4.68.1", "papaparse": "^5.4.1", "plotly.js": "^2.35.2", "react": "^18.3.1", "react-big-calendar": "^1.13.4", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-grid-layout": "^1.4.4", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.4.1", "react-markdown": "^10.0.1", "react-pdf": "^9.1.1", "react-player": "^2.16.0", "react-plotly.js": "^2.6.0", "xlsx": "^0.18.5", "zod": "^3.23.8", "zustand": "^5.0.0-rc.2"}, "devDependencies": {"@axe-core/react": "^4.10.0", "@playwright/test": "^1.46.1", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@types/node": "^20.16.5", "@types/papaparse": "^5.3.14", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.5.0", "@typescript-eslint/parser": "^8.5.0", "eslint": "^8.57.1", "eslint-config-next": "15.3.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.3.3", "supabase": "^2.24.3", "typescript": "^5.6.2"}}