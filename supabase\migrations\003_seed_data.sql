-- Insert default tenant
INSERT INTO tenants (name, slug, settings) VALUES 
('Demo Organization', 'demo-org', '{"theme": "light", "features": ["ai_chat", "gamification", "reports"]}');

-- Insert default roles
INSERT INTO roles (tenant_id, name, permissions, is_default) VALUES 
(1, 'Super Admin', ARRAY['admin'], false),
(1, 'Admin', ARRAY['view_dashboard', 'view_learning_paths', 'create_learning_paths', 'edit_learning_paths', 'delete_learning_paths', 'assign_learning_paths', 'view_learners', 'create_learners', 'edit_learners', 'view_content', 'upload_content', 'edit_content', 'view_reports', 'export_reports', 'view_settings', 'edit_settings'], false),
(1, 'Instructor', ARRAY['view_dashboard', 'view_learning_paths', 'create_learning_paths', 'edit_learning_paths', 'assign_learning_paths', 'view_learners', 'view_content', 'upload_content', 'view_reports'], false),
(1, 'Learner', ARRAY['view_dashboard'], true);

-- Insert default competencies
INSERT INTO competencies (tenant_id, name, description, category, level_definitions) VALUES 
(1, 'JavaScript Programming', 'Proficiency in JavaScript programming language', 'Technical', '{"1": "Basic syntax and concepts", "2": "Functions and objects", "3": "Advanced concepts and frameworks", "4": "Expert level with optimization", "5": "Thought leader and mentor"}'),
(1, 'React Development', 'Building user interfaces with React', 'Technical', '{"1": "Basic components", "2": "State and props", "3": "Hooks and context", "4": "Performance optimization", "5": "Architecture and patterns"}'),
(1, 'Project Management', 'Managing projects and teams effectively', 'Soft Skills', '{"1": "Basic planning", "2": "Team coordination", "3": "Risk management", "4": "Strategic planning", "5": "Organizational leadership"}'),
(1, 'Communication', 'Effective verbal and written communication', 'Soft Skills', '{"1": "Basic communication", "2": "Clear presentation", "3": "Persuasive communication", "4": "Leadership communication", "5": "Inspirational speaking"}');

-- Insert sample learning paths
INSERT INTO learning_paths (tenant_id, title, description, objectives, prerequisites, difficulty_level, estimated_duration, is_live, is_featured, category, tags, created_by) VALUES
(1, 'JavaScript Fundamentals', 'Learn the basics of JavaScript programming', ARRAY['Understand variables and data types', 'Master functions and scope', 'Work with objects and arrays'], ARRAY['Basic computer literacy'], 'beginner', 480, true, true, 'Programming', ARRAY['javascript', 'programming', 'web-development'], NULL),
(1, 'React Development Bootcamp', 'Comprehensive React.js development course', ARRAY['Build React components', 'Manage state effectively', 'Create full applications'], ARRAY['JavaScript Fundamentals'], 'intermediate', 720, true, true, 'Programming', ARRAY['react', 'javascript', 'frontend'], NULL),
(1, 'Advanced Python Programming', 'Deep dive into Python for experienced developers', ARRAY['Master advanced Python concepts', 'Build scalable applications', 'Optimize performance'], ARRAY['Basic Python knowledge'], 'advanced', 600, true, false, 'Programming', ARRAY['python', 'advanced', 'backend'], NULL),
(1, 'Project Management Essentials', 'Core project management skills and methodologies', ARRAY['Plan and execute projects', 'Manage teams effectively', 'Handle project risks'], ARRAY[]::TEXT[], 'beginner', 360, true, true, 'Management', ARRAY['project-management', 'leadership', 'soft-skills'], NULL);

-- Insert modules for JavaScript Fundamentals
INSERT INTO modules (path_id, title, description, order_index) VALUES 
(1, 'Introduction to JavaScript', 'Getting started with JavaScript basics', 1),
(1, 'Variables and Data Types', 'Understanding JavaScript data types and variables', 2),
(1, 'Functions and Scope', 'Working with functions and understanding scope', 3),
(1, 'Objects and Arrays', 'Manipulating objects and arrays in JavaScript', 4),
(1, 'DOM Manipulation', 'Interacting with the Document Object Model', 5);

-- Insert lessons for Introduction to JavaScript module
INSERT INTO lessons (module_id, title, description, type, content_text, order_index, estimated_duration) VALUES 
(1, 'What is JavaScript?', 'Introduction to JavaScript and its uses', 'text', 'JavaScript is a versatile programming language primarily used for web development. It allows you to create interactive and dynamic web pages...', 1, 15),
(1, 'Setting up Development Environment', 'How to set up your coding environment', 'video', NULL, 2, 20),
(1, 'Your First JavaScript Program', 'Writing and running your first JavaScript code', 'interactive', NULL, 3, 25),
(1, 'JavaScript Basics Quiz', 'Test your understanding of JavaScript basics', 'quiz', NULL, 4, 10);

-- Insert lessons for Variables and Data Types module
INSERT INTO lessons (module_id, title, description, type, content_text, order_index, estimated_duration, passing_score) VALUES 
(2, 'Understanding Variables', 'Learn about var, let, and const', 'text', 'Variables are containers for storing data values. In JavaScript, you can declare variables using var, let, or const keywords...', 1, 20, NULL),
(2, 'Data Types Overview', 'Exploring JavaScript data types', 'video', NULL, 2, 25, NULL),
(2, 'Working with Strings', 'String manipulation and methods', 'interactive', NULL, 3, 30, NULL),
(2, 'Numbers and Math Operations', 'Numeric operations in JavaScript', 'text', 'JavaScript has only one type of number. Numbers can be written with or without decimals...', 4, 20, NULL),
(2, 'Variables and Data Types Quiz', 'Assessment of variables and data types knowledge', 'quiz', NULL, 5, 15, 80);

-- Insert sample content library items
INSERT INTO content_library (tenant_id, title, description, type, file_url, tags, uploaded_by, is_public) VALUES 
(1, 'JavaScript Cheat Sheet', 'Quick reference for JavaScript syntax', 'pdf', '/content/javascript-cheat-sheet.pdf', ARRAY['javascript', 'reference'], NULL, true),
(1, 'React Component Patterns', 'Common patterns for React components', 'video', '/content/react-patterns.mp4', ARRAY['react', 'patterns'], NULL, true),
(1, 'Project Planning Template', 'Template for project planning documents', 'document', '/content/project-template.docx', ARRAY['project-management', 'template'], NULL, true);

-- Insert path competencies mapping
INSERT INTO path_competencies (path_id, competency_id, level_gained) VALUES 
(1, 1, 2), -- JavaScript Fundamentals -> JavaScript Programming (Level 2)
(2, 1, 1), -- React Bootcamp -> JavaScript Programming (Level 1 additional)
(2, 2, 3), -- React Bootcamp -> React Development (Level 3)
(4, 3, 2), -- Project Management -> Project Management (Level 2)
(4, 4, 1); -- Project Management -> Communication (Level 1)

-- Create a function to handle new user registration
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert user into users table with default role
    INSERT INTO users (id, tenant_id, role_id, email, full_name)
    VALUES (
        NEW.id,
        1, -- Default tenant for demo
        (SELECT id FROM roles WHERE tenant_id = 1 AND is_default = true LIMIT 1),
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1))
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user registration
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Create function to update user last login
CREATE OR REPLACE FUNCTION update_user_last_login()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE users 
    SET last_login_at = NOW()
    WHERE id = NEW.user_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for user login tracking
CREATE TRIGGER on_auth_session_created
    AFTER INSERT ON auth.sessions
    FOR EACH ROW EXECUTE FUNCTION update_user_last_login();

-- Create function to log user actions
CREATE OR REPLACE FUNCTION log_user_action(
    action_name TEXT,
    resource_type TEXT,
    resource_id TEXT DEFAULT NULL,
    old_values JSONB DEFAULT NULL,
    new_values JSONB DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO audit_logs (
        tenant_id,
        user_id,
        action,
        resource_type,
        resource_id,
        old_values,
        new_values,
        ip_address,
        created_at
    ) VALUES (
        get_user_tenant_id(),
        auth.uid(),
        action_name,
        resource_type,
        resource_id,
        old_values,
        new_values,
        inet_client_addr(),
        NOW()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to send notification
CREATE OR REPLACE FUNCTION send_notification(
    user_id UUID,
    title TEXT,
    message TEXT,
    notification_type TEXT DEFAULT 'info',
    action_url TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO notifications (
        tenant_id,
        user_id,
        title,
        message,
        type,
        action_url
    ) VALUES (
        get_user_tenant_id(),
        user_id,
        title,
        message,
        notification_type,
        action_url
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
