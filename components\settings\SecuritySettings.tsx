'use client'

import React from 'react'
import {
  Box,
  Card,
  CardContent,
  <PERSON>po<PERSON>,
  Alert,
  Grid,
  Switch,
  FormControlLabel,
  TextField,
  Slider,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton
} from '@mui/material'
import {
  Security as SecurityIcon,
  Shield as ShieldIcon,
  Key as KeyIcon,
  Timer as TimerIcon,
  Delete as DeleteIcon,
  Add as AddIcon
} from '@mui/icons-material'
import { motion } from 'framer-motion'

export default function SecuritySettings() {
  return (
    <Grid container spacing={3}>
      {/* Authentication Methods */}
      <Grid item xs={12}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={3}>
                <KeyIcon sx={{ mr: 1 }} />
                <Typography variant="h6" fontWeight="bold">
                  Authentication Methods
                </Typography>
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Email/Password Login"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={<Switch />}
                    label="Google SSO"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={<Switch />}
                    label="Microsoft Azure AD"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={<Switch />}
                    label="SAML 2.0"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </motion.div>
      </Grid>

      {/* Multi-Factor Authentication */}
      <Grid item xs={12}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={3}>
                <ShieldIcon sx={{ mr: 1 }} />
                <Typography variant="h6" fontWeight="bold">
                  Multi-Factor Authentication
                </Typography>
                <Chip label="Recommended" color="primary" size="small" sx={{ ml: 2 }} />
              </Box>

              <FormControlLabel
                control={<Switch />}
                label="Require MFA for all users"
              />
              
              <Alert severity="info" sx={{ mt: 2 }}>
                When enabled, all users will be required to set up two-factor authentication on their next login.
              </Alert>
            </CardContent>
          </Card>
        </motion.div>
      </Grid>

      {/* Session Management */}
      <Grid item xs={12}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={3}>
                <TimerIcon sx={{ mr: 1 }} />
                <Typography variant="h6" fontWeight="bold">
                  Session Management
                </Typography>
              </Box>

              <Box mb={3}>
                <Typography gutterBottom>
                  Session Timeout: 8 hours
                </Typography>
                <Slider
                  defaultValue={8}
                  min={1}
                  max={24}
                  step={1}
                  marks
                  valueLabelDisplay="auto"
                  valueLabelFormat={(value) => `${value}h`}
                />
              </Box>

              <FormControlLabel
                control={<Switch defaultChecked />}
                label="Remember me option"
              />
            </CardContent>
          </Card>
        </motion.div>
      </Grid>

      {/* IP Whitelisting */}
      <Grid item xs={12}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={3}>
                <SecurityIcon sx={{ mr: 1 }} />
                <Typography variant="h6" fontWeight="bold">
                  IP Access Control
                </Typography>
              </Box>

              <TextField
                fullWidth
                label="Add IP Address or Range"
                placeholder="***********/24 or ***********"
                sx={{ mb: 2 }}
              />

              <List>
                <ListItem>
                  <ListItemText
                    primary="***********/24"
                    secondary="Office Network"
                  />
                  <ListItemSecondaryAction>
                    <IconButton edge="end">
                      <DeleteIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </motion.div>
      </Grid>

      {/* Data Retention */}
      <Grid item xs={12}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Data Retention Policies
              </Typography>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Log Retention (days)"
                    type="number"
                    defaultValue={90}
                    helperText="How long to keep audit logs"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="User Data Retention (years)"
                    type="number"
                    defaultValue={7}
                    helperText="GDPR compliance requirement"
                  />
                </Grid>
              </Grid>

              <Alert severity="warning" sx={{ mt: 2 }}>
                Changes to data retention policies will affect compliance reporting. Consult your legal team before making changes.
              </Alert>
            </CardContent>
          </Card>
        </motion.div>
      </Grid>
    </Grid>
  )
}
