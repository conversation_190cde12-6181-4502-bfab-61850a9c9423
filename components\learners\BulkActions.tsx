'use client'

import React from 'react'
import {
  Card,
  CardContent,
  Box,
  Typography,
  Button,
  Chip,
  Divider,
  useTheme,
  alpha
} from '@mui/material'
import {
  Assignment as AssignIcon,
  Message as MessageIcon,
  Group as GroupIcon,
  Block as SuspendIcon,
  CheckCircle as ActivateIcon,
  Clear as ClearIcon
} from '@mui/icons-material'
import { motion } from 'framer-motion'

interface BulkActionsProps {
  selectedCount: number
  onAssign: () => void
  onMessage: () => void
  onAddToGroup?: () => void
  onSuspend?: () => void
  onActivate?: () => void
  onClearSelection: () => void
}

const BulkActions: React.FC<BulkActionsProps> = ({
  selectedCount,
  onAssign,
  onMessage,
  onAddToGroup,
  onSuspend,
  onActivate,
  onClearSelection
}) => {
  const theme = useTheme()

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <Card
        sx={{
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.primary.main, 0.05)} 100%)`,
          border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
        }}
      >
        <CardContent sx={{ py: 2 }}>
          <Box display="flex" alignItems="center" justifyContent="space-between" flexWrap="wrap" gap={2}>
            <Box display="flex" alignItems="center" gap={2}>
              <Chip
                label={`${selectedCount} selected`}
                color="primary"
                variant="filled"
                size="medium"
              />
              <Typography variant="body2" color="text.secondary">
                Choose an action to apply to selected learners
              </Typography>
            </Box>

            <Box display="flex" alignItems="center" gap={1} flexWrap="wrap">
              <Button
                variant="outlined"
                size="small"
                startIcon={<AssignIcon />}
                onClick={onAssign}
                sx={{ minWidth: 'auto' }}
              >
                Assign Path
              </Button>

              <Button
                variant="outlined"
                size="small"
                startIcon={<MessageIcon />}
                onClick={onMessage}
                sx={{ minWidth: 'auto' }}
              >
                Send Message
              </Button>

              {onAddToGroup && (
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<GroupIcon />}
                  onClick={onAddToGroup}
                  sx={{ minWidth: 'auto' }}
                >
                  Add to Group
                </Button>
              )}

              <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

              {onSuspend && (
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<SuspendIcon />}
                  onClick={onSuspend}
                  color="warning"
                  sx={{ minWidth: 'auto' }}
                >
                  Suspend
                </Button>
              )}

              {onActivate && (
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<ActivateIcon />}
                  onClick={onActivate}
                  color="success"
                  sx={{ minWidth: 'auto' }}
                >
                  Activate
                </Button>
              )}

              <Button
                variant="text"
                size="small"
                startIcon={<ClearIcon />}
                onClick={onClearSelection}
                color="inherit"
                sx={{ minWidth: 'auto' }}
              >
                Clear
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  )
}

export default BulkActions
