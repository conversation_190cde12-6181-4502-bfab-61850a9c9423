'use client'

import React, { useState } from 'react'
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardActions,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Alert
} from '@mui/material'
import {
  Add as AddIcon,
  ExpandMore as ExpandMoreIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  DragIndicator as DragIcon,
  PlayArrow as PlayIcon,
  Quiz as QuizIcon,
  PictureAsPdf as PdfIcon,
  Link as LinkIcon,
  Code as CodeIcon
} from '@mui/icons-material'
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core'
import { arrayMove, SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'

import { PathCreationForm, LessonType } from '@/lib/types/learning-paths'
import EnhancedLessonCreator from './EnhancedLessonCreator'
import RichTextEditor from '../common/RichTextEditor'

interface PathBuilderProps {
  modules: any[]
  onChange: (modules: any[]) => void
  pathData: Partial<PathCreationForm>
}

interface SortableModuleProps {
  module: any
  onEdit: () => void
  onDelete: () => void
  onAddLesson: () => void
  onEditLesson: (lesson: any) => void
  onDeleteLesson: (lessonId: string) => void
}

const SortableModule: React.FC<SortableModuleProps> = ({
  module,
  onEdit,
  onDelete,
  onAddLesson,
  onEditLesson,
  onDeleteLesson
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id: module.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  const getLessonIcon = (type: LessonType) => {
    switch (type) {
      case 'video':
        return <PlayIcon />
      case 'pdf':
        return <PdfIcon />
      case 'quiz':
        return <QuizIcon />
      case 'link':
        return <LinkIcon />
      case 'simulation':
        return <CodeIcon />
      default:
        return <PlayIcon />
    }
  }

  const totalDuration = module.lessons?.reduce((total: number, lesson: any) => 
    total + (lesson.estimated_duration || 0), 0) || 0

  return (
    <div ref={setNodeRef} style={style}>
      <Accordion defaultExpanded>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          sx={{ 
            '& .MuiAccordionSummary-content': { 
              alignItems: 'center',
              gap: 2
            }
          }}
        >
          <IconButton
            size="small"
            {...attributes}
            {...listeners}
            sx={{ cursor: 'grab' }}
          >
            <DragIcon />
          </IconButton>
          
          <Box flexGrow={1}>
            <Typography variant="h6" fontWeight="bold">
              {module.title || 'Untitled Module'}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {module.lessons?.length || 0} lessons • {Math.round(totalDuration)} minutes
            </Typography>
          </Box>

          <Box display="flex" gap={1}>
            <IconButton size="small" onClick={onEdit}>
              <EditIcon />
            </IconButton>
            <IconButton size="small" onClick={onDelete} color="error">
              <DeleteIcon />
            </IconButton>
          </Box>
        </AccordionSummary>

        <AccordionDetails>
          <Box>
            {module.description && (
              <Typography variant="body2" color="text.secondary" mb={2}>
                {module.description}
              </Typography>
            )}

            {/* Lessons */}
            <Box mb={2}>
              {module.lessons && module.lessons.length > 0 ? (
                module.lessons.map((lesson: any, index: number) => (
                  <Card key={lesson.id} variant="outlined" sx={{ mb: 1 }}>
                    <CardContent sx={{ py: 1.5 }}>
                      <Box display="flex" alignItems="center" gap={2}>
                        <Box color="text.secondary">
                          {getLessonIcon(lesson.type)}
                        </Box>
                        
                        <Box flexGrow={1}>
                          <Typography variant="body2" fontWeight="medium">
                            {lesson.title || 'Untitled Lesson'}
                          </Typography>
                          <Box display="flex" gap={1} alignItems="center">
                            <Chip 
                              label={lesson.type} 
                              size="small" 
                              variant="outlined" 
                            />
                            <Typography variant="caption" color="text.secondary">
                              {lesson.estimated_duration || 0} min
                            </Typography>
                            {lesson.is_optional && (
                              <Chip 
                                label="Optional" 
                                size="small" 
                                color="secondary" 
                                variant="outlined" 
                              />
                            )}
                          </Box>
                        </Box>

                        <Box display="flex" gap={1}>
                          <IconButton 
                            size="small" 
                            onClick={() => onEditLesson(lesson)}
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton 
                            size="small" 
                            onClick={() => onDeleteLesson(lesson.id)}
                            color="error"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <Alert severity="info" sx={{ mb: 2 }}>
                  No lessons added yet. Click "Add Lesson" to get started.
                </Alert>
              )}
            </Box>

            <Button
              startIcon={<AddIcon />}
              onClick={onAddLesson}
              variant="outlined"
              size="small"
            >
              Add Lesson
            </Button>
          </Box>
        </AccordionDetails>
      </Accordion>
    </div>
  )
}

const PathBuilder: React.FC<PathBuilderProps> = ({ modules, onChange, pathData }) => {
  const [moduleDialogOpen, setModuleDialogOpen] = useState(false)
  const [enhancedLessonDialogOpen, setEnhancedLessonDialogOpen] = useState(false)
  const [editingModule, setEditingModule] = useState<any>(null)
  const [editingLesson, setEditingLesson] = useState<any>(null)
  const [currentModuleId, setCurrentModuleId] = useState<string>('')

  const [moduleForm, setModuleForm] = useState({
    title: '',
    description: '',
    is_optional: false,
    estimated_duration: 60
  })

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  const handleDragEnd = (event: any) => {
    const { active, over } = event

    if (active.id !== over.id) {
      const oldIndex = modules.findIndex(module => module.id === active.id)
      const newIndex = modules.findIndex(module => module.id === over.id)
      
      onChange(arrayMove(modules, oldIndex, newIndex))
    }
  }

  const handleAddModule = () => {
    setEditingModule(null)
    setModuleForm({
      title: '',
      description: '',
      is_optional: false,
      estimated_duration: 60
    })
    setModuleDialogOpen(true)
  }

  const handleEditModule = (module: any) => {
    setEditingModule(module)
    setModuleForm({
      title: module.title || '',
      description: module.description || '',
      is_optional: module.is_optional || false,
      estimated_duration: module.estimated_duration || 60
    })
    setModuleDialogOpen(true)
  }

  const handleSaveModule = () => {
    if (editingModule) {
      // Update existing module
      const updatedModules = modules.map(module =>
        module.id === editingModule.id
          ? { ...module, ...moduleForm }
          : module
      )
      onChange(updatedModules)
    } else {
      // Add new module
      const newModule = {
        id: `module-${Date.now()}`,
        ...moduleForm,
        order: modules.length,
        lessons: [],
        dependencies: []
      }
      onChange([...modules, newModule])
    }
    setModuleDialogOpen(false)
  }

  const handleDeleteModule = (moduleId: string) => {
    onChange(modules.filter(module => module.id !== moduleId))
  }

  const handleAddLesson = (moduleId: string) => {
    setCurrentModuleId(moduleId)
    setEditingLesson(null)
    setEnhancedLessonDialogOpen(true)
  }

  const handleEditLesson = (lesson: any) => {
    setEditingLesson(lesson)
    setCurrentModuleId(
      modules.find(m => m.lessons?.some((l: any) => l.id === lesson.id))?.id || ''
    )
    setEnhancedLessonDialogOpen(true)
  }

  const handleSaveEnhancedLesson = (lessonContent: any) => {
    const updatedModules = modules.map(module => {
      if (module.id === currentModuleId) {
        const lessons = module.lessons || []

        if (editingLesson) {
          // Update existing lesson
          return {
            ...module,
            lessons: lessons.map((lesson: any) =>
              lesson.id === editingLesson.id
                ? {
                    ...lesson,
                    title: lessonContent.title,
                    description: lessonContent.description,
                    type: lessonContent.type,
                    content_url: lessonContent.uploadResult?.url || lessonContent.url,
                    content_metadata: {
                      ...lessonContent.metadata,
                      preview: lessonContent.preview,
                      storagePath: lessonContent.uploadResult?.path,
                      uploadResult: lessonContent.uploadResult,
                      aiGenerated: lessonContent.metadata?.aiGenerated || false
                    },
                    estimated_duration: lessonContent.metadata?.estimatedDuration || 30
                  }
                : lesson
            )
          }
        } else {
          // Add new lesson
          const newLesson = {
            id: `lesson-${Date.now()}`,
            title: lessonContent.title,
            description: lessonContent.description,
            type: lessonContent.type,
            content_url: lessonContent.uploadResult?.url || lessonContent.url,
            content_metadata: {
              ...lessonContent.metadata,
              preview: lessonContent.preview,
              storagePath: lessonContent.uploadResult?.path,
              uploadResult: lessonContent.uploadResult,
              aiGenerated: lessonContent.metadata?.aiGenerated || false
            },
            estimated_duration: lessonContent.metadata?.estimatedDuration || 30,
            order: lessons.length,
            is_mandatory: true,
            dependencies: [],
            interactions: []
          }
          return {
            ...module,
            lessons: [...lessons, newLesson]
          }
        }
      }
      return module
    })

    onChange(updatedModules)
    setEnhancedLessonDialogOpen(false)
  }

  const handleDeleteLesson = (moduleId: string, lessonId: string) => {
    const updatedModules = modules.map(module => {
      if (module.id === moduleId) {
        return {
          ...module,
          lessons: module.lessons?.filter((lesson: any) => lesson.id !== lessonId) || []
        }
      }
      return module
    })
    onChange(updatedModules)
  }

  return (
    <Box>
      {/* Add Module Button */}
      <Box mb={3}>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddModule}
          size="large"
        >
          Add Module
        </Button>
      </Box>

      {/* Modules List */}
      {modules.length > 0 ? (
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext items={modules.map(m => m.id)} strategy={verticalListSortingStrategy}>
            <Box display="flex" flexDirection="column" gap={2}>
              {modules.map((module) => (
                <SortableModule
                  key={module.id}
                  module={module}
                  onEdit={() => handleEditModule(module)}
                  onDelete={() => handleDeleteModule(module.id)}
                  onAddLesson={() => handleAddLesson(module.id)}
                  onEditLesson={handleEditLesson}
                  onDeleteLesson={(lessonId) => handleDeleteLesson(module.id, lessonId)}
                />
              ))}
            </Box>
          </SortableContext>
        </DndContext>
      ) : (
        <Alert severity="info">
          No modules created yet. Click "Add Module" to start building your learning path structure.
        </Alert>
      )}

      {/* Module Dialog */}
      <Dialog open={moduleDialogOpen} onClose={() => setModuleDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingModule ? 'Edit Module' : 'Add New Module'}
        </DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" gap={2} mt={1}>
            <TextField
              fullWidth
              label="Module Title"
              value={moduleForm.title}
              onChange={(e) => setModuleForm(prev => ({ ...prev, title: e.target.value }))}
              required
            />
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Description
              </Typography>
              <RichTextEditor
                content={moduleForm.description}
                onChange={(content) => setModuleForm(prev => ({ ...prev, description: content }))}
                placeholder="Enter module description..."
                minHeight={120}
              />
            </Box>
            <TextField
              fullWidth
              type="number"
              label="Estimated Duration (minutes)"
              value={moduleForm.estimated_duration}
              onChange={(e) => setModuleForm(prev => ({ ...prev, estimated_duration: parseInt(e.target.value) || 0 }))}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setModuleDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleSaveModule} 
            variant="contained"
            disabled={!moduleForm.title.trim()}
          >
            {editingModule ? 'Update' : 'Add'} Module
          </Button>
        </DialogActions>
      </Dialog>

      {/* Enhanced Lesson Creator */}
      <EnhancedLessonCreator
        open={enhancedLessonDialogOpen}
        onClose={() => setEnhancedLessonDialogOpen(false)}
        onSave={handleSaveEnhancedLesson}
        moduleId={currentModuleId}
        editingLesson={editingLesson}
      />
    </Box>
  )
}

export default PathBuilder
