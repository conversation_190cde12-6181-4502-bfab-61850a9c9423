'use client'

import { useState } from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  IconButton,
  Chip,
  Avatar,
  AvatarGroup,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  ListItemSecondaryAction,
  Autocomplete,
  useTheme,
  Divider,
  Menu,
} from '@mui/material'
import {
  Share as ShareIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Download as DownloadIcon,
  Link as LinkIcon,
  People as PeopleIcon,
  Security as SecurityIcon,
  AccessTime as TimeIcon,
  MoreVert as MoreVertIcon,
  Public as PublicIcon,
  Lock as LockIcon,
  Group as GroupIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'

interface SharedReport {
  id: number
  name: string
  description: string
  type: string
  sharedBy: string
  sharedWith: SharedUser[]
  permissions: 'view' | 'edit' | 'admin'
  visibility: 'private' | 'team' | 'organization' | 'public'
  shareLink?: string
  expiresAt?: string
  createdAt: string
  lastAccessed: string
  accessCount: number
  isOwner: boolean
}

interface SharedUser {
  id: string
  name: string
  email: string
  avatar?: string
  permissions: 'view' | 'edit' | 'admin'
  lastAccessed?: string
}

export default function SharedReports() {
  const theme = useTheme()
  const [sharedReports, setSharedReports] = useState<SharedReport[]>([
    {
      id: 1,
      name: 'Q4 Performance Dashboard',
      description: 'Comprehensive Q4 performance metrics and analytics',
      type: 'Dashboard',
      sharedBy: 'John Doe',
      sharedWith: [
        { id: '1', name: 'Jane Smith', email: '<EMAIL>', permissions: 'view' },
        { id: '2', name: 'Mike Johnson', email: '<EMAIL>', permissions: 'edit' },
        { id: '3', name: 'Sarah Wilson', email: '<EMAIL>', permissions: 'view' },
      ],
      permissions: 'admin',
      visibility: 'team',
      shareLink: 'https://app.zenithlearn.ai/shared/q4-dashboard-abc123',
      expiresAt: '2024-03-15T23:59:59Z',
      createdAt: '2024-01-10T10:00:00Z',
      lastAccessed: '2024-01-15T14:30:00Z',
      accessCount: 47,
      isOwner: true,
    },
    {
      id: 2,
      name: 'Engagement Trends Report',
      description: 'Weekly engagement trends across all departments',
      type: 'Report',
      sharedBy: 'Jane Smith',
      sharedWith: [
        { id: '4', name: 'John Doe', email: '<EMAIL>', permissions: 'view' },
        { id: '5', name: 'HR Team', email: '<EMAIL>', permissions: 'view' },
      ],
      permissions: 'view',
      visibility: 'organization',
      createdAt: '2024-01-12T09:00:00Z',
      lastAccessed: '2024-01-15T11:20:00Z',
      accessCount: 23,
      isOwner: false,
    },
    {
      id: 3,
      name: 'Learning Path Analytics',
      description: 'Detailed analytics for all learning paths and completion rates',
      type: 'Analytics',
      sharedBy: 'Mike Johnson',
      sharedWith: [
        { id: '6', name: 'Leadership Team', email: '<EMAIL>', permissions: 'view' },
      ],
      permissions: 'edit',
      visibility: 'private',
      shareLink: 'https://app.zenithlearn.ai/shared/learning-analytics-xyz789',
      expiresAt: '2024-02-28T23:59:59Z',
      createdAt: '2024-01-08T15:30:00Z',
      lastAccessed: '2024-01-14T16:45:00Z',
      accessCount: 12,
      isOwner: false,
    },
  ])

  const [shareDialogOpen, setShareDialogOpen] = useState(false)
  const [selectedReport, setSelectedReport] = useState<SharedReport | null>(null)
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [shareConfig, setShareConfig] = useState({
    users: [] as string[],
    permissions: 'view' as 'view' | 'edit' | 'admin',
    visibility: 'private' as 'private' | 'team' | 'organization' | 'public',
    expiresAt: '',
    allowDownload: true,
    requireLogin: true,
  })

  // Mock users for autocomplete
  const availableUsers = [
    { id: '1', name: 'Alice Johnson', email: '<EMAIL>' },
    { id: '2', name: 'Bob Smith', email: '<EMAIL>' },
    { id: '3', name: 'Carol Davis', email: '<EMAIL>' },
    { id: '4', name: 'David Wilson', email: '<EMAIL>' },
    { id: '5', name: 'Eva Brown', email: '<EMAIL>' },
  ]

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, report: SharedReport) => {
    setAnchorEl(event.currentTarget)
    setSelectedReport(report)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
    setSelectedReport(null)
  }

  const handleShare = (report: SharedReport) => {
    setSelectedReport(report)
    setShareDialogOpen(true)
    handleMenuClose()
  }

  const getVisibilityIcon = (visibility: string) => {
    switch (visibility) {
      case 'public':
        return <PublicIcon />
      case 'organization':
        return <GroupIcon />
      case 'team':
        return <PeopleIcon />
      case 'private':
        return <LockIcon />
      default:
        return <LockIcon />
    }
  }

  const getVisibilityColor = (visibility: string) => {
    switch (visibility) {
      case 'public':
        return 'success'
      case 'organization':
        return 'info'
      case 'team':
        return 'warning'
      case 'private':
        return 'default'
      default:
        return 'default'
    }
  }

  const getPermissionColor = (permission: string) => {
    switch (permission) {
      case 'admin':
        return 'error'
      case 'edit':
        return 'warning'
      case 'view':
        return 'success'
      default:
        return 'default'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    })
  }

  const isExpired = (expiresAt?: string) => {
    if (!expiresAt) return false
    return new Date(expiresAt) < new Date()
  }

  const copyShareLink = (link: string) => {
    navigator.clipboard.writeText(link)
    // Show success message
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h6" fontWeight="bold">
          Shared Reports ({sharedReports.length})
        </Typography>
        <Button
          variant="contained"
          startIcon={<ShareIcon />}
          onClick={() => setShareDialogOpen(true)}
        >
          Share Report
        </Button>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={2} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <ShareIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h4" color="primary" fontWeight="bold">
                {sharedReports.filter(r => r.isOwner).length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Shared by Me
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <PeopleIcon color="info" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h4" color="info.main" fontWeight="bold">
                {sharedReports.filter(r => !r.isOwner).length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Shared with Me
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <ViewIcon color="success" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h4" color="success.main" fontWeight="bold">
                {sharedReports.reduce((sum, r) => sum + r.accessCount, 0)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Views
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <LinkIcon color="warning" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h4" color="warning.main" fontWeight="bold">
                {sharedReports.filter(r => r.shareLink).length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Public Links
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Shared Reports List */}
      <Grid container spacing={3}>
        {sharedReports.map((report, index) => (
          <Grid item xs={12} md={6} lg={4} key={report.id}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  opacity: isExpired(report.expiresAt) ? 0.6 : 1,
                }}
              >
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                    <Box display="flex" alignItems="center">
                      <Avatar
                        sx={{
                          bgcolor: report.isOwner ? theme.palette.primary.main : theme.palette.secondary.main,
                          mr: 1,
                          width: 32,
                          height: 32,
                        }}
                      >
                        <ShareIcon />
                      </Avatar>
                      <Box>
                        <Typography variant="h6" fontWeight="bold" sx={{ fontSize: '1rem' }}>
                          {report.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {report.type} • by {report.sharedBy}
                        </Typography>
                      </Box>
                    </Box>
                    <IconButton
                      size="small"
                      onClick={(e) => handleMenuOpen(e, report)}
                    >
                      <MoreVertIcon />
                    </IconButton>
                  </Box>

                  <Typography variant="body2" color="text.secondary" mb={2}>
                    {report.description}
                  </Typography>

                  <Box display="flex" flexWrap="wrap" gap={1} mb={2}>
                    <Chip
                      icon={getVisibilityIcon(report.visibility)}
                      label={report.visibility}
                      size="small"
                      color={getVisibilityColor(report.visibility) as any}
                      variant="outlined"
                    />
                    <Chip
                      label={report.permissions}
                      size="small"
                      color={getPermissionColor(report.permissions) as any}
                      variant="filled"
                    />
                    {isExpired(report.expiresAt) && (
                      <Chip
                        label="Expired"
                        size="small"
                        color="error"
                        variant="filled"
                      />
                    )}
                  </Box>

                  <Box mb={2}>
                    <Typography variant="caption" color="text.secondary" gutterBottom>
                      Shared with ({report.sharedWith.length})
                    </Typography>
                    <AvatarGroup max={4} sx={{ justifyContent: 'flex-start' }}>
                      {report.sharedWith.map((user) => (
                        <Avatar
                          key={user.id}
                          sx={{ width: 24, height: 24 }}
                          title={user.name}
                        >
                          {user.name[0]}
                        </Avatar>
                      ))}
                    </AvatarGroup>
                  </Box>

                  <Box>
                    <Typography variant="caption" color="text.secondary">
                      {report.accessCount} views • Last accessed {formatDate(report.lastAccessed)}
                    </Typography>
                    {report.expiresAt && (
                      <>
                        <br />
                        <Typography variant="caption" color="text.secondary">
                          Expires: {formatDate(report.expiresAt)}
                        </Typography>
                      </>
                    )}
                  </Box>
                </CardContent>

                <CardActions>
                  <Button
                    size="small"
                    startIcon={<ViewIcon />}
                    onClick={() => console.log('View report:', report.id)}
                  >
                    View
                  </Button>
                  {report.shareLink && (
                    <Button
                      size="small"
                      startIcon={<LinkIcon />}
                      onClick={() => copyShareLink(report.shareLink!)}
                    >
                      Copy Link
                    </Button>
                  )}
                  {report.isOwner && (
                    <IconButton
                      size="small"
                      onClick={() => handleShare(report)}
                    >
                      <EditIcon />
                    </IconButton>
                  )}
                </CardActions>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => console.log('View report')}>
          <ViewIcon sx={{ mr: 1 }} />
          View Report
        </MenuItem>
        <MenuItem onClick={() => console.log('Download report')}>
          <DownloadIcon sx={{ mr: 1 }} />
          Download
        </MenuItem>
        {selectedReport?.isOwner && (
          <>
            <MenuItem onClick={() => handleShare(selectedReport)}>
              <EditIcon sx={{ mr: 1 }} />
              Edit Sharing
            </MenuItem>
            <Divider />
            <MenuItem onClick={handleMenuClose} sx={{ color: 'error.main' }}>
              <DeleteIcon sx={{ mr: 1 }} />
              Remove Share
            </MenuItem>
          </>
        )}
      </Menu>

      {/* Share Dialog */}
      <Dialog open={shareDialogOpen} onClose={() => setShareDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          Share Report: {selectedReport?.name || 'New Report'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <Autocomplete
                multiple
                options={availableUsers}
                getOptionLabel={(option) => `${option.name} (${option.email})`}
                value={availableUsers.filter(user => shareConfig.users.includes(user.id))}
                onChange={(_, newValue) => {
                  setShareConfig(prev => ({
                    ...prev,
                    users: newValue.map(user => user.id)
                  }))
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Share with users"
                    placeholder="Select users to share with"
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Permissions</InputLabel>
                <Select
                  value={shareConfig.permissions}
                  onChange={(e) => setShareConfig(prev => ({ ...prev, permissions: e.target.value as any }))}
                >
                  <MenuItem value="view">View Only</MenuItem>
                  <MenuItem value="edit">Can Edit</MenuItem>
                  <MenuItem value="admin">Admin Access</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Visibility</InputLabel>
                <Select
                  value={shareConfig.visibility}
                  onChange={(e) => setShareConfig(prev => ({ ...prev, visibility: e.target.value as any }))}
                >
                  <MenuItem value="private">Private</MenuItem>
                  <MenuItem value="team">Team</MenuItem>
                  <MenuItem value="organization">Organization</MenuItem>
                  <MenuItem value="public">Public</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Expiration Date (optional)"
                type="datetime-local"
                value={shareConfig.expiresAt}
                onChange={(e) => setShareConfig(prev => ({ ...prev, expiresAt: e.target.value }))}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>

          {selectedReport?.sharedWith && selectedReport.sharedWith.length > 0 && (
            <Box mt={3}>
              <Typography variant="subtitle2" gutterBottom>
                Current Shares
              </Typography>
              <List>
                {selectedReport.sharedWith.map((user) => (
                  <ListItem key={user.id}>
                    <ListItemAvatar>
                      <Avatar>{user.name[0]}</Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={user.name}
                      secondary={user.email}
                    />
                    <ListItemSecondaryAction>
                      <Chip
                        label={user.permissions}
                        size="small"
                        color={getPermissionColor(user.permissions) as any}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShareDialogOpen(false)}>Cancel</Button>
          <Button variant="contained" onClick={() => setShareDialogOpen(false)}>
            Share Report
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}
