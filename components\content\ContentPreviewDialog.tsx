'use client'

import { useState, useCallback } from 'react'
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Chip,
  IconButton,
  Tabs,
  Tab,
  Paper,
  Grid,
  Divider,
  Alert,
  CircularProgress
} from '@mui/material'
import {
  Close as CloseIcon,
  Download as DownloadIcon,
  Edit as EditIcon,
  Share as ShareIcon,
  Fullscreen as FullscreenIcon,
  Info as InfoIcon,
  Analytics as AnalyticsIcon,
  History as HistoryIcon
} from '@mui/icons-material'
import { motion, AnimatePresence } from 'framer-motion'
import { formatDistanceToNow, format } from 'date-fns'
import ReactPlayer from 'react-player'

import type { ContentItem } from '@/lib/types/content'

interface ContentPreviewDialogProps {
  open: boolean
  onClose: () => void
  content: ContentItem | null
}

interface TabPanelProps {
  children?: React.ReactNode
  index: number
  value: number
}

const TabPanel = ({ children, value, index, ...other }: TabPanelProps) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`preview-tabpanel-${index}`}
      aria-labelledby={`preview-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  )
}

const ContentPreviewDialog = ({ open, onClose, content }: ContentPreviewDialogProps) => {
  const [activeTab, setActiveTab] = useState(0)
  const [loading, setLoading] = useState(false)
  const [fullscreen, setFullscreen] = useState(false)

  const handleTabChange = useCallback((event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue)
  }, [])

  const handleDownload = useCallback(() => {
    if (content?.file_url) {
      const link = document.createElement('a')
      link.href = content.file_url
      link.download = content.title
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }, [content])

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown size'
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  const formatDuration = (seconds?: number) => {
    if (!seconds) return null
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }

  const renderPreview = () => {
    if (!content) return null

    switch (content.type) {
      case 'video':
        return (
          <Box sx={{ position: 'relative', paddingTop: '56.25%', bgcolor: 'black' }}>
            <ReactPlayer
              url={content.file_url}
              width="100%"
              height="100%"
              style={{ position: 'absolute', top: 0, left: 0 }}
              controls
              light={content.thumbnail_url}
            />
          </Box>
        )

      case 'image':
        return (
          <Box
            component="img"
            src={content.file_url}
            alt={content.title}
            sx={{
              width: '100%',
              height: 'auto',
              maxHeight: 400,
              objectFit: 'contain',
              borderRadius: 1
            }}
          />
        )

      case 'pdf':
        return (
          <Box sx={{ height: 500, border: 1, borderColor: 'divider', borderRadius: 1 }}>
            <iframe
              src={`${content.file_url}#toolbar=1&navpanes=1&scrollbar=1`}
              width="100%"
              height="100%"
              style={{ border: 'none' }}
              title={content.title}
            />
          </Box>
        )

      case 'audio':
        return (
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <audio controls style={{ width: '100%', maxWidth: 400 }}>
              <source src={content.file_url} type={content.mime_type} />
              Your browser does not support the audio element.
            </audio>
          </Box>
        )

      default:
        return (
          <Alert severity="info" sx={{ m: 2 }}>
            Preview not available for this content type. Click download to view the file.
          </Alert>
        )
    }
  }

  const renderMetadata = () => {
    if (!content) return null

    return (
      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              File Information
            </Typography>
            <Box display="flex" flexDirection="column" gap={1}>
              <Box display="flex" justifyContent="space-between">
                <Typography variant="body2" color="text.secondary">
                  File Size:
                </Typography>
                <Typography variant="body2">
                  {formatFileSize(content.file_size)}
                </Typography>
              </Box>
              <Box display="flex" justifyContent="space-between">
                <Typography variant="body2" color="text.secondary">
                  Type:
                </Typography>
                <Chip label={content.type.toUpperCase()} size="small" />
              </Box>
              <Box display="flex" justifyContent="space-between">
                <Typography variant="body2" color="text.secondary">
                  MIME Type:
                </Typography>
                <Typography variant="body2">
                  {content.mime_type || 'Unknown'}
                </Typography>
              </Box>
              {content.duration && (
                <Box display="flex" justifyContent="space-between">
                  <Typography variant="body2" color="text.secondary">
                    Duration:
                  </Typography>
                  <Typography variant="body2">
                    {formatDuration(content.duration)}
                  </Typography>
                </Box>
              )}
              <Box display="flex" justifyContent="space-between">
                <Typography variant="body2" color="text.secondary">
                  Created:
                </Typography>
                <Typography variant="body2">
                  {format(new Date(content.created_at), 'MMM dd, yyyy')}
                </Typography>
              </Box>
              <Box display="flex" justifyContent="space-between">
                <Typography variant="body2" color="text.secondary">
                  Modified:
                </Typography>
                <Typography variant="body2">
                  {formatDistanceToNow(new Date(content.updated_at), { addSuffix: true })}
                </Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Content Details
            </Typography>
            <Box display="flex" flexDirection="column" gap={2}>
              <Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Description:
                </Typography>
                <Typography variant="body2">
                  {content.description || 'No description available'}
                </Typography>
              </Box>
              
              {content.tags.length > 0 && (
                <Box>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Tags:
                  </Typography>
                  <Box display="flex" flexWrap="wrap" gap={0.5}>
                    {content.tags.map((tag) => (
                      <Chip key={tag} label={tag} size="small" variant="outlined" />
                    ))}
                  </Box>
                </Box>
              )}

              <Box display="flex" justifyContent="space-between">
                <Typography variant="body2" color="text.secondary">
                  Visibility:
                </Typography>
                <Chip
                  label={content.is_public ? 'Public' : 'Private'}
                  size="small"
                  color={content.is_public ? 'success' : 'default'}
                />
              </Box>
            </Box>
          </Paper>
        </Grid>

        {/* AI Metadata */}
        {content.metadata?.ai_tags && (
          <Grid item xs={12}>
            <Paper sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom>
                AI Analysis
              </Typography>
              <Grid container spacing={2}>
                {content.metadata.ai_difficulty_level && (
                  <Grid item xs={12} sm={4}>
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="body2" color="text.secondary">
                        Difficulty:
                      </Typography>
                      <Chip
                        label={content.metadata.ai_difficulty_level}
                        size="small"
                        color="primary"
                      />
                    </Box>
                  </Grid>
                )}
                {content.metadata.ai_estimated_duration && (
                  <Grid item xs={12} sm={4}>
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="body2" color="text.secondary">
                        Est. Duration:
                      </Typography>
                      <Typography variant="body2">
                        {content.metadata.ai_estimated_duration} min
                      </Typography>
                    </Box>
                  </Grid>
                )}
                {content.metadata.ai_tags && (
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      AI Generated Tags:
                    </Typography>
                    <Box display="flex" flexWrap="wrap" gap={0.5}>
                      {content.metadata.ai_tags.map((tag: string) => (
                        <Chip key={tag} label={tag} size="small" color="secondary" />
                      ))}
                    </Box>
                  </Grid>
                )}
                {content.metadata.ai_summary && (
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      AI Summary:
                    </Typography>
                    <Typography variant="body2">
                      {content.metadata.ai_summary}
                    </Typography>
                  </Grid>
                )}
              </Grid>
            </Paper>
          </Grid>
        )}
      </Grid>
    )
  }

  const renderAnalytics = () => {
    return (
      <Box p={2}>
        <Alert severity="info">
          Analytics data will be available once the content has been accessed by learners.
        </Alert>
        
        {/* Placeholder for analytics charts */}
        <Grid container spacing={2} mt={2}>
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 2, textAlign: 'center', minHeight: 200 }}>
              <Typography variant="h6" gutterBottom>
                View Statistics
              </Typography>
              <Typography variant="body2" color="text.secondary">
                No data available yet
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 2, textAlign: 'center', minHeight: 200 }}>
              <Typography variant="h6" gutterBottom>
                Engagement Metrics
              </Typography>
              <Typography variant="body2" color="text.secondary">
                No data available yet
              </Typography>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    )
  }

  const renderVersionHistory = () => {
    return (
      <Box p={2}>
        <Alert severity="info">
          Version history will show previous versions of this content.
        </Alert>
        
        <Paper sx={{ mt: 2, p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Current Version
          </Typography>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Box>
              <Typography variant="body2">
                Version 1.0
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Created {formatDistanceToNow(new Date(content?.created_at || ''), { addSuffix: true })}
              </Typography>
            </Box>
            <Chip label="Current" color="primary" size="small" />
          </Box>
        </Paper>
      </Box>
    )
  }

  if (!content) return null

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth={fullscreen ? false : 'lg'}
      fullWidth
      fullScreen={fullscreen}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography variant="h6" component="div">
              {content.title}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {content.type.toUpperCase()} • {formatFileSize(content.file_size)}
            </Typography>
          </Box>
          <Box>
            <IconButton onClick={() => setFullscreen(!fullscreen)}>
              <FullscreenIcon />
            </IconButton>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={handleTabChange}>
            <Tab icon={<InfoIcon />} label="Preview" />
            <Tab icon={<InfoIcon />} label="Details" />
            <Tab icon={<AnalyticsIcon />} label="Analytics" />
            <Tab icon={<HistoryIcon />} label="History" />
          </Tabs>
        </Box>

        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            <TabPanel value={activeTab} index={0}>
              <Box p={2}>
                {loading ? (
                  <Box display="flex" justifyContent="center" p={4}>
                    <CircularProgress />
                  </Box>
                ) : (
                  renderPreview()
                )}
              </Box>
            </TabPanel>

            <TabPanel value={activeTab} index={1}>
              <Box p={2}>
                {renderMetadata()}
              </Box>
            </TabPanel>

            <TabPanel value={activeTab} index={2}>
              {renderAnalytics()}
            </TabPanel>

            <TabPanel value={activeTab} index={3}>
              {renderVersionHistory()}
            </TabPanel>
          </motion.div>
        </AnimatePresence>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          Close
        </Button>
        <Button startIcon={<ShareIcon />}>
          Share
        </Button>
        <Button startIcon={<EditIcon />}>
          Edit
        </Button>
        <Button
          variant="contained"
          startIcon={<DownloadIcon />}
          onClick={handleDownload}
        >
          Download
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default ContentPreviewDialog
