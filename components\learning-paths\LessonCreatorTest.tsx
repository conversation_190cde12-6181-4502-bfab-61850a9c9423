'use client'

import React, { useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Alert,
  List,
  ListItem,
  ListItemText,
  Chip
} from '@mui/material'
import {
  CloudUpload as UploadIcon,
  CheckCircle as SuccessIcon
} from '@mui/icons-material'
import EnhancedLessonCreator from './EnhancedLessonCreator'

export default function LessonCreatorTest() {
  const [dialogOpen, setDialogOpen] = useState(false)
  const [createdLessons, setCreatedLessons] = useState<any[]>([])
  const [testStatus, setTestStatus] = useState<'idle' | 'success' | 'error'>('idle')

  const handleLessonSave = (lessonData: any) => {
    console.log('Lesson saved:', lessonData)
    setCreatedLessons(prev => [...prev, {
      ...lessonData,
      id: Date.now(),
      createdAt: new Date().toISOString()
    }])
    setTestStatus('success')
    setDialogOpen(false)
  }

  const handleTestDragDrop = () => {
    setDialogOpen(true)
    setTestStatus('idle')
  }

  return (
    <Box>
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Enhanced Lesson Creator Test
          </Typography>
          
          <Typography variant="body2" color="text.secondary" mb={3}>
            Test the drag-and-drop file upload functionality and async operations in the Enhanced Lesson Creator.
          </Typography>

          <Button
            variant="contained"
            startIcon={<UploadIcon />}
            onClick={handleTestDragDrop}
            size="large"
            sx={{ mb: 3 }}
          >
            Test Lesson Creator
          </Button>

          {testStatus === 'success' && (
            <Alert severity="success" sx={{ mb: 2 }}>
              <Typography variant="subtitle2">
                Lesson Creator Test Successful!
              </Typography>
              <Typography variant="body2">
                The async file upload and drag-and-drop functionality is working correctly.
              </Typography>
            </Alert>
          )}

          {createdLessons.length > 0 && (
            <Card variant="outlined">
              <CardContent>
                <Typography variant="subtitle1" gutterBottom>
                  Created Lessons ({createdLessons.length})
                </Typography>
                
                <List>
                  {createdLessons.map((lesson, index) => (
                    <ListItem key={lesson.id} divider={index < createdLessons.length - 1}>
                      <ListItemText
                        primary={
                          <Box display="flex" alignItems="center" gap={1}>
                            <Typography variant="body1">
                              {lesson.title}
                            </Typography>
                            <Chip
                              label={lesson.type}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                            {lesson.uploadResult && (
                              <Chip
                                label="File Uploaded"
                                size="small"
                                color="success"
                                icon={<SuccessIcon />}
                              />
                            )}
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              {lesson.description || 'No description'}
                            </Typography>
                            {lesson.uploadResult && (
                              <Typography variant="caption" color="success.main">
                                File: {lesson.uploadResult.metadata.name} ({(lesson.uploadResult.metadata.size / 1024).toFixed(1)} KB)
                              </Typography>
                            )}
                            {lesson.url && !lesson.uploadResult && (
                              <Typography variant="caption" color="info.main">
                                URL: {lesson.url}
                              </Typography>
                            )}
                            <Typography variant="caption" display="block" color="text.secondary">
                              Created: {new Date(lesson.createdAt).toLocaleString()}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          )}

          <EnhancedLessonCreator
            open={dialogOpen}
            onClose={() => setDialogOpen(false)}
            onSave={handleLessonSave}
            moduleId="test-module"
          />

          <Box mt={3}>
            <Typography variant="subtitle2" gutterBottom>
              Test Instructions
            </Typography>
            <Typography variant="body2" color="text.secondary">
              1. Click "Test Lesson Creator" to open the dialog<br/>
              2. Try dragging and dropping a file (video, PDF, or PowerPoint)<br/>
              3. Fill in the lesson title and description<br/>
              4. Click "Save Lesson" to test the async operations<br/>
              5. Verify that the file upload progress works correctly<br/>
              6. Check that the lesson appears in the list below
            </Typography>
          </Box>

          <Box mt={2}>
            <Typography variant="subtitle2" gutterBottom>
              Fixed Issues
            </Typography>
            <Box display="flex" flexWrap="wrap" gap={1}>
              <Chip label="Async onDrop callback" size="small" color="success" variant="outlined" />
              <Chip label="Proper error handling" size="small" color="success" variant="outlined" />
              <Chip label="useCallback dependencies" size="small" color="success" variant="outlined" />
              <Chip label="No duplicate functions" size="small" color="success" variant="outlined" />
              <Chip label="TypeScript compilation" size="small" color="success" variant="outlined" />
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Box>
  )
}
