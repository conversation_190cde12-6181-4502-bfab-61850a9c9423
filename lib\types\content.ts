export interface ContentItem {
  id: number
  tenant_id: number
  title: string
  description?: string
  type: ContentType
  file_url: string
  file_size?: number
  mime_type?: string
  duration?: number // for video/audio in seconds
  tags: string[]
  metadata: ContentMetadata
  uploaded_by?: string
  is_public: boolean
  created_at: string
  updated_at: string
  // Computed fields
  preview_url?: string
  thumbnail_url?: string
  category?: ContentCategory
}

export type ContentType = 
  | 'video' 
  | 'pdf' 
  | 'image' 
  | 'audio' 
  | 'document' 
  | 'scorm'
  | 'presentation'
  | 'quiz'
  | 'interactive'

export interface ContentMetadata {
  // File metadata
  original_filename?: string
  file_extension?: string
  storage_path?: string
  
  // Video metadata
  video_duration?: number
  video_resolution?: string
  video_codec?: string
  video_bitrate?: number
  
  // PDF metadata
  pdf_pages?: number
  pdf_text_content?: string
  
  // Image metadata
  image_width?: number
  image_height?: number
  image_format?: string
  
  // Audio metadata
  audio_duration?: number
  audio_bitrate?: number
  audio_format?: string
  
  // SCORM metadata
  scorm_version?: string
  scorm_manifest?: any
  
  // AI-generated metadata
  ai_tags?: string[]
  ai_summary?: string
  ai_difficulty_level?: 'beginner' | 'intermediate' | 'advanced'
  ai_estimated_duration?: number
  ai_keywords?: string[]
  
  // Processing status
  processing_status?: 'pending' | 'processing' | 'completed' | 'failed'
  processing_error?: string
  
  // Version control
  version?: number
  parent_content_id?: number
  
  // Usage tracking
  view_count?: number
  download_count?: number
  last_accessed?: string
  
  // Compliance
  contains_pii?: boolean
  compliance_status?: 'pending' | 'approved' | 'rejected'
  compliance_notes?: string
}

export interface ContentCategory {
  id: number
  tenant_id: number
  name: string
  description?: string
  parent_id?: number
  color?: string
  icon?: string
  sort_order?: number
  created_at: string
  updated_at: string
  // Computed fields
  children?: ContentCategory[]
  content_count?: number
}

export interface ContentFilter {
  search?: string
  type?: ContentType[]
  category_id?: number[]
  tags?: string[]
  uploaded_by?: string[]
  date_range?: {
    start: string
    end: string
  }
  file_size_range?: {
    min: number
    max: number
  }
  duration_range?: {
    min: number
    max: number
  }
  is_public?: boolean
  sort_by?: 'title' | 'created_at' | 'updated_at' | 'file_size' | 'view_count'
  sort_order?: 'asc' | 'desc'
  page?: number
  limit?: number
}

export interface ContentUploadRequest {
  title: string
  description?: string
  type: ContentType
  category_id?: number
  tags?: string[]
  is_public?: boolean
  file: File
  // AI enhancement options
  auto_tag?: boolean
  auto_categorize?: boolean
  extract_text?: boolean
  generate_thumbnail?: boolean
}

export interface ContentUploadResult {
  content_id: number
  file_url: string
  storage_path: string
  metadata: ContentMetadata
  processing_status: 'pending' | 'completed' | 'failed'
  ai_suggestions?: {
    tags: string[]
    category: string
    difficulty_level: string
    estimated_duration: number
  }
}

export interface ContentPreview {
  content_id: number
  preview_type: 'thumbnail' | 'text' | 'video_preview' | 'pdf_preview'
  preview_url?: string
  preview_data?: any
  text_excerpt?: string
  page_count?: number
  duration?: number
}

export interface ContentUsageStats {
  content_id: number
  total_views: number
  total_downloads: number
  unique_viewers: number
  avg_view_duration?: number
  completion_rate?: number
  engagement_score: number
  last_accessed: string
  popular_tags: string[]
  usage_by_date: Array<{
    date: string
    views: number
    downloads: number
  }>
}

export interface ContentValidationResult {
  content_id: number
  is_valid: boolean
  validation_errors: string[]
  validation_warnings: string[]
  compliance_issues: string[]
  quality_score: number
  recommendations: string[]
}

export interface BulkContentOperation {
  operation: 'delete' | 'update_category' | 'update_tags' | 'update_visibility'
  content_ids: number[]
  parameters?: {
    category_id?: number
    tags?: string[]
    is_public?: boolean
  }
}

export interface ContentSearchResult {
  items: ContentItem[]
  total_count: number
  page: number
  limit: number
  has_more: boolean
  facets?: {
    types: Array<{ type: ContentType; count: number }>
    categories: Array<{ category: ContentCategory; count: number }>
    tags: Array<{ tag: string; count: number }>
    uploaders: Array<{ uploader: string; count: number }>
  }
}

export interface ContentVersion {
  id: number
  content_id: number
  version_number: number
  title: string
  description?: string
  file_url: string
  metadata: ContentMetadata
  created_by: string
  created_at: string
  change_notes?: string
}

export interface ContentReview {
  id: number
  content_id: number
  reviewer_id: string
  status: 'pending' | 'approved' | 'rejected' | 'needs_changes'
  comments?: string
  reviewed_at?: string
  created_at: string
}

export interface SmartFolder {
  id: number
  tenant_id: number
  name: string
  description?: string
  rules: SmartFolderRule[]
  auto_update: boolean
  created_at: string
  updated_at: string
  // Computed fields
  content_count?: number
  last_updated?: string
}

export interface SmartFolderRule {
  field: 'type' | 'tags' | 'category' | 'uploaded_by' | 'file_size' | 'created_at'
  operator: 'equals' | 'contains' | 'starts_with' | 'greater_than' | 'less_than' | 'in' | 'not_in'
  value: any
  logic?: 'AND' | 'OR'
}

// API Response types
export interface ContentApiResponse<T = any> {
  data: T
  success: boolean
  message?: string
  errors?: string[]
}

export interface ContentListResponse extends ContentApiResponse<ContentSearchResult> {}
export interface ContentDetailResponse extends ContentApiResponse<ContentItem> {}
export interface ContentUploadResponse extends ContentApiResponse<ContentUploadResult> {}
export interface ContentCategoriesResponse extends ContentApiResponse<ContentCategory[]> {}
export interface ContentStatsResponse extends ContentApiResponse<ContentUsageStats> {}
