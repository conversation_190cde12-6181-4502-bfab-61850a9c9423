'use client'

import { useState, useCallback } from 'react'
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Chip,
  Menu,
  MenuItem,
  Collapse,
  Badge,
  Tooltip
} from '@mui/material'
import {
  Folder as FolderIcon,
  FolderOpen as FolderOpenIcon,
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Category as CategoryIcon
} from '@mui/icons-material'
import { motion, AnimatePresence } from 'framer-motion'
import toast from 'react-hot-toast'

import type { ContentCategory } from '@/lib/types/content'
import { ContentService } from '@/lib/services/contentService'

interface ContentCategoriesProps {
  categories: ContentCategory[]
  selectedCategory?: number
  onCategorySelect: (categoryId: number | null) => void
}

interface CategoryDialogData {
  id?: number
  name: string
  description: string
  parent_id?: number
  color: string
}

const ContentCategories = ({ 
  categories, 
  selectedCategory, 
  onCategorySelect 
}: ContentCategoriesProps) => {
  const [expandedCategories, setExpandedCategories] = useState<Set<number>>(new Set())
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<CategoryDialogData | null>(null)
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null)
  const [selectedMenuCategory, setSelectedMenuCategory] = useState<ContentCategory | null>(null)

  // Build category tree
  const buildCategoryTree = useCallback((categories: ContentCategory[]): ContentCategory[] => {
    const categoryMap = new Map<number, ContentCategory>()
    const rootCategories: ContentCategory[] = []

    // First pass: create map and add children array
    categories.forEach(category => {
      categoryMap.set(category.id, { ...category, children: [] })
    })

    // Second pass: build tree structure
    categories.forEach(category => {
      const categoryWithChildren = categoryMap.get(category.id)!
      if (category.parent_id) {
        const parent = categoryMap.get(category.parent_id)
        if (parent) {
          parent.children = parent.children || []
          parent.children.push(categoryWithChildren)
        }
      } else {
        rootCategories.push(categoryWithChildren)
      }
    })

    return rootCategories
  }, [])

  const categoryTree = buildCategoryTree(categories)

  const handleCategoryClick = useCallback((categoryId: number) => {
    if (selectedCategory === categoryId) {
      onCategorySelect(null) // Deselect if already selected
    } else {
      onCategorySelect(categoryId)
    }
  }, [selectedCategory, onCategorySelect])

  const handleExpandToggle = useCallback((categoryId: number) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev)
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId)
      } else {
        newSet.add(categoryId)
      }
      return newSet
    })
  }, [])

  const handleMenuOpen = useCallback((event: React.MouseEvent<HTMLElement>, category: ContentCategory) => {
    event.stopPropagation()
    setMenuAnchor(event.currentTarget)
    setSelectedMenuCategory(category)
  }, [])

  const handleMenuClose = useCallback(() => {
    setMenuAnchor(null)
    setSelectedMenuCategory(null)
  }, [])

  const handleCreateCategory = useCallback(() => {
    setEditingCategory({
      name: '',
      description: '',
      color: '#1976d2'
    })
    setDialogOpen(true)
  }, [])

  const handleEditCategory = useCallback((category: ContentCategory) => {
    setEditingCategory({
      id: category.id,
      name: category.name,
      description: category.description || '',
      parent_id: category.parent_id,
      color: category.color || '#1976d2'
    })
    setDialogOpen(true)
    handleMenuClose()
  }, [handleMenuClose])

  const handleDeleteCategory = useCallback(async (category: ContentCategory) => {
    if (window.confirm(`Are you sure you want to delete "${category.name}"?`)) {
      try {
        // Implementation would call ContentService.deleteCategory
        toast.success('Category deleted successfully')
        handleMenuClose()
      } catch (error) {
        toast.error('Failed to delete category')
      }
    }
  }, [handleMenuClose])

  const handleSaveCategory = useCallback(async () => {
    if (!editingCategory) return

    try {
      if (editingCategory.id) {
        // Update existing category
        // await ContentService.updateCategory(editingCategory.id, editingCategory)
        toast.success('Category updated successfully')
      } else {
        // Create new category
        // await ContentService.createCategory(editingCategory)
        toast.success('Category created successfully')
      }
      setDialogOpen(false)
      setEditingCategory(null)
    } catch (error) {
      toast.error('Failed to save category')
    }
  }, [editingCategory])

  const renderCategory = useCallback((category: ContentCategory, level: number = 0) => {
    const hasChildren = category.children && category.children.length > 0
    const isExpanded = expandedCategories.has(category.id)
    const isSelected = selectedCategory === category.id

    return (
      <motion.div
        key={category.id}
        initial={{ opacity: 0, x: -10 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.2, delay: level * 0.05 }}
      >
        <ListItem disablePadding sx={{ pl: level * 2 }}>
          <ListItemButton
            selected={isSelected}
            onClick={() => handleCategoryClick(category.id)}
            sx={{
              borderRadius: 1,
              mx: 0.5,
              '&.Mui-selected': {
                bgcolor: 'primary.light',
                '&:hover': {
                  bgcolor: 'primary.light',
                }
              }
            }}
          >
            <ListItemIcon sx={{ minWidth: 36 }}>
              {hasChildren ? (
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleExpandToggle(category.id)
                  }}
                >
                  {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              ) : (
                <Box
                  sx={{
                    width: 24,
                    height: 24,
                    borderRadius: '50%',
                    bgcolor: category.color || 'primary.main',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <CategoryIcon sx={{ fontSize: 14, color: 'white' }} />
                </Box>
              )}
            </ListItemIcon>

            <ListItemText
              primary={
                <Box display="flex" alignItems="center" gap={1}>
                  <Typography variant="body2" fontWeight={isSelected ? 'bold' : 'normal'}>
                    {category.name}
                  </Typography>
                  {category.content_count !== undefined && (
                    <Chip
                      label={category.content_count}
                      size="small"
                      variant="outlined"
                      sx={{ height: 20, fontSize: '0.75rem' }}
                    />
                  )}
                </Box>
              }
              secondary={level === 0 ? category.description : undefined}
            />

            <IconButton
              size="small"
              onClick={(e) => handleMenuOpen(e, category)}
              sx={{ opacity: 0.7, '&:hover': { opacity: 1 } }}
            >
              <MoreVertIcon fontSize="small" />
            </IconButton>
          </ListItemButton>
        </ListItem>

        {/* Child Categories */}
        {hasChildren && (
          <Collapse in={isExpanded} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {category.children!.map(child => renderCategory(child, level + 1))}
            </List>
          </Collapse>
        )}
      </motion.div>
    )
  }, [expandedCategories, selectedCategory, handleCategoryClick, handleExpandToggle, handleMenuOpen])

  return (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ pb: 1 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6" fontWeight="bold">
            Categories
          </Typography>
          <Tooltip title="Create Category">
            <IconButton size="small" onClick={handleCreateCategory}>
              <AddIcon />
            </IconButton>
          </Tooltip>
        </Box>

        {/* All Content Option */}
        <ListItem disablePadding>
          <ListItemButton
            selected={!selectedCategory}
            onClick={() => onCategorySelect(null)}
            sx={{
              borderRadius: 1,
              mx: 0.5,
              '&.Mui-selected': {
                bgcolor: 'primary.light',
                '&:hover': {
                  bgcolor: 'primary.light',
                }
              }
            }}
          >
            <ListItemIcon sx={{ minWidth: 36 }}>
              <FolderOpenIcon color={!selectedCategory ? 'primary' : 'inherit'} />
            </ListItemIcon>
            <ListItemText
              primary={
                <Typography variant="body2" fontWeight={!selectedCategory ? 'bold' : 'normal'}>
                  All Content
                </Typography>
              }
            />
            <Chip
              label={categories.reduce((sum, cat) => sum + (cat.content_count || 0), 0)}
              size="small"
              variant="outlined"
              sx={{ height: 20, fontSize: '0.75rem' }}
            />
          </ListItemButton>
        </ListItem>
      </CardContent>

      {/* Categories List */}
      <Box sx={{ flex: 1, overflow: 'auto', px: 1 }}>
        <List dense>
          <AnimatePresence>
            {categoryTree.map(category => renderCategory(category))}
          </AnimatePresence>
        </List>

        {categories.length === 0 && (
          <Box p={2} textAlign="center">
            <Typography variant="body2" color="text.secondary" gutterBottom>
              No categories yet
            </Typography>
            <Button
              variant="outlined"
              size="small"
              startIcon={<AddIcon />}
              onClick={handleCreateCategory}
            >
              Create First Category
            </Button>
          </Box>
        )}
      </Box>

      {/* Context Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => selectedMenuCategory && handleEditCategory(selectedMenuCategory)}>
          <EditIcon sx={{ mr: 1 }} fontSize="small" />
          Edit
        </MenuItem>
        <MenuItem 
          onClick={() => selectedMenuCategory && handleDeleteCategory(selectedMenuCategory)}
          sx={{ color: 'error.main' }}
        >
          <DeleteIcon sx={{ mr: 1 }} fontSize="small" />
          Delete
        </MenuItem>
      </Menu>

      {/* Category Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingCategory?.id ? 'Edit Category' : 'Create Category'}
        </DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" gap={2} mt={1}>
            <TextField
              fullWidth
              label="Category Name"
              value={editingCategory?.name || ''}
              onChange={(e) => setEditingCategory(prev => prev ? { ...prev, name: e.target.value } : null)}
              required
            />
            <TextField
              fullWidth
              label="Description"
              multiline
              rows={3}
              value={editingCategory?.description || ''}
              onChange={(e) => setEditingCategory(prev => prev ? { ...prev, description: e.target.value } : null)}
            />
            <TextField
              fullWidth
              label="Color"
              type="color"
              value={editingCategory?.color || '#1976d2'}
              onChange={(e) => setEditingCategory(prev => prev ? { ...prev, color: e.target.value } : null)}
              InputProps={{
                sx: { height: 56 }
              }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleSaveCategory}
            variant="contained"
            disabled={!editingCategory?.name.trim()}
          >
            {editingCategory?.id ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Card>
  )
}

export default ContentCategories
