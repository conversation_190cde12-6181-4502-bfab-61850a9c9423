name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18.x'

jobs:
  # Code Quality and Linting
  lint:
    name: 🔍 Code Quality & Linting
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🔍 Run ESLint
        run: npm run lint

      - name: 🎨 Check Prettier formatting
        run: npx prettier --check .

      - name: 🔧 TypeScript type checking
        run: npm run type-check

  # Unit and Integration Tests
  test:
    name: 🧪 Unit & Integration Tests
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🧪 Run unit tests
        run: npm test -- --coverage --watchAll=false
        env:
          CI: true

      - name: 📊 Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  # Build Application
  build:
    name: 🏗️ Build Application
    runs-on: ubuntu-latest
    needs: [lint, test]
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🏗️ Build application
        run: npm run build
        env:
          NEXT_TELEMETRY_DISABLED: 1

      - name: 📦 Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-files
          path: .next/
          retention-days: 1

  # End-to-End Tests
  e2e:
    name: 🎭 E2E Tests
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 📦 Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-files
          path: .next/

      - name: 🎭 Install Playwright browsers
        run: npx playwright install --with-deps

      - name: 🎭 Run E2E tests
        run: npm run test:e2e
        env:
          CI: true

      - name: 📊 Upload E2E test results
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 7

  # Security Scanning
  security:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔒 Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: 📊 Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: 🔍 Run npm audit
        run: npm audit --audit-level moderate

  # Accessibility Testing
  accessibility:
    name: ♿ Accessibility Tests
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📥 Install dependencies
        run: npm ci

      - name: 📦 Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-files
          path: .next/

      - name: ♿ Run accessibility tests
        run: |
          npm start &
          sleep 10
          npx @axe-core/cli http://localhost:3000 --exit
        env:
          CI: true

  # Deploy to Vercel (only on main branch)
  deploy:
    name: 🚀 Deploy to Vercel
    runs-on: ubuntu-latest
    needs: [lint, test, build, e2e, security, accessibility]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🚀 Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'

  # Notify on Success/Failure
  notify:
    name: 📢 Notify Results
    runs-on: ubuntu-latest
    needs: [lint, test, build, e2e, security, accessibility]
    if: always()
    steps:
      - name: 📢 Notify success
        if: ${{ needs.lint.result == 'success' && needs.test.result == 'success' && needs.build.result == 'success' && needs.e2e.result == 'success' && needs.security.result == 'success' && needs.accessibility.result == 'success' }}
        run: echo "✅ All checks passed successfully!"

      - name: 📢 Notify failure
        if: ${{ needs.lint.result == 'failure' || needs.test.result == 'failure' || needs.build.result == 'failure' || needs.e2e.result == 'failure' || needs.security.result == 'failure' || needs.accessibility.result == 'failure' }}
        run: echo "❌ Some checks failed. Please review the logs."
