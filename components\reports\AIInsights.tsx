'use client'

import { useState, useEffect } from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  IconButton,
  Chip,
  Alert,
  LinearProgress,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Paper,
  useTheme,
} from '@mui/material'
import {
  Psychology as AIIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Warning as WarningIcon,
  Lightbulb as InsightIcon,
  AutoAwesome as MagicIcon,
  Refresh as RefreshIcon,
  Schedule as ScheduleIcon,
  People as PeopleIcon,
  School as SchoolIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js'
import { Line, Bar } from 'react-chartjs-2'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Toolt<PERSON>,
  <PERSON>,
  <PERSON>ller
)

interface AIInsight {
  id: string
  type: 'trend' | 'anomaly' | 'prediction' | 'recommendation'
  title: string
  description: string
  confidence: number
  impact: 'high' | 'medium' | 'low'
  category: string
  actionable: boolean
  suggestedActions: string[]
  data?: any
  timestamp: string
}

interface EngagementPrediction {
  learnerId: string
  learnerName: string
  currentEngagement: number
  predictedEngagement: number
  riskLevel: 'high' | 'medium' | 'low'
  factors: string[]
}

export default function AIInsights() {
  const theme = useTheme()
  const [insights, setInsights] = useState<AIInsight[]>([])
  const [predictions, setPredictions] = useState<EngagementPrediction[]>([])
  const [isGenerating, setIsGenerating] = useState(false)
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())

  // Mock AI insights data
  const mockInsights: AIInsight[] = [
    {
      id: '1',
      type: 'trend',
      title: 'Declining Engagement in Data Science Course',
      description: 'Engagement in the Data Science fundamentals course has dropped by 23% over the past 2 weeks. This trend is particularly pronounced among learners in the 25-35 age group.',
      confidence: 87,
      impact: 'high',
      category: 'Engagement',
      actionable: true,
      suggestedActions: [
        'Review course content for complexity',
        'Add interactive elements to lessons',
        'Send targeted re-engagement emails',
        'Schedule instructor check-ins'
      ],
      timestamp: '2024-01-15T10:30:00Z',
    },
    {
      id: '2',
      type: 'prediction',
      title: 'Completion Rate Forecast',
      description: 'Based on current trends, the overall completion rate for Q1 is predicted to reach 78%, which is 5% below the target of 83%.',
      confidence: 92,
      impact: 'medium',
      category: 'Performance',
      actionable: true,
      suggestedActions: [
        'Implement early intervention strategies',
        'Provide additional support resources',
        'Adjust course pacing',
        'Increase mentor availability'
      ],
      timestamp: '2024-01-15T09:15:00Z',
    },
    {
      id: '3',
      type: 'anomaly',
      title: 'Unusual Quiz Performance Pattern',
      description: 'Quiz scores in Module 3 of the Leadership course show an unexpected spike, with 89% of learners scoring above 90%. This may indicate quiz difficulty issues.',
      confidence: 76,
      impact: 'medium',
      category: 'Assessment',
      actionable: true,
      suggestedActions: [
        'Review quiz questions for clarity',
        'Adjust difficulty level',
        'Analyze answer patterns',
        'Update assessment criteria'
      ],
      timestamp: '2024-01-15T08:45:00Z',
    },
    {
      id: '4',
      type: 'recommendation',
      title: 'Optimal Learning Path Suggestion',
      description: 'Learners who complete the Python Basics course show 34% higher engagement when followed by the Data Visualization course rather than Advanced Python.',
      confidence: 84,
      impact: 'high',
      category: 'Learning Path',
      actionable: true,
      suggestedActions: [
        'Update default learning path sequence',
        'Create personalized recommendations',
        'A/B test the new sequence',
        'Monitor completion rates'
      ],
      timestamp: '2024-01-15T07:20:00Z',
    },
  ]

  // Mock engagement predictions
  const mockPredictions: EngagementPrediction[] = [
    {
      learnerId: '1',
      learnerName: 'Alice Johnson',
      currentEngagement: 85,
      predictedEngagement: 62,
      riskLevel: 'high',
      factors: ['Decreased login frequency', 'Lower quiz scores', 'Missed deadlines'],
    },
    {
      learnerId: '2',
      learnerName: 'Bob Smith',
      currentEngagement: 72,
      predictedEngagement: 58,
      riskLevel: 'medium',
      factors: ['Irregular study patterns', 'Limited discussion participation'],
    },
    {
      learnerId: '3',
      learnerName: 'Carol Davis',
      currentEngagement: 68,
      predictedEngagement: 45,
      riskLevel: 'high',
      factors: ['Extended absence', 'No recent activity', 'Overdue assignments'],
    },
  ]

  useEffect(() => {
    // Simulate loading insights
    setIsGenerating(true)
    setTimeout(() => {
      setInsights(mockInsights)
      setPredictions(mockPredictions)
      setIsGenerating(false)
      setLastUpdated(new Date())
    }, 2000)
  }, [])

  const generateNewInsights = () => {
    setIsGenerating(true)
    // Simulate AI processing
    setTimeout(() => {
      setInsights(mockInsights)
      setPredictions(mockPredictions)
      setIsGenerating(false)
      setLastUpdated(new Date())
    }, 3000)
  }

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'trend':
        return <TrendingUpIcon />
      case 'anomaly':
        return <WarningIcon />
      case 'prediction':
        return <ScheduleIcon />
      case 'recommendation':
        return <InsightIcon />
      default:
        return <AIIcon />
    }
  }

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'trend':
        return 'info'
      case 'anomaly':
        return 'warning'
      case 'prediction':
        return 'secondary'
      case 'recommendation':
        return 'success'
      default:
        return 'primary'
    }
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high':
        return 'error'
      case 'medium':
        return 'warning'
      case 'low':
        return 'success'
      default:
        return 'default'
    }
  }

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high':
        return theme.palette.error.main
      case 'medium':
        return theme.palette.warning.main
      case 'low':
        return theme.palette.success.main
      default:
        return theme.palette.grey[500]
    }
  }

  // Chart data for engagement prediction trends
  const engagementTrendData = {
    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Predicted'],
    datasets: [
      {
        label: 'Current Trend',
        data: [85, 82, 78, 75, 72],
        borderColor: theme.palette.primary.main,
        backgroundColor: `${theme.palette.primary.main}20`,
        fill: true,
      },
      {
        label: 'AI Prediction',
        data: [null, null, null, 75, 68],
        borderColor: theme.palette.warning.main,
        backgroundColor: `${theme.palette.warning.main}20`,
        borderDash: [5, 5],
        fill: false,
      },
    ],
  }

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
      },
    },
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h6" fontWeight="bold" gutterBottom>
            AI-Powered Insights
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Last updated: {lastUpdated.toLocaleString()}
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={isGenerating ? null : <MagicIcon />}
          onClick={generateNewInsights}
          disabled={isGenerating}
        >
          {isGenerating ? 'Generating...' : 'Generate New Insights'}
        </Button>
      </Box>

      {isGenerating && (
        <Box mb={3}>
          <Alert severity="info" sx={{ mb: 2 }}>
            <Box display="flex" alignItems="center">
              <AIIcon sx={{ mr: 1 }} />
              AI is analyzing your data to generate insights...
            </Box>
          </Alert>
          <LinearProgress />
        </Box>
      )}

      <Grid container spacing={3}>
        {/* AI Insights */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Latest Insights ({insights.length})
              </Typography>
              
              <Box>
                <AnimatePresence>
                  {insights.map((insight, index) => (
                    <motion.div
                      key={insight.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Paper
                        sx={{
                          p: 2,
                          mb: 2,
                          border: 1,
                          borderColor: 'divider',
                          '&:hover': {
                            boxShadow: 2,
                          },
                        }}
                      >
                        <Box display="flex" alignItems="flex-start" mb={2}>
                          <Avatar
                            sx={{
                              bgcolor: `${getInsightColor(insight.type)}.main`,
                              mr: 2,
                              width: 40,
                              height: 40,
                            }}
                          >
                            {getInsightIcon(insight.type)}
                          </Avatar>
                          <Box flex={1}>
                            <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                              <Typography variant="subtitle1" fontWeight="bold">
                                {insight.title}
                              </Typography>
                              <Box display="flex" gap={1}>
                                <Chip
                                  label={insight.type}
                                  size="small"
                                  color={getInsightColor(insight.type) as any}
                                  variant="outlined"
                                />
                                <Chip
                                  label={`${insight.impact} impact`}
                                  size="small"
                                  color={getImpactColor(insight.impact) as any}
                                  variant="outlined"
                                />
                              </Box>
                            </Box>
                            <Typography variant="body2" color="text.secondary" mb={2}>
                              {insight.description}
                            </Typography>
                            <Box display="flex" alignItems="center" justifyContent="space-between">
                              <Box display="flex" alignItems="center">
                                <Typography variant="caption" color="text.secondary" mr={1}>
                                  Confidence:
                                </Typography>
                                <LinearProgress
                                  variant="determinate"
                                  value={insight.confidence}
                                  sx={{ width: 100, mr: 1 }}
                                />
                                <Typography variant="caption" color="text.secondary">
                                  {insight.confidence}%
                                </Typography>
                              </Box>
                              {insight.actionable && (
                                <Button size="small" variant="outlined">
                                  View Actions
                                </Button>
                              )}
                            </Box>
                          </Box>
                        </Box>
                      </Paper>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Engagement Predictions */}
        <Grid item xs={12} lg={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Engagement Risk Alerts
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Learners at risk of disengagement
              </Typography>
              
              <List>
                {predictions.map((prediction) => (
                  <ListItem key={prediction.learnerId} sx={{ px: 0 }}>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: getRiskColor(prediction.riskLevel) }}>
                        <PeopleIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={prediction.learnerName}
                      secondary={
                        <Box>
                          <Typography variant="caption" color="text.secondary">
                            Current: {prediction.currentEngagement}% → Predicted: {prediction.predictedEngagement}%
                          </Typography>
                          <br />
                          <Chip
                            label={`${prediction.riskLevel} risk`}
                            size="small"
                            sx={{
                              bgcolor: getRiskColor(prediction.riskLevel),
                              color: 'white',
                              mt: 0.5,
                            }}
                          />
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>

          {/* Engagement Trend Chart */}
          <Card sx={{ mt: 2 }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Engagement Trend Prediction
              </Typography>
              <Box height={200}>
                <Line data={engagementTrendData} options={chartOptions} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* AI Recommendations Summary */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                AI Recommendations Summary
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <TrendingUpIcon color="success" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="h4" color="success.main" fontWeight="bold">
                      12
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Actionable Insights
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <WarningIcon color="warning" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="h4" color="warning.main" fontWeight="bold">
                      5
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Risk Alerts
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <ScheduleIcon color="info" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="h4" color="info.main" fontWeight="bold">
                      8
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Predictions
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <InsightIcon color="secondary" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="h4" color="secondary.main" fontWeight="bold">
                      92%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Avg Confidence
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  )
}
