import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'placeholder-service-key'

// Client for user-facing operations (with RLS)
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Admin client for settings and admin operations (bypasses RLS)
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Types for our database schema
export interface Database {
  public: {
    Tables: {
      tenants: {
        Row: {
          id: number
          name: string
          created_at: string
        }
        Insert: {
          id?: number
          name: string
          created_at?: string
        }
        Update: {
          id?: number
          name?: string
          created_at?: string
        }
      }
      users: {
        Row: {
          id: string
          tenant_id: number
          email: string
          role_id: number
          full_name?: string
          avatar_url?: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          tenant_id: number
          email: string
          role_id: number
          full_name?: string
          avatar_url?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          tenant_id?: number
          email?: string
          role_id?: number
          full_name?: string
          avatar_url?: string
          created_at?: string
          updated_at?: string
        }
      }
      roles: {
        Row: {
          id: number
          tenant_id: number
          name: string
          permissions: string[]
          created_at: string
        }
        Insert: {
          id?: number
          tenant_id: number
          name: string
          permissions: string[]
          created_at?: string
        }
        Update: {
          id?: number
          tenant_id?: number
          name?: string
          permissions?: string[]
          created_at?: string
        }
      }
      learning_paths: {
        Row: {
          id: number
          tenant_id: number
          title: string
          description?: string
          objectives?: string[]
          prerequisites?: string[]
          difficulty_level?: string
          estimated_duration?: number
          is_live: boolean
          is_featured?: boolean
          category?: string
          tags?: string[]
          thumbnail_url?: string
          created_by?: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          tenant_id: number
          title: string
          description?: string
          objectives?: string[]
          prerequisites?: string[]
          difficulty_level?: string
          estimated_duration?: number
          is_live?: boolean
          is_featured?: boolean
          category?: string
          tags?: string[]
          thumbnail_url?: string
          created_by?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          tenant_id?: number
          title?: string
          description?: string
          objectives?: string[]
          prerequisites?: string[]
          difficulty_level?: string
          estimated_duration?: number
          is_live?: boolean
          is_featured?: boolean
          category?: string
          tags?: string[]
          thumbnail_url?: string
          created_by?: string
          created_at?: string
          updated_at?: string
        }
      }
      modules: {
        Row: {
          id: number
          path_id: number
          title: string
          description?: string
          order_index: number
          is_optional?: boolean
          unlock_conditions?: any
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          path_id: number
          title: string
          description?: string
          order_index: number
          is_optional?: boolean
          unlock_conditions?: any
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          path_id?: number
          title?: string
          description?: string
          order_index?: number
          is_optional?: boolean
          unlock_conditions?: any
          created_at?: string
          updated_at?: string
        }
      }
      lessons: {
        Row: {
          id: number
          module_id: number
          title: string
          description?: string
          type: 'video' | 'pdf' | 'quiz' | 'text' | 'interactive' | 'scorm'
          content_url?: string
          content_text?: string
          content_metadata?: any
          order_index: number
          estimated_duration?: number
          is_mandatory?: boolean
          passing_score?: number
          max_attempts?: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          module_id: number
          title: string
          description?: string
          type: 'video' | 'pdf' | 'quiz' | 'text' | 'interactive' | 'scorm'
          content_url?: string
          content_text?: string
          content_metadata?: any
          order_index: number
          estimated_duration?: number
          is_mandatory?: boolean
          passing_score?: number
          max_attempts?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          module_id?: number
          title?: string
          description?: string
          type?: 'video' | 'pdf' | 'quiz' | 'text' | 'interactive' | 'scorm'
          content_url?: string
          content_text?: string
          content_metadata?: any
          order_index?: number
          estimated_duration?: number
          is_mandatory?: boolean
          passing_score?: number
          max_attempts?: number
          created_at?: string
          updated_at?: string
        }
      }
      learner_assignments: {
        Row: {
          learner_id: string
          path_id: number
          assigned_at: string
          due_date?: string
        }
        Insert: {
          learner_id: string
          path_id: number
          assigned_at?: string
          due_date?: string
        }
        Update: {
          learner_id?: string
          path_id?: number
          assigned_at?: string
          due_date?: string
        }
      }
      progress: {
        Row: {
          id: number
          learner_id: string
          lesson_id: number
          status: 'not_started' | 'in_progress' | 'completed'
          score?: number
          completed_at?: string
          created_at: string
        }
        Insert: {
          id?: number
          learner_id: string
          lesson_id: number
          status?: 'not_started' | 'in_progress' | 'completed'
          score?: number
          completed_at?: string
          created_at?: string
        }
        Update: {
          id?: number
          learner_id?: string
          lesson_id?: number
          status?: 'not_started' | 'in_progress' | 'completed'
          score?: number
          completed_at?: string
          created_at?: string
        }
      }
      groups: {
        Row: {
          id: number
          tenant_id: number
          name: string
          description?: string
          start_date?: string
          end_date?: string
          status: 'active' | 'inactive' | 'archived' | 'draft'
          tags?: string[]
          parent_group_id?: number
          settings?: any
          created_by: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          tenant_id: number
          name: string
          description?: string
          start_date?: string
          end_date?: string
          status?: 'active' | 'inactive' | 'archived' | 'draft'
          tags?: string[]
          parent_group_id?: number
          settings?: any
          created_by: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          tenant_id?: number
          name?: string
          description?: string
          start_date?: string
          end_date?: string
          status?: 'active' | 'inactive' | 'archived' | 'draft'
          tags?: string[]
          parent_group_id?: number
          settings?: any
          created_by?: string
          created_at?: string
          updated_at?: string
        }
      }
      group_members: {
        Row: {
          id: number
          group_id: number
          learner_id: string
          role: 'member' | 'moderator' | 'admin'
          joined_at: string
          added_by: string
        }
        Insert: {
          id?: number
          group_id: number
          learner_id: string
          role?: 'member' | 'moderator' | 'admin'
          joined_at?: string
          added_by: string
        }
        Update: {
          id?: number
          group_id?: number
          learner_id?: string
          role?: 'member' | 'moderator' | 'admin'
          joined_at?: string
          added_by?: string
        }
      }
      group_assignments: {
        Row: {
          id: number
          group_id: number
          path_id: number
          assigned_by: string
          assigned_at: string
          start_date?: string
          due_date?: string
          is_mandatory: boolean
          settings?: any
        }
        Insert: {
          id?: number
          group_id: number
          path_id: number
          assigned_by: string
          assigned_at?: string
          start_date?: string
          due_date?: string
          is_mandatory?: boolean
          settings?: any
        }
        Update: {
          id?: number
          group_id?: number
          path_id?: number
          assigned_by?: string
          assigned_at?: string
          start_date?: string
          due_date?: string
          is_mandatory?: boolean
          settings?: any
        }
      }
      group_progress: {
        Row: {
          id: number
          group_id: number
          path_id: number
          total_members: number
          completed_members: number
          in_progress_members: number
          not_started_members: number
          average_score?: number
          completion_rate: number
          updated_at: string
        }
        Insert: {
          id?: number
          group_id: number
          path_id: number
          total_members: number
          completed_members?: number
          in_progress_members?: number
          not_started_members?: number
          average_score?: number
          completion_rate?: number
          updated_at?: string
        }
        Update: {
          id?: number
          group_id?: number
          path_id?: number
          total_members?: number
          completed_members?: number
          in_progress_members?: number
          not_started_members?: number
          average_score?: number
          completion_rate?: number
          updated_at?: string
        }
      }
      group_messages: {
        Row: {
          id: number
          group_id: number
          sender_id: string
          message: string
          message_type: 'announcement' | 'chat' | 'system'
          channels: string[]
          metadata?: any
          sent_at: string
        }
        Insert: {
          id?: number
          group_id: number
          sender_id: string
          message: string
          message_type?: 'announcement' | 'chat' | 'system'
          channels?: string[]
          metadata?: any
          sent_at?: string
        }
        Update: {
          id?: number
          group_id?: number
          sender_id?: string
          message?: string
          message_type?: 'announcement' | 'chat' | 'system'
          channels?: string[]
          metadata?: any
          sent_at?: string
        }
      }
      group_milestones: {
        Row: {
          id: number
          group_id: number
          title: string
          description?: string
          due_date: string
          completion_criteria?: any
          is_completed: boolean
          completed_at?: string
          created_by: string
          created_at: string
        }
        Insert: {
          id?: number
          group_id: number
          title: string
          description?: string
          due_date: string
          completion_criteria?: any
          is_completed?: boolean
          completed_at?: string
          created_by: string
          created_at?: string
        }
        Update: {
          id?: number
          group_id?: number
          title?: string
          description?: string
          due_date?: string
          completion_criteria?: any
          is_completed?: boolean
          completed_at?: string
          created_by?: string
          created_at?: string
        }
      }
      assignment_templates: {
        Row: {
          id: number
          tenant_id: number
          name: string
          description?: string
          template_data: any
          created_by: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          tenant_id: number
          name: string
          description?: string
          template_data: any
          created_by: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          tenant_id?: number
          name?: string
          description?: string
          template_data?: any
          created_by?: string
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
