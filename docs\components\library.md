# Component Library Documentation

This document provides comprehensive documentation for all React components in the Groups and Batches feature, including props, usage examples, and customization options.

## 📦 Component Overview

The Groups and Batches feature includes 15+ React components organized into logical groups:

```
components/groups/
├── GroupsDashboard.tsx      # Main dashboard with grid/list views
├── GroupForm.tsx            # Create/edit group modal
├── GroupFilters.tsx         # Advanced filtering component
├── MemberManagement.tsx     # Member operations and management
├── GroupAssignments.tsx     # Learning path assignment management
├── GroupProgress.tsx        # Progress tracking and analytics
├── GroupCommunication.tsx   # Messaging and announcements
├── GroupMilestones.tsx      # Timeline and milestone management
└── shared/                  # Shared utility components
    ├── GroupCard.tsx        # Individual group card component
    ├── MemberCard.tsx       # Individual member card component
    └── ProgressChart.tsx    # Reusable progress visualization
```

## 🎛️ Core Components

### GroupsDashboard

Main dashboard component for displaying and managing groups.

**Props:**
```typescript
interface GroupsDashboardProps {
  groups: Group[]
  loading: boolean
  viewMode: 'grid' | 'list'
  selectedGroups: number[]
  onCreateGroup: () => void
  onEditGroup: (group: Group) => void
  onDeleteGroup: (groupId: number) => void
  onViewGroup: (group: Group) => void
}
```

**Usage:**
```tsx
import GroupsDashboard from '@/components/groups/GroupsDashboard'

function GroupsPage() {
  const { groups, loading } = useGroupsStore()
  
  return (
    <GroupsDashboard
      groups={groups}
      loading={loading}
      viewMode="grid"
      selectedGroups={[]}
      onCreateGroup={() => setCreateModalOpen(true)}
      onEditGroup={(group) => setEditingGroup(group)}
      onDeleteGroup={(id) => handleDeleteGroup(id)}
      onViewGroup={(group) => router.push(`/groups/${group.id}`)}
    />
  )
}
```

**Features:**
- Grid and list view modes with smooth transitions
- Bulk selection and operations
- Real-time data updates
- Advanced filtering integration
- Responsive design for all screen sizes
- Accessibility compliance (WCAG 2.1 AA)

**Customization:**
```tsx
// Custom styling
<GroupsDashboard
  groups={groups}
  loading={loading}
  sx={{
    '& .group-card': {
      borderRadius: 2,
      boxShadow: 3
    }
  }}
  cardProps={{
    elevation: 2,
    variant: 'outlined'
  }}
/>
```

### GroupForm

Modal component for creating and editing groups.

**Props:**
```typescript
interface GroupFormProps {
  open: boolean
  onClose: () => void
  onSubmit: (data: Partial<Group>) => void
  group?: Group | null
  title?: string
  loading?: boolean
}
```

**Usage:**
```tsx
import GroupForm from '@/components/groups/GroupForm'

function GroupManagement() {
  const [formOpen, setFormOpen] = useState(false)
  const [editingGroup, setEditingGroup] = useState<Group | null>(null)
  
  return (
    <GroupForm
      open={formOpen}
      onClose={() => setFormOpen(false)}
      onSubmit={handleSubmit}
      group={editingGroup}
      title={editingGroup ? 'Edit Group' : 'Create Group'}
      loading={isSubmitting}
    />
  )
}
```

**Features:**
- Multi-step form with validation
- Rich settings configuration
- Tag management with autocomplete
- Date validation and scheduling
- Real-time form validation
- Accessibility support

### GroupFilters

Advanced filtering component with multiple criteria.

**Props:**
```typescript
interface GroupFiltersProps {
  filters: Partial<GroupFilters>
  onFiltersChange: (filters: Partial<GroupFilters>) => void
  onReset: () => void
}
```

**Usage:**
```tsx
import GroupFilters from '@/components/groups/GroupFilters'

function FilterableGroupList() {
  const { filters, setFilters } = useGroupsStore()
  
  return (
    <GroupFilters
      filters={filters}
      onFiltersChange={setFilters}
      onReset={() => setFilters({})}
    />
  )
}
```

**Features:**
- Text search with debouncing
- Multi-select tag filtering
- Date range selection
- Numeric range sliders
- Advanced collapsible options
- Filter persistence

## 👥 Member Management Components

### MemberManagement

Comprehensive member management interface.

**Props:**
```typescript
interface MemberManagementProps {
  group: Group
  members: GroupMember[]
  onRefresh: () => void
}
```

**Usage:**
```tsx
import MemberManagement from '@/components/groups/MemberManagement'

function GroupDetailPage() {
  const { selectedGroup, members } = useGroupsStore()
  
  return (
    <MemberManagement
      group={selectedGroup}
      members={members}
      onRefresh={loadGroupDetails}
    />
  )
}
```

**Features:**
- DataGrid with sorting and filtering
- Bulk member addition via search or CSV
- Role management with permissions
- Drag-and-drop member transfer
- Real-time member status updates
- Export capabilities

**Sub-components:**
- `AddMembersDialog`: Modal for adding new members
- `MemberCard`: Individual member display card
- `RoleSelector`: Role assignment component
- `BulkUploader`: CSV/Excel upload interface

## 📚 Assignment Components

### GroupAssignments

Learning path assignment management.

**Props:**
```typescript
interface GroupAssignmentsProps {
  group: Group
  assignments: GroupAssignment[]
  onRefresh: () => void
}
```

**Usage:**
```tsx
import GroupAssignments from '@/components/groups/GroupAssignments'

function AssignmentManagement() {
  const { selectedGroup, assignments } = useGroupsStore()
  
  return (
    <GroupAssignments
      group={selectedGroup}
      assignments={assignments}
      onRefresh={loadAssignments}
    />
  )
}
```

**Features:**
- Assignment wizard with step-by-step flow
- Scheduling with date validation
- Mandatory vs optional assignment types
- Template-based assignments
- Bulk assignment operations
- Progress tracking integration

**Sub-components:**
- `AssignPathDialog`: Modal for path assignment
- `AssignmentCard`: Individual assignment display
- `SchedulingPicker`: Date and time selection
- `TemplateSelector`: Assignment template chooser

## 📊 Analytics Components

### GroupProgress

Progress tracking and analytics dashboard.

**Props:**
```typescript
interface GroupProgressProps {
  group: Group
  progress: GroupProgress[]
  onRefresh: () => void
}
```

**Usage:**
```tsx
import GroupProgress from '@/components/groups/GroupProgress'

function ProgressDashboard() {
  const { selectedGroup, progress } = useGroupsStore()
  
  return (
    <GroupProgress
      group={selectedGroup}
      progress={progress}
      onRefresh={refreshProgress}
    />
  )
}
```

**Features:**
- Interactive charts with Chart.js
- Multi-tab interface (Overview, Details, Analytics)
- Real-time progress updates
- Export and reporting capabilities
- Drill-down to individual learner progress
- Customizable chart types

**Chart Components:**
- `CompletionTrendChart`: Line chart for progress over time
- `StatusDistributionChart`: Doughnut chart for status breakdown
- `PathComparisonChart`: Bar chart for path comparison
- `ProgressHeatmap`: Heatmap for detailed analysis

## 💬 Communication Components

### GroupCommunication

Messaging and communication hub.

**Props:**
```typescript
interface GroupCommunicationProps {
  group: Group
  onRefresh: () => void
}
```

**Usage:**
```tsx
import GroupCommunication from '@/components/groups/GroupCommunication'

function CommunicationHub() {
  const { selectedGroup } = useGroupsStore()
  
  return (
    <GroupCommunication
      group={selectedGroup}
      onRefresh={loadMessages}
    />
  )
}
```

**Features:**
- Multi-channel message composition
- Rich text editor with @tiptap/react
- Message history with threading
- Real-time chat with Supabase Realtime
- File attachments and media sharing
- Message priority and delivery status

**Sub-components:**
- `MessageComposer`: Rich text message editor
- `MessageList`: Scrollable message history
- `ChannelSelector`: Multi-channel delivery options
- `AttachmentUploader`: File upload interface

## 🎯 Milestone Components

### GroupMilestones

Timeline and milestone management.

**Props:**
```typescript
interface GroupMilestonesProps {
  group: Group
  onRefresh: () => void
}
```

**Usage:**
```tsx
import GroupMilestones from '@/components/groups/GroupMilestones'

function MilestoneTracking() {
  const { selectedGroup } = useGroupsStore()
  
  return (
    <GroupMilestones
      group={selectedGroup}
      onRefresh={loadMilestones}
    />
  )
}
```

**Features:**
- Visual timeline with Material-UI Timeline
- Milestone creation and editing
- Progress tracking and completion
- Automated reminders and notifications
- Achievement badges and celebrations
- Calendar integration

**Sub-components:**
- `MilestoneDialog`: Modal for milestone creation/editing
- `TimelineView`: Visual timeline component
- `MilestoneCard`: Individual milestone display
- `ProgressIndicator`: Completion progress visualization

## 🔧 Utility Components

### Shared Components

Reusable components used across the feature.

**GroupCard:**
```typescript
interface GroupCardProps {
  group: Group
  onClick?: (group: Group) => void
  onEdit?: (group: Group) => void
  onDelete?: (groupId: number) => void
  selected?: boolean
  variant?: 'default' | 'compact' | 'detailed'
}
```

**MemberCard:**
```typescript
interface MemberCardProps {
  member: GroupMember
  onClick?: (member: GroupMember) => void
  showRole?: boolean
  showProgress?: boolean
  variant?: 'default' | 'compact' | 'detailed'
}
```

**ProgressChart:**
```typescript
interface ProgressChartProps {
  data: any[]
  type: 'line' | 'bar' | 'doughnut' | 'radar'
  options?: ChartOptions
  height?: number
  responsive?: boolean
}
```

## 🎨 Theming and Customization

### Theme Integration

All components integrate with Material-UI theming:

```tsx
import { createTheme, ThemeProvider } from '@mui/material/styles'

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
  components: {
    // Custom component overrides
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        },
      },
    },
  },
})

function App() {
  return (
    <ThemeProvider theme={theme}>
      <GroupsDashboard {...props} />
    </ThemeProvider>
  )
}
```

### Custom Styling

Components accept custom styling through the `sx` prop:

```tsx
<GroupsDashboard
  sx={{
    '& .group-card': {
      transition: 'all 0.3s ease',
      '&:hover': {
        transform: 'translateY(-4px)',
        boxShadow: 4,
      },
    },
    '& .group-grid': {
      gap: 3,
    },
  }}
/>
```

### Animation Customization

Framer Motion animations can be customized:

```tsx
<GroupCard
  initial={{ opacity: 0, y: 20 }}
  animate={{ opacity: 1, y: 0 }}
  transition={{ duration: 0.3, delay: index * 0.1 }}
  whileHover={{ scale: 1.02 }}
  whileTap={{ scale: 0.98 }}
/>
```

## 🔍 Testing Components

### Unit Testing

Components are designed for easy testing:

```tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { ThemeProvider } from '@mui/material/styles'
import GroupForm from '@/components/groups/GroupForm'

describe('GroupForm', () => {
  it('renders create form correctly', () => {
    render(
      <ThemeProvider theme={theme}>
        <GroupForm
          open={true}
          onClose={jest.fn()}
          onSubmit={jest.fn()}
        />
      </ThemeProvider>
    )
    
    expect(screen.getByText('Create Group')).toBeInTheDocument()
    expect(screen.getByLabelText('Group Name')).toBeInTheDocument()
  })
  
  it('validates required fields', async () => {
    const onSubmit = jest.fn()
    render(
      <GroupForm
        open={true}
        onClose={jest.fn()}
        onSubmit={onSubmit}
      />
    )
    
    fireEvent.click(screen.getByText('Create Group'))
    
    expect(screen.getByText('Group name is required')).toBeInTheDocument()
    expect(onSubmit).not.toHaveBeenCalled()
  })
})
```

### Integration Testing

Test component interactions:

```tsx
import { renderWithProviders } from '@/test-utils'
import GroupsDashboard from '@/components/groups/GroupsDashboard'

describe('GroupsDashboard Integration', () => {
  it('handles group creation flow', async () => {
    const { user } = renderWithProviders(
      <GroupsDashboard {...mockProps} />
    )
    
    await user.click(screen.getByText('Create Group'))
    await user.type(screen.getByLabelText('Group Name'), 'Test Group')
    await user.click(screen.getByText('Create'))
    
    expect(mockProps.onCreateGroup).toHaveBeenCalled()
  })
})
```

## 📱 Responsive Design

All components are fully responsive:

```tsx
// Responsive grid layout
<Grid container spacing={{ xs: 2, md: 3 }}>
  <Grid item xs={12} sm={6} md={4} lg={3}>
    <GroupCard group={group} />
  </Grid>
</Grid>

// Responsive typography
<Typography 
  variant="h4" 
  sx={{
    fontSize: { xs: '1.5rem', sm: '2rem', md: '2.5rem' }
  }}
>
  Groups Dashboard
</Typography>

// Responsive spacing
<Box sx={{ p: { xs: 2, sm: 3, md: 4 } }}>
  Content
</Box>
```

## ♿ Accessibility Features

Components include comprehensive accessibility support:

- **Keyboard Navigation**: Full keyboard support for all interactions
- **Screen Reader Support**: Proper ARIA labels and descriptions
- **Focus Management**: Logical focus order and visible focus indicators
- **Color Contrast**: WCAG 2.1 AA compliant color schemes
- **Semantic HTML**: Proper heading hierarchy and landmark regions

```tsx
// Accessibility example
<Button
  aria-label="Create new group"
  aria-describedby="create-group-help"
  onClick={onCreateGroup}
>
  Create Group
</Button>
<Typography id="create-group-help" variant="caption">
  Opens a dialog to create a new learning group
</Typography>
```

This component library provides a comprehensive foundation for building the Groups and Batches feature with consistent design, robust functionality, and excellent user experience.
