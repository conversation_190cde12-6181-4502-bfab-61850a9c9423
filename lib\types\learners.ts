// Learner Types for ZenithLearn AI

export interface Learner {
  id: string
  email: string
  full_name: string
  avatar_url?: string
  tenant_id: number
  role_id: number
  role?: Role
  status: 'active' | 'inactive' | 'suspended'
  last_active?: string
  created_at: string
  updated_at: string
  // Custom fields
  department?: string
  position?: string
  manager_id?: string
  hire_date?: string
  phone?: string
  location?: string
  // Computed fields
  progress?: LearnerProgress
  assignments?: LearnerAssignment[]
  groups?: Group[]
  skills?: LearnerSkill[]
  engagement_score?: number
  completion_rate?: number
  total_paths?: number
  completed_paths?: number
  active_paths?: number
  overdue_assignments?: number
}

export interface Role {
  id: number
  name: string
  description?: string
  permissions: string[]
  tenant_id: number
}

export interface LearnerProgress {
  user_id: string
  path_id: number
  lesson_id?: number
  status: 'not_started' | 'in_progress' | 'completed' | 'failed'
  progress_percentage: number
  score?: number
  time_spent: number // in minutes
  last_accessed?: string
  completed_at?: string
  created_at: string
  updated_at: string
}

export interface LearnerAssignment {
  id: number
  path_id: number
  learner_id: string
  assigned_by: string
  assigned_at: string
  due_date?: string
  status: 'assigned' | 'in_progress' | 'completed' | 'overdue' | 'cancelled'
  completion_percentage: number
  completed_at?: string
  notes?: string
  // Related data
  path?: {
    id: number
    title: string
    difficulty_level: string
    estimated_duration: number
  }
  assigned_by_user?: {
    id: string
    full_name: string
    email: string
  }
}

export interface Group {
  id: number
  name: string
  description?: string
  tenant_id: number
  created_by: string
  created_at: string
  updated_at: string
  // Computed fields
  member_count?: number
  members?: GroupMember[]
}

export interface GroupMember {
  id: number
  group_id: number
  user_id: string
  role: 'member' | 'admin'
  joined_at: string
  // Related data
  user?: Pick<Learner, 'id' | 'full_name' | 'email' | 'avatar_url'>
}

export interface LearnerSkill {
  id: number
  user_id: string
  skill_name: string
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  verified: boolean
  acquired_at?: string
  expires_at?: string
  source: 'self_assessed' | 'path_completion' | 'assessment' | 'manager_assigned'
}

export interface EngagementAlert {
  id: number
  user_id: string
  type: 'low_activity' | 'overdue_assignment' | 'poor_performance' | 'at_risk'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  suggested_actions: string[]
  created_at: string
  resolved_at?: string
  resolved_by?: string
}

export interface LearnerFilters {
  search?: string
  status?: 'all' | 'active' | 'inactive' | 'suspended'
  role?: 'all' | string
  group?: 'all' | number
  department?: 'all' | string
  engagement_risk?: 'all' | 'low' | 'medium' | 'high'
  completion_rate?: {
    min?: number
    max?: number
  }
  last_active?: {
    days?: number
  }
  has_overdue?: boolean
}

export interface LearnerSortOptions {
  field: 'full_name' | 'email' | 'last_active' | 'completion_rate' | 'engagement_score' | 'created_at'
  direction: 'asc' | 'desc'
}

export interface BulkAction {
  type: 'assign_path' | 'add_to_group' | 'remove_from_group' | 'suspend' | 'activate' | 'send_message'
  data: any
}

export interface LearnerAnalytics {
  total_learners: number
  active_learners: number
  inactive_learners: number
  suspended_learners: number
  average_completion_rate: number
  average_engagement_score: number
  at_risk_learners: number
  overdue_assignments: number
  top_performers: Learner[]
  engagement_trends: {
    date: string
    active_users: number
    completions: number
    new_registrations: number
  }[]
}

export interface LearnerJourneyEvent {
  id: string
  user_id: string
  type: 'registration' | 'login' | 'path_assigned' | 'lesson_started' | 'lesson_completed' | 'path_completed' | 'quiz_taken' | 'badge_earned'
  title: string
  description?: string
  metadata?: any
  timestamp: string
}

export interface CustomField {
  id: number
  tenant_id: number
  field_name: string
  field_type: 'text' | 'number' | 'date' | 'select' | 'multiselect' | 'boolean'
  field_label: string
  field_options?: string[] // For select/multiselect
  is_required: boolean
  is_searchable: boolean
  display_order: number
  created_at: string
}

export interface LearnerCustomFieldValue {
  id: number
  user_id: string
  field_id: number
  value: string | number | boolean | string[]
  updated_at: string
}

// API Response Types
export interface LearnersResponse {
  data: Learner[]
  total: number
  page: number
  limit: number
  has_more: boolean
}

export interface LearnerStatsResponse {
  analytics: LearnerAnalytics
  recent_activity: LearnerJourneyEvent[]
  alerts: EngagementAlert[]
}

// Form Types
export interface CreateLearnerData {
  email: string
  full_name: string
  role_id: number
  department?: string
  position?: string
  manager_id?: string
  phone?: string
  location?: string
  send_welcome_email?: boolean
  custom_fields?: Record<string, any>
}

export interface UpdateLearnerData extends Partial<CreateLearnerData> {
  status?: 'active' | 'inactive' | 'suspended'
  avatar_url?: string
}

export interface BulkImportData {
  file: File
  mapping: Record<string, string> // CSV column to field mapping
  send_welcome_emails: boolean
  default_role_id: number
}

export interface AssignPathData {
  path_id: number
  learner_ids: string[]
  due_date?: string
  notes?: string
  send_notification?: boolean
}

export interface CreateGroupData {
  name: string
  description?: string
  member_ids?: string[]
}

export interface MessageData {
  recipient_ids: string[]
  subject: string
  content: string
  send_email?: boolean
  send_notification?: boolean
}
