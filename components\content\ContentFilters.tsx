'use client'

import { useState, useCallback } from 'react'
import {
  Box,
  Card,
  CardContent,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Autocomplete,
  Button,
  Grid,
  Typography,
  Slider,
  Switch,
  FormControlLabel,
  Divider,
  IconButton,
  Collapse
} from '@mui/material'
import {
  Search as SearchIcon,
  Clear as ClearIcon,
  FilterList as FilterIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon
} from '@mui/icons-material'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { motion } from 'framer-motion'

import type { ContentFilter, ContentCategory, ContentType } from '@/lib/types/content'

interface ContentFiltersProps {
  filter: ContentFilter
  onFilterChange: (filter: Partial<ContentFilter>) => void
  categories: ContentCategory[]
}

const CONTENT_TYPES: { value: ContentType; label: string }[] = [
  { value: 'video', label: 'Video' },
  { value: 'pdf', label: 'PDF' },
  { value: 'document', label: 'Document' },
  { value: 'presentation', label: 'Presentation' },
  { value: 'image', label: 'Image' },
  { value: 'audio', label: 'Audio' },
  { value: 'scorm', label: 'SCORM' },
  { value: 'quiz', label: 'Quiz' },
  { value: 'interactive', label: 'Interactive' }
]

const SORT_OPTIONS = [
  { value: 'title', label: 'Title' },
  { value: 'created_at', label: 'Date Created' },
  { value: 'updated_at', label: 'Date Modified' },
  { value: 'file_size', label: 'File Size' },
  { value: 'view_count', label: 'Views' }
]

const ContentFilters = ({ filter, onFilterChange, categories }: ContentFiltersProps) => {
  const [expanded, setExpanded] = useState(false)
  const [searchValue, setSearchValue] = useState(filter.search || '')

  const handleSearchChange = useCallback((value: string) => {
    setSearchValue(value)
    // Debounce search
    const timeoutId = setTimeout(() => {
      onFilterChange({ search: value || undefined })
    }, 300)
    
    return () => clearTimeout(timeoutId)
  }, [onFilterChange])

  const handleTypeChange = useCallback((types: ContentType[]) => {
    onFilterChange({ type: types.length > 0 ? types : undefined })
  }, [onFilterChange])

  const handleCategoryChange = useCallback((categoryIds: number[]) => {
    onFilterChange({ category_id: categoryIds.length > 0 ? categoryIds : undefined })
  }, [onFilterChange])

  const handleTagsChange = useCallback((tags: string[]) => {
    onFilterChange({ tags: tags.length > 0 ? tags : undefined })
  }, [onFilterChange])

  const handleDateRangeChange = useCallback((field: 'start' | 'end', date: Date | null) => {
    const currentRange = filter.date_range || { start: '', end: '' }
    const newRange = {
      ...currentRange,
      [field]: date ? date.toISOString() : ''
    }
    
    if (newRange.start || newRange.end) {
      onFilterChange({ date_range: newRange })
    } else {
      onFilterChange({ date_range: undefined })
    }
  }, [filter.date_range, onFilterChange])

  const handleFileSizeChange = useCallback((range: number[]) => {
    if (range[0] === 0 && range[1] === 1000) {
      onFilterChange({ file_size_range: undefined })
    } else {
      onFilterChange({
        file_size_range: {
          min: range[0] * 1024 * 1024, // Convert MB to bytes
          max: range[1] * 1024 * 1024
        }
      })
    }
  }, [onFilterChange])

  const handleSortChange = useCallback((sortBy: string, sortOrder: 'asc' | 'desc') => {
    onFilterChange({ sort_by: sortBy as any, sort_order: sortOrder })
  }, [onFilterChange])

  const handleClearFilters = useCallback(() => {
    setSearchValue('')
    onFilterChange({
      search: undefined,
      type: undefined,
      category_id: undefined,
      tags: undefined,
      date_range: undefined,
      file_size_range: undefined,
      is_public: undefined,
      sort_by: 'created_at',
      sort_order: 'desc'
    })
  }, [onFilterChange])

  const hasActiveFilters = !!(
    filter.search ||
    filter.type?.length ||
    filter.category_id?.length ||
    filter.tags?.length ||
    filter.date_range ||
    filter.file_size_range ||
    filter.is_public !== undefined
  )

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card sx={{ mb: 2 }}>
        <CardContent>
          {/* Search and Quick Filters */}
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Search content..."
                value={searchValue}
                onChange={(e) => handleSearchChange(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                  endAdornment: searchValue && (
                    <IconButton
                      size="small"
                      onClick={() => handleSearchChange('')}
                    >
                      <ClearIcon />
                    </IconButton>
                  )
                }}
              />
            </Grid>
            
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Sort By</InputLabel>
                <Select
                  value={filter.sort_by || 'created_at'}
                  label="Sort By"
                  onChange={(e) => handleSortChange(e.target.value, filter.sort_order || 'desc')}
                >
                  {SORT_OPTIONS.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Order</InputLabel>
                <Select
                  value={filter.sort_order || 'desc'}
                  label="Order"
                  onChange={(e) => handleSortChange(filter.sort_by || 'created_at', e.target.value as 'asc' | 'desc')}
                >
                  <MenuItem value="desc">Newest First</MenuItem>
                  <MenuItem value="asc">Oldest First</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={1}>
              <Box display="flex" justifyContent="center">
                <IconButton
                  onClick={() => setExpanded(!expanded)}
                  color={expanded ? 'primary' : 'default'}
                >
                  {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              </Box>
            </Grid>
          </Grid>

          {/* Advanced Filters */}
          <Collapse in={expanded}>
            <Box mt={3}>
              <Divider sx={{ mb: 3 }} />
              
              <Grid container spacing={3}>
                {/* Content Types */}
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Content Types
                  </Typography>
                  <Autocomplete
                    multiple
                    options={CONTENT_TYPES}
                    getOptionLabel={(option) => option.label}
                    value={CONTENT_TYPES.filter(type => filter.type?.includes(type.value))}
                    onChange={(_, newValue) => handleTypeChange(newValue.map(v => v.value))}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip
                          variant="outlined"
                          label={option.label}
                          {...getTagProps({ index })}
                          key={option.value}
                        />
                      ))
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        placeholder="Select content types..."
                        variant="outlined"
                      />
                    )}
                  />
                </Grid>

                {/* Categories */}
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Categories
                  </Typography>
                  <Autocomplete
                    multiple
                    options={categories}
                    getOptionLabel={(option) => option.name}
                    value={categories.filter(cat => filter.category_id?.includes(cat.id))}
                    onChange={(_, newValue) => handleCategoryChange(newValue.map(v => v.id))}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip
                          variant="outlined"
                          label={option.name}
                          {...getTagProps({ index })}
                          key={option.id}
                        />
                      ))
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        placeholder="Select categories..."
                        variant="outlined"
                      />
                    )}
                  />
                </Grid>

                {/* Tags */}
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Tags
                  </Typography>
                  <Autocomplete
                    multiple
                    freeSolo
                    options={[]} // Would be populated with existing tags
                    value={filter.tags || []}
                    onChange={(_, newValue) => handleTagsChange(newValue)}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip
                          variant="outlined"
                          label={option}
                          {...getTagProps({ index })}
                          key={option}
                        />
                      ))
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        placeholder="Add tags..."
                        variant="outlined"
                      />
                    )}
                  />
                </Grid>

                {/* File Size Range */}
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    File Size (MB)
                  </Typography>
                  <Box px={2}>
                    <Slider
                      value={[
                        filter.file_size_range?.min ? filter.file_size_range.min / (1024 * 1024) : 0,
                        filter.file_size_range?.max ? filter.file_size_range.max / (1024 * 1024) : 1000
                      ]}
                      onChange={(_, newValue) => handleFileSizeChange(newValue as number[])}
                      valueLabelDisplay="auto"
                      min={0}
                      max={1000}
                      marks={[
                        { value: 0, label: '0MB' },
                        { value: 100, label: '100MB' },
                        { value: 500, label: '500MB' },
                        { value: 1000, label: '1GB' }
                      ]}
                    />
                  </Box>
                </Grid>

                {/* Date Range */}
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Date Range
                  </Typography>
                  <Box display="flex" gap={2}>
                    <DatePicker
                      label="From"
                      value={filter.date_range?.start ? new Date(filter.date_range.start) : null}
                      onChange={(date) => handleDateRangeChange('start', date)}
                      slotProps={{ textField: { size: 'small' } }}
                    />
                    <DatePicker
                      label="To"
                      value={filter.date_range?.end ? new Date(filter.date_range.end) : null}
                      onChange={(date) => handleDateRangeChange('end', date)}
                      slotProps={{ textField: { size: 'small' } }}
                    />
                  </Box>
                </Grid>

                {/* Visibility */}
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Visibility
                  </Typography>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={filter.is_public === true}
                        onChange={(e) => onFilterChange({ 
                          is_public: e.target.checked ? true : undefined 
                        })}
                      />
                    }
                    label="Public content only"
                  />
                </Grid>
              </Grid>

              {/* Filter Actions */}
              <Box mt={3} display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="body2" color="text.secondary">
                  {hasActiveFilters && 'Active filters applied'}
                </Typography>
                
                <Button
                  variant="outlined"
                  startIcon={<ClearIcon />}
                  onClick={handleClearFilters}
                  disabled={!hasActiveFilters}
                >
                  Clear All Filters
                </Button>
              </Box>
            </Box>
          </Collapse>
        </CardContent>
      </Card>
    </motion.div>
  )
}

export default ContentFilters
