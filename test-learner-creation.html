<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Learner Creation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>Test Learner Creation</h1>
    
    <form id="learnerForm">
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" required>
        </div>
        
        <div class="form-group">
            <label for="full_name">Full Name:</label>
            <input type="text" id="full_name" name="full_name" required>
        </div>
        
        <div class="form-group">
            <label for="role_id">Role:</label>
            <select id="role_id" name="role_id" required>
                <option value="">Loading roles...</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="department">Department:</label>
            <input type="text" id="department" name="department">
        </div>
        
        <div class="form-group">
            <label for="position">Position:</label>
            <input type="text" id="position" name="position">
        </div>
        
        <div class="form-group">
            <label for="phone">Phone:</label>
            <input type="text" id="phone" name="phone">
        </div>
        
        <div class="form-group">
            <label for="location">Location:</label>
            <input type="text" id="location" name="location">
        </div>
        
        <button type="submit">Create Learner</button>
    </form>
    
    <div id="result"></div>

    <script>
        // Load available roles when page loads
        async function loadRoles() {
            const roleSelect = document.getElementById('role_id');
            roleSelect.innerHTML = '<option value="">Loading roles...</option>';

            try {
                console.log('Loading roles from API...');
                const response = await fetch('http://localhost:3002/api/roles');
                console.log('Roles API response status:', response.status);

                const result = await response.json();
                console.log('Roles API result:', result);

                roleSelect.innerHTML = '';

                if (result.success && result.data && result.data.roles) {
                    console.log('Found roles:', result.data.roles);

                    // Add default option
                    const defaultOption = document.createElement('option');
                    defaultOption.value = '';
                    defaultOption.textContent = 'Use default role';
                    roleSelect.appendChild(defaultOption);

                    // Add role options
                    result.data.roles.forEach(role => {
                        const option = document.createElement('option');
                        option.value = role.id;
                        option.textContent = `${role.name} (${role.user_count || 0} users)`;
                        if (role.is_default) {
                            option.textContent += ' - Default';
                            option.selected = true;
                        }
                        roleSelect.appendChild(option);
                        console.log('Added role option:', role.id, role.name);
                    });

                    console.log('Roles loaded successfully');
                } else {
                    console.error('Invalid roles API response:', result);
                    roleSelect.innerHTML = '<option value="">Error: Invalid API response</option>';
                }
            } catch (error) {
                console.error('Error loading roles:', error);
                roleSelect.innerHTML = '<option value="">Error: Failed to load roles</option>';
            }
        }

        // Load roles when page loads
        document.addEventListener('DOMContentLoaded', loadRoles);

        document.getElementById('learnerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const roleIdValue = formData.get('role_id');
            const learnerData = {
                email: formData.get('email'),
                full_name: formData.get('full_name'),
                department: formData.get('department'),
                position: formData.get('position'),
                phone: formData.get('phone'),
                location: formData.get('location'),
                send_welcome_email: true
            };

            // Only include role_id if a specific role is selected
            if (roleIdValue && roleIdValue !== '') {
                learnerData.role_id = parseInt(roleIdValue);
                console.log('Using selected role_id:', learnerData.role_id);
            } else {
                console.log('No role selected, will use default role');
            }
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Creating learner...</p>';

            console.log('Submitting learner data:', learnerData);

            try {
                const response = await fetch('http://localhost:3002/api/learners', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(learnerData)
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>Success!</h3>
                            <p>Learner created successfully:</p>
                            <pre>${JSON.stringify(result.data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>Error</h3>
                            <p>${result.error || 'Unknown error occurred'}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>Network Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
