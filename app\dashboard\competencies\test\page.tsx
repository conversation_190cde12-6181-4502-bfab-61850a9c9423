'use client'

import { useState } from 'react'
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Alert,
  CircularProgress,
  Divider,
  Chip,
  List,
  ListItem,
  ListItemText,
} from '@mui/material'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import toast from 'react-hot-toast'

import { supabase } from '@/lib/supabase'
import { useAuthStore } from '@/lib/store'

export default function CompetenciesTestPage() {
  const { user } = useAuthStore()
  const queryClient = useQueryClient()
  const [testResults, setTestResults] = useState<string[]>([])

  // Test database connection
  const { data: competencies, isLoading, error } = useQuery({
    queryKey: ['test-competencies', user?.tenant_id],
    queryFn: async () => {
      if (!user?.tenant_id) throw new Error('No tenant ID')

      const { data, error } = await supabase
        .from('competencies')
        .select(`
          *,
          path_competencies(count),
          user_competencies(count)
        `)
        .eq('tenant_id', user.tenant_id)
        .limit(5)

      if (error) throw error
      return data
    },
    enabled: !!user?.tenant_id,
  })

  // Test competency creation
  const createTestMutation = useMutation({
    mutationFn: async () => {
      const testCompetency = {
        tenant_id: user?.tenant_id,
        name: `Test Competency ${Date.now()}`,
        description: 'This is a test competency created for testing purposes',
        category: 'Testing',
        tags: ['test', 'demo'],
        status: 'draft',
        is_public: false,
        created_by: user?.id,
        metadata: {
          level_definitions: {
            '1': 'Basic understanding',
            '2': 'Intermediate knowledge',
            '3': 'Advanced proficiency',
          },
        },
      }

      const { data, error } = await supabase
        .from('competencies')
        .insert([testCompetency])
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['test-competencies'] })
      setTestResults(prev => [...prev, `✅ Created test competency: ${data.name}`])
      toast.success('Test competency created successfully')
    },
    onError: (error) => {
      setTestResults(prev => [...prev, `❌ Failed to create competency: ${error.message}`])
      toast.error('Failed to create test competency')
    },
  })

  // Test skill gap detection
  const testSkillGaps = useQuery({
    queryKey: ['test-skill-gaps', user?.tenant_id],
    queryFn: async () => {
      if (!user?.tenant_id) return []

      const { data, error } = await supabase
        .from('skill_gaps')
        .select(`
          *,
          users!skill_gaps_learner_id_fkey(full_name, email),
          competencies!skill_gaps_competency_id_fkey(name, category)
        `)
        .eq('users.tenant_id', user.tenant_id)
        .limit(3)

      if (error) throw error
      return data
    },
    enabled: !!user?.tenant_id,
  })

  // Test competency categories
  const testCategories = useQuery({
    queryKey: ['test-categories', user?.tenant_id],
    queryFn: async () => {
      if (!user?.tenant_id) return []

      const { data, error } = await supabase
        .from('competency_categories')
        .select('*')
        .eq('tenant_id', user.tenant_id)
        .limit(5)

      if (error) throw error
      return data
    },
    enabled: !!user?.tenant_id,
  })

  const runAllTests = async () => {
    setTestResults([])
    setTestResults(prev => [...prev, '🧪 Starting competency system tests...'])

    // Test 1: Database connection
    try {
      const { data, error } = await supabase
        .from('competencies')
        .select('count')
        .eq('tenant_id', user?.tenant_id)

      if (error) throw error
      setTestResults(prev => [...prev, '✅ Database connection successful'])
    } catch (error) {
      setTestResults(prev => [...prev, `❌ Database connection failed: ${error}`])
    }

    // Test 2: RLS policies
    try {
      const { data, error } = await supabase
        .from('competencies')
        .select('*')
        .eq('tenant_id', 999999) // Try to access different tenant

      // Should return empty array due to RLS
      if (data && data.length === 0) {
        setTestResults(prev => [...prev, '✅ RLS policies working correctly'])
      } else {
        setTestResults(prev => [...prev, '⚠️ RLS policies may not be working'])
      }
    } catch (error) {
      setTestResults(prev => [...prev, '✅ RLS policies blocking unauthorized access'])
    }

    // Test 3: Create competency
    createTestMutation.mutate()
  }

  const clearTestData = async () => {
    try {
      const { error } = await supabase
        .from('competencies')
        .delete()
        .eq('tenant_id', user?.tenant_id)
        .like('name', 'Test Competency%')

      if (error) throw error
      
      queryClient.invalidateQueries({ queryKey: ['test-competencies'] })
      setTestResults(prev => [...prev, '🧹 Cleaned up test data'])
      toast.success('Test data cleaned up')
    } catch (error) {
      setTestResults(prev => [...prev, `❌ Failed to clean up: ${error}`])
      toast.error('Failed to clean up test data')
    }
  }

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Competencies System Test
      </Typography>
      
      <Typography variant="body1" color="textSecondary" paragraph>
        This page tests the competencies system functionality including database connections,
        RLS policies, and CRUD operations.
      </Typography>

      <Box display="flex" gap={2} mb={3}>
        <Button
          variant="contained"
          onClick={runAllTests}
          disabled={createTestMutation.isPending}
        >
          Run All Tests
        </Button>
        <Button
          variant="outlined"
          onClick={clearTestData}
          color="warning"
        >
          Clear Test Data
        </Button>
      </Box>

      {/* Test Results */}
      {testResults.length > 0 && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Test Results
            </Typography>
            <List dense>
              {testResults.map((result, index) => (
                <ListItem key={index} disablePadding>
                  <ListItemText primary={result} />
                </ListItem>
              ))}
            </List>
          </CardContent>
        </Card>
      )}

      {/* Current Data Display */}
      <Box display="flex" flexDirection="column" gap={3}>
        {/* Competencies */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Current Competencies
            </Typography>
            {isLoading ? (
              <CircularProgress size={24} />
            ) : error ? (
              <Alert severity="error">
                Error loading competencies: {error.message}
              </Alert>
            ) : (
              <Box>
                <Typography variant="body2" color="textSecondary" gutterBottom>
                  Found {competencies?.length || 0} competencies
                </Typography>
                {competencies?.map((comp) => (
                  <Box key={comp.id} mb={2}>
                    <Box display="flex" alignItems="center" gap={1} mb={1}>
                      <Typography variant="subtitle2">{comp.name}</Typography>
                      <Chip label={comp.status} size="small" color="primary" />
                      {comp.category && (
                        <Chip label={comp.category} size="small" variant="outlined" />
                      )}
                    </Box>
                    <Typography variant="body2" color="textSecondary">
                      {comp.description}
                    </Typography>
                    {comp.tags && comp.tags.length > 0 && (
                      <Box display="flex" gap={0.5} mt={1}>
                        {comp.tags.map((tag: string, index: number) => (
                          <Chip key={index} label={tag} size="small" variant="outlined" />
                        ))}
                      </Box>
                    )}
                  </Box>
                ))}
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Categories */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Competency Categories
            </Typography>
            {testCategories.isLoading ? (
              <CircularProgress size={24} />
            ) : testCategories.error ? (
              <Alert severity="error">
                Error loading categories: {testCategories.error.message}
              </Alert>
            ) : (
              <Box display="flex" gap={1} flexWrap="wrap">
                {testCategories.data?.map((category) => (
                  <Chip
                    key={category.id}
                    label={category.name}
                    sx={{ backgroundColor: category.color, color: 'white' }}
                  />
                ))}
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Skill Gaps */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Sample Skill Gaps
            </Typography>
            {testSkillGaps.isLoading ? (
              <CircularProgress size={24} />
            ) : testSkillGaps.error ? (
              <Alert severity="error">
                Error loading skill gaps: {testSkillGaps.error.message}
              </Alert>
            ) : (
              <Box>
                <Typography variant="body2" color="textSecondary" gutterBottom>
                  Found {testSkillGaps.data?.length || 0} skill gaps
                </Typography>
                {testSkillGaps.data?.map((gap) => (
                  <Box key={gap.id} mb={2}>
                    <Typography variant="subtitle2">
                      {gap.users?.full_name || gap.users?.email} - {gap.competencies?.name}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Gap Score: {Math.round(gap.gap_score * 100)}% | 
                      Confidence: {Math.round(gap.confidence_score * 100)}%
                    </Typography>
                  </Box>
                ))}
              </Box>
            )}
          </CardContent>
        </Card>
      </Box>
    </Box>
  )
}
