# Migration Troubleshooting Guide

## Error: "cannot determine type of empty array" (SQLSTATE 42P18)

### Problem
This error occurs when PostgreSQL encounters an empty array (`ARRAY[]`) and cannot determine its data type.

### Solution
The issue has been **FIXED** in the migration files. The empty array in the `prerequisites` field has been cast to the correct type:

```sql
-- Before (causes error):
ARRAY[]

-- After (fixed):
ARRAY[]::TEXT[]
```

### If You Still Encounter This Error

1. **Use the Fixed Migration File**
   - The main `003_seed_data.sql` file has been updated with the fix
   - Re-run the migration with the updated file

2. **Use the Simple Migration File**
   - If you continue to have issues, use `003_seed_data_simple.sql`
   - This file has explicit type casting for all arrays

3. **Manual Fix**
   - If editing SQL manually, always cast empty arrays:
   ```sql
   -- For text arrays:
   ARRAY[]::TEXT[]
   
   -- For integer arrays:
   ARRAY[]::INTEGER[]
   
   -- For UUID arrays:
   ARRAY[]::UUID[]
   ```

## Running Migrations

### Option 1: Supabase CLI (Recommended)
```bash
# Make sure you're in the project directory
cd zenithaiadmin

# Run all migrations
supabase db push

# Or run specific migration
supabase db push --include-all
```

### Option 2: Manual SQL Execution
1. Go to Supabase Dashboard → SQL Editor
2. Run migrations in order:
   - `001_initial_schema.sql`
   - `002_rls_policies.sql` 
   - `003_seed_data.sql` (or `003_seed_data_simple.sql`)

### Option 3: Reset and Retry
If migrations are partially applied:

```bash
# Reset the database (WARNING: This deletes all data)
supabase db reset

# Then push migrations again
supabase db push
```

## Verification Steps

After running migrations, verify they worked:

1. **Check Tables Created**
   ```sql
   SELECT table_name FROM information_schema.tables 
   WHERE table_schema = 'public';
   ```

2. **Check Sample Data**
   ```sql
   SELECT * FROM tenants;
   SELECT * FROM roles;
   SELECT * FROM learning_paths LIMIT 5;
   ```

3. **Check RLS Policies**
   ```sql
   SELECT schemaname, tablename, policyname 
   FROM pg_policies 
   WHERE schemaname = 'public';
   ```

## Common Migration Issues

### 1. Permission Errors
```
ERROR: permission denied for schema public
```
**Solution**: Make sure you're using the service role key, not the anon key.

### 2. Table Already Exists
```
ERROR: relation "table_name" already exists
```
**Solution**: Either drop the table first or use `CREATE TABLE IF NOT EXISTS`.

### 3. Foreign Key Violations
```
ERROR: insert or update on table "roles" violates foreign key constraint "roles_tenant_id_fkey"
```
**Cause**: The roles table is trying to reference tenant_id=1, but no tenant with ID 1 exists.

**Solutions**:
1. **Use the fixed migration**: `003_seed_data_fixed.sql` includes proper dependency checking
2. **Run diagnostic**: Use `000_diagnostic.sql` to check current database state
3. **Follow recovery guide**: See `MIGRATION_RECOVERY_GUIDE.md` for step-by-step fix
4. **Manual fix**: Ensure tenant is created before roles:
   ```sql
   -- First create tenant
   INSERT INTO tenants (name, slug, settings) VALUES
   ('Demo Organization', 'demo-org', '{}');

   -- Then create roles with correct tenant_id
   INSERT INTO roles (tenant_id, name, permissions, is_default)
   SELECT t.id, 'Admin', ARRAY['admin'], false
   FROM tenants t WHERE t.slug = 'demo-org';
   ```

### 4. RLS Policy Conflicts
```
ERROR: policy "policy_name" for table "table_name" already exists
```
**Solution**: Drop existing policies first or use `CREATE POLICY IF NOT EXISTS` (PostgreSQL 15+).

## Migration File Structure

The migrations are designed to run in this order:

1. **001_initial_schema.sql**
   - Creates all tables
   - Sets up relationships
   - Creates indexes
   - Creates triggers

2. **002_rls_policies.sql**
   - Enables RLS on all tables
   - Creates helper functions
   - Creates security policies

3. **003_seed_data.sql**
   - Inserts sample data
   - Creates utility functions
   - Sets up triggers

## Rollback Strategy

If you need to rollback migrations:

### Complete Reset
```bash
supabase db reset
```

### Manual Rollback
```sql
-- Drop tables in reverse order
DROP TABLE IF EXISTS audit_logs CASCADE;
DROP TABLE IF EXISTS notifications CASCADE;
DROP TABLE IF EXISTS path_competencies CASCADE;
DROP TABLE IF EXISTS user_competencies CASCADE;
DROP TABLE IF EXISTS competencies CASCADE;
DROP TABLE IF EXISTS content_library CASCADE;
DROP TABLE IF EXISTS progress CASCADE;
DROP TABLE IF EXISTS learner_assignments CASCADE;
DROP TABLE IF EXISTS lessons CASCADE;
DROP TABLE IF EXISTS modules CASCADE;
DROP TABLE IF EXISTS learning_paths CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS roles CASCADE;
DROP TABLE IF EXISTS tenants CASCADE;

-- Drop functions
DROP FUNCTION IF EXISTS handle_new_user() CASCADE;
DROP FUNCTION IF EXISTS update_user_last_login() CASCADE;
DROP FUNCTION IF EXISTS log_user_action(TEXT, TEXT, TEXT, JSONB, JSONB) CASCADE;
DROP FUNCTION IF EXISTS send_notification(UUID, TEXT, TEXT, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS get_user_tenant_id() CASCADE;
DROP FUNCTION IF EXISTS user_has_permission(TEXT) CASCADE;
```

## Testing Migrations

After running migrations, test the setup:

1. **Create a Test User**
   ```sql
   INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at)
   VALUES (
     gen_random_uuid(),
     '<EMAIL>',
     crypt('password123', gen_salt('bf')),
     NOW()
   );
   ```

2. **Test RLS Policies**
   ```sql
   -- This should work (admin can see all)
   SET LOCAL role authenticated;
   SET LOCAL request.jwt.claims TO '{"sub": "user-id", "role": "authenticated"}';
   SELECT * FROM tenants;
   ```

3. **Test Functions**
   ```sql
   SELECT get_user_tenant_id();
   SELECT user_has_permission('view_dashboard');
   ```

## Getting Help

If you continue to have issues:

1. Check the [Supabase Documentation](https://supabase.com/docs/guides/database)
2. Review the [PostgreSQL Array Documentation](https://www.postgresql.org/docs/current/arrays.html)
3. Join the [Supabase Discord](https://discord.supabase.com)
4. Create an issue in the project repository

## Success Indicators

You'll know migrations succeeded when:

- ✅ All tables are created without errors
- ✅ Sample data is inserted (check `tenants`, `roles`, `learning_paths`)
- ✅ RLS policies are active (check `pg_policies` view)
- ✅ Functions are created (check `pg_proc` for custom functions)
- ✅ Triggers are active (check `pg_trigger`)

The application should then connect to Supabase without errors!
