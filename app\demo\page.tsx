'use client'

import { useState } from 'react'
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Container,
  AppBar,
  <PERSON><PERSON><PERSON>,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  useTheme,
  ThemeProvider,
} from '@mui/material'
import {
  Dashboard as DashboardIcon,
  School as SchoolIcon,
  People as PeopleIcon,
  LibraryBooks as LibraryIcon,
  Assessment as ReportsIcon,
  Settings as SettingsIcon,
  TrendingUp as TrendingUpIcon,
  AccessTime as TimeIcon,
} from '@mui/icons-material'

import { theme } from '@/lib/theme'
import MetricCard from '@/components/dashboard/MetricCard'
import ChartCard from '@/components/dashboard/ChartCard'

const drawerWidth = 280

// Mock data for demo
const mockData = {
  activeLearners: 1247,
  activeLearnersTrend: 12.5,
  completionRate: 78.3,
  completionRateTrend: 5.2,
  avgTimeSpent: 45.2,
  avgTimeSpentTrend: -2.1,
  engagementScore: 85.7,
  engagementScoreTrend: 8.3,
  completionTrends: {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    data: [65, 72, 68, 75, 78, 82],
  },
  popularPaths: [
    { name: 'JavaScript Fundamentals', learners: 234, completion: 85 },
    { name: 'React Development', learners: 189, completion: 72 },
    { name: 'Python Basics', learners: 156, completion: 91 },
    { name: 'Data Science Intro', learners: 143, completion: 68 },
  ],
}

const sidebarItems = [
  { text: 'Dashboard', icon: <DashboardIcon />, active: true },
  { text: 'Learning Paths', icon: <SchoolIcon />, active: false },
  { text: 'Learners', icon: <PeopleIcon />, active: false },
  { text: 'Content Library', icon: <LibraryIcon />, active: false },
  { text: 'Reports', icon: <ReportsIcon />, active: false },
  { text: 'Settings', icon: <SettingsIcon />, active: false },
]

export default function DemoPage() {
  const muiTheme = useTheme()

  return (
    <ThemeProvider theme={theme}>
      <Box sx={{ display: 'flex', minHeight: '100vh', bgcolor: 'background.default' }}>
        {/* Sidebar */}
        <Drawer
          variant="permanent"
          sx={{
            width: drawerWidth,
            flexShrink: 0,
            '& .MuiDrawer-paper': {
              width: drawerWidth,
              boxSizing: 'border-box',
            },
          }}
        >
          <Box sx={{ p: 2, display: 'flex', alignItems: 'center', minHeight: 64 }}>
            <Typography variant="h6" noWrap component="div" fontWeight="bold">
              ZenithLearn AI
            </Typography>
          </Box>
          <Divider />
          <List sx={{ px: 1 }}>
            {sidebarItems.map((item) => (
              <ListItem key={item.text} disablePadding sx={{ mb: 0.5 }}>
                <Button
                  fullWidth
                  startIcon={item.icon}
                  variant={item.active ? 'contained' : 'text'}
                  sx={{
                    justifyContent: 'flex-start',
                    textTransform: 'none',
                    borderRadius: 1,
                    py: 1,
                  }}
                >
                  {item.text}
                </Button>
              </ListItem>
            ))}
          </List>
        </Drawer>

        {/* Main Content */}
        <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
          {/* Header */}
          <Box mb={3}>
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              Welcome to ZenithLearn AI Demo!
            </Typography>
            <Typography variant="body1" color="text.secondary" gutterBottom>
              This is a demonstration of the ZenithLearn AI admin dashboard built with Next.js 14, Material-UI, and modern React patterns.
            </Typography>
            <Box mt={2} p={2} bgcolor="info.light" borderRadius={1}>
              <Typography variant="body2" color="info.contrastText">
                <strong>Demo Mode:</strong> This is a static demo with mock data. In the full application, this data would be fetched from Supabase and updated in real-time.
              </Typography>
            </Box>
          </Box>

          {/* Metrics Row */}
          <Grid container spacing={3} mb={3}>
            <Grid item xs={12} sm={6} md={3}>
              <MetricCard
                title="Active Learners"
                value={mockData.activeLearners}
                change={mockData.activeLearnersTrend}
                icon={<PeopleIcon />}
                color="primary"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <MetricCard
                title="Completion Rate"
                value={`${mockData.completionRate}%`}
                change={mockData.completionRateTrend}
                icon={<SchoolIcon />}
                color="success"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <MetricCard
                title="Avg. Time Spent"
                value={`${mockData.avgTimeSpent}m`}
                change={mockData.avgTimeSpentTrend}
                icon={<TimeIcon />}
                color="info"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <MetricCard
                title="Engagement Score"
                value={mockData.engagementScore}
                change={mockData.engagementScoreTrend}
                icon={<TrendingUpIcon />}
                color="warning"
              />
            </Grid>
          </Grid>

          {/* Charts Row */}
          <Grid container spacing={3} mb={3}>
            <Grid item xs={12} md={8}>
              <ChartCard
                title="Completion Trends"
                subtitle="Monthly completion rates over time"
                data={mockData.completionTrends}
                type="line"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Card sx={{ height: '100%' }}>
                <CardContent>
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    Popular Learning Paths
                  </Typography>
                  <Box>
                    {mockData.popularPaths.map((path, index) => (
                      <Box
                        key={index}
                        display="flex"
                        justifyContent="space-between"
                        alignItems="center"
                        py={1}
                        borderBottom={index < mockData.popularPaths.length - 1 ? 1 : 0}
                        borderColor="divider"
                      >
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {path.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {path.learners} learners
                          </Typography>
                        </Box>
                        <Typography
                          variant="body2"
                          color={path.completion >= 80 ? 'success.main' : 'text.secondary'}
                          fontWeight="medium"
                        >
                          {path.completion}%
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Features Overview */}
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                ZenithLearn AI Features
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                    MVP Features (3 Months)
                  </Typography>
                  <Box component="ul" sx={{ pl: 2 }}>
                    <li>Dashboard with KPI widgets and charts</li>
                    <li>AI-powered learning path creation</li>
                    <li>Learner management with progress tracking</li>
                    <li>Content library with smart tagging</li>
                    <li>Comprehensive reporting system</li>
                    <li>Multi-tenant architecture</li>
                  </Box>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                    Full Version Features (6 Months)
                  </Typography>
                  <Box component="ul" sx={{ pl: 2 }}>
                    <li>Predictive analytics with TensorFlow.js</li>
                    <li>Adaptive learning paths</li>
                    <li>Gamification with badges and leaderboards</li>
                    <li>External system integrations</li>
                    <li>Multilingual support (5 languages)</li>
                    <li>Advanced collaboration tools</li>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Box>
      </Box>
    </ThemeProvider>
  )
}
