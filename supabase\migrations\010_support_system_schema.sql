-- Support System Schema for ZenithLearn AI
-- This migration creates comprehensive support and help functionality

-- Create knowledge_base table for articles and guides
CREATE TABLE knowledge_base (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    summary TEXT,
    category VARCHAR(100),
    tags TEXT[] DEFAULT '{}',
    type VARCHAR(20) CHECK (type IN ('article', 'faq', 'video', 'guide', 'tutorial')) DEFAULT 'article',
    difficulty_level VARCHAR(20) CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')) DEFAULT 'beginner',
    estimated_read_time INTEGER, -- in minutes
    view_count INTEGER DEFAULT 0,
    helpful_votes INTEGER DEFAULT 0,
    unhelpful_votes INTEGER DEFAULT 0,
    is_featured BOOLEAN DEFAULT FALSE,
    is_published BOOLEAN DEFAULT TRUE,
    video_url TEXT,
    attachments JSON<PERSON> DEFAULT '[]',
    metadata JSONB DEFAULT '{}',
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create support_tickets table
CREATE TABLE support_tickets (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    ticket_number VARCHAR(20) UNIQUE NOT NULL,
    subject VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(100) NOT NULL,
    priority VARCHAR(20) CHECK (priority IN ('low', 'medium', 'high', 'urgent')) DEFAULT 'medium',
    status VARCHAR(20) CHECK (status IN ('open', 'in_progress', 'waiting_response', 'resolved', 'closed')) DEFAULT 'open',
    assigned_to UUID REFERENCES users(id),
    resolution TEXT,
    satisfaction_rating INTEGER CHECK (satisfaction_rating >= 1 AND satisfaction_rating <= 5),
    satisfaction_feedback TEXT,
    context_data JSONB DEFAULT '{}', -- course, lesson, device info, etc.
    attachments JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE,
    closed_at TIMESTAMP WITH TIME ZONE
);

-- Create support_chat_sessions table
CREATE TABLE support_chat_sessions (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    agent_id UUID REFERENCES users(id),
    ticket_id INTEGER REFERENCES support_tickets(id),
    status VARCHAR(20) CHECK (status IN ('waiting', 'active', 'ended')) DEFAULT 'waiting',
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ended_at TIMESTAMP WITH TIME ZONE,
    wait_time INTEGER, -- in seconds
    session_duration INTEGER, -- in seconds
    satisfaction_rating INTEGER CHECK (satisfaction_rating >= 1 AND satisfaction_rating <= 5),
    metadata JSONB DEFAULT '{}'
);

-- Create support_chat_messages table
CREATE TABLE support_chat_messages (
    id SERIAL PRIMARY KEY,
    session_id INTEGER REFERENCES support_chat_sessions(id) ON DELETE CASCADE,
    sender_id UUID REFERENCES users(id) ON DELETE CASCADE,
    message TEXT NOT NULL,
    message_type VARCHAR(20) CHECK (message_type IN ('text', 'file', 'system', 'ai_suggestion')) DEFAULT 'text',
    attachments JSONB DEFAULT '[]',
    is_read BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create support_chat_history table for AI chatbot
CREATE TABLE support_chat_history (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_id UUID NOT NULL,
    message TEXT NOT NULL,
    response TEXT,
    message_type VARCHAR(20) CHECK (message_type IN ('user', 'ai', 'system')) DEFAULT 'user',
    context_data JSONB DEFAULT '{}',
    confidence_score DECIMAL(3,2),
    escalated_to_human BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create community_threads table
CREATE TABLE community_threads (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    author_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    category VARCHAR(100),
    tags TEXT[] DEFAULT '{}',
    is_pinned BOOLEAN DEFAULT FALSE,
    is_locked BOOLEAN DEFAULT FALSE,
    view_count INTEGER DEFAULT 0,
    reply_count INTEGER DEFAULT 0,
    upvotes INTEGER DEFAULT 0,
    downvotes INTEGER DEFAULT 0,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create community_replies table
CREATE TABLE community_replies (
    id SERIAL PRIMARY KEY,
    thread_id INTEGER REFERENCES community_threads(id) ON DELETE CASCADE,
    author_id UUID REFERENCES users(id) ON DELETE CASCADE,
    parent_reply_id INTEGER REFERENCES community_replies(id),
    content TEXT NOT NULL,
    upvotes INTEGER DEFAULT 0,
    downvotes INTEGER DEFAULT 0,
    is_solution BOOLEAN DEFAULT FALSE,
    is_flagged BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create community_votes table
CREATE TABLE community_votes (
    id SERIAL PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    thread_id INTEGER REFERENCES community_threads(id) ON DELETE CASCADE,
    reply_id INTEGER REFERENCES community_replies(id) ON DELETE CASCADE,
    vote_type VARCHAR(10) CHECK (vote_type IN ('upvote', 'downvote')) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, thread_id),
    UNIQUE(user_id, reply_id),
    CHECK ((thread_id IS NOT NULL AND reply_id IS NULL) OR (thread_id IS NULL AND reply_id IS NOT NULL))
);

-- Create community_kudos table
CREATE TABLE community_kudos (
    id SERIAL PRIMARY KEY,
    giver_id UUID REFERENCES users(id) ON DELETE CASCADE,
    receiver_id UUID REFERENCES users(id) ON DELETE CASCADE,
    thread_id INTEGER REFERENCES community_threads(id),
    reply_id INTEGER REFERENCES community_replies(id),
    message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CHECK ((thread_id IS NOT NULL AND reply_id IS NULL) OR (thread_id IS NULL AND reply_id IS NOT NULL))
);

-- Create article_feedback table
CREATE TABLE article_feedback (
    id SERIAL PRIMARY KEY,
    article_id INTEGER REFERENCES knowledge_base(id) ON DELETE CASCADE,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    vote VARCHAR(10) CHECK (vote IN ('helpful', 'unhelpful')) NOT NULL,
    feedback_text TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(article_id, learner_id)
);

-- Create interactive_guides table
CREATE TABLE interactive_guides (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    steps_json JSONB NOT NULL,
    estimated_duration INTEGER, -- in minutes
    difficulty_level VARCHAR(20) CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')) DEFAULT 'beginner',
    tags TEXT[] DEFAULT '{}',
    is_published BOOLEAN DEFAULT TRUE,
    completion_count INTEGER DEFAULT 0,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create learner_guide_progress table
CREATE TABLE learner_guide_progress (
    id SERIAL PRIMARY KEY,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    guide_id INTEGER REFERENCES interactive_guides(id) ON DELETE CASCADE,
    current_step INTEGER DEFAULT 0,
    completed_steps INTEGER[] DEFAULT '{}',
    is_completed BOOLEAN DEFAULT FALSE,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(learner_id, guide_id)
);

-- Create support_suggestions table
CREATE TABLE support_suggestions (
    id SERIAL PRIMARY KEY,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    suggestion_type VARCHAR(50) NOT NULL,
    suggestion_data JSONB NOT NULL,
    confidence_score DECIMAL(3,2),
    is_dismissed BOOLEAN DEFAULT FALSE,
    is_clicked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    dismissed_at TIMESTAMP WITH TIME ZONE
);

-- Create learner_feedback table
CREATE TABLE learner_feedback (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(50) CHECK (type IN ('bug', 'feature', 'improvement', 'general')) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    category VARCHAR(100),
    status VARCHAR(20) CHECK (status IN ('submitted', 'reviewed', 'in_progress', 'completed', 'rejected')) DEFAULT 'submitted',
    attachments JSONB DEFAULT '[]',
    is_anonymous BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create support_diagnostics table
CREATE TABLE support_diagnostics (
    id SERIAL PRIMARY KEY,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_id UUID NOT NULL,
    diagnostic_type VARCHAR(50) NOT NULL,
    results_json JSONB NOT NULL,
    device_info JSONB DEFAULT '{}',
    browser_info JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create troubleshooter_flows table
CREATE TABLE troubleshooter_flows (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    flow_json JSONB NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    usage_count INTEGER DEFAULT 0,
    success_rate DECIMAL(5,2) DEFAULT 0,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create learner_troubleshooter table
CREATE TABLE learner_troubleshooter (
    id SERIAL PRIMARY KEY,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    flow_id INTEGER REFERENCES troubleshooter_flows(id) ON DELETE CASCADE,
    session_id UUID NOT NULL,
    current_step INTEGER DEFAULT 0,
    completed_steps INTEGER[] DEFAULT '{}',
    is_resolved BOOLEAN DEFAULT FALSE,
    resolution_path TEXT[],
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Create learner_rewards table for gamification
CREATE TABLE learner_rewards (
    id SERIAL PRIMARY KEY,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    reward_type VARCHAR(50) NOT NULL,
    reward_id VARCHAR(100) NOT NULL,
    points INTEGER DEFAULT 0,
    badge_data JSONB DEFAULT '{}',
    earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(learner_id, reward_type, reward_id)
);

-- Create indexes for better performance
CREATE INDEX idx_knowledge_base_tenant_id ON knowledge_base(tenant_id);
CREATE INDEX idx_knowledge_base_category ON knowledge_base(category);
CREATE INDEX idx_knowledge_base_tags ON knowledge_base USING GIN(tags);
CREATE INDEX idx_knowledge_base_published ON knowledge_base(is_published);
CREATE INDEX idx_knowledge_base_search ON knowledge_base USING GIN(to_tsvector('english', title || ' ' || content));

CREATE INDEX idx_support_tickets_tenant_id ON support_tickets(tenant_id);
CREATE INDEX idx_support_tickets_learner_id ON support_tickets(learner_id);
CREATE INDEX idx_support_tickets_status ON support_tickets(status);
CREATE INDEX idx_support_tickets_category ON support_tickets(category);
CREATE INDEX idx_support_tickets_assigned_to ON support_tickets(assigned_to);
CREATE INDEX idx_support_tickets_created_at ON support_tickets(created_at);

CREATE INDEX idx_support_chat_sessions_learner_id ON support_chat_sessions(learner_id);
CREATE INDEX idx_support_chat_sessions_agent_id ON support_chat_sessions(agent_id);
CREATE INDEX idx_support_chat_sessions_status ON support_chat_sessions(status);

CREATE INDEX idx_support_chat_messages_session_id ON support_chat_messages(session_id);
CREATE INDEX idx_support_chat_messages_sender_id ON support_chat_messages(sender_id);
CREATE INDEX idx_support_chat_messages_created_at ON support_chat_messages(created_at);

CREATE INDEX idx_support_chat_history_learner_id ON support_chat_history(learner_id);
CREATE INDEX idx_support_chat_history_session_id ON support_chat_history(session_id);

CREATE INDEX idx_community_threads_tenant_id ON community_threads(tenant_id);
CREATE INDEX idx_community_threads_author_id ON community_threads(author_id);
CREATE INDEX idx_community_threads_category ON community_threads(category);
CREATE INDEX idx_community_threads_tags ON community_threads USING GIN(tags);
CREATE INDEX idx_community_threads_last_activity ON community_threads(last_activity_at);

CREATE INDEX idx_community_replies_thread_id ON community_replies(thread_id);
CREATE INDEX idx_community_replies_author_id ON community_replies(author_id);
CREATE INDEX idx_community_replies_parent_id ON community_replies(parent_reply_id);

CREATE INDEX idx_community_votes_user_id ON community_votes(user_id);
CREATE INDEX idx_community_votes_thread_id ON community_votes(thread_id);
CREATE INDEX idx_community_votes_reply_id ON community_votes(reply_id);

CREATE INDEX idx_article_feedback_article_id ON article_feedback(article_id);
CREATE INDEX idx_article_feedback_learner_id ON article_feedback(learner_id);

CREATE INDEX idx_interactive_guides_tenant_id ON interactive_guides(tenant_id);
CREATE INDEX idx_interactive_guides_category ON interactive_guides(category);
CREATE INDEX idx_interactive_guides_published ON interactive_guides(is_published);

CREATE INDEX idx_learner_guide_progress_learner_id ON learner_guide_progress(learner_id);
CREATE INDEX idx_learner_guide_progress_guide_id ON learner_guide_progress(guide_id);

CREATE INDEX idx_support_suggestions_learner_id ON support_suggestions(learner_id);
CREATE INDEX idx_support_suggestions_type ON support_suggestions(suggestion_type);

CREATE INDEX idx_learner_feedback_tenant_id ON learner_feedback(tenant_id);
CREATE INDEX idx_learner_feedback_learner_id ON learner_feedback(learner_id);
CREATE INDEX idx_learner_feedback_type ON learner_feedback(type);
CREATE INDEX idx_learner_feedback_status ON learner_feedback(status);

CREATE INDEX idx_support_diagnostics_learner_id ON support_diagnostics(learner_id);
CREATE INDEX idx_support_diagnostics_session_id ON support_diagnostics(session_id);

CREATE INDEX idx_troubleshooter_flows_tenant_id ON troubleshooter_flows(tenant_id);
CREATE INDEX idx_troubleshooter_flows_category ON troubleshooter_flows(category);
CREATE INDEX idx_troubleshooter_flows_active ON troubleshooter_flows(is_active);

CREATE INDEX idx_learner_troubleshooter_learner_id ON learner_troubleshooter(learner_id);
CREATE INDEX idx_learner_troubleshooter_flow_id ON learner_troubleshooter(flow_id);
CREATE INDEX idx_learner_troubleshooter_session_id ON learner_troubleshooter(session_id);

CREATE INDEX idx_learner_rewards_learner_id ON learner_rewards(learner_id);
CREATE INDEX idx_learner_rewards_type ON learner_rewards(reward_type);

-- Add updated_at triggers
CREATE TRIGGER update_knowledge_base_updated_at BEFORE UPDATE ON knowledge_base FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_support_tickets_updated_at BEFORE UPDATE ON support_tickets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_community_threads_updated_at BEFORE UPDATE ON community_threads FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_community_replies_updated_at BEFORE UPDATE ON community_replies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_interactive_guides_updated_at BEFORE UPDATE ON interactive_guides FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_learner_feedback_updated_at BEFORE UPDATE ON learner_feedback FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_troubleshooter_flows_updated_at BEFORE UPDATE ON troubleshooter_flows FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to generate ticket numbers
CREATE OR REPLACE FUNCTION generate_ticket_number()
RETURNS TRIGGER AS $$
BEGIN
    NEW.ticket_number := 'TKT-' || LPAD(NEW.id::text, 6, '0');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-generate ticket numbers
CREATE TRIGGER generate_support_ticket_number
    AFTER INSERT ON support_tickets
    FOR EACH ROW
    EXECUTE FUNCTION generate_ticket_number();

-- Function to update thread reply count
CREATE OR REPLACE FUNCTION update_thread_reply_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE community_threads
        SET reply_count = reply_count + 1,
            last_activity_at = NOW()
        WHERE id = NEW.thread_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE community_threads
        SET reply_count = reply_count - 1
        WHERE id = OLD.thread_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update thread reply count
CREATE TRIGGER update_community_thread_reply_count
    AFTER INSERT OR DELETE ON community_replies
    FOR EACH ROW
    EXECUTE FUNCTION update_thread_reply_count();

-- Function to update vote counts
CREATE OR REPLACE FUNCTION update_vote_counts()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        IF NEW.thread_id IS NOT NULL THEN
            IF NEW.vote_type = 'upvote' THEN
                UPDATE community_threads SET upvotes = upvotes + 1 WHERE id = NEW.thread_id;
            ELSE
                UPDATE community_threads SET downvotes = downvotes + 1 WHERE id = NEW.thread_id;
            END IF;
        ELSIF NEW.reply_id IS NOT NULL THEN
            IF NEW.vote_type = 'upvote' THEN
                UPDATE community_replies SET upvotes = upvotes + 1 WHERE id = NEW.reply_id;
            ELSE
                UPDATE community_replies SET downvotes = downvotes + 1 WHERE id = NEW.reply_id;
            END IF;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        IF OLD.thread_id IS NOT NULL THEN
            IF OLD.vote_type = 'upvote' THEN
                UPDATE community_threads SET upvotes = upvotes - 1 WHERE id = OLD.thread_id;
            ELSE
                UPDATE community_threads SET downvotes = downvotes - 1 WHERE id = OLD.thread_id;
            END IF;
        ELSIF OLD.reply_id IS NOT NULL THEN
            IF OLD.vote_type = 'upvote' THEN
                UPDATE community_replies SET upvotes = upvotes - 1 WHERE id = OLD.reply_id;
            ELSE
                UPDATE community_replies SET downvotes = downvotes - 1 WHERE id = OLD.reply_id;
            END IF;
        END IF;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update vote counts
CREATE TRIGGER update_community_vote_counts
    AFTER INSERT OR DELETE ON community_votes
    FOR EACH ROW
    EXECUTE FUNCTION update_vote_counts();

-- Function to update article feedback counts
CREATE OR REPLACE FUNCTION update_article_feedback_counts()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        IF NEW.vote = 'helpful' THEN
            UPDATE knowledge_base SET helpful_votes = helpful_votes + 1 WHERE id = NEW.article_id;
        ELSE
            UPDATE knowledge_base SET unhelpful_votes = unhelpful_votes + 1 WHERE id = NEW.article_id;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        IF OLD.vote = 'helpful' AND NEW.vote = 'unhelpful' THEN
            UPDATE knowledge_base
            SET helpful_votes = helpful_votes - 1, unhelpful_votes = unhelpful_votes + 1
            WHERE id = NEW.article_id;
        ELSIF OLD.vote = 'unhelpful' AND NEW.vote = 'helpful' THEN
            UPDATE knowledge_base
            SET helpful_votes = helpful_votes + 1, unhelpful_votes = unhelpful_votes - 1
            WHERE id = NEW.article_id;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        IF OLD.vote = 'helpful' THEN
            UPDATE knowledge_base SET helpful_votes = helpful_votes - 1 WHERE id = OLD.article_id;
        ELSE
            UPDATE knowledge_base SET unhelpful_votes = unhelpful_votes - 1 WHERE id = OLD.article_id;
        END IF;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update article feedback counts
CREATE TRIGGER update_knowledge_base_feedback_counts
    AFTER INSERT OR UPDATE OR DELETE ON article_feedback
    FOR EACH ROW
    EXECUTE FUNCTION update_article_feedback_counts();
