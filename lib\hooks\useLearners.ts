import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { 
  learnersApi, 
  groupsApi, 
  messagingApi, 
  analyticsApi,
  customFieldsApi 
} from '@/lib/api/learners'
import {
  Learner,
  LearnerFilters,
  LearnerSortOptions,
  CreateLearnerData,
  UpdateLearnerData,
  BulkImportData,
  AssignPathData,
  CreateGroupData,
  MessageData
} from '@/lib/types/learners'

// Learners Hooks
export const useLearners = (
  filters?: Partial<LearnerFilters>,
  sort?: LearnerSortOptions,
  page = 1,
  limit = 20
) => {
  return useQuery({
    queryKey: ['learners', filters, sort, page, limit],
    queryFn: () => learnersApi.getLearners(filters, sort, page, limit),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export const useLearner = (learnerId: string) => {
  return useQuery({
    queryKey: ['learner', learnerId],
    queryFn: () => learnersApi.getLearner(learnerId),
    enabled: !!learnerId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export const useCreateLearner = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateLearnerData) => learnersApi.createLearner(data),
    onSuccess: (newLearner) => {
      queryClient.invalidateQueries({ queryKey: ['learners'] })
      queryClient.invalidateQueries({ queryKey: ['learner-stats'] })
      toast.success(`Learner ${newLearner.full_name} created successfully`)
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create learner')
    },
  })
}

export const useUpdateLearner = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ learnerId, updates }: { learnerId: string; updates: UpdateLearnerData }) =>
      learnersApi.updateLearner(learnerId, updates),
    onSuccess: (updatedLearner) => {
      queryClient.invalidateQueries({ queryKey: ['learners'] })
      queryClient.invalidateQueries({ queryKey: ['learner', updatedLearner.id] })
      queryClient.invalidateQueries({ queryKey: ['learner-stats'] })
      toast.success('Learner updated successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update learner')
    },
  })
}

export const useDeleteLearner = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (learnerId: string) => learnersApi.deleteLearner(learnerId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['learners'] })
      queryClient.invalidateQueries({ queryKey: ['learner-stats'] })
      toast.success('Learner deactivated successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to deactivate learner')
    },
  })
}

export const useBulkImportLearners = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: BulkImportData) => learnersApi.bulkImportLearners(data),
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['learners'] })
      queryClient.invalidateQueries({ queryKey: ['learner-stats'] })
      toast.success(`Successfully imported ${result.success} learners`)
      if (result.errors.length > 0) {
        toast.error(`${result.errors.length} errors occurred during import`)
      }
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to import learners')
    },
  })
}

export const useAssignPath = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: AssignPathData) => learnersApi.assignPath(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['learners'] })
      queryClient.invalidateQueries({ queryKey: ['learner-stats'] })
      toast.success('Learning path assigned successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to assign learning path')
    },
  })
}

export const useLearnerStats = () => {
  return useQuery({
    queryKey: ['learner-stats'],
    queryFn: () => learnersApi.getLearnerStats(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

// Groups Hooks
export const useGroups = () => {
  return useQuery({
    queryKey: ['groups'],
    queryFn: () => groupsApi.getGroups(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export const useCreateGroup = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateGroupData) => groupsApi.createGroup(data),
    onSuccess: (newGroup) => {
      queryClient.invalidateQueries({ queryKey: ['groups'] })
      queryClient.invalidateQueries({ queryKey: ['learners'] })
      toast.success(`Group "${newGroup.name}" created successfully`)
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create group')
    },
  })
}

export const useAddMembersToGroup = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ groupId, userIds }: { groupId: number; userIds: string[] }) =>
      groupsApi.addMembersToGroup(groupId, userIds),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['groups'] })
      queryClient.invalidateQueries({ queryKey: ['learners'] })
      toast.success('Members added to group successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to add members to group')
    },
  })
}

export const useRemoveMembersFromGroup = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ groupId, userIds }: { groupId: number; userIds: string[] }) =>
      groupsApi.removeMembersFromGroup(groupId, userIds),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['groups'] })
      queryClient.invalidateQueries({ queryKey: ['learners'] })
      toast.success('Members removed from group successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to remove members from group')
    },
  })
}

export const useDeleteGroup = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (groupId: number) => groupsApi.deleteGroup(groupId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['groups'] })
      queryClient.invalidateQueries({ queryKey: ['learners'] })
      toast.success('Group deleted successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete group')
    },
  })
}

// Messaging Hooks
export const useSendMessage = () => {
  return useMutation({
    mutationFn: (data: MessageData) => messagingApi.sendMessage(data),
    onSuccess: () => {
      toast.success('Message sent successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to send message')
    },
  })
}

// Analytics Hooks
export const useEngagementAlerts = () => {
  return useQuery({
    queryKey: ['engagement-alerts'],
    queryFn: () => analyticsApi.getEngagementAlerts(),
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export const useLearnerJourney = (userId: string, limit = 50) => {
  return useQuery({
    queryKey: ['learner-journey', userId, limit],
    queryFn: () => analyticsApi.getLearnerJourney(userId, limit),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export const useResolveAlert = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (alertId: number) => analyticsApi.resolveAlert(alertId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['engagement-alerts'] })
      toast.success('Alert resolved successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to resolve alert')
    },
  })
}

// Custom Fields Hooks
export const useCustomFields = () => {
  return useQuery({
    queryKey: ['custom-fields'],
    queryFn: () => customFieldsApi.getCustomFields(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export const useCreateCustomField = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: any) => customFieldsApi.createCustomField(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['custom-fields'] })
      toast.success('Custom field created successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create custom field')
    },
  })
}
