import { supabase } from '@/lib/supabase'
import { useAuthStore } from '@/lib/store'
import {
  <PERSON><PERSON>,
  LearnerFilt<PERSON>,
  LearnerSortOptions,
  LearnersResponse,
  LearnerStatsResponse,
  CreateLearnerData,
  UpdateLearnerData,
  BulkImportData,
  AssignPathData,
  CreateGroupData,
  MessageData,
  Group,
  LearnerProgress,
  EngagementAlert,
  LearnerJourneyEvent,
  CustomField,
  LearnerCustomFieldValue
} from '@/lib/types/learners'

// Helper function to get current user context
const getCurrentUserContext = () => {
  const { user, tenant } = useAuthStore.getState()

  // Development fallback - remove in production
  if (!user || !tenant) {
    console.warn('No authenticated user found, using development fallback')
    return {
      user: {
        id: '5cd09c93-eb00-470f-a605-c6d0d057bdd6',
        email: '<EMAIL>',
        tenant_id: 3,
        role_id: 1
      },
      tenant: {
        id: 3,
        name: 'Development Tenant'
      }
    }
  }
  return { user, tenant }
}

// Transform database user to Learner type
const transformLearner = (dbUser: any): Learner => {
  return {
    id: dbUser.id,
    email: dbUser.email,
    full_name: dbUser.full_name || dbUser.email,
    avatar_url: dbUser.avatar_url,
    tenant_id: dbUser.tenant_id,
    role_id: dbUser.role_id,
    status: dbUser.status || 'active',
    last_active: dbUser.last_active,
    created_at: dbUser.created_at,
    updated_at: dbUser.updated_at,
    department: dbUser.department,
    position: dbUser.position,
    manager_id: dbUser.manager_id,
    hire_date: dbUser.hire_date,
    phone: dbUser.phone,
    location: dbUser.location,
    // Add computed fields if available
    role: dbUser.roles,
    progress: dbUser.progress,
    assignments: dbUser.learner_assignments,
    groups: dbUser.group_members?.map((gm: any) => gm.groups),
    engagement_score: dbUser.engagement_score,
    completion_rate: dbUser.completion_rate,
    total_paths: dbUser.total_paths,
    completed_paths: dbUser.completed_paths,
    active_paths: dbUser.active_paths,
    overdue_assignments: dbUser.overdue_assignments
  }
}

// Learners API
export const learnersApi = {
  // Get all learners with filters and pagination
  async getLearners(
    filters?: Partial<LearnerFilters>,
    sort?: LearnerSortOptions,
    page = 1,
    limit = 20
  ): Promise<LearnersResponse> {
    const { user, tenant } = getCurrentUserContext()

    let query = supabase
      .from('users')
      .select(`
        *,
        roles:roles(*),
        learner_assignments:learner_assignments!learner_assignments_learner_id_fkey(
          *,
          learning_paths:learning_paths(id, title, difficulty_level, estimated_duration)
        ),
        group_members:group_members(
          groups:groups(*)
        )
      `)
      .eq('tenant_id', tenant.id)
      .neq('role_id', 1) // Exclude admin users

    // Apply filters
    if (filters?.search) {
      query = query.or(`full_name.ilike.%${filters.search}%,email.ilike.%${filters.search}%`)
    }

    if (filters?.status && filters.status !== 'all') {
      query = query.eq('status', filters.status)
    }

    if (filters?.role && filters.role !== 'all') {
      query = query.eq('role_id', filters.role)
    }

    if (filters?.department && filters.department !== 'all') {
      query = query.eq('department', filters.department)
    }

    if (filters?.last_active?.days) {
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - filters.last_active.days)
      query = query.gte('last_active', cutoffDate.toISOString())
    }

    // Apply sorting
    if (sort) {
      query = query.order(sort.field, { ascending: sort.direction === 'asc' })
    } else {
      query = query.order('created_at', { ascending: false })
    }

    // Apply pagination
    const from = (page - 1) * limit
    const to = from + limit - 1
    query = query.range(from, to)

    const { data, error, count } = await query

    if (error) throw error

    const learners = data?.map(transformLearner) || []

    return {
      data: learners,
      total: count || 0,
      page,
      limit,
      has_more: (count || 0) > page * limit
    }
  },

  // Get single learner by ID
  async getLearner(learnerId: string): Promise<Learner> {
    const { user, tenant } = getCurrentUserContext()

    const { data, error } = await supabase
      .from('users')
      .select(`
        *,
        roles:roles(*),
        learner_assignments:learner_assignments!learner_assignments_learner_id_fkey(
          *,
          learning_paths:learning_paths(id, title, difficulty_level, estimated_duration),
          assigned_by_user:users!learner_assignments_assigned_by_fkey(id, full_name, email)
        ),
        group_members:group_members(
          groups:groups(*)
        ),
        progress:progress(*),
        user_skills:user_skills(*)
      `)
      .eq('id', learnerId)
      .eq('tenant_id', tenant.id)
      .single()

    if (error) throw error

    return transformLearner(data)
  },

  // Create new learner
  async createLearner(learnerData: CreateLearnerData): Promise<Learner> {
    const { user, tenant } = getCurrentUserContext()

    try {
      // Step 1: Create user in Supabase Auth using Admin API
      const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
        email: learnerData.email,
        password: this.generateTemporaryPassword(),
        email_confirm: true, // Auto-confirm email
        user_metadata: {
          full_name: learnerData.full_name,
          department: learnerData.department,
          position: learnerData.position,
          phone: learnerData.phone,
          location: learnerData.location
        }
      })

      if (authError) {
        console.error('Auth user creation error:', authError)
        throw new Error(`Failed to create user account: ${authError.message}`)
      }

      if (!authUser.user) {
        throw new Error('Failed to create user account: No user returned')
      }

      // Step 2: Wait a moment for the trigger to create the profile
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Step 3: Update the user profile with additional data
      const { data: updatedUser, error: updateError } = await supabase
        .from('users')
        .update({
          role_id: learnerData.role_id,
          department: learnerData.department,
          position: learnerData.position,
          manager_id: learnerData.manager_id,
          phone: learnerData.phone,
          location: learnerData.location,
          status: 'active',
          updated_at: new Date().toISOString()
        })
        .eq('id', authUser.user.id)
        .eq('tenant_id', tenant.id)
        .select()
        .single()

      if (updateError) {
        console.error('Profile update error:', updateError)
        // If profile update fails, we should clean up the auth user
        await supabase.auth.admin.deleteUser(authUser.user.id)
        throw new Error(`Failed to update user profile: ${updateError.message}`)
      }

      // Handle custom fields if provided
      if (learnerData.custom_fields) {
        await this.updateCustomFields(authUser.user.id, learnerData.custom_fields)
      }

      // Send welcome email if requested
      if (learnerData.send_welcome_email) {
        await this.sendWelcomeEmail(authUser.user.id)
      }

      return transformLearner(updatedUser)
    } catch (error) {
      console.error('Create learner error:', error)
      throw error
    }
  },

  // Generate a temporary password for new users
  generateTemporaryPassword(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
    let password = ''
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return password
  },

  // Update learner
  async updateLearner(learnerId: string, updates: UpdateLearnerData): Promise<Learner> {
    const { user, tenant } = getCurrentUserContext()

    const { custom_fields, ...dbUpdates } = updates

    const { data, error } = await supabase
      .from('users')
      .update({
        ...dbUpdates,
        updated_at: new Date().toISOString()
      })
      .eq('id', learnerId)
      .eq('tenant_id', tenant.id)
      .select()
      .single()

    if (error) throw error

    // Handle custom fields if provided
    if (custom_fields) {
      await this.updateCustomFields(learnerId, custom_fields)
    }

    return transformLearner(data)
  },

  // Delete learner (soft delete)
  async deleteLearner(learnerId: string): Promise<void> {
    const { user, tenant } = getCurrentUserContext()

    const { error } = await supabase
      .from('users')
      .update({ 
        status: 'inactive',
        updated_at: new Date().toISOString()
      })
      .eq('id', learnerId)
      .eq('tenant_id', tenant.id)

    if (error) throw error
  },

  // Bulk import learners from CSV
  async bulkImportLearners(importData: BulkImportData): Promise<{ success: number; errors: string[] }> {
    try {
      const { data, error } = await supabase.functions.invoke('bulk-import-learners', {
        body: {
          file: importData.file,
          mapping: importData.mapping,
          send_welcome_emails: importData.send_welcome_emails,
          default_role_id: importData.default_role_id
        }
      })

      if (error) throw error
      return data
    } catch (error) {
      console.error('Bulk import error:', error)
      throw error
    }
  },

  // Assign learning path to learners
  async assignPath(assignmentData: AssignPathData): Promise<void> {
    const { user, tenant } = getCurrentUserContext()

    const assignments = assignmentData.learner_ids.map(learnerId => ({
      path_id: assignmentData.path_id,
      learner_id: learnerId,
      assigned_by: user.id,
      due_date: assignmentData.due_date,
      notes: assignmentData.notes,
      status: 'assigned'
    }))

    const { error } = await supabase
      .from('learner_assignments')
      .insert(assignments)

    if (error) throw error

    // Send notifications if requested
    if (assignmentData.send_notification) {
      await supabase.functions.invoke('send-assignment-notifications', {
        body: {
          learner_ids: assignmentData.learner_ids,
          path_id: assignmentData.path_id
        }
      })
    }
  },

  // Get learner statistics and analytics
  async getLearnerStats(): Promise<LearnerStatsResponse> {
    const { user, tenant } = getCurrentUserContext()

    try {
      const { data, error } = await supabase.functions.invoke('get-learner-analytics', {
        body: { tenant_id: tenant.id }
      })

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error fetching learner stats:', error)
      throw error
    }
  },

  // Update custom field values
  async updateCustomFields(userId: string, customFields: Record<string, any>): Promise<void> {
    const fieldValues = Object.entries(customFields).map(([fieldId, value]) => ({
      user_id: userId,
      field_id: parseInt(fieldId),
      value: value,
      updated_at: new Date().toISOString()
    }))

    const { error } = await supabase
      .from('user_custom_field_values')
      .upsert(fieldValues, { onConflict: 'user_id,field_id' })

    if (error) throw error
  },

  // Send welcome email
  async sendWelcomeEmail(userId: string): Promise<void> {
    try {
      await supabase.functions.invoke('send-welcome-email', {
        body: { user_id: userId }
      })
    } catch (error) {
      console.error('Error sending welcome email:', error)
      // Don't throw error for email failures
    }
  }
}

// Groups API
export const groupsApi = {
  // Get all groups
  async getGroups(): Promise<Group[]> {
    const { user, tenant } = getCurrentUserContext()

    const { data, error } = await supabase
      .from('groups')
      .select(`
        *,
        group_members:group_members(
          *,
          users:users(id, full_name, email, avatar_url)
        )
      `)
      .eq('tenant_id', tenant.id)
      .order('name')

    if (error) throw error

    return data?.map(group => ({
      ...group,
      member_count: group.group_members?.length || 0,
      members: group.group_members
    })) || []
  },

  // Create new group
  async createGroup(groupData: CreateGroupData): Promise<Group> {
    const { user, tenant } = getCurrentUserContext()

    const { member_ids, ...dbData } = groupData

    const { data, error } = await supabase
      .from('groups')
      .insert({
        ...dbData,
        tenant_id: tenant.id,
        created_by: user.id
      })
      .select()
      .single()

    if (error) throw error

    // Add members if provided
    if (member_ids && member_ids.length > 0) {
      await this.addMembersToGroup(data.id, member_ids)
    }

    return data
  },

  // Add members to group
  async addMembersToGroup(groupId: number, userIds: string[]): Promise<void> {
    const members = userIds.map(userId => ({
      group_id: groupId,
      user_id: userId,
      role: 'member',
      joined_at: new Date().toISOString()
    }))

    const { error } = await supabase
      .from('group_members')
      .insert(members)

    if (error) throw error
  },

  // Remove members from group
  async removeMembersFromGroup(groupId: number, userIds: string[]): Promise<void> {
    const { error } = await supabase
      .from('group_members')
      .delete()
      .eq('group_id', groupId)
      .in('user_id', userIds)

    if (error) throw error
  },

  // Delete group
  async deleteGroup(groupId: number): Promise<void> {
    const { user, tenant } = getCurrentUserContext()

    const { error } = await supabase
      .from('groups')
      .delete()
      .eq('id', groupId)
      .eq('tenant_id', tenant.id)

    if (error) throw error
  }
}

// Messaging API
export const messagingApi = {
  // Send message to learners
  async sendMessage(messageData: MessageData): Promise<void> {
    const { user, tenant } = getCurrentUserContext()

    try {
      const { error } = await supabase.functions.invoke('send-learner-message', {
        body: {
          ...messageData,
          sender_id: user.id,
          tenant_id: tenant.id
        }
      })

      if (error) throw error
    } catch (error) {
      console.error('Error sending message:', error)
      throw error
    }
  }
}

// Analytics API
export const analyticsApi = {
  // Get engagement alerts
  async getEngagementAlerts(): Promise<EngagementAlert[]> {
    const { user, tenant } = getCurrentUserContext()

    const { data, error } = await supabase
      .from('engagement_alerts')
      .select('*')
      .eq('tenant_id', tenant.id)
      .is('resolved_at', null)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  },

  // Get learner journey events
  async getLearnerJourney(userId: string, limit = 50): Promise<LearnerJourneyEvent[]> {
    const { user, tenant } = getCurrentUserContext()

    const { data, error } = await supabase
      .from('user_activity')
      .select('*')
      .eq('user_id', userId)
      .order('timestamp', { ascending: false })
      .limit(limit)

    if (error) throw error
    return data || []
  },

  // Resolve engagement alert
  async resolveAlert(alertId: number): Promise<void> {
    const { user } = getCurrentUserContext()

    const { error } = await supabase
      .from('engagement_alerts')
      .update({
        resolved_at: new Date().toISOString(),
        resolved_by: user.id
      })
      .eq('id', alertId)

    if (error) throw error
  }
}

// Custom Fields API
export const customFieldsApi = {
  // Get custom fields for tenant
  async getCustomFields(): Promise<CustomField[]> {
    const { user, tenant } = getCurrentUserContext()

    const { data, error } = await supabase
      .from('custom_fields')
      .select('*')
      .eq('tenant_id', tenant.id)
      .order('display_order')

    if (error) throw error
    return data || []
  },

  // Create custom field
  async createCustomField(fieldData: Omit<CustomField, 'id' | 'tenant_id' | 'created_at'>): Promise<CustomField> {
    const { user, tenant } = getCurrentUserContext()

    const { data, error } = await supabase
      .from('custom_fields')
      .insert({
        ...fieldData,
        tenant_id: tenant.id
      })
      .select()
      .single()

    if (error) throw error
    return data
  }
}
