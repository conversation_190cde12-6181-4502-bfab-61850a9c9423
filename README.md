# ZenithLearn AI - Admin Dashboard

A modern, AI-powered Learning Management System (LMS) built with Next.js 14, Material-UI, and Supabase.

## 🚀 Features

### MVP Features (3 Months)
- **Dashboard**: KPI widgets, charts, and scheduling calendar
- **Learning Path Management**: AI-powered path creation wizard with drag-and-drop editor
- **Learner Management**: User management with bulk operations and progress tracking
- **Content Library**: File upload, smart tagging, and content management
- **Reports**: Comprehensive reporting with export capabilities
- **Settings**: Tenant customization and user management

### Full Version Features (6 Months)
- **Advanced Analytics**: Predictive analytics with TensorFlow.js
- **Adaptive Learning**: Dynamic path adjustments based on performance
- **Gamification**: Badges, points, and leaderboards
- **Integrations**: External system integrations (HR tools, calendars)
- **Multilingual Support**: 5 languages with auto-detection
- **Advanced Collaboration**: Real-time chat and ticket system

## 🛠️ Tech Stack

- **Frontend**: Next.js 14.2.x with TypeScript and App Router
- **UI Library**: Material-UI (MUI) v5.15.x with custom theming
- **State Management**: Zustand for lightweight state management
- **Data Fetching**: TanStack React Query for caching and synchronization
- **Backend**: Supabase (PostgreSQL, Auth, Storage, Edge Functions)
- **Charts**: Chart.js with react-chartjs-2
- **Testing**: Jest, Testing Library, Playwright
- **Deployment**: Vercel for frontend, Supabase cloud for backend

## 📋 Prerequisites

- Node.js 18.x or higher
- npm or yarn
- Supabase account
- OpenAI API key (for AI features)

## 🚀 Getting Started

### 1. Clone the repository

```bash
git clone <repository-url>
cd zenithaiadmin
```

### 2. Install dependencies

```bash
npm install
```

### 3. Set up environment variables

Copy the example environment file and fill in your values:

```bash
cp .env.local.example .env.local
```

Required environment variables:
- `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key
- `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key
- `OPENAI_API_KEY`: Your OpenAI API key

### 4. Set up Supabase

1. Create a new Supabase project
2. Run the database migrations (SQL files will be provided)
3. Set up Row Level Security (RLS) policies
4. Configure authentication providers if needed

### 5. Run the development server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🎯 Current Status

The ZenithLearn AI project foundation has been successfully built and is ready for development! Here's what's currently working:

### ✅ Completed Features

1. **Project Architecture**
   - Next.js 14.2.x with TypeScript and App Router
   - Material-UI v5.15.x with custom theming (light/dark mode support)
   - Zustand for state management
   - React Query for data fetching and caching
   - Comprehensive folder structure

2. **UI Components**
   - Responsive dashboard layout with sidebar navigation
   - KPI metric cards with trend indicators
   - Interactive charts using Chart.js
   - Recent activity feed
   - Quick actions panel
   - Notification system

3. **Authentication System**
   - Auth provider structure ready for Supabase integration
   - Role-based access control framework
   - Protected routes and permissions system

4. **Testing Infrastructure**
   - Jest configuration for unit tests
   - Playwright setup for E2E tests
   - Sample tests for components
   - Accessibility testing with axe-core

5. **Demo Page**
   - Fully functional demo at `/demo` route
   - Mock data showcasing all dashboard features
   - Responsive design for mobile and desktop

### 🚀 Demo

Visit [http://localhost:3000/demo](http://localhost:3000/demo) to see the application in action with mock data.

### 🔧 Next Steps

To complete the MVP, you'll need to:

1. **Set up Supabase**
   - Create a Supabase project
   - Run the provided database migrations
   - Update environment variables with real Supabase credentials

2. **Implement Core Features**
   - Learning path creation wizard
   - Learner management system
   - Content library with file uploads
   - Real-time progress tracking

3. **AI Integration**
   - Connect OpenAI API for content generation
   - Implement AI chat assistant
   - Add smart content tagging

4. **Deploy**
   - Deploy to Vercel
   - Set up CI/CD pipeline
   - Configure monitoring and analytics

## 🧪 Testing

### Test Learner Accounts

For testing the admin dashboard analytics and learner management features, use these pre-configured test accounts:

| **Name** | **Email** | **Password** | **Status** | **Department** | **Job Title** | **Location** |
|----------|-----------|--------------|------------|----------------|---------------|--------------|
| Sarah Johnson | `<EMAIL>` | `TestPass123!` | **Active** | Marketing | Marketing Specialist | New York |
| Michael Chen | `<EMAIL>` | `TestPass123!` | **Active** | Engineering | Software Developer | San Francisco |
| Emily Rodriguez | `<EMAIL>` | `TestPass123!` | **Inactive** | Sales | Sales Representative | Chicago |
| David Thompson | `<EMAIL>` | `TestPass123!` | **Active** | HR | HR Coordinator | Austin |
| Lisa Wang | `<EMAIL>` | `TestPass123!` | **Inactive** | Finance | Financial Analyst | Seattle |

#### Test Data Features:
- **Tenant ID**: `3` (all users belong to the same tenant for multi-tenant testing)
- **Role**: Learner role (role_id: 12)
- **Learning Assignments**: Each learner has different learning paths with varying completion rates
- **Progress Data**: Realistic progress tracking with scores, time spent, and completion percentages
- **Status Variety**: Mix of active/inactive users and various assignment statuses for comprehensive analytics testing

#### Expected Analytics Results:
- **Total Learners**: 5
- **Active Learners**: 3 (Sarah, Michael, David)
- **Inactive Learners**: 2 (Emily, Lisa)
- **Overdue Assignments**: 2 (Emily, Lisa)
- **At-Risk Learners**: 2 (users with overdue assignments)
- **Average Completion Rate**: ~50% (based on progress data)

### Unit Tests

```bash
npm test
```

### E2E Tests

```bash
npm run test:e2e
```

### Type Checking

```bash
npm run type-check
```

## 📁 Project Structure

```
├── app/                    # Next.js App Router pages
│   ├── dashboard/         # Dashboard pages
│   ├── login/            # Authentication pages
│   └── layout.tsx        # Root layout
├── components/           # Reusable UI components
│   ├── dashboard/       # Dashboard-specific components
│   ├── layout/          # Layout components
│   └── providers/       # Context providers
├── lib/                 # Utility libraries
│   ├── auth.ts         # Authentication utilities
│   ├── store.ts        # Zustand stores
│   ├── supabase.ts     # Supabase client
│   └── theme.ts        # MUI theme configuration
├── __tests__/          # Unit tests
├── tests/e2e/          # E2E tests
└── public/             # Static assets
```

## 🎨 Design System

The application uses Material-UI with a custom theme that supports:
- Light and dark modes
- Tenant-specific branding
- Responsive design
- Accessibility (WCAG 2.1 AA compliance)

## 🔐 Authentication & Authorization

- Supabase Auth with email/password
- Role-based access control (RBAC)
- Multi-tenant architecture with data isolation
- Row Level Security (RLS) policies

## 📊 Database Schema

The application uses PostgreSQL with the following main tables:
- `tenants`: Multi-tenant organization data
- `users`: User profiles and roles
- `learning_paths`: Learning path definitions
- `modules` & `lessons`: Course content structure
- `progress`: Learner progress tracking
- `assignments`: Path assignments to learners

## 🚀 Deployment

### Vercel (Recommended)

1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Manual Deployment

```bash
npm run build
npm start
```

## 🔧 Configuration

### Environment Variables

See `.env.local.example` for all available configuration options.

### Supabase Configuration

1. Database setup with RLS policies
2. Storage buckets for file uploads
3. Edge Functions for AI integration
4. Authentication providers configuration

## 📈 Performance

- Target: <500ms page loads
- Support: 10,000 concurrent users per tenant
- Optimization: Image optimization, code splitting, caching

## 🛡️ Security

- HTTPS enforcement
- Content Security Policy (CSP)
- XSS protection
- CSRF protection
- Data encryption at rest and in transit

## 🌐 Internationalization

- Support for 5 languages
- Auto-detection based on browser locale
- RTL language support

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue in the GitHub repository
- Contact the development team
- Check the documentation

## 🗺️ Roadmap

### Phase 1 (MVP - 3 months)
- [x] Project setup and architecture
- [x] Next.js 14.2.x with TypeScript and App Router
- [x] Material-UI v5.15.x with custom theming
- [x] Zustand state management
- [x] React Query for data fetching
- [x] Authentication system structure
- [x] Dashboard with KPI widgets and charts
- [x] Responsive layout with sidebar navigation
- [x] Testing setup (Jest + Playwright)
- [x] Demo page with mock data
- [ ] Supabase integration and database setup
- [ ] Learning path creation wizard
- [ ] Learner management system
- [ ] Content library with file upload
- [ ] Comprehensive reporting system

### Phase 2 (Full Version - 6 months)
- [ ] Advanced analytics
- [ ] AI-powered features
- [ ] Gamification
- [ ] Integrations
- [ ] Mobile app
- [ ] Advanced collaboration tools

---

Built with ❤️ by the ZenithLearn AI team
