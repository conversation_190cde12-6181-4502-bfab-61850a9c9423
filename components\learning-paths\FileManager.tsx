'use client'

import React, { useState, useEffect } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  IconButton,
  Chip,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Avatar,
  LinearProgress,
  Alert,
  Tooltip
} from '@mui/material'
import {
  CloudUpload as UploadIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Visibility as ViewIcon,
  VideoFile as VideoIcon,
  PictureAsPdf as PdfIcon,
  Slideshow as PptIcon,
  Image as ImageIcon,
  AudioFile as AudioIcon,
  InsertDriveFile as FileIcon,
  Security as SecurityIcon,
  Schedule as TimeIcon,
  Storage as StorageIcon
} from '@mui/icons-material'
import { format } from 'date-fns'
import { StorageService, FileUploadResult } from '@/lib/services/storageService'

interface FileManagerProps {
  files: FileUploadResult[]
  onFileDelete?: (filePath: string) => void
  onFileView?: (file: FileUploadResult) => void
  showUploadStats?: boolean
  tenantId?: number
}

interface StorageStats {
  totalFiles: number
  totalSize: number
  videoFiles: number
  documentFiles: number
  presentationFiles: number
  imageFiles: number
}

export default function FileManager({
  files,
  onFileDelete,
  onFileView,
  showUploadStats = false,
  tenantId
}: FileManagerProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedFile, setSelectedFile] = useState<FileUploadResult | null>(null)
  const [storageStats, setStorageStats] = useState<StorageStats | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  useEffect(() => {
    if (showUploadStats && tenantId) {
      loadStorageStats()
    }
  }, [showUploadStats, tenantId])

  const loadStorageStats = async () => {
    try {
      // This would call a Supabase function to get storage stats
      // For now, calculate from provided files
      const stats: StorageStats = {
        totalFiles: files.length,
        totalSize: files.reduce((total, file) => total + file.metadata.size, 0),
        videoFiles: files.filter(f => f.metadata.type.startsWith('video/')).length,
        documentFiles: files.filter(f => f.metadata.type === 'application/pdf' || 
          f.metadata.type.includes('document')).length,
        presentationFiles: files.filter(f => f.metadata.type.includes('presentation')).length,
        imageFiles: files.filter(f => f.metadata.type.startsWith('image/')).length
      }
      setStorageStats(stats)
    } catch (error) {
      console.error('Error loading storage stats:', error)
    }
  }

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('video/')) return <VideoIcon />
    if (mimeType === 'application/pdf') return <PdfIcon />
    if (mimeType.includes('presentation')) return <PptIcon />
    if (mimeType.startsWith('image/')) return <ImageIcon />
    if (mimeType.startsWith('audio/')) return <AudioIcon />
    return <FileIcon />
  }

  const getFileTypeColor = (mimeType: string): 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success' => {
    if (mimeType.startsWith('video/')) return 'primary'
    if (mimeType === 'application/pdf') return 'error'
    if (mimeType.includes('presentation')) return 'warning'
    if (mimeType.startsWith('image/')) return 'success'
    if (mimeType.startsWith('audio/')) return 'info'
    return 'secondary'
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleDeleteClick = (file: FileUploadResult) => {
    setSelectedFile(file)
    setDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = async () => {
    if (!selectedFile) return

    setIsDeleting(true)
    try {
      await StorageService.deleteFile(selectedFile.path)
      onFileDelete?.(selectedFile.path)
      setDeleteDialogOpen(false)
      setSelectedFile(null)
    } catch (error) {
      console.error('Error deleting file:', error)
    } finally {
      setIsDeleting(false)
    }
  }

  const handleDownload = async (file: FileUploadResult) => {
    try {
      const signedUrl = await StorageService.getSignedUrl(file.path, 300) // 5 minutes
      window.open(signedUrl, '_blank')
    } catch (error) {
      console.error('Error downloading file:', error)
    }
  }

  if (files.length === 0) {
    return (
      <Card>
        <CardContent sx={{ textAlign: 'center', py: 4 }}>
          <UploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No files uploaded yet
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Upload files to see them here
          </Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Box>
      {/* Storage Statistics */}
      {showUploadStats && storageStats && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Storage Overview
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6} sm={3}>
                <Box textAlign="center">
                  <Typography variant="h4" color="primary">
                    {storageStats.totalFiles}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Total Files
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Box textAlign="center">
                  <Typography variant="h4" color="secondary">
                    {formatFileSize(storageStats.totalSize)}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Total Size
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Box textAlign="center">
                  <Typography variant="h4" color="info.main">
                    {storageStats.videoFiles}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Videos
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Box textAlign="center">
                  <Typography variant="h4" color="success.main">
                    {storageStats.documentFiles}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Documents
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Files List */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Uploaded Files ({files.length})
          </Typography>
          
          <List>
            {files.map((file, index) => (
              <ListItem key={index} divider={index < files.length - 1}>
                <ListItemIcon>
                  <Avatar sx={{ bgcolor: 'transparent' }}>
                    {getFileIcon(file.metadata.type)}
                  </Avatar>
                </ListItemIcon>
                
                <ListItemText
                  primary={
                    <Box display="flex" alignItems="center" gap={1}>
                      <Typography variant="body1" noWrap>
                        {file.metadata.name}
                      </Typography>
                      <Chip
                        label={file.metadata.type.split('/')[1]?.toUpperCase() || 'FILE'}
                        size="small"
                        color={getFileTypeColor(file.metadata.type)}
                        variant="outlined"
                      />
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        {formatFileSize(file.metadata.size)} • 
                        {format(new Date(file.metadata.lastModified), 'MMM dd, yyyy HH:mm')}
                      </Typography>
                      <Box display="flex" alignItems="center" gap={1} mt={0.5}>
                        <SecurityIcon fontSize="small" color="success" />
                        <Typography variant="caption" color="success.main">
                          Securely stored with tenant isolation
                        </Typography>
                      </Box>
                    </Box>
                  }
                />
                
                <ListItemSecondaryAction>
                  <Box display="flex" gap={1}>
                    <Tooltip title="View">
                      <IconButton
                        size="small"
                        onClick={() => onFileView?.(file)}
                      >
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                    
                    <Tooltip title="Download">
                      <IconButton
                        size="small"
                        onClick={() => handleDownload(file)}
                      >
                        <DownloadIcon />
                      </IconButton>
                    </Tooltip>
                    
                    <Tooltip title="Delete">
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => handleDeleteClick(file)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete File</DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            This action cannot be undone. The file will be permanently deleted from storage.
          </Alert>
          <Typography>
            Are you sure you want to delete "{selectedFile?.metadata.name}"?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            disabled={isDeleting}
          >
            {isDeleting ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}
