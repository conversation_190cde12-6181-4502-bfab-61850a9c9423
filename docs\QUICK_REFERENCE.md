# Quick Reference Guide

This is a condensed reference for the Groups and Batches feature, perfect for developers who need quick access to key information.

## 🚀 Quick Start Checklist

- [ ] Install dependencies: `npm install @mui/material @emotion/react @emotion/styled`
- [ ] Run database migrations from `docs/database/migrations/`
- [ ] Copy component files to `components/groups/`
- [ ] Add navigation item to dashboard
- [ ] Configure environment variables
- [ ] Test basic functionality

## 📊 Database Tables

| Table | Purpose | Key Columns |
|-------|---------|-------------|
| `groups` | Core group data | `id`, `tenant_id`, `name`, `status`, `settings` |
| `group_members` | Member relationships | `group_id`, `learner_id`, `role` |
| `group_assignments` | Path assignments | `group_id`, `path_id`, `due_date`, `is_mandatory` |
| `group_progress` | Progress tracking | `group_id`, `path_id`, `completion_rate` |
| `group_messages` | Communication | `group_id`, `message`, `channels` |
| `group_milestones` | Timeline tracking | `group_id`, `title`, `due_date` |
| `assignment_templates` | Reusable configs | `tenant_id`, `name`, `template_data` |

## 🔧 Essential API Endpoints

### Groups
```typescript
// Get groups
GET /groups?tenant_id=eq.1&status=eq.active

// Create group
POST /groups
{ name: "Group Name", tenant_id: 1, created_by: "uuid" }

// Update group
PATCH /groups?id=eq.1
{ name: "Updated Name" }
```

### Members
```typescript
// Get members
GET /group_members?group_id=eq.1

// Add member
POST /group_members
{ group_id: 1, learner_id: "uuid", role: "member" }

// Update role
PATCH /group_members?id=eq.1
{ role: "moderator" }
```

### Assignments
```typescript
// Get assignments
GET /group_assignments?group_id=eq.1

// Assign path
POST /group_assignments
{ group_id: 1, path_id: 5, is_mandatory: true }
```

## 🎨 Key Components

### GroupsDashboard
```tsx
import GroupsDashboard from '@/components/groups/GroupsDashboard'

<GroupsDashboard
  groups={groups}
  loading={loading}
  onCreateGroup={() => setCreateModalOpen(true)}
  onEditGroup={(group) => setEditingGroup(group)}
/>
```

### GroupForm
```tsx
import GroupForm from '@/components/groups/GroupForm'

<GroupForm
  open={formOpen}
  onClose={() => setFormOpen(false)}
  onSubmit={handleSubmit}
  group={editingGroup}
/>
```

### MemberManagement
```tsx
import MemberManagement from '@/components/groups/MemberManagement'

<MemberManagement
  group={selectedGroup}
  members={members}
  onRefresh={loadGroupDetails}
/>
```

## 🏪 Store Usage

```typescript
import { useGroupsStore } from '@/lib/store'

function MyComponent() {
  const {
    groups,
    selectedGroup,
    loading,
    setGroups,
    addGroup,
    updateGroup,
    deleteGroup,
    setSelectedGroup,
  } = useGroupsStore()

  // Use store data and actions
}
```

## 🔄 Real-time Subscriptions

```typescript
// Subscribe to group changes
const subscription = supabase
  .channel('groups-changes')
  .on('postgres_changes', 
    { 
      event: '*', 
      schema: 'public', 
      table: 'groups',
      filter: `tenant_id=eq.${tenantId}`
    }, 
    (payload) => {
      // Handle real-time updates
    }
  )
  .subscribe()

// Clean up
return () => subscription.unsubscribe()
```

## 🎛️ Feature Flags

```typescript
// Enable/disable features
const groupFeatureFlags = {
  enable_group_creation: true,
  enable_group_hierarchy: true,
  enable_bulk_member_add: true,
  enable_group_communication: true,
  enable_ai_grouping: false,
  enable_advanced_analytics: true,
}
```

## 🔒 Common RLS Patterns

```sql
-- View groups in tenant
CREATE POLICY "tenant_groups" ON groups
  FOR SELECT USING (
    tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid())
  );

-- Create groups in tenant
CREATE POLICY "create_groups" ON groups
  FOR INSERT WITH CHECK (
    tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid())
    AND created_by = auth.uid()
  );
```

## 📈 Progress Calculation

```sql
-- Refresh group progress
SELECT refresh_group_progress(1); -- For specific group
SELECT refresh_group_progress(); -- For all groups
```

## 🎨 Common Styling Patterns

```tsx
// Responsive grid
<Grid container spacing={{ xs: 2, md: 3 }}>
  <Grid item xs={12} sm={6} md={4}>
    <GroupCard group={group} />
  </Grid>
</Grid>

// Status chips
<Chip
  label={group.status}
  color={getStatusColor(group.status)}
  variant="outlined"
/>

// Progress indicators
<LinearProgress
  variant="determinate"
  value={completionRate}
  sx={{ height: 6, borderRadius: 3 }}
/>
```

## 🧪 Testing Patterns

```typescript
// Mock Supabase
const mockSupabase = {
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      eq: jest.fn(() => Promise.resolve({ data: [], error: null }))
    }))
  }))
}

// Test component
render(
  <ThemeProvider theme={theme}>
    <GroupCard group={mockGroup} />
  </ThemeProvider>
)
```

## 🚨 Common Issues & Quick Fixes

### Groups not loading
```typescript
// Check authentication
const { data: { user } } = await supabase.auth.getUser()
console.log('User:', user)

// Check RLS policies
SELECT * FROM pg_policies WHERE tablename = 'groups';
```

### Real-time not working
```sql
-- Check realtime enabled
SELECT * FROM pg_publication_tables WHERE pubname = 'supabase_realtime';

-- Enable if missing
ALTER PUBLICATION supabase_realtime ADD TABLE groups;
```

### TypeScript errors
```bash
# Regenerate types
supabase gen types typescript --local > lib/types/supabase.ts

# Check imports
import { Group } from '@/lib/types/groups'
```

## 📱 Mobile Considerations

```tsx
// Responsive breakpoints
const isMobile = useMediaQuery(theme.breakpoints.down('sm'))

// Mobile-friendly components
<Drawer
  variant={isMobile ? 'temporary' : 'permanent'}
  open={isMobile ? drawerOpen : true}
/>

// Touch-friendly interactions
<IconButton size="large" sx={{ p: 2 }}>
  <EditIcon />
</IconButton>
```

## 🔧 Performance Tips

```typescript
// Lazy load components
const GroupForm = lazy(() => import('@/components/groups/GroupForm'))

// Memoize expensive calculations
const groupStats = useMemo(() => 
  calculateGroupStatistics(groups), [groups]
)

// Debounce search
const debouncedSearch = useDebounce(searchTerm, 300)
```

## 📊 Analytics Queries

```sql
-- Group usage statistics
SELECT 
  COUNT(*) as total_groups,
  COUNT(CASE WHEN status = 'active' THEN 1 END) as active_groups,
  AVG(member_count) as avg_members_per_group
FROM groups 
WHERE tenant_id = 1;

-- Top performing groups
SELECT 
  g.name,
  AVG(gp.completion_rate) as avg_completion
FROM groups g
JOIN group_progress gp ON g.id = gp.group_id
GROUP BY g.id, g.name
ORDER BY avg_completion DESC
LIMIT 10;
```

## 🎯 Quick Deployment

```bash
# Build for production
npm run build

# Check for errors
npm run lint
npm run type-check

# Deploy to Vercel
vercel --prod

# Or deploy to other platforms
npm run export
```

## 📞 Support Resources

- **Documentation**: `/docs/` folder
- **API Reference**: `/docs/api/reference.md`
- **Troubleshooting**: `/docs/troubleshooting/common-issues.md`
- **Examples**: `/docs/examples/basic-usage.md`
- **Contributing**: `/docs/CONTRIBUTING.md`

---

*This quick reference covers the most commonly used features and patterns. For detailed information, refer to the complete documentation.*
