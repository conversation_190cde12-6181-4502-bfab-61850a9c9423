## 📋 Pull Request Description

### 🎯 What does this PR do?
Brief description of the changes made in this pull request.

### 🔗 Related Issues
Fixes #(issue_number)
Closes #(issue_number)
Related to #(issue_number)

## 🧪 Type of Change
Please delete options that are not relevant:

- [ ] 🐛 Bug fix (non-breaking change which fixes an issue)
- [ ] ✨ New feature (non-breaking change which adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 Documentation update
- [ ] 🎨 Style/UI changes
- [ ] ♻️ Code refactoring (no functional changes)
- [ ] ⚡ Performance improvements
- [ ] 🧪 Test additions or updates
- [ ] 🔧 Build/CI changes

## 🔍 Changes Made
Detailed list of changes:

- Change 1
- Change 2
- Change 3

## 📸 Screenshots (if applicable)
Add screenshots to help reviewers understand the changes:

### Before
<!-- Add before screenshots here -->

### After
<!-- Add after screenshots here -->

## ✅ Testing Checklist
Please confirm you have completed the following:

### Code Quality
- [ ] Code follows the project's coding standards
- [ ] Self-review of code completed
- [ ] Code is properly commented, particularly in hard-to-understand areas
- [ ] No console.log statements left in production code
- [ ] TypeScript types are properly defined

### Testing
- [ ] Unit tests added/updated for new functionality
- [ ] Integration tests added/updated if applicable
- [ ] E2E tests added/updated for critical user flows
- [ ] All existing tests pass
- [ ] New tests pass
- [ ] Manual testing completed

### Documentation
- [ ] Documentation updated (if applicable)
- [ ] README updated (if applicable)
- [ ] API documentation updated (if applicable)
- [ ] Comments added to complex code sections

### Performance & Security
- [ ] Performance impact considered and tested
- [ ] Security implications reviewed
- [ ] No sensitive data exposed
- [ ] Accessibility requirements met (WCAG 2.1 AA)

### Dependencies
- [ ] No unnecessary dependencies added
- [ ] Package.json updated appropriately
- [ ] Lock file updated

## 🚀 Deployment Notes
Any special deployment considerations:

- [ ] Database migrations required
- [ ] Environment variables need to be updated
- [ ] Third-party service configuration needed
- [ ] Cache clearing required
- [ ] Feature flags need to be toggled

## 📊 Performance Impact
Describe any performance implications:

- Bundle size impact: [increase/decrease/no change]
- Runtime performance: [improved/degraded/no change]
- Database queries: [optimized/new queries added/no change]

## 🔒 Security Considerations
Any security implications of this change:

- [ ] No new security vulnerabilities introduced
- [ ] Input validation implemented where needed
- [ ] Authentication/authorization properly handled
- [ ] Data sanitization implemented

## 🧭 Migration Guide (if breaking change)
If this is a breaking change, provide migration instructions:

```bash
# Migration steps
```

## 📝 Additional Notes
Any additional information that reviewers should know:

## 🏷️ Labels
Please add appropriate labels to this PR:
- Priority: `priority/low`, `priority/medium`, `priority/high`, `priority/critical`
- Size: `size/XS`, `size/S`, `size/M`, `size/L`, `size/XL`
- Type: `type/bug`, `type/feature`, `type/docs`, `type/refactor`

---

## 👀 Reviewer Checklist
For reviewers to complete:

- [ ] Code review completed
- [ ] Tests reviewed and verified
- [ ] Documentation reviewed
- [ ] Security implications considered
- [ ] Performance impact assessed
- [ ] Breaking changes identified and documented
