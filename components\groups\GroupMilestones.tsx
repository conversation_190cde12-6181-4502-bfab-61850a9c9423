'use client'

import { useState } from 'react'
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Chip,
  LinearProgress,
  IconButton,
  Avatar,
  Tooltip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Stack,
} from '@mui/material'
import {
  Add as AddIcon,
  Flag as FlagIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  Warning as WarningIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { useForm, Controller } from 'react-hook-form'

import { Group, GroupMilestone } from '@/lib/types/groups'
import { GroupsService } from '@/lib/services/groups'

interface GroupMilestonesProps {
  group: Group
  onRefresh: () => void
}

interface MilestoneFormData {
  title: string
  description: string
  due_date: Date | null
  completion_criteria: any
}

interface MilestoneDialogProps {
  open: boolean
  onClose: () => void
  onSubmit: (data: MilestoneFormData) => void
  milestone?: GroupMilestone | null
  loading?: boolean
}

function MilestoneDialog({ open, onClose, onSubmit, milestone, loading }: MilestoneDialogProps) {
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isValid },
  } = useForm<MilestoneFormData>({
    defaultValues: {
      title: milestone?.title || '',
      description: milestone?.description || '',
      due_date: milestone?.due_date ? new Date(milestone.due_date) : null,
      completion_criteria: milestone?.completion_criteria || {},
    },
  })

  const handleFormSubmit = (data: MilestoneFormData) => {
    onSubmit(data)
    reset()
  }

  const handleClose = () => {
    reset()
    onClose()
  }

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {milestone ? 'Edit Milestone' : 'Create Milestone'}
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <Controller
              name="title"
              control={control}
              rules={{ required: 'Title is required' }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Milestone Title"
                  fullWidth
                  error={!!errors.title}
                  helperText={errors.title?.message}
                  placeholder="Enter milestone title"
                />
              )}
            />
          </Grid>

          <Grid item xs={12}>
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Description"
                  fullWidth
                  multiline
                  rows={3}
                  placeholder="Describe what needs to be accomplished"
                />
              )}
            />
          </Grid>

          <Grid item xs={12}>
            <Controller
              name="due_date"
              control={control}
              rules={{ required: 'Due date is required' }}
              render={({ field }) => (
                <DatePicker
                  {...field}
                  label="Due Date"
                  minDate={new Date()}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      error: !!errors.due_date,
                      helperText: errors.due_date?.message,
                    },
                  }}
                />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Cancel</Button>
        <Button
          onClick={handleSubmit(handleFormSubmit)}
          variant="contained"
          disabled={!isValid || loading}
        >
          {loading ? 'Saving...' : milestone ? 'Update' : 'Create'}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default function GroupMilestones({ group, onRefresh }: GroupMilestonesProps) {
  const [milestoneDialogOpen, setMilestoneDialogOpen] = useState(false)
  const [editingMilestone, setEditingMilestone] = useState<GroupMilestone | null>(null)
  const [loading, setLoading] = useState(false)
  const [milestones, setMilestones] = useState<GroupMilestone[]>([
    {
      id: 1,
      group_id: group.id,
      title: 'Complete JavaScript Fundamentals',
      description: 'All members should complete the JavaScript basics course',
      due_date: '2024-02-15T00:00:00Z',
      completion_criteria: { completion_rate: 80 },
      is_completed: false,
      created_by: 'admin',
      created_at: '2024-01-01T00:00:00Z',
    },
    {
      id: 2,
      group_id: group.id,
      title: 'Mid-term Assessment',
      description: 'Complete the mid-term assessment with minimum 70% score',
      due_date: '2024-03-01T00:00:00Z',
      completion_criteria: { minimum_score: 70 },
      is_completed: false,
      created_by: 'admin',
      created_at: '2024-01-01T00:00:00Z',
    },
  ])

  const handleCreateMilestone = async (data: MilestoneFormData) => {
    try {
      setLoading(true)
      const milestoneData = {
        group_id: group.id,
        title: data.title,
        description: data.description,
        due_date: data.due_date?.toISOString() || '',
        completion_criteria: data.completion_criteria,
      }
      
      // TODO: Call GroupsService.createMilestone
      console.log('Creating milestone:', milestoneData)
      
      // Mock adding milestone
      const newMilestone: GroupMilestone = {
        id: Date.now(),
        ...milestoneData,
        is_completed: false,
        created_by: 'current_user',
        created_at: new Date().toISOString(),
      }
      setMilestones([...milestones, newMilestone])
      setMilestoneDialogOpen(false)
    } catch (error) {
      console.error('Failed to create milestone:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleEditMilestone = async (data: MilestoneFormData) => {
    if (!editingMilestone) return

    try {
      setLoading(true)
      // TODO: Call GroupsService.updateMilestone
      console.log('Updating milestone:', data)
      
      // Mock updating milestone
      setMilestones(milestones.map(m => 
        m.id === editingMilestone.id 
          ? { ...m, ...data, due_date: data.due_date?.toISOString() || m.due_date }
          : m
      ))
      setEditingMilestone(null)
    } catch (error) {
      console.error('Failed to update milestone:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteMilestone = async (milestoneId: number) => {
    try {
      // TODO: Call GroupsService.deleteMilestone
      console.log('Deleting milestone:', milestoneId)
      setMilestones(milestones.filter(m => m.id !== milestoneId))
    } catch (error) {
      console.error('Failed to delete milestone:', error)
    }
  }

  const handleToggleComplete = async (milestoneId: number) => {
    try {
      // TODO: Call GroupsService.toggleMilestoneComplete
      console.log('Toggling milestone completion:', milestoneId)
      setMilestones(milestones.map(m => 
        m.id === milestoneId 
          ? { ...m, is_completed: !m.is_completed, completed_at: !m.is_completed ? new Date().toISOString() : undefined }
          : m
      ))
    } catch (error) {
      console.error('Failed to toggle milestone completion:', error)
    }
  }

  const getMilestoneStatus = (milestone: GroupMilestone) => {
    if (milestone.is_completed) return 'completed'
    
    const now = new Date()
    const dueDate = new Date(milestone.due_date)
    const daysUntilDue = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
    
    if (daysUntilDue < 0) return 'overdue'
    if (daysUntilDue <= 7) return 'due_soon'
    return 'on_track'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success'
      case 'overdue':
        return 'error'
      case 'due_soon':
        return 'warning'
      case 'on_track':
        return 'info'
      default:
        return 'default'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon />
      case 'overdue':
        return <WarningIcon />
      case 'due_soon':
        return <ScheduleIcon />
      case 'on_track':
        return <FlagIcon />
      default:
        return <FlagIcon />
    }
  }

  const getDaysRemaining = (dueDate: string) => {
    const now = new Date()
    const due = new Date(dueDate)
    const daysRemaining = Math.ceil((due.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
    return daysRemaining
  }

  const completedMilestones = milestones.filter(m => m.is_completed).length
  const totalMilestones = milestones.length
  const completionRate = totalMilestones > 0 ? (completedMilestones / totalMilestones) * 100 : 0

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">Group Milestones</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setMilestoneDialogOpen(true)}
        >
          Add Milestone
        </Button>
      </Box>

      {/* Progress Summary */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Typography variant="h6" gutterBottom>
                Milestone Progress
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  {completedMilestones} of {totalMilestones} milestones completed
                </Typography>
                <Typography variant="h6" color="primary">
                  {completionRate.toFixed(0)}%
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={completionRate}
                sx={{ height: 8, borderRadius: 4 }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Chip
                  icon={<CheckCircleIcon />}
                  label={`${completedMilestones} Completed`}
                  color="success"
                  variant="outlined"
                />
                <Chip
                  icon={<ScheduleIcon />}
                  label={`${totalMilestones - completedMilestones} Pending`}
                  color="warning"
                  variant="outlined"
                />
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Milestones Timeline */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Timeline
          </Typography>
          
          {milestones.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 8 }}>
              <FlagIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No milestones set
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Create milestones to track group progress and achievements
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setMilestoneDialogOpen(true)}
              >
                Create First Milestone
              </Button>
            </Box>
          ) : (
            <List sx={{ width: '100%' }}>
              {milestones
                .sort((a, b) => new Date(a.due_date).getTime() - new Date(b.due_date).getTime())
                .map((milestone, index) => {
                  const status = getMilestoneStatus(milestone)
                  const daysRemaining = getDaysRemaining(milestone.due_date)

                  return (
                    <Box key={milestone.id}>
                      <ListItem
                        alignItems="flex-start"
                        sx={{
                          py: 3,
                          px: 2,
                          border: 1,
                          borderColor: 'divider',
                          borderRadius: 2,
                          mb: 2,
                          bgcolor: milestone.is_completed ? 'success.50' : 'background.paper',
                        }}
                      >
                        <ListItemIcon sx={{ mt: 1 }}>
                          <Avatar
                            sx={{
                              bgcolor: getStatusColor(status) === 'success' ? 'success.main' :
                                      getStatusColor(status) === 'error' ? 'error.main' :
                                      getStatusColor(status) === 'warning' ? 'warning.main' : 'info.main',
                              width: 40,
                              height: 40,
                            }}
                          >
                            {getStatusIcon(status)}
                          </Avatar>
                        </ListItemIcon>

                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                              <Typography variant="h6" component="span">
                                {milestone.title}
                              </Typography>
                              <Chip
                                label={status.replace('_', ' ')}
                                color={getStatusColor(status) as any}
                                size="small"
                                variant="outlined"
                              />
                              {milestone.is_completed && milestone.completed_at && (
                                <Chip
                                  label={`Completed ${new Date(milestone.completed_at).toLocaleDateString()}`}
                                  color="success"
                                  size="small"
                                  variant="outlined"
                                />
                              )}
                            </Box>
                          }
                          secondary={
                            <Box>
                              {milestone.description && (
                                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                  {milestone.description}
                                </Typography>
                              )}
                              <Stack direction="row" spacing={2} alignItems="center">
                                <Typography variant="body2" color="text.secondary">
                                  Due: {new Date(milestone.due_date).toLocaleDateString()}
                                </Typography>
                                {!milestone.is_completed && (
                                  <Typography
                                    variant="body2"
                                    color={daysRemaining > 0 ? 'text.secondary' : 'error.main'}
                                  >
                                    {daysRemaining > 0 ? `${daysRemaining} days left` : `${Math.abs(daysRemaining)} days overdue`}
                                  </Typography>
                                )}
                              </Stack>
                            </Box>
                          }
                        />

                        <ListItemSecondaryAction>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Tooltip title={milestone.is_completed ? 'Mark as incomplete' : 'Mark as complete'}>
                              <IconButton
                                size="small"
                                onClick={() => handleToggleComplete(milestone.id)}
                                color={milestone.is_completed ? 'success' : 'default'}
                              >
                                <CheckCircleIcon />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Edit milestone">
                              <IconButton
                                size="small"
                                onClick={() => {
                                  setEditingMilestone(milestone)
                                  setMilestoneDialogOpen(true)
                                }}
                              >
                                <EditIcon />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Delete milestone">
                              <IconButton
                                size="small"
                                onClick={() => handleDeleteMilestone(milestone.id)}
                                color="error"
                              >
                                <DeleteIcon />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </ListItemSecondaryAction>
                      </ListItem>
                    </Box>
                  )
                })}
            </List>
          )}
        </CardContent>
      </Card>

      {/* Milestone Dialog */}
      <MilestoneDialog
        open={milestoneDialogOpen}
        onClose={() => {
          setMilestoneDialogOpen(false)
          setEditingMilestone(null)
        }}
        onSubmit={editingMilestone ? handleEditMilestone : handleCreateMilestone}
        milestone={editingMilestone}
        loading={loading}
      />
    </Box>
  )
}
