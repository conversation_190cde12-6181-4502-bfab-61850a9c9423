import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { createClient } from '@supabase/supabase-js'

// Helper function to get authenticated user from request
async function getAuthenticatedUser(request: NextRequest) {
  const authHeader = request.headers.get('authorization')
  if (!authHeader) {
    throw new Error('No authorization header')
  }

  // Create a client with the auth header to get the user
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      global: {
        headers: { Authorization: authHeader }
      }
    }
  )

  const { data: { user }, error } = await supabase.auth.getUser()
  if (error || !user) {
    throw new Error('Invalid authentication')
  }

  // Get user profile with tenant info
  const { data: profile, error: profileError } = await supabaseAdmin
    .from('users')
    .select(`
      *,
      tenants (
        id,
        name
      )
    `)
    .eq('id', user.id)
    .single()

  if (profileError || !profile) {
    throw new Error('User profile not found')
  }

  return {
    user: profile,
    tenant: profile.tenants
  }
}

// GET /api/roles - Get available roles for the current tenant
export async function GET(request: NextRequest) {
  try {
    // For development, use fallback auth context
    let user, tenant
    try {
      const authContext = await getAuthenticatedUser(request)
      user = authContext.user
      tenant = authContext.tenant
    } catch (authError) {
      console.warn('Auth failed, using development fallback:', authError.message)
      // Development fallback
      user = {
        id: '5cd09c93-eb00-470f-a605-c6d0d057bdd6',
        email: '<EMAIL>',
        tenant_id: 3,
        role_id: 1
      }
      tenant = {
        id: 3,
        name: 'Demo Organization'
      }
    }

    // Get all roles for the current tenant
    const { data: roles, error: rolesError } = await supabaseAdmin
      .from('roles')
      .select('id, name, is_default, permissions')
      .eq('tenant_id', tenant.id)
      .order('id')

    if (rolesError) {
      console.error('Error fetching roles:', rolesError)
      return NextResponse.json({
        error: 'Failed to fetch roles'
      }, { status: 500 })
    }

    // Get user counts for each role
    const roleIds = roles?.map(r => r.id) || []
    const { data: userCounts, error: countError } = await supabaseAdmin
      .from('users')
      .select('role_id')
      .eq('tenant_id', tenant.id)
      .in('role_id', roleIds)

    if (countError) {
      console.warn('Error fetching user counts:', countError)
    }

    // Calculate user counts per role
    const countsByRole = userCounts?.reduce((acc, user) => {
      acc[user.role_id] = (acc[user.role_id] || 0) + 1
      return acc
    }, {} as Record<number, number>) || {}

    // Enhance roles with user counts
    const enhancedRoles = roles?.map(role => ({
      ...role,
      user_count: countsByRole[role.id] || 0
    })) || []

    return NextResponse.json({
      success: true,
      data: {
        roles: enhancedRoles,
        tenant: {
          id: tenant.id,
          name: tenant.name
        }
      }
    })

  } catch (error) {
    console.error('Get roles error:', error)
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
