# Getting Started Guide

This guide will help you quickly set up and start using the Groups and Batches feature in ZenithLearn AI. Follow these steps to get up and running in minutes.

## 🚀 Quick Setup

### Prerequisites

Before you begin, ensure you have:

- ✅ Node.js 18+ installed
- ✅ Next.js 14.2.x project set up
- ✅ Supabase project configured
- ✅ Material-UI v5.15.x installed
- ✅ TypeScript configured

### 1. Install Dependencies

Install the required packages for the Groups and Batches feature:

```bash
# Core dependencies
npm install @mui/material @emotion/react @emotion/styled
npm install @mui/icons-material @mui/x-data-grid @mui/x-date-pickers
npm install @tanstack/react-query@5.59.13 zustand@5.0.0-rc.2
npm install react-hook-form @hookform/resolvers
npm install framer-motion chart.js react-chartjs-2
npm install @dnd-kit/core @dnd-kit/sortable @dnd-kit/utilities
npm install react-dropzone @tiptap/react @tiptap/starter-kit

# Development dependencies
npm install -D @types/react @types/node
```

### 2. Database Setup

Run the database migrations to create the required tables:

```bash
# Navigate to your project directory
cd your-zenithlearn-project

# Run migrations in order
psql -h your-supabase-host -U postgres -d postgres -f docs/database/migrations/001_create_groups_table.sql
psql -h your-supabase-host -U postgres -d postgres -f docs/database/migrations/002_enhance_group_members.sql
# ... continue with all migration files
```

Or use the Supabase CLI:

```bash
# Initialize Supabase (if not already done)
supabase init

# Apply migrations
supabase db push

# Generate TypeScript types
supabase gen types typescript --local > lib/types/supabase.ts
```

### 3. Environment Configuration

Add the required environment variables to your `.env.local`:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Feature Flags (optional)
NEXT_PUBLIC_ENABLE_GROUPS_FEATURE=true
NEXT_PUBLIC_ENABLE_AI_GROUPING=false
NEXT_PUBLIC_ENABLE_ADVANCED_ANALYTICS=true
```

### 4. Copy Component Files

Copy the Groups and Batches components to your project:

```bash
# Create the groups components directory
mkdir -p components/groups

# Copy all component files
cp -r path/to/groups-components/* components/groups/

# Copy type definitions
cp lib/types/groups.ts your-project/lib/types/
cp lib/services/groups.ts your-project/lib/services/

# Update store file
# Merge the groups store code into your existing lib/store.ts
```

### 5. Update Navigation

Add the Groups navigation item to your dashboard layout:

```tsx
// app/dashboard/layout.tsx
import { Groups as GroupsIcon } from '@mui/icons-material'

const navigationItems = [
  // ... existing items
  {
    text: 'Groups & Batches',
    icon: <GroupsIcon />,
    href: '/dashboard/groups',
    permission: 'view_groups',
  },
  // ... other items
]
```

## 🎯 Basic Usage

### Creating Your First Group

1. **Navigate to Groups**: Go to `/dashboard/groups` in your application
2. **Click Create Group**: Use the "Create Group" button in the top-right
3. **Fill in Details**: 
   - Enter a group name (required)
   - Add a description
   - Select status (active, inactive, draft, archived)
   - Add tags for categorization
4. **Configure Settings**: 
   - Set notification preferences
   - Configure communication options
   - Set progress tracking preferences
5. **Save**: Click "Create Group" to save

### Adding Members to a Group

1. **Open Group Details**: Click on a group card or use the "View" action
2. **Go to Members Tab**: Click the "Members" tab
3. **Add Members**: 
   - Click "Add Members" button
   - Search for learners by name or email
   - Select multiple learners
   - Choose their role (Member, Moderator, Admin)
   - Click "Add Members"

### Assigning Learning Paths

1. **Navigate to Assignments Tab**: In the group details view
2. **Click Assign Path**: Use the "Assign Path" button
3. **Select Learning Path**: Choose from available paths
4. **Set Schedule**: 
   - Optional start date
   - Optional due date
   - Mark as mandatory or optional
5. **Configure Settings**: 
   - Auto-assign to new members
   - Grace period for deadlines
6. **Assign**: Click "Assign Path" to save

### Tracking Progress

1. **View Progress Tab**: In the group details view
2. **Monitor Statistics**: 
   - Total members and completion rates
   - Average scores and time spent
   - Individual learner progress
3. **Use Analytics**: 
   - View completion trends over time
   - Compare progress across different paths
   - Export reports for stakeholders

## 🔧 Configuration Options

### Feature Flags

Control which features are available:

```typescript
// lib/config/features.ts
export const groupFeatureFlags = {
  enable_group_creation: true,
  enable_group_hierarchy: true,
  enable_bulk_member_add: true,
  enable_group_communication: true,
  enable_ai_grouping: false, // Requires OpenAI integration
  enable_auto_reassignment: false,
  enable_advanced_analytics: true,
  enable_group_integrations: false,
}
```

### Default Group Settings

Customize default settings for new groups:

```typescript
// lib/config/defaults.ts
export const defaultGroupSettings = {
  notifications: {
    email_enabled: true,
    sms_enabled: false,
    in_app_enabled: true,
    announcement_frequency: 'immediate',
  },
  communication: {
    chat_enabled: true,
    file_sharing_enabled: true,
    video_calls_enabled: false,
  },
  progress: {
    auto_progress_tracking: true,
    milestone_reminders: true,
    completion_certificates: true,
  },
  access: {
    self_enrollment: false,
    member_invite_permissions: ['admin', 'moderator'],
    content_access_level: 'full',
  },
}
```

## 🎨 Customization

### Theming

Customize the appearance to match your brand:

```tsx
// lib/theme/groups.ts
import { createTheme } from '@mui/material/styles'

export const groupsTheme = createTheme({
  palette: {
    primary: {
      main: '#1976d2', // Your brand color
    },
    secondary: {
      main: '#dc004e',
    },
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        },
      },
    },
  },
})
```

### Custom Components

Override default components with your own:

```tsx
// components/groups/custom/CustomGroupCard.tsx
import { GroupCard } from '@/components/groups/shared/GroupCard'

export function CustomGroupCard(props) {
  return (
    <GroupCard
      {...props}
      sx={{
        // Your custom styles
        border: '2px solid',
        borderColor: 'primary.main',
        '&:hover': {
          borderColor: 'secondary.main',
        },
      }}
    />
  )
}
```

## 🔒 Security Setup

### Row Level Security

Ensure RLS policies are properly configured:

```sql
-- Verify RLS is enabled
SELECT tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename LIKE 'group%';

-- Check policies exist
SELECT tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename LIKE 'group%';
```

### User Permissions

Set up proper user roles and permissions:

```typescript
// lib/auth/permissions.ts
export const groupPermissions = {
  view_groups: ['admin', 'manager', 'instructor'],
  create_groups: ['admin', 'manager'],
  edit_groups: ['admin', 'manager'],
  delete_groups: ['admin'],
  manage_members: ['admin', 'manager', 'moderator'],
  assign_paths: ['admin', 'manager', 'instructor'],
  view_progress: ['admin', 'manager', 'instructor'],
  send_messages: ['admin', 'manager', 'moderator'],
}
```

## 📊 Real-time Features

### Enable Real-time Updates

Set up Supabase Realtime subscriptions:

```tsx
// hooks/useGroupsRealtime.ts
import { useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { useGroupsStore } from '@/lib/store'

export function useGroupsRealtime(tenantId: number) {
  const { addGroup, updateGroup, deleteGroup } = useGroupsStore()

  useEffect(() => {
    const subscription = supabase
      .channel('groups-changes')
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'groups',
          filter: `tenant_id=eq.${tenantId}`
        }, 
        (payload) => {
          switch (payload.eventType) {
            case 'INSERT':
              addGroup(payload.new)
              break
            case 'UPDATE':
              updateGroup(payload.new.id, payload.new)
              break
            case 'DELETE':
              deleteGroup(payload.old.id)
              break
          }
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [tenantId])
}
```

## 🧪 Testing Your Setup

### Verify Installation

Run these checks to ensure everything is working:

```bash
# Check database tables exist
npm run db:check

# Verify components render
npm run test:components

# Test API endpoints
npm run test:api

# Check real-time connections
npm run test:realtime
```

### Create Test Data

Use the provided test data script:

```typescript
// scripts/create-test-data.ts
import { supabase } from '@/lib/supabase'

async function createTestData() {
  // Create test groups
  const { data: groups } = await supabase
    .from('groups')
    .insert([
      {
        name: 'Test Engineering Group',
        description: 'Test group for development',
        status: 'active',
        tags: ['test', 'engineering'],
        tenant_id: 1,
        created_by: 'test-user-id'
      }
    ])
    .select()

  console.log('Created test groups:', groups)
}

createTestData()
```

## 🚨 Troubleshooting

### Common Issues

**Groups not loading:**
- Check database connection
- Verify RLS policies are correct
- Ensure user has proper permissions

**Real-time not working:**
- Check Supabase Realtime is enabled
- Verify subscription filters
- Check network connectivity

**Components not rendering:**
- Ensure all dependencies are installed
- Check for TypeScript errors
- Verify theme provider is set up

### Getting Help

- 📚 Check the [API Reference](../api/reference.md)
- 🔧 Review [Troubleshooting Guide](../troubleshooting/common-issues.md)
- 💬 Join our community Discord
- 📧 Contact <NAME_EMAIL>

## 🎉 Next Steps

Now that you have the Groups and Batches feature set up:

1. **Explore Advanced Features**: Check out [Advanced Configuration](./advanced-configuration.md)
2. **Integrate with AI**: Set up [AI-powered features](./ai-integration.md)
3. **Customize Further**: Review [Component Customization](../components/customization.md)
4. **Deploy to Production**: Follow the [Deployment Guide](./deployment.md)

Congratulations! You now have a fully functional Groups and Batches feature in your ZenithLearn AI application. 🚀
