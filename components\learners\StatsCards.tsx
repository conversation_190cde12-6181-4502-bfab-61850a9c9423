'use client'

import React from 'react'
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Box,
  Skeleton,
  useTheme,
  alpha
} from '@mui/material'
import {
  People as PeopleIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  Assignment as AssignmentIcon,
  School as SchoolIcon,
  Speed as SpeedIcon
} from '@mui/icons-material'
import { motion } from 'framer-motion'

import { LearnerAnalytics } from '@/lib/types/learners'

interface StatsCardsProps {
  data?: LearnerAnalytics
  loading: boolean
}

interface StatCardProps {
  title: string
  value: string | number
  subtitle?: string
  icon: React.ReactNode
  color: string
  trend?: {
    value: number
    isPositive: boolean
  }
  loading?: boolean
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  color,
  trend,
  loading = false
}) => {
  const theme = useTheme()

  if (loading) {
    return (
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
            <Skeleton variant="circular" width={48} height={48} />
            <Skeleton variant="text" width={60} height={24} />
          </Box>
          <Skeleton variant="text" width="80%" height={32} />
          <Skeleton variant="text" width="60%" height={20} />
        </CardContent>
      </Card>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card 
        sx={{ 
          height: '100%',
          background: `linear-gradient(135deg, ${alpha(color, 0.1)} 0%, ${alpha(color, 0.05)} 100%)`,
          border: `1px solid ${alpha(color, 0.2)}`,
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: theme.shadows[8],
          },
          transition: 'all 0.3s ease-in-out'
        }}
      >
        <CardContent>
          <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
            <Box
              sx={{
                width: 48,
                height: 48,
                borderRadius: 2,
                backgroundColor: alpha(color, 0.1),
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: color
              }}
            >
              {icon}
            </Box>
            {trend && (
              <Box
                display="flex"
                alignItems="center"
                gap={0.5}
                sx={{
                  color: trend.isPositive ? 'success.main' : 'error.main',
                  backgroundColor: trend.isPositive 
                    ? alpha(theme.palette.success.main, 0.1)
                    : alpha(theme.palette.error.main, 0.1),
                  px: 1,
                  py: 0.5,
                  borderRadius: 1,
                  fontSize: '0.75rem'
                }}
              >
                <TrendingUpIcon 
                  sx={{ 
                    fontSize: 16,
                    transform: trend.isPositive ? 'none' : 'rotate(180deg)'
                  }} 
                />
                {Math.abs(trend.value)}%
              </Box>
            )}
          </Box>

          <Typography variant="h4" fontWeight="bold" color="text.primary" gutterBottom>
            {typeof value === 'number' ? value.toLocaleString() : value}
          </Typography>

          <Typography variant="h6" color="text.secondary" gutterBottom>
            {title}
          </Typography>

          {subtitle && (
            <Typography variant="body2" color="text.secondary">
              {subtitle}
            </Typography>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}

const StatsCards: React.FC<StatsCardsProps> = ({ data, loading }) => {
  const theme = useTheme()

  const stats = [
    {
      title: 'Total Learners',
      value: data?.total_learners || 0,
      subtitle: `${data?.active_learners || 0} active`,
      icon: <PeopleIcon />,
      color: theme.palette.primary.main,
      trend: {
        value: 12,
        isPositive: true
      }
    },
    {
      title: 'Avg Completion Rate',
      value: `${Math.round(data?.average_completion_rate || 0)}%`,
      subtitle: 'Across all paths',
      icon: <SchoolIcon />,
      color: theme.palette.success.main,
      trend: {
        value: 8,
        isPositive: true
      }
    },
    {
      title: 'Engagement Score',
      value: Math.round(data?.average_engagement_score || 0),
      subtitle: 'Average score',
      icon: <SpeedIcon />,
      color: theme.palette.info.main,
      trend: {
        value: 3,
        isPositive: false
      }
    },
    {
      title: 'At Risk Learners',
      value: data?.at_risk_learners || 0,
      subtitle: 'Need attention',
      icon: <WarningIcon />,
      color: theme.palette.warning.main
    },
    {
      title: 'Overdue Assignments',
      value: data?.overdue_assignments || 0,
      subtitle: 'Require follow-up',
      icon: <AssignmentIcon />,
      color: theme.palette.error.main
    },
    {
      title: 'Top Performers',
      value: data?.top_performers?.length || 0,
      subtitle: 'This month',
      icon: <TrendingUpIcon />,
      color: theme.palette.secondary.main
    }
  ]

  return (
    <Grid container spacing={3}>
      {stats.map((stat, index) => (
        <Grid item xs={12} sm={6} md={4} lg={2} key={stat.title}>
          <StatCard
            title={stat.title}
            value={stat.value}
            subtitle={stat.subtitle}
            icon={stat.icon}
            color={stat.color}
            trend={stat.trend}
            loading={loading}
          />
        </Grid>
      ))}
    </Grid>
  )
}

export default StatsCards
