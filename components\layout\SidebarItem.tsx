'use client'

import { usePathname, useRouter } from 'next/navigation'
import {
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  useTheme,
} from '@mui/material'

import { useAuthStore } from '@/lib/store'
import { hasPermission } from '@/lib/auth'

interface SidebarItemProps {
  text: string
  icon: React.ReactNode
  href: string
  permission?: string
}

export default function SidebarItem({ text, icon, href, permission }: SidebarItemProps) {
  const router = useRouter()
  const pathname = usePathname()
  const theme = useTheme()
  const { user } = useAuthStore()

  // Check if user has permission to view this item
  if (permission && user) {
    // For now, we'll assume all users have all permissions
    // In a real app, you'd check user.permissions or user.role.permissions
    const userPermissions = ['admin'] // This would come from user data
    if (!hasPermission(userPermissions, permission)) {
      return null
    }
  }

  const isActive = pathname === href || (href !== '/dashboard' && pathname.startsWith(href))

  const handleClick = () => {
    router.push(href)
  }

  return (
    <ListItem disablePadding sx={{ mb: 0.5 }}>
      <ListItemButton
        onClick={handleClick}
        sx={{
          borderRadius: 1,
          mx: 1,
          backgroundColor: isActive ? theme.palette.primary.main : 'transparent',
          color: isActive ? theme.palette.primary.contrastText : theme.palette.text.primary,
          '&:hover': {
            backgroundColor: isActive 
              ? theme.palette.primary.dark 
              : theme.palette.action.hover,
          },
          transition: theme.transitions.create(['background-color', 'color'], {
            duration: theme.transitions.duration.short,
          }),
        }}
      >
        <ListItemIcon
          sx={{
            color: isActive ? theme.palette.primary.contrastText : theme.palette.text.secondary,
            minWidth: 40,
          }}
        >
          {icon}
        </ListItemIcon>
        <ListItemText 
          primary={text}
          primaryTypographyProps={{
            fontSize: '0.875rem',
            fontWeight: isActive ? 600 : 400,
          }}
        />
      </ListItemButton>
    </ListItem>
  )
}
