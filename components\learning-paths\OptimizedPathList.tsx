'use client'

import React, { useMemo, useState, useCallback } from 'react'
import {
  Grid,
  Box,
  Skeleton,
  Alert,
  Pagination,
  Typography,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  InputAdornment,
  Fab
} from '@mui/material'
import {
  Search as SearchIcon,
  Add as AddIcon,
  FilterList as FilterIcon
} from '@mui/icons-material'
import { motion, AnimatePresence } from 'framer-motion'
import { useVirtualizer } from '@tanstack/react-virtual'
import { LearningPath } from '@/lib/types/learning-paths'
import PathCard from './PathCard'

interface OptimizedPathListProps {
  paths: LearningPath[]
  isLoading: boolean
  error: any
  totalCount: number
  currentPage: number
  onPageChange: (page: number) => void
  onCreatePath: () => void
  onViewPath: (path: LearningPath) => void
  onEditPath: (path: LearningPath) => void
  onMenuClick: (event: React.MouseEvent<HTMLElement>, path: LearningPath) => void
  filters: {
    search: string
    status: string
    category: string
    difficulty: string
  }
  onFiltersChange: (filters: any) => void
}

const ITEMS_PER_PAGE = 12
const SKELETON_COUNT = 6

export default function OptimizedPathList({
  paths,
  isLoading,
  error,
  totalCount,
  currentPage,
  onPageChange,
  onCreatePath,
  onViewPath,
  onEditPath,
  onMenuClick,
  filters,
  onFiltersChange
}: OptimizedPathListProps) {
  const [showFilters, setShowFilters] = useState(false)

  // Memoized filtered and sorted paths
  const processedPaths = useMemo(() => {
    if (!paths) return []
    
    return paths.map((path, index) => ({
      ...path,
      // Add computed properties for better performance
      moduleCount: path.modules?.length || 0,
      lessonCount: path.modules?.reduce((total, module) => 
        total + (module.lessons?.length || 0), 0) || 0,
      totalDuration: path.modules?.reduce((total, module) =>
        total + (module.lessons?.reduce((lessonTotal: number, lesson: any) =>
          lessonTotal + (lesson.estimated_duration || 0), 0) || 0), 0) || 0,
      index
    }))
  }, [paths])

  // Debounced search handler
  const handleSearchChange = useCallback(
    debounce((value: string) => {
      onFiltersChange({ ...filters, search: value })
    }, 300),
    [filters, onFiltersChange]
  )

  const handleFilterChange = useCallback((key: string, value: string) => {
    onFiltersChange({ ...filters, [key]: value })
  }, [filters, onFiltersChange])

  // Calculate pagination
  const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE)

  if (error) {
    return (
      <Alert 
        severity="error" 
        sx={{ mt: 2 }}
        action={
          <Chip 
            label="Retry" 
            onClick={() => window.location.reload()} 
            color="error" 
            variant="outlined" 
          />
        }
      >
        Failed to load learning paths. Please check your connection and try again.
      </Alert>
    )
  }

  return (
    <Box>
      {/* Search and Filters */}
      <Box mb={3}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search learning paths..."
              defaultValue={filters.search}
              onChange={(e) => handleSearchChange(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              size="small"
            />
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Box display="flex" gap={1} alignItems="center">
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  label="Status"
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                >
                  <MenuItem value="all">All</MenuItem>
                  <MenuItem value="published">Published</MenuItem>
                  <MenuItem value="draft">Draft</MenuItem>
                </Select>
              </FormControl>

              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Category</InputLabel>
                <Select
                  value={filters.category}
                  label="Category"
                  onChange={(e) => handleFilterChange('category', e.target.value)}
                >
                  <MenuItem value="all">All</MenuItem>
                  <MenuItem value="Programming">Programming</MenuItem>
                  <MenuItem value="Business">Business</MenuItem>
                  <MenuItem value="Design">Design</MenuItem>
                  <MenuItem value="Management">Management</MenuItem>
                </Select>
              </FormControl>

              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Difficulty</InputLabel>
                <Select
                  value={filters.difficulty}
                  label="Difficulty"
                  onChange={(e) => handleFilterChange('difficulty', e.target.value)}
                >
                  <MenuItem value="all">All</MenuItem>
                  <MenuItem value="beginner">Beginner</MenuItem>
                  <MenuItem value="intermediate">Intermediate</MenuItem>
                  <MenuItem value="advanced">Advanced</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Results Summary */}
      <Box mb={2}>
        <Typography variant="body2" color="text.secondary">
          {isLoading ? 'Loading...' : `${totalCount} learning paths found`}
        </Typography>
      </Box>

      {/* Learning Paths Grid */}
      <Grid container spacing={3}>
        {isLoading ? (
          // Optimized skeleton loading
          Array.from({ length: SKELETON_COUNT }).map((_, index) => (
            <Grid item xs={12} sm={6} lg={4} key={`skeleton-${index}`}>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: index * 0.1 }}
              >
                <Skeleton 
                  variant="rectangular" 
                  height={280} 
                  sx={{ borderRadius: 2 }}
                />
              </motion.div>
            </Grid>
          ))
        ) : processedPaths.length === 0 ? (
          <Grid item xs={12}>
            <Alert severity="info" sx={{ textAlign: 'center' }}>
              No learning paths found. Try adjusting your filters or create a new learning path.
            </Alert>
          </Grid>
        ) : (
          <AnimatePresence mode="popLayout">
            {processedPaths.map((path, index) => (
              <Grid item xs={12} sm={6} lg={4} key={path.id}>
                <motion.div
                  layout
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ 
                    duration: 0.3,
                    delay: index * 0.05,
                    layout: { duration: 0.3 }
                  }}
                  whileHover={{ y: -4 }}
                >
                  <PathCard
                    path={path}
                    onView={() => onViewPath(path)}
                    onEdit={() => onEditPath(path)}
                    onMenuClick={(event) => onMenuClick(event, path)}
                  />
                </motion.div>
              </Grid>
            ))}
          </AnimatePresence>
        )}
      </Grid>

      {/* Pagination */}
      {totalPages > 1 && (
        <Box display="flex" justifyContent="center" mt={4}>
          <Pagination
            count={totalPages}
            page={currentPage}
            onChange={(_, page) => onPageChange(page)}
            color="primary"
            size="large"
            showFirstButton
            showLastButton
          />
        </Box>
      )}

      {/* Floating Action Button for Mobile */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
          display: { xs: 'flex', md: 'none' }
        }}
        onClick={onCreatePath}
      >
        <AddIcon />
      </Fab>
    </Box>
  )
}

// Utility function for debouncing
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}
