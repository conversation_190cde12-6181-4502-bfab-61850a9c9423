import { supabase } from '@/lib/supabase'
import { useAuthStore } from '@/lib/store'
import {
  LearningPath,
  Mo<PERSON>le,
  Lesson,
  LearnerAssignment,
  Assignment,
  PathTemplate,
  PathVersion,
  PathFilters,
  PathSortOptions,
  AIPathSuggestion
} from '@/lib/types/learning-paths'

// Helper function to get current user context
const getCurrentUserContext = () => {
  const { user, tenant } = useAuthStore.getState()

  // Development fallback - remove in production
  if (!user || !tenant) {
    console.warn('No authenticated user found, using development fallback')
    return {
      user: {
        id: '5cd09c93-eb00-470f-a605-c6d0d057bdd6',
        email: '<EMAIL>',
        tenant_id: 3,
        role_id: 1
      },
      tenant: {
        id: 3,
        name: 'Development Tenant'
      }
    }
  }
  return { user, tenant }
}

// Helper function to transform database row to our interface
const transformLearningPath = (row: any): LearningPath => {
  return {
    ...row,
    // Map database columns to interface properties
    difficulty: row.difficulty_level,
    duration: row.estimated_duration,
    status: row.is_live ? 'published' : 'draft',
    assignments: row.learner_assignments || [],
    metadata: {
      estimated_hours: 0,
      completion_rate: 0,
      average_rating: 0,
      total_learners: 0,
      prerequisites: row.prerequisites || [],
      skills: row.tags || []
    }
  }
}

const transformModule = (row: any): Module => {
  return {
    ...row,
    order: row.order_index,
    estimated_duration: row.lessons?.reduce((total: number, lesson: any) =>
      total + (lesson.estimated_duration || 0), 0) || 0,
    dependencies: []
  }
}

const transformLesson = (row: any): Lesson => {
  return {
    ...row,
    order: row.order_index,
    is_optional: !row.is_mandatory,
    content: {
      video_url: row.content_url,
      pdf_url: row.content_url,
      external_url: row.content_url,
      ...row.content_metadata
    },
    dependencies: [],
    interactions: []
  }
}

// Learning Paths API
export const learningPathsApi = {
  // Get all learning paths with filters and pagination
  async getPaths(
    filters?: Partial<PathFilters>,
    sort?: PathSortOptions,
    page = 1,
    limit = 20
  ) {
    // For development, temporarily disable RLS to test data loading
    const { user, tenant } = getCurrentUserContext()

    let query = supabase
      .from('learning_paths')
      .select(`
        *,
        modules:modules(
          *,
          lessons:lessons(*)
        ),
        learner_assignments:learner_assignments!learner_assignments_path_id_fkey(*),
        created_by_user:users!learning_paths_created_by_fkey(id, full_name, email)
      `)
      .eq('tenant_id', tenant.id) // Manually filter by tenant for now

    // Apply filters
    if (filters?.search) {
      query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`)
    }
    if (filters?.status && filters.status !== 'all') {
      if (filters.status === 'published') {
        query = query.eq('is_live', true)
      } else if (filters.status === 'draft') {
        query = query.eq('is_live', false)
      }
    }
    if (filters?.category && filters.category !== 'all') {
      query = query.eq('category', filters.category)
    }
    if (filters?.difficulty && filters.difficulty !== 'all') {
      query = query.eq('difficulty_level', filters.difficulty)
    }
    if (filters?.created_by) {
      query = query.eq('created_by', filters.created_by)
    }
    if (filters?.tags && filters.tags.length > 0) {
      query = query.contains('tags', filters.tags)
    }

    // Apply sorting
    if (sort) {
      const sortField = sort.field === 'difficulty' ? 'difficulty_level' :
                       sort.field === 'duration' ? 'estimated_duration' : sort.field
      query = query.order(sortField, { ascending: sort.direction === 'asc' })
    } else {
      query = query.order('updated_at', { ascending: false })
    }

    // Apply pagination
    const from = (page - 1) * limit
    const to = from + limit - 1
    query = query.range(from, to)

    const { data, error, count } = await query

    if (error) throw error

    const transformedData = data?.map(transformLearningPath) || []

    return {
      data: transformedData,
      count: count || 0,
      page,
      limit
    }
  },

  // Get single learning path by ID
  async getPath(id: string) {
    const { user, tenant } = getCurrentUserContext()

    const { data, error } = await supabase
      .from('learning_paths')
      .select(`
        *,
        modules:modules(
          *,
          lessons:lessons(*)
        ),
        learner_assignments:learner_assignments!learner_assignments_path_id_fkey(*),
        created_by_user:users!learning_paths_created_by_fkey(id, full_name, email)
      `)
      .eq('id', parseInt(id))
      .eq('tenant_id', tenant.id) // Manually filter by tenant
      .single()

    if (error) throw error
    return transformLearningPath(data)
  },

  // Create new learning path
  async createPath(pathData: any) {
    const { user, tenant } = getCurrentUserContext()

    // Transform the data to match database schema
    const dbData = {
      title: pathData.title,
      description: pathData.description,
      objectives: pathData.objectives || [],
      prerequisites: pathData.prerequisites || [],
      difficulty_level: pathData.difficulty || pathData.difficulty_level || 'beginner',
      estimated_duration: pathData.duration || pathData.estimated_duration || 4,
      category: pathData.category,
      tags: pathData.tags || [],
      is_live: pathData.status === 'published' || false,
      is_featured: false,
      thumbnail_url: pathData.thumbnail_url,
      tenant_id: tenant.id,
      created_by: user.id
    }

    const { data, error } = await supabase
      .from('learning_paths')
      .insert(dbData)
      .select()
      .single()

    if (error) throw error
    return transformLearningPath(data)
  },

  // Update learning path
  async updatePath(id: string, updates: any) {
    // Transform the updates to match database schema
    const dbUpdates: any = {
      updated_at: new Date().toISOString()
    }

    if (updates.title) dbUpdates.title = updates.title
    if (updates.description) dbUpdates.description = updates.description
    if (updates.objectives) dbUpdates.objectives = updates.objectives
    if (updates.prerequisites) dbUpdates.prerequisites = updates.prerequisites
    if (updates.difficulty || updates.difficulty_level) {
      dbUpdates.difficulty_level = updates.difficulty || updates.difficulty_level
    }
    if (updates.duration || updates.estimated_duration) {
      dbUpdates.estimated_duration = updates.duration || updates.estimated_duration
    }
    if (updates.category) dbUpdates.category = updates.category
    if (updates.tags) dbUpdates.tags = updates.tags
    if (updates.status !== undefined) {
      dbUpdates.is_live = updates.status === 'published'
    }
    if (updates.is_live !== undefined) dbUpdates.is_live = updates.is_live
    if (updates.is_featured !== undefined) dbUpdates.is_featured = updates.is_featured
    if (updates.thumbnail_url) dbUpdates.thumbnail_url = updates.thumbnail_url

    const { data, error } = await supabase
      .from('learning_paths')
      .update(dbUpdates)
      .eq('id', parseInt(id))
      .select()
      .single()

    if (error) throw error
    return transformLearningPath(data)
  },

  // Delete learning path
  async deletePath(id: string) {
    const { error } = await supabase
      .from('learning_paths')
      .delete()
      .eq('id', parseInt(id))

    if (error) throw error
  },

  // Publish learning path
  async publishPath(id: string) {
    const { data, error } = await supabase
      .from('learning_paths')
      .update({
        is_live: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', parseInt(id))
      .select()
      .single()

    if (error) throw error
    return transformLearningPath(data)
  },

  // Duplicate learning path
  async duplicatePath(id: string, newTitle: string) {
    const originalPath = await this.getPath(id)

    const { modules, assignments, learner_assignments, ...pathData } = originalPath
    
    const duplicatedPath = {
      ...pathData,
      id: undefined,
      title: newTitle,
      status: 'draft' as const,
      published_at: null,
      created_at: undefined,
      updated_at: undefined
    }

    const { data, error } = await supabase
      .from('learning_paths')
      .insert(duplicatedPath)
      .select()
      .single()

    if (error) throw error

    // Duplicate modules and lessons
    if (modules && modules.length > 0) {
      for (const module of modules) {
        await modulesApi.createModule(data.id, {
          ...module,
          id: undefined,
          path_id: data.id,
          created_at: undefined,
          updated_at: undefined
        })
      }
    }

    return data as LearningPath
  }
}

// Modules API
export const modulesApi = {
  // Create new module
  async createModule(pathId: string, moduleData: any) {
    getCurrentUserContext() // Ensure user is authenticated

    const dbData = {
      path_id: parseInt(pathId),
      title: moduleData.title,
      description: moduleData.description,
      order_index: moduleData.order || moduleData.order_index || 0,
      is_optional: moduleData.is_optional || false,
      unlock_conditions: moduleData.unlock_conditions || {}
    }

    const { data, error } = await supabase
      .from('modules')
      .insert(dbData)
      .select()
      .single()

    if (error) throw error
    return transformModule(data)
  },

  // Update module
  async updateModule(id: string, updates: any) {
    const dbUpdates: any = {
      updated_at: new Date().toISOString()
    }

    if (updates.title) dbUpdates.title = updates.title
    if (updates.description) dbUpdates.description = updates.description
    if (updates.order !== undefined || updates.order_index !== undefined) {
      dbUpdates.order_index = updates.order || updates.order_index
    }
    if (updates.is_optional !== undefined) dbUpdates.is_optional = updates.is_optional
    if (updates.unlock_conditions) dbUpdates.unlock_conditions = updates.unlock_conditions

    const { data, error } = await supabase
      .from('modules')
      .update(dbUpdates)
      .eq('id', parseInt(id))
      .select()
      .single()

    if (error) throw error
    return transformModule(data)
  },

  // Delete module
  async deleteModule(id: string) {
    const { error } = await supabase
      .from('modules')
      .delete()
      .eq('id', parseInt(id))

    if (error) throw error
  },

  // Reorder modules
  async reorderModules(pathId: string, moduleOrders: { id: string; order: number }[]) {
    const updates = moduleOrders.map(({ id, order }) => ({
      id: parseInt(id),
      order_index: order,
      updated_at: new Date().toISOString()
    }))

    const { error } = await supabase
      .from('modules')
      .upsert(updates)

    if (error) throw error
  }
}

// Lessons API
export const lessonsApi = {
  // Create new lesson
  async createLesson(moduleId: string, lessonData: any) {
    const dbData = {
      module_id: parseInt(moduleId),
      title: lessonData.title,
      description: lessonData.description,
      type: lessonData.type,
      content_url: lessonData.content_url || lessonData.content?.video_url || lessonData.content?.pdf_url || lessonData.content?.external_url,
      content_text: lessonData.content?.content_text,
      content_metadata: {
        ...lessonData.content_metadata,
        storagePath: lessonData.content_metadata?.storagePath,
        uploadResult: lessonData.content_metadata?.uploadResult,
        preview: lessonData.content_metadata?.preview,
        aiGenerated: lessonData.content_metadata?.aiGenerated || false,
        fileMetadata: lessonData.content_metadata?.uploadResult?.metadata
      },
      order_index: lessonData.order || lessonData.order_index || 0,
      estimated_duration: lessonData.estimated_duration || 30,
      is_mandatory: !lessonData.is_optional,
      passing_score: lessonData.passing_score,
      max_attempts: lessonData.max_attempts || 3
    }

    const { data, error } = await supabase
      .from('lessons')
      .insert(dbData)
      .select()
      .single()

    if (error) throw error
    return transformLesson(data)
  },

  // Update lesson
  async updateLesson(id: string, updates: any) {
    const dbUpdates: any = {
      updated_at: new Date().toISOString()
    }

    if (updates.title) dbUpdates.title = updates.title
    if (updates.description) dbUpdates.description = updates.description
    if (updates.type) dbUpdates.type = updates.type
    if (updates.content) {
      dbUpdates.content_url = updates.content.video_url || updates.content.pdf_url || updates.content.external_url
      dbUpdates.content_text = updates.content.content_text
      dbUpdates.content_metadata = updates.content
    }
    if (updates.order !== undefined || updates.order_index !== undefined) {
      dbUpdates.order_index = updates.order || updates.order_index
    }
    if (updates.estimated_duration) dbUpdates.estimated_duration = updates.estimated_duration
    if (updates.is_optional !== undefined) dbUpdates.is_mandatory = !updates.is_optional
    if (updates.passing_score) dbUpdates.passing_score = updates.passing_score
    if (updates.max_attempts) dbUpdates.max_attempts = updates.max_attempts

    const { data, error } = await supabase
      .from('lessons')
      .update(dbUpdates)
      .eq('id', parseInt(id))
      .select()
      .single()

    if (error) throw error
    return transformLesson(data)
  },

  // Delete lesson
  async deleteLesson(id: string) {
    const { error } = await supabase
      .from('lessons')
      .delete()
      .eq('id', parseInt(id))

    if (error) throw error
  },

  // Reorder lessons
  async reorderLessons(moduleId: string, lessonOrders: { id: string; order: number }[]) {
    const updates = lessonOrders.map(({ id, order }) => ({
      id: parseInt(id),
      order_index: order,
      updated_at: new Date().toISOString()
    }))

    const { error } = await supabase
      .from('lessons')
      .upsert(updates)

    if (error) throw error
  }
}

// Assignments API
export const assignmentsApi = {
  // Create assignment
  async createAssignment(assignmentData: any) {
    const dbData = {
      path_id: parseInt(assignmentData.path_id),
      learner_id: assignmentData.learner_id,
      assigned_by: assignmentData.assigned_by,
      due_date: assignmentData.due_date,
      status: assignmentData.status || 'assigned',
      completion_percentage: assignmentData.completion_percentage || 0
    }

    const { data, error } = await supabase
      .from('learner_assignments')
      .insert(dbData)
      .select()
      .single()

    if (error) throw error
    return data as LearnerAssignment
  },

  // Bulk create assignments
  async createBulkAssignments(assignments: any[]) {
    const assignmentsWithTimestamp = assignments.map(assignment => ({
      path_id: parseInt(assignment.path_id),
      learner_id: assignment.learner_id,
      assigned_by: assignment.assigned_by,
      due_date: assignment.due_date,
      status: assignment.status || 'assigned',
      completion_percentage: assignment.completion_percentage || 0
    }))

    const { data, error } = await supabase
      .from('learner_assignments')
      .insert(assignmentsWithTimestamp)
      .select()

    if (error) throw error
    return data as LearnerAssignment[]
  },

  // Get assignments for path
  async getPathAssignments(pathId: string) {
    const { data, error } = await supabase
      .from('learner_assignments')
      .select(`
        *,
        learner:users!learner_assignments_learner_id_fkey(id, full_name, email),
        assigned_by_user:users!learner_assignments_assigned_by_fkey(id, full_name, email)
      `)
      .eq('path_id', parseInt(pathId))

    if (error) throw error
    return data as LearnerAssignment[]
  }
}

// Templates API
export const templatesApi = {
  // Get all templates
  async getTemplates() {
    const { data, error } = await supabase
      .from('path_templates')
      .select('*')
      .order('usage_count', { ascending: false })

    if (error) throw error
    return data as PathTemplate[]
  },

  // Create template from path
  async createTemplate(pathId: string, templateData: Omit<PathTemplate, 'id' | 'created_at' | 'structure'>) {
    const path = await learningPathsApi.getPath(pathId)

    const { modules, assignments, learner_assignments, ...structure } = path

    const { data, error } = await supabase
      .from('path_templates')
      .insert({
        ...templateData,
        structure: structure as any
      })
      .select()
      .single()

    if (error) throw error
    return data as PathTemplate
  }
}

// AI API
export const aiApi = {
  // Generate learning path suggestions
  async generatePathSuggestions(prompt: string, options: {
    difficulty?: string
    duration?: number
    category?: string
  }): Promise<AIPathSuggestion> {
    try {
      console.log('Calling AI path generation with:', { prompt, options })

      const { data, error } = await supabase.functions.invoke('ai-generate-path', {
        body: { prompt, options }
      })

      if (error) {
        console.error('Supabase function error:', error)
        throw new Error(error.message || 'Failed to generate learning path')
      }

      if (!data) {
        throw new Error('No data returned from AI service')
      }

      console.log('AI generation successful:', data)
      return data as AIPathSuggestion
    } catch (error) {
      console.error('AI generation error:', error)
      throw error
    }
  },

  // Enhance lesson content
  async enhanceLesson(lessonId: string, content: string) {
    try {
      const { data, error } = await supabase.functions.invoke('ai-enhance-lesson', {
        body: { lessonId, content }
      })

      if (error) {
        console.error('Lesson enhancement error:', error)
        throw new Error(error.message || 'Failed to enhance lesson')
      }

      return data
    } catch (error) {
      console.error('Lesson enhancement error:', error)
      throw error
    }
  },

  // Generate quiz from content (placeholder - implement if needed)
  async generateQuiz(content: string, questionCount = 5) {
    // For now, return a mock quiz structure
    return {
      questions: Array.from({ length: questionCount }, (_, i) => ({
        id: `q${i + 1}`,
        type: 'multiple_choice',
        question: `Question ${i + 1} based on the content`,
        options: ['Option A', 'Option B', 'Option C', 'Option D'],
        correct_answer: 'Option A',
        explanation: 'This is the correct answer because...',
        points: 1
      })),
      passing_score: 70,
      max_attempts: 3,
      time_limit: questionCount * 2,
      randomize_questions: true,
      show_correct_answers: true
    }
  }
}
