/* Dashboard Grid Layout Styles */
.react-grid-layout {
  position: relative;
  min-height: 100vh;
}

.react-grid-item {
  transition: all 200ms ease;
  transition-property: left, top, width, height;
  border-radius: 12px;
  overflow: hidden;
}

.react-grid-item.cssTransforms {
  transition-property: transform, width, height;
}

.react-grid-item > .react-resizable-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  bottom: 0;
  right: 0;
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNiIgaGVpZ2h0PSI2IiB2aWV3Qm94PSIwIDAgNiA2IiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxnIGZpbGw9IiM0NDQiIGZpbGwtb3BhY2l0eT0iLjMiPjxwYXRoIGQ9Im0gNiw2IDAgLTYgLTYsNiB6Ii8+PC9nPjwvc3ZnPg==') no-repeat;
  background-position: bottom right;
  padding: 0 3px 3px 0;
  background-repeat: no-repeat;
  background-origin: content-box;
  box-sizing: border-box;
  cursor: se-resize;
  z-index: 10;
}

.react-grid-item > .react-resizable-handle::after {
  content: '';
  position: absolute;
  right: 3px;
  bottom: 3px;
  width: 5px;
  height: 5px;
  border-right: 2px solid rgba(0, 0, 0, 0.4);
  border-bottom: 2px solid rgba(0, 0, 0, 0.4);
}

.react-grid-item.resizing {
  z-index: 1;
  will-change: width, height;
}

.react-grid-item.react-draggable-dragging {
  transition: none;
  z-index: 3;
  will-change: transform;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  transform: rotate(2deg) !important;
}

.react-grid-item.dropping {
  visibility: hidden;
}

.react-grid-item.react-grid-placeholder {
  background: rgba(0, 0, 0, 0.1);
  opacity: 0.2;
  transition-duration: 100ms;
  z-index: 2;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
  border: 2px dashed #ccc;
  border-radius: 12px;
}

/* Widget Container Styles */
.dashboard-widget {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.dashboard-widget:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .react-grid-layout {
    margin: 0 -10px;
  }
  
  .react-grid-item {
    margin: 10px;
  }
  
  .react-grid-item > .react-resizable-handle {
    display: none;
  }
}

@media (max-width: 480px) {
  .react-grid-layout {
    margin: 0 -5px;
  }
  
  .react-grid-item {
    margin: 5px;
  }
}

/* Mood Selector Modal Styles */
.mood-selector-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 999;
  backdrop-filter: blur(2px);
}

.mood-selector-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

/* Animation improvements */
.react-grid-item {
  will-change: transform, width, height;
}

.react-grid-item.react-draggable-dragging {
  will-change: transform;
}

.react-grid-item.resizing {
  will-change: width, height;
}

/* Prevent layout shifts during animations */
.dashboard-container {
  contain: layout style paint;
}

/* Smooth scrolling for mobile */
@media (max-width: 768px) {
  .dashboard-container {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .react-grid-item > .react-resizable-handle::after {
    border-color: #000;
  }
  
  .react-grid-placeholder {
    border-color: #000;
    background: rgba(0, 0, 0, 0.3);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .react-grid-item {
    transition: none;
  }
  
  .dashboard-widget {
    transition: none;
  }
  
  .dashboard-widget:hover {
    transform: none;
  }
}

/* Focus styles for accessibility */
.react-grid-item:focus-within {
  outline: 2px solid #1976d2;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .react-grid-item > .react-resizable-handle {
    display: none;
  }
  
  .react-grid-item {
    break-inside: avoid;
    page-break-inside: avoid;
  }
}
