'use client'

import { useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Chip,
  Avatar,
  AvatarGroup,
  IconButton,
  Menu,
  MenuItem,
  Grid,
  LinearProgress,
  Tooltip,
  Skeleton,
} from '@mui/material'
import {
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  FileCopy as DuplicateIcon,
  Archive as ArchiveIcon,
  Visibility as ViewIcon,
  People as PeopleIcon,
  School as SchoolIcon,
  TrendingUp as ProgressIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material'
import { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid'
import { motion } from 'framer-motion'

import { Group } from '@/lib/types/groups'

interface GroupsDashboardProps {
  groups: Group[]
  loading: boolean
  viewMode: 'grid' | 'list'
  selectedGroups: number[]
  onCreateGroup: () => void
  onEditGroup: (group: Group) => void
  onDeleteGroup: (groupId: number) => void
  onViewGroup: (group: Group) => void
}

export default function GroupsDashboard({
  groups,
  loading,
  viewMode,
  selectedGroups,
  onCreateGroup,
  onEditGroup,
  onDeleteGroup,
  onViewGroup,
}: GroupsDashboardProps) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null)

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, group: Group) => {
    event.stopPropagation()
    setAnchorEl(event.currentTarget)
    setSelectedGroup(group)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
    setSelectedGroup(null)
  }

  const handleMenuAction = (action: string) => {
    if (!selectedGroup) return

    switch (action) {
      case 'view':
        onViewGroup(selectedGroup)
        break
      case 'edit':
        onEditGroup(selectedGroup)
        break
      case 'duplicate':
        // Handle duplicate
        break
      case 'archive':
        // Handle archive
        break
      case 'delete':
        onDeleteGroup(selectedGroup.id)
        break
    }
    handleMenuClose()
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success'
      case 'inactive':
        return 'warning'
      case 'archived':
        return 'default'
      case 'draft':
        return 'info'
      default:
        return 'default'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  if (viewMode === 'list') {
    const columns: GridColDef[] = [
      {
        field: 'name',
        headerName: 'Group Name',
        flex: 1,
        minWidth: 200,
        renderCell: (params) => (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
              {params.row.name[0]}
            </Avatar>
            <Box>
              <Typography variant="body2" fontWeight="medium">
                {params.row.name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {params.row.description?.substring(0, 50)}...
              </Typography>
            </Box>
          </Box>
        ),
      },
      {
        field: 'status',
        headerName: 'Status',
        width: 120,
        renderCell: (params) => (
          <Chip
            label={params.value}
            color={getStatusColor(params.value) as any}
            size="small"
            variant="outlined"
          />
        ),
      },
      {
        field: 'member_count',
        headerName: 'Members',
        width: 100,
        renderCell: (params) => (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <PeopleIcon fontSize="small" color="action" />
            <Typography variant="body2">{params.value || 0}</Typography>
          </Box>
        ),
      },
      {
        field: 'assigned_paths_count',
        headerName: 'Paths',
        width: 100,
        renderCell: (params) => (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <SchoolIcon fontSize="small" color="action" />
            <Typography variant="body2">{params.value || 0}</Typography>
          </Box>
        ),
      },
      {
        field: 'completion_rate',
        headerName: 'Progress',
        width: 150,
        renderCell: (params) => (
          <Box sx={{ width: '100%' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
              <Typography variant="caption">{params.value || 0}%</Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={params.value || 0}
              sx={{ height: 6, borderRadius: 3 }}
            />
          </Box>
        ),
      },
      {
        field: 'created_at',
        headerName: 'Created',
        width: 120,
        renderCell: (params) => (
          <Typography variant="body2" color="text.secondary">
            {formatDate(params.value)}
          </Typography>
        ),
      },
      {
        field: 'actions',
        type: 'actions',
        headerName: 'Actions',
        width: 100,
        getActions: (params) => [
          <GridActionsCellItem
            key="view"
            icon={<ViewIcon />}
            label="View"
            onClick={() => onViewGroup(params.row)}
          />,
          <GridActionsCellItem
            key="edit"
            icon={<EditIcon />}
            label="Edit"
            onClick={() => onEditGroup(params.row)}
          />,
          <GridActionsCellItem
            key="more"
            icon={<MoreVertIcon />}
            label="More"
            onClick={(event) => handleMenuOpen(event, params.row)}
          />,
        ],
      },
    ]

    return (
      <Card>
        <DataGrid
          rows={groups}
          columns={columns}
          loading={loading}
          checkboxSelection
          disableRowSelectionOnClick
          autoHeight
          pageSizeOptions={[10, 25, 50]}
          initialState={{
            pagination: { paginationModel: { pageSize: 25 } },
          }}
          sx={{
            border: 'none',
            '& .MuiDataGrid-cell:focus': {
              outline: 'none',
            },
          }}
        />
      </Card>
    )
  }

  // Grid View
  return (
    <Box>
      {loading ? (
        <Grid container spacing={3}>
          {Array.from({ length: 6 }).map((_, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <Card>
                <CardContent>
                  <Skeleton variant="text" width="60%" height={32} />
                  <Skeleton variant="text" width="100%" height={20} sx={{ mt: 1 }} />
                  <Skeleton variant="text" width="80%" height={20} />
                  <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
                    <Skeleton variant="rectangular" width={60} height={24} />
                    <Skeleton variant="rectangular" width={80} height={24} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      ) : groups.length === 0 ? (
        <Card sx={{ textAlign: 'center', py: 8 }}>
          <CardContent>
            <PeopleIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No groups found
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Create your first group to start organizing learners
            </Typography>
            <Button variant="contained" onClick={onCreateGroup}>
              Create Group
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Grid container spacing={3}>
          {groups.map((group, index) => (
            <Grid item xs={12} sm={6} md={4} key={group.id}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card
                  sx={{
                    height: '100%',
                    cursor: 'pointer',
                    transition: 'all 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 4,
                    },
                  }}
                  onClick={() => onViewGroup(group)}
                >
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Avatar sx={{ bgcolor: 'primary.main' }}>
                          {group.name[0]}
                        </Avatar>
                        <Box>
                          <Typography variant="h6" component="h3" noWrap>
                            {group.name}
                          </Typography>
                          <Chip
                            label={group.status}
                            color={getStatusColor(group.status) as any}
                            size="small"
                            variant="outlined"
                          />
                        </Box>
                      </Box>
                      <IconButton
                        size="small"
                        onClick={(event) => handleMenuOpen(event, group)}
                      >
                        <MoreVertIcon />
                      </IconButton>
                    </Box>

                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{
                        mb: 2,
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                      }}
                    >
                      {group.description || 'No description provided'}
                    </Typography>

                    <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <PeopleIcon fontSize="small" color="action" />
                        <Typography variant="body2" color="text.secondary">
                          {group.member_count || 0} members
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <SchoolIcon fontSize="small" color="action" />
                        <Typography variant="body2" color="text.secondary">
                          {group.assigned_paths_count || 0} paths
                        </Typography>
                      </Box>
                    </Box>

                    {group.completion_rate !== undefined && (
                      <Box sx={{ mb: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                          <Typography variant="body2" color="text.secondary">
                            Progress
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {group.completion_rate}%
                          </Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={group.completion_rate}
                          sx={{ height: 6, borderRadius: 3 }}
                        />
                      </Box>
                    )}

                    {group.tags && group.tags.length > 0 && (
                      <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap', mb: 1 }}>
                        {group.tags.slice(0, 3).map((tag) => (
                          <Chip
                            key={tag}
                            label={tag}
                            size="small"
                            variant="outlined"
                            sx={{ fontSize: '0.75rem' }}
                          />
                        ))}
                        {group.tags.length > 3 && (
                          <Chip
                            label={`+${group.tags.length - 3}`}
                            size="small"
                            variant="outlined"
                            sx={{ fontSize: '0.75rem' }}
                          />
                        )}
                      </Box>
                    )}
                  </CardContent>

                  <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
                    <Typography variant="caption" color="text.secondary">
                      Created {formatDate(group.created_at)}
                    </Typography>
                    <Box>
                      {group.members && group.members.length > 0 && (
                        <AvatarGroup max={3} sx={{ '& .MuiAvatar-root': { width: 24, height: 24 } }}>
                          {group.members.slice(0, 3).map((member) => (
                            <Avatar
                              key={member.id}
                              src={member.avatar_url}
                              alt={member.full_name}
                              sx={{ width: 24, height: 24 }}
                            >
                              {member.full_name?.[0]}
                            </Avatar>
                          ))}
                        </AvatarGroup>
                      )}
                    </Box>
                  </CardActions>
                </Card>
              </motion.div>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => handleMenuAction('view')}>
          <ViewIcon sx={{ mr: 1 }} />
          View Details
        </MenuItem>
        <MenuItem onClick={() => handleMenuAction('edit')}>
          <EditIcon sx={{ mr: 1 }} />
          Edit Group
        </MenuItem>
        <MenuItem onClick={() => handleMenuAction('duplicate')}>
          <DuplicateIcon sx={{ mr: 1 }} />
          Duplicate
        </MenuItem>
        <MenuItem onClick={() => handleMenuAction('archive')}>
          <ArchiveIcon sx={{ mr: 1 }} />
          Archive
        </MenuItem>
        <MenuItem onClick={() => handleMenuAction('delete')} sx={{ color: 'error.main' }}>
          <DeleteIcon sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>
    </Box>
  )
}
