'use client'

import React, { useState } from 'react'
import {
  Box,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Paper,
  Grid,
  Switch,
  FormControlLabel,
  Autocomplete,
  Card,
  CardContent,
  Avatar,
  Button,
  IconButton,
  Tooltip,
} from '@mui/material'
import {
  School as SchoolIcon,
  Schedule as ScheduleIcon,
  Category as CategoryIcon,
  Label as TagIcon,
  Image as ImageIcon,
  CloudUpload as UploadIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'

interface PathBasicInfoProps {
  data: any
  onChange: (data: any) => void
}

const difficultyLevels = [
  { value: 'beginner', label: 'Beginner', color: '#4caf50' },
  { value: 'intermediate', label: 'Intermediate', color: '#ff9800' },
  { value: 'advanced', label: 'Advanced', color: '#f44336' },
]

const categories = [
  'Technology',
  'Business',
  'Design',
  'Marketing',
  'Data Science',
  'Programming',
  'Project Management',
  'Leadership',
  'Communication',
  'Sales',
  'Finance',
  'Healthcare',
  'Education',
  'Engineering',
  'Creative Arts',
]

const popularTags = [
  'JavaScript', 'React', 'Python', 'Machine Learning', 'UI/UX', 'Leadership',
  'Project Management', 'Data Analysis', 'Digital Marketing', 'Cloud Computing',
  'Agile', 'Scrum', 'DevOps', 'Cybersecurity', 'Mobile Development', 'AI',
]

export default function PathBasicInfo({ data, onChange }: PathBasicInfoProps) {
  const [objectives, setObjectives] = useState<string[]>([])
  const [prerequisites, setPrerequisites] = useState<string[]>([])
  const [tags, setTags] = useState<string[]>([])
  const [newObjective, setNewObjective] = useState('')
  const [newPrerequisite, setNewPrerequisite] = useState('')
  const [isInitialized, setIsInitialized] = useState(false)

  // Sync local state with props when data changes - but only if data actually changed
  React.useEffect(() => {
    const newObjectives = data.objectives || []
    const newPrerequisites = data.prerequisites || []
    const newTags = data.tags || []

    // Only update if the data has actually changed to prevent loops
    if (
      JSON.stringify(newObjectives) !== JSON.stringify(objectives) ||
      JSON.stringify(newPrerequisites) !== JSON.stringify(prerequisites) ||
      JSON.stringify(newTags) !== JSON.stringify(tags) ||
      !isInitialized
    ) {
      setObjectives(newObjectives)
      setPrerequisites(newPrerequisites)
      setTags(newTags)
      setIsInitialized(true)
    }
  }, [data.objectives, data.prerequisites, data.tags, objectives, prerequisites, tags, isInitialized])

  const handleFieldChange = React.useCallback((field: string, value: any) => {
    onChange({ [field]: value })
  }, [onChange])

  const handleAddObjective = React.useCallback(() => {
    if (newObjective.trim()) {
      const newObjectives = [...objectives, newObjective.trim()]
      setObjectives(newObjectives)
      handleFieldChange('objectives', newObjectives)
      setNewObjective('')
    }
  }, [newObjective, objectives, handleFieldChange])

  const handleRemoveObjective = React.useCallback((index: number) => {
    const newObjectives = objectives.filter((_, i) => i !== index)
    setObjectives(newObjectives)
    handleFieldChange('objectives', newObjectives)
  }, [objectives, handleFieldChange])

  const handleAddPrerequisite = React.useCallback(() => {
    if (newPrerequisite.trim()) {
      const newPrerequisites = [...prerequisites, newPrerequisite.trim()]
      setPrerequisites(newPrerequisites)
      handleFieldChange('prerequisites', newPrerequisites)
      setNewPrerequisite('')
    }
  }, [newPrerequisite, prerequisites, handleFieldChange])

  const handleRemovePrerequisite = React.useCallback((index: number) => {
    const newPrerequisites = prerequisites.filter((_, i) => i !== index)
    setPrerequisites(newPrerequisites)
    handleFieldChange('prerequisites', newPrerequisites)
  }, [prerequisites, handleFieldChange])

  const handleTagsChange = React.useCallback((event: any, newTags: string[]) => {
    setTags(newTags)
    handleFieldChange('tags', newTags)
  }, [handleFieldChange])

  const getDifficultyColor = (difficulty: string) => {
    const level = difficultyLevels.find(l => l.value === difficulty)
    return level?.color || '#1976d2'
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Paper sx={{ p: 3 }}>
        <Typography variant="h5" fontWeight="bold" gutterBottom>
          Basic Information
        </Typography>
        <Typography variant="body2" color="text.secondary" mb={3}>
          Configure the fundamental details of your learning path
        </Typography>

        <Grid container spacing={3}>
          {/* Title and Description */}
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Learning Path Title"
              value={data.title || ''}
              onChange={(e) => handleFieldChange('title', e.target.value)}
              placeholder="Enter a compelling title for your learning path"
              variant="outlined"
              required
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Description"
              value={data.description || ''}
              onChange={(e) => handleFieldChange('description', e.target.value)}
              placeholder="Describe what learners will achieve in this path"
              multiline
              rows={4}
              variant="outlined"
            />
          </Grid>

          {/* Difficulty and Duration */}
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>Difficulty Level</InputLabel>
              <Select
                value={data.difficulty || 'beginner'}
                onChange={(e) => handleFieldChange('difficulty', e.target.value)}
                label="Difficulty Level"
              >
                {difficultyLevels.map((level) => (
                  <MenuItem key={level.value} value={level.value}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Box
                        sx={{
                          width: 12,
                          height: 12,
                          borderRadius: '50%',
                          bgcolor: level.color,
                        }}
                      />
                      {level.label}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Estimated Duration (weeks)"
              type="number"
              value={data.duration || 4}
              onChange={(e) => handleFieldChange('duration', parseInt(e.target.value) || 4)}
              inputProps={{ min: 1, max: 52 }}
              variant="outlined"
            />
          </Grid>

          {/* Category */}
          <Grid item xs={12} md={6}>
            <Autocomplete
              options={categories}
              value={data.category || ''}
              onChange={(event, newValue) => handleFieldChange('category', newValue || '')}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Category"
                  placeholder="Select or type a category"
                  variant="outlined"
                />
              )}
              freeSolo
            />
          </Grid>

          {/* Tags */}
          <Grid item xs={12} md={6}>
            <Autocomplete
              multiple
              options={popularTags}
              value={tags}
              onChange={handleTagsChange}
              renderTags={(value, getTagProps) =>
                value.map((option, index) => (
                  <Chip
                    variant="outlined"
                    label={option}
                    {...getTagProps({ index })}
                    key={option}
                  />
                ))
              }
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Tags"
                  placeholder="Add relevant tags"
                  variant="outlined"
                />
              )}
              freeSolo
            />
          </Grid>

          {/* Learning Objectives */}
          <Grid item xs={12}>
            <Card variant="outlined">
              <CardContent>
                <Box display="flex" alignItems="center" gap={1} mb={2}>
                  <Avatar sx={{ bgcolor: 'primary.main', width: 32, height: 32 }}>
                    <SchoolIcon fontSize="small" />
                  </Avatar>
                  <Typography variant="h6" fontWeight="bold">
                    Learning Objectives
                  </Typography>
                </Box>

                <Box display="flex" gap={1} mb={2}>
                  <TextField
                    fullWidth
                    size="small"
                    placeholder="Add a learning objective"
                    value={newObjective}
                    onChange={(e) => setNewObjective(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleAddObjective()}
                  />
                  <Button variant="outlined" onClick={handleAddObjective}>
                    Add
                  </Button>
                </Box>

                <Box display="flex" flexWrap="wrap" gap={1}>
                  {objectives.map((objective, index) => (
                    <Chip
                      key={index}
                      label={objective}
                      onDelete={() => handleRemoveObjective(index)}
                      color="primary"
                      variant="outlined"
                    />
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Prerequisites */}
          <Grid item xs={12}>
            <Card variant="outlined">
              <CardContent>
                <Box display="flex" alignItems="center" gap={1} mb={2}>
                  <Avatar sx={{ bgcolor: 'warning.main', width: 32, height: 32 }}>
                    <ScheduleIcon fontSize="small" />
                  </Avatar>
                  <Typography variant="h6" fontWeight="bold">
                    Prerequisites
                  </Typography>
                </Box>

                <Box display="flex" gap={1} mb={2}>
                  <TextField
                    fullWidth
                    size="small"
                    placeholder="Add a prerequisite"
                    value={newPrerequisite}
                    onChange={(e) => setNewPrerequisite(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleAddPrerequisite()}
                  />
                  <Button variant="outlined" onClick={handleAddPrerequisite}>
                    Add
                  </Button>
                </Box>

                <Box display="flex" flexWrap="wrap" gap={1}>
                  {prerequisites.map((prerequisite, index) => (
                    <Chip
                      key={index}
                      label={prerequisite}
                      onDelete={() => handleRemovePrerequisite(index)}
                      color="warning"
                      variant="outlined"
                    />
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Settings */}
          <Grid item xs={12}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  Settings
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={data.is_template || false}
                          onChange={(e) => handleFieldChange('is_template', e.target.checked)}
                        />
                      }
                      label="Save as Template"
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={data.is_featured || false}
                          onChange={(e) => handleFieldChange('is_featured', e.target.checked)}
                        />
                      }
                      label="Featured Path"
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Thumbnail Upload */}
          <Grid item xs={12}>
            <Card variant="outlined">
              <CardContent>
                <Box display="flex" alignItems="center" gap={1} mb={2}>
                  <Avatar sx={{ bgcolor: 'info.main', width: 32, height: 32 }}>
                    <ImageIcon fontSize="small" />
                  </Avatar>
                  <Typography variant="h6" fontWeight="bold">
                    Thumbnail Image
                  </Typography>
                </Box>

                <Box
                  sx={{
                    border: '2px dashed',
                    borderColor: 'divider',
                    borderRadius: 2,
                    p: 3,
                    textAlign: 'center',
                    cursor: 'pointer',
                    '&:hover': {
                      borderColor: 'primary.main',
                      bgcolor: 'action.hover',
                    },
                  }}
                >
                  <UploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
                  <Typography variant="body1" gutterBottom>
                    Click to upload thumbnail image
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Recommended size: 1200x630px (PNG, JPG)
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Paper>
    </motion.div>
  )
}
