import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface TagContentRequest {
  title: string
  description?: string
  content?: string
  type: 'video' | 'pdf' | 'ppt' | 'quiz' | 'text' | 'interactive'
}

interface TagContentResponse {
  tags: string[]
  category: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedDuration: number
  skills: string[]
  prerequisites: string[]
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { title, description, content, type }: TagContentRequest = await req.json()

    if (!title) {
      return new Response(
        JSON.stringify({ error: 'Title is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get OpenAI API key from environment
    const openaiApiKey = Deno.env.get('OPENAI_API_KEY')
    if (!openaiApiKey) {
      throw new Error('OpenAI API key not configured')
    }

    // Prepare content for analysis
    const contentToAnalyze = [
      `Title: ${title}`,
      description ? `Description: ${description}` : '',
      content ? `Content: ${content.substring(0, 1000)}` : '', // Limit content length
      `Content Type: ${type}`
    ].filter(Boolean).join('\n\n')

    // Create OpenAI prompt
    const prompt = `
Analyze the following educational content and provide structured metadata:

${contentToAnalyze}

Please provide a JSON response with the following structure:
{
  "tags": ["tag1", "tag2", "tag3"], // 3-8 relevant tags
  "category": "category_name", // Main category (e.g., "Programming", "Business", "Design")
  "difficulty": "beginner|intermediate|advanced", // Difficulty level
  "estimatedDuration": 30, // Estimated duration in minutes
  "skills": ["skill1", "skill2"], // 2-5 skills learners will gain
  "prerequisites": ["prereq1", "prereq2"] // 0-3 prerequisites (can be empty array)
}

Guidelines:
- Tags should be specific and relevant to the content
- Category should be broad but descriptive
- Difficulty should match the content complexity
- Duration should be realistic for the content type and complexity
- Skills should be actionable outcomes
- Prerequisites should only include essential prior knowledge

Respond only with valid JSON.`

    // Call OpenAI API
    const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert educational content analyst. Provide accurate, helpful metadata for learning content.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 500,
        temperature: 0.3,
      }),
    })

    if (!openaiResponse.ok) {
      const errorData = await openaiResponse.text()
      console.error('OpenAI API error:', errorData)
      throw new Error(`OpenAI API error: ${openaiResponse.status}`)
    }

    const openaiData = await openaiResponse.json()
    const aiResponse = openaiData.choices[0]?.message?.content

    if (!aiResponse) {
      throw new Error('No response from OpenAI')
    }

    // Parse AI response
    let parsedResponse: TagContentResponse
    try {
      parsedResponse = JSON.parse(aiResponse)
    } catch (parseError) {
      console.error('Failed to parse AI response:', aiResponse)
      // Fallback response
      parsedResponse = {
        tags: [title.split(' ')[0], type, 'learning'],
        category: 'General',
        difficulty: 'beginner',
        estimatedDuration: type === 'video' ? 15 : type === 'quiz' ? 10 : 30,
        skills: ['Knowledge'],
        prerequisites: []
      }
    }

    // Validate and sanitize response
    const sanitizedResponse: TagContentResponse = {
      tags: Array.isArray(parsedResponse.tags) 
        ? parsedResponse.tags.slice(0, 8).filter(tag => typeof tag === 'string' && tag.length > 0)
        : ['learning'],
      category: typeof parsedResponse.category === 'string' 
        ? parsedResponse.category 
        : 'General',
      difficulty: ['beginner', 'intermediate', 'advanced'].includes(parsedResponse.difficulty)
        ? parsedResponse.difficulty
        : 'beginner',
      estimatedDuration: typeof parsedResponse.estimatedDuration === 'number' && parsedResponse.estimatedDuration > 0
        ? Math.min(parsedResponse.estimatedDuration, 480) // Max 8 hours
        : 30,
      skills: Array.isArray(parsedResponse.skills)
        ? parsedResponse.skills.slice(0, 5).filter(skill => typeof skill === 'string' && skill.length > 0)
        : ['Knowledge'],
      prerequisites: Array.isArray(parsedResponse.prerequisites)
        ? parsedResponse.prerequisites.slice(0, 3).filter(prereq => typeof prereq === 'string' && prereq.length > 0)
        : []
    }

    // Log successful analysis
    console.log('Content analysis completed:', {
      title,
      type,
      tags: sanitizedResponse.tags.length,
      category: sanitizedResponse.category
    })

    return new Response(
      JSON.stringify(sanitizedResponse),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in ai-tag-content function:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Failed to analyze content',
        details: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

/* Example usage:
POST /functions/v1/ai-tag-content
{
  "title": "Introduction to React Hooks",
  "description": "Learn how to use React Hooks to manage state and side effects in functional components",
  "content": "React Hooks are functions that let you use state and other React features...",
  "type": "video"
}

Response:
{
  "tags": ["react", "hooks", "javascript", "frontend", "web-development"],
  "category": "Programming",
  "difficulty": "intermediate",
  "estimatedDuration": 45,
  "skills": ["React Hooks", "State Management", "Functional Components"],
  "prerequisites": ["Basic React", "JavaScript ES6"]
}
*/
