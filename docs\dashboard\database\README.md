# Database Documentation

## 📋 Overview

The ZenithLearn AI Learner Dashboard uses Supabase PostgreSQL with 7 specialized tables that support advanced features like AI insights, mood tracking, gamification, and real-time updates. All tables implement Row Level Security (RLS) for multi-tenant data isolation.

## 🏗 Database Architecture

### Technology Stack
- **Database**: PostgreSQL 15+ via Supabase
- **Security**: Row Level Security (RLS) policies
- **Real-time**: Supabase real-time subscriptions
- **Migrations**: Supabase CLI migration system
- **Backup**: Automated daily backups
- **Monitoring**: Built-in performance analytics

### Connection Details
```typescript
// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
```

## 📊 Database Schema Overview

### Table Structure
```
Dashboard Database Schema
├── ai_insights              # AI-generated learning insights
├── mood_entries             # Daily mood and wellness tracking
├── daily_challenges         # Gamified learning challenges
├── gamification_data        # Points, levels, and achievements
├── dashboard_customization  # User interface preferences
├── peer_comparison_data     # Anonymous performance metrics
└── voice_commands_log       # Voice interaction history
```

### Relationships Diagram
```
users (Supabase Auth)
├── ai_insights (1:many)
├── mood_entries (1:many)
├── daily_challenges (1:many)
├── gamification_data (1:1)
├── dashboard_customization (1:1)
├── peer_comparison_data (1:many)
└── voice_commands_log (1:many)
```

## 🧠 AI Insights Table

### Purpose
Stores AI-generated learning insights, recommendations, and performance analysis.

### Schema
```sql
CREATE TABLE ai_insights (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL,
  insight_type VARCHAR(50) NOT NULL CHECK (insight_type IN ('performance', 'recommendation', 'warning', 'achievement')),
  title VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
  confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
  action_items JSONB DEFAULT '[]',
  metadata JSONB DEFAULT '{}',
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_ai_insights_user_id ON ai_insights(user_id);
CREATE INDEX idx_ai_insights_tenant_id ON ai_insights(tenant_id);
CREATE INDEX idx_ai_insights_type ON ai_insights(insight_type);
CREATE INDEX idx_ai_insights_created_at ON ai_insights(created_at DESC);
```

### Sample Data
```sql
INSERT INTO ai_insights (user_id, tenant_id, insight_type, title, content, confidence_score, action_items) VALUES
('user-uuid', 'tenant-uuid', 'performance', 'Learning Efficiency Improvement', 
 'Your learning efficiency has increased by 15% this week...', 0.85,
 '["Continue current study schedule", "Focus on JavaScript fundamentals"]');
```

## 😊 Mood Entries Table

### Purpose
Tracks daily mood, energy levels, and wellness data for personalized learning adaptation.

### Schema
```sql
CREATE TABLE mood_entries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL,
  mood VARCHAR(20) NOT NULL CHECK (mood IN ('excited', 'happy', 'neutral', 'tired', 'stressed')),
  energy_level INTEGER CHECK (energy_level >= 1 AND energy_level <= 10),
  notes TEXT,
  tags JSONB DEFAULT '[]',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_mood_entries_user_id ON mood_entries(user_id);
CREATE INDEX idx_mood_entries_tenant_id ON mood_entries(tenant_id);
CREATE INDEX idx_mood_entries_created_at ON mood_entries(created_at DESC);
CREATE INDEX idx_mood_entries_mood ON mood_entries(mood);
```

### Sample Data
```sql
INSERT INTO mood_entries (user_id, tenant_id, mood, energy_level, notes) VALUES
('user-uuid', 'tenant-uuid', 'happy', 8, 'Great morning workout!');
```

## 🏆 Daily Challenges Table

### Purpose
Manages gamified daily learning challenges with progress tracking and rewards.

### Schema
```sql
CREATE TABLE daily_challenges (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL,
  challenge_type VARCHAR(50) NOT NULL CHECK (challenge_type IN ('learning_time', 'quiz_completion', 'reading', 'social')),
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  target_value INTEGER NOT NULL,
  current_value INTEGER DEFAULT 0,
  points_reward INTEGER DEFAULT 0,
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'expired')),
  due_date DATE NOT NULL,
  completed_at TIMESTAMP WITH TIME ZONE,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_daily_challenges_user_id ON daily_challenges(user_id);
CREATE INDEX idx_daily_challenges_tenant_id ON daily_challenges(tenant_id);
CREATE INDEX idx_daily_challenges_status ON daily_challenges(status);
CREATE INDEX idx_daily_challenges_due_date ON daily_challenges(due_date);
```

## 🎮 Gamification Data Table

### Purpose
Stores user gamification progress including points, levels, badges, and achievements.

### Schema
```sql
CREATE TABLE gamification_data (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID UNIQUE REFERENCES auth.users(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL,
  current_level INTEGER DEFAULT 1,
  experience_points INTEGER DEFAULT 0,
  points_to_next_level INTEGER DEFAULT 100,
  total_badges INTEGER DEFAULT 0,
  learning_streak INTEGER DEFAULT 0,
  longest_streak INTEGER DEFAULT 0,
  achievements JSONB DEFAULT '[]',
  badges JSONB DEFAULT '[]',
  milestones JSONB DEFAULT '[]',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_gamification_data_user_id ON gamification_data(user_id);
CREATE INDEX idx_gamification_data_tenant_id ON gamification_data(tenant_id);
CREATE INDEX idx_gamification_data_level ON gamification_data(current_level);
```

## ⚙️ Dashboard Customization Table

### Purpose
Stores user interface preferences and dashboard layout customizations.

### Schema
```sql
CREATE TABLE dashboard_customization (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID UNIQUE REFERENCES auth.users(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL,
  widget_visibility JSONB DEFAULT '{}',
  layout_preferences JSONB DEFAULT '{}',
  theme_settings JSONB DEFAULT '{}',
  notification_preferences JSONB DEFAULT '{}',
  accessibility_settings JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_dashboard_customization_user_id ON dashboard_customization(user_id);
CREATE INDEX idx_dashboard_customization_tenant_id ON dashboard_customization(tenant_id);
```

## 📊 Peer Comparison Data Table

### Purpose
Stores anonymous performance metrics for peer comparison and benchmarking.

### Schema
```sql
CREATE TABLE peer_comparison_data (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL,
  metric_type VARCHAR(50) NOT NULL,
  metric_value DECIMAL(10,2) NOT NULL,
  percentile DECIMAL(5,2),
  cohort_id UUID,
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_peer_comparison_user_id ON peer_comparison_data(user_id);
CREATE INDEX idx_peer_comparison_tenant_id ON peer_comparison_data(tenant_id);
CREATE INDEX idx_peer_comparison_metric_type ON peer_comparison_data(metric_type);
CREATE INDEX idx_peer_comparison_period ON peer_comparison_data(period_start, period_end);
```

## 🎤 Voice Commands Log Table

### Purpose
Logs voice interactions for analytics and improving voice recognition accuracy.

### Schema
```sql
CREATE TABLE voice_commands_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL,
  command_text TEXT NOT NULL,
  intent VARCHAR(100),
  confidence_score DECIMAL(3,2),
  execution_status VARCHAR(20) DEFAULT 'pending' CHECK (execution_status IN ('pending', 'success', 'failed')),
  response_text TEXT,
  execution_time_ms INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_voice_commands_user_id ON voice_commands_log(user_id);
CREATE INDEX idx_voice_commands_tenant_id ON voice_commands_log(tenant_id);
CREATE INDEX idx_voice_commands_intent ON voice_commands_log(intent);
CREATE INDEX idx_voice_commands_created_at ON voice_commands_log(created_at DESC);
```

## 🔐 Row Level Security (RLS) Policies

### Security Overview
All tables implement RLS policies to ensure:
- Users can only access their own data
- Tenant isolation is enforced
- Service role has administrative access
- Real-time subscriptions respect security boundaries

### Example RLS Policy
```sql
-- AI Insights RLS Policy
CREATE POLICY "Users can view their own insights" ON ai_insights
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own insights" ON ai_insights
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own insights" ON ai_insights
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own insights" ON ai_insights
  FOR DELETE USING (auth.uid() = user_id);

-- Enable RLS
ALTER TABLE ai_insights ENABLE ROW LEVEL SECURITY;
```

## 🔄 Real-time Subscriptions

### Supabase Real-time Setup
```typescript
// Subscribe to dashboard updates
const subscription = supabase
  .channel('dashboard_updates')
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'daily_challenges',
    filter: `user_id=eq.${user.id}`
  }, (payload) => {
    handleChallengeUpdate(payload);
  })
  .on('postgres_changes', {
    event: 'INSERT',
    schema: 'public',
    table: 'ai_insights',
    filter: `user_id=eq.${user.id}`
  }, (payload) => {
    handleNewInsight(payload.new);
  })
  .subscribe();
```

## 📈 Performance Optimization

### Indexing Strategy
- **Primary Keys**: UUID with gen_random_uuid()
- **Foreign Keys**: Indexed for join performance
- **Query Patterns**: Indexes match common query patterns
- **Composite Indexes**: Multi-column indexes for complex queries

### Query Optimization
```sql
-- Optimized query for dashboard data
SELECT 
  g.current_level,
  g.experience_points,
  COUNT(dc.id) as active_challenges,
  AVG(me.energy_level) as avg_energy
FROM gamification_data g
LEFT JOIN daily_challenges dc ON g.user_id = dc.user_id AND dc.status = 'active'
LEFT JOIN mood_entries me ON g.user_id = me.user_id AND me.created_at >= CURRENT_DATE
WHERE g.user_id = $1 AND g.tenant_id = $2
GROUP BY g.current_level, g.experience_points;
```

---

**Next Steps**: Explore [Migration Scripts](./migrations.md) for database setup or check [RLS Policies](./rls-policies.md) for detailed security configuration.
