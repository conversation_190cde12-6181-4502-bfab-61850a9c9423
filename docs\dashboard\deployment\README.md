# Deployment Documentation

## 📋 Overview

This guide covers the complete deployment process for the ZenithLearn AI Learner Dashboard, from initial setup to production deployment on Vercel with Supabase backend.

## 🏗 Deployment Architecture

### Technology Stack
- **Frontend**: Next.js 14 deployed on Vercel
- **Backend**: Supabase (PostgreSQL + Auth + Real-time)
- **CDN**: Vercel Edge Network
- **Domain**: Custom domain with SSL
- **Monitoring**: Vercel Analytics + Supabase Monitoring

### Environment Overview
```
Production Environment
├── Vercel (Frontend Hosting)
│   ├── Next.js Application
│   ├── API Routes
│   ├── Static Assets
│   └── Edge Functions
├── Supabase (Backend Services)
│   ├── PostgreSQL Database
│   ├── Authentication
│   ├── Real-time Subscriptions
│   └── Storage
└── External Services
    ├── Domain Provider
    ├── Email Service
    └── Analytics
```

## 🚀 Quick Deployment Guide

### Prerequisites Checklist
- [ ] Node.js 18+ installed
- [ ] Git repository access
- [ ] Vercel account
- [ ] Supabase account
- [ ] Domain name (optional)

### 1. Repository Setup
```bash
# Clone the repository
git clone https://github.com/your-org/zenithlearn-ai-learner.git
cd zenithlearn-ai-learner

# Install dependencies
npm install

# Verify installation
npm run build
```

### 2. Supabase Setup
```bash
# Install Supabase CLI
npm install -g @supabase/cli

# Login to Supabase
supabase login

# Link to your project
supabase link --project-ref your-project-ref

# Run migrations
supabase db push
```

### 3. Environment Configuration
```bash
# Copy environment template
cp .env.example .env.local

# Configure environment variables
# See Environment Variables section below
```

### 4. Vercel Deployment
```bash
# Install Vercel CLI
npm install -g vercel

# Deploy to Vercel
vercel

# Follow the prompts:
# - Link to existing project or create new
# - Configure environment variables
# - Deploy to production
```

## 🔧 Environment Variables

### Required Environment Variables
```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Application Configuration
NEXT_PUBLIC_APP_URL=https://your-domain.com
NEXT_PUBLIC_APP_NAME="ZenithLearn AI"

# Authentication
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=https://your-domain.com

# Optional: Analytics
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=your-analytics-id
```

### Environment-Specific Variables

#### Development (.env.local)
```bash
NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-local-anon-key
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

#### Production (Vercel Dashboard)
```bash
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-production-anon-key
NEXT_PUBLIC_APP_URL=https://your-domain.com
```

## 🗄️ Database Migration

### Initial Database Setup
```bash
# Create Supabase project
supabase projects create zenithlearn-dashboard

# Initialize local development
supabase init

# Start local development
supabase start

# Apply migrations
supabase db push

# Seed initial data (optional)
supabase db seed
```

### Migration Files
```sql
-- migrations/001_initial_schema.sql
-- Create dashboard-specific tables
-- See database/migrations.md for complete scripts
```

### RLS Policy Setup
```bash
# Apply RLS policies
supabase db push --include-all

# Verify policies
supabase db diff
```

## 🌐 Vercel Configuration

### vercel.json Configuration
```json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "regions": ["iad1"],
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "Access-Control-Allow-Origin",
          "value": "*"
        },
        {
          "key": "Access-Control-Allow-Methods",
          "value": "GET, POST, PUT, DELETE, OPTIONS"
        },
        {
          "key": "Access-Control-Allow-Headers",
          "value": "Content-Type, Authorization"
        }
      ]
    }
  ],
  "rewrites": [
    {
      "source": "/dashboard",
      "destination": "/learner/dashboard"
    }
  ]
}
```

### Build Configuration
```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  images: {
    domains: ['your-supabase-project.supabase.co'],
  },
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  async redirects() {
    return [
      {
        source: '/',
        destination: '/learner/dashboard',
        permanent: false,
      },
    ];
  },
};

module.exports = nextConfig;
```

## 🔐 Security Configuration

### Supabase Security
```sql
-- Enable RLS on all tables
ALTER TABLE ai_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE mood_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_challenges ENABLE ROW LEVEL SECURITY;
-- ... other tables

-- Configure Auth settings
UPDATE auth.config SET 
  site_url = 'https://your-domain.com',
  jwt_exp = 3600,
  refresh_token_rotation_enabled = true;
```

### Vercel Security Headers
```javascript
// next.config.js security headers
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block'
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  }
];
```

## 📊 Monitoring & Analytics

### Vercel Analytics Setup
```bash
# Install Vercel Analytics
npm install @vercel/analytics

# Add to app layout
import { Analytics } from '@vercel/analytics/react';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        {children}
        <Analytics />
      </body>
    </html>
  );
}
```

### Supabase Monitoring
```typescript
// Monitor database performance
const { data, error } = await supabase
  .from('dashboard_metrics')
  .select('*')
  .order('created_at', { ascending: false })
  .limit(100);

// Log API performance
console.log('API Response Time:', performance.now() - startTime);
```

## 🔄 CI/CD Pipeline

### GitHub Actions Workflow
```yaml
# .github/workflows/deploy.yml
name: Deploy to Vercel

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test
      
      - name: Build application
        run: npm run build
      
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
```

## 🚨 Health Checks

### Application Health Check
```typescript
// app/api/health/route.ts
export async function GET() {
  try {
    // Check database connection
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (error) throw error;

    return Response.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      database: 'connected',
      version: process.env.npm_package_version
    });
  } catch (error) {
    return Response.json({
      status: 'unhealthy',
      error: error.message
    }, { status: 500 });
  }
}
```

### Monitoring Dashboard
```bash
# Check application status
curl https://your-domain.com/api/health

# Monitor key metrics
curl https://your-domain.com/api/metrics
```

## 🔧 Performance Optimization

### Build Optimization
```javascript
// next.config.js optimizations
const nextConfig = {
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['@mui/material', '@mui/icons-material'],
  },
  images: {
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
  },
};
```

### Database Optimization
```sql
-- Create performance indexes
CREATE INDEX CONCURRENTLY idx_dashboard_performance 
ON ai_insights(user_id, created_at DESC) 
WHERE is_read = false;

-- Analyze query performance
EXPLAIN ANALYZE SELECT * FROM daily_challenges 
WHERE user_id = $1 AND status = 'active';
```

## 📋 Deployment Checklist

### Pre-Deployment
- [ ] All tests passing
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] Security headers configured
- [ ] Performance optimizations applied

### Post-Deployment
- [ ] Health checks passing
- [ ] SSL certificate active
- [ ] Analytics tracking
- [ ] Error monitoring setup
- [ ] Backup verification

### Production Verification
- [ ] Dashboard loads successfully
- [ ] User authentication working
- [ ] Real-time features functional
- [ ] API endpoints responding
- [ ] Database queries optimized

---

**Next Steps**: Check [Troubleshooting Guide](./troubleshooting.md) for common deployment issues or review [Environment Configuration](./environment.md) for detailed setup instructions.
