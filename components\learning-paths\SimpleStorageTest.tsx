'use client'

import React, { useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Alert,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  Chip
} from '@mui/material'
import {
  CloudUpload as UploadIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon
} from '@mui/icons-material'

export default function SimpleStorageTest() {
  const [isRunning, setIsRunning] = useState(false)
  const [results, setResults] = useState<string[]>([])
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle')

  const addResult = (message: string) => {
    setResults(prev => [...prev, message])
  }

  const createTestFile = (name: string, type: string, size: number): File => {
    const content = new Array(size).fill('a').join('')
    const blob = new Blob([content], { type })
    return new File([blob], name, { type, lastModified: Date.now() })
  }

  const runBasicTest = async () => {
    setIsRunning(true)
    setResults([])
    setStatus('idle')

    try {
      addResult('Starting basic storage test...')

      // Test 1: Check if we can create a test file
      addResult('✓ Creating test file...')
      const testFile = createTestFile('test-video.mp4', 'video/mp4', 1024) // 1KB
      addResult(`✓ Test file created: ${testFile.name} (${testFile.size} bytes)`)

      // Test 2: Check Supabase connection
      addResult('✓ Testing Supabase connection...')
      
      // Import StorageService dynamically to avoid SSR issues
      const { StorageService } = await import('@/lib/services/storageService')
      addResult('✓ StorageService imported successfully')

      // Test 3: Test file validation
      addResult('✓ Testing file validation...')
      try {
        const oversizedFile = createTestFile('large-file.mp4', 'video/mp4', 200 * 1024 * 1024) // 200MB
        await StorageService.uploadFile(oversizedFile)
        addResult('✗ File validation failed - oversized file was accepted')
        setStatus('error')
      } catch (error) {
        addResult('✓ File validation working - oversized file rejected')
      }

      // Test 4: Test valid file upload
      addResult('✓ Testing file upload...')
      try {
        const uploadResult = await StorageService.uploadFile(testFile, (progress) => {
          addResult(`Upload progress: ${progress.percentage}%`)
        })
        
        addResult(`✓ File uploaded successfully to: ${uploadResult.path}`)
        addResult(`✓ File URL: ${uploadResult.url}`)
        
        // Test 5: Test file access
        addResult('✓ Testing file access...')
        const publicUrl = StorageService.getPublicUrl(uploadResult.path)
        addResult(`✓ Public URL generated: ${publicUrl.length > 0 ? 'Yes' : 'No'}`)
        
        const signedUrl = await StorageService.getSignedUrl(uploadResult.path, 300)
        addResult(`✓ Signed URL generated: ${signedUrl.length > 0 ? 'Yes' : 'No'}`)
        
        // Test 6: Test file deletion
        addResult('✓ Testing file deletion...')
        await StorageService.deleteFile(uploadResult.path)
        addResult('✓ File deleted successfully')
        
        setStatus('success')
        addResult('🎉 All tests passed successfully!')
        
      } catch (error) {
        addResult(`✗ Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
        setStatus('error')
      }

    } catch (error) {
      addResult(`✗ Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
      setStatus('error')
    } finally {
      setIsRunning(false)
    }
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Simple Storage Test
        </Typography>
        
        <Typography variant="body2" color="text.secondary" mb={3}>
          This test validates basic storage functionality including file upload, validation, and deletion.
        </Typography>

        <Button
          variant="contained"
          startIcon={<UploadIcon />}
          onClick={runBasicTest}
          disabled={isRunning}
          size="large"
          sx={{ mb: 3 }}
        >
          {isRunning ? 'Running Test...' : 'Run Storage Test'}
        </Button>

        {isRunning && (
          <Box sx={{ mb: 2 }}>
            <LinearProgress />
          </Box>
        )}

        {status === 'success' && (
          <Alert severity="success" sx={{ mb: 2 }}>
            <Typography variant="subtitle2">
              Storage Test Completed Successfully!
            </Typography>
            <Typography variant="body2">
              All storage functionality is working correctly.
            </Typography>
          </Alert>
        )}

        {status === 'error' && (
          <Alert severity="error" sx={{ mb: 2 }}>
            <Typography variant="subtitle2">
              Storage Test Failed
            </Typography>
            <Typography variant="body2">
              Some storage functionality is not working correctly. Check the results below.
            </Typography>
          </Alert>
        )}

        {results.length > 0 && (
          <Card variant="outlined">
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                Test Results
              </Typography>
              
              <List dense>
                {results.map((result, index) => (
                  <ListItem key={index} sx={{ py: 0.5 }}>
                    <ListItemText
                      primary={
                        <Box display="flex" alignItems="center" gap={1}>
                          {result.startsWith('✓') && <SuccessIcon color="success" fontSize="small" />}
                          {result.startsWith('✗') && <ErrorIcon color="error" fontSize="small" />}
                          <Typography variant="body2" component="span">
                            {result}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        )}

        <Box mt={3}>
          <Typography variant="subtitle2" gutterBottom>
            Test Coverage
          </Typography>
          <Box display="flex" flexWrap="wrap" gap={1}>
            <Chip label="File Validation" size="small" color="primary" variant="outlined" />
            <Chip label="File Upload" size="small" color="primary" variant="outlined" />
            <Chip label="Progress Tracking" size="small" color="primary" variant="outlined" />
            <Chip label="URL Generation" size="small" color="primary" variant="outlined" />
            <Chip label="File Deletion" size="small" color="primary" variant="outlined" />
            <Chip label="Error Handling" size="small" color="primary" variant="outlined" />
          </Box>
        </Box>
      </CardContent>
    </Card>
  )
}
