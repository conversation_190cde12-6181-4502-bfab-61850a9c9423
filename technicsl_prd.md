ZenithLearn AI: Admin App Product Requirements Document (PRD)
1. Executive Summary
ZenithLearn AI Admin App is a modern, web-based interface designed to empower administrators to manage training programs within a multi-tenant, AI-powered Learning Management System (LMS). Targeting enterprises, educational institutions, and banks in the $372 billion e-learning market (CAGR 10.3% through 2028), the app leverages Next.js for a performant, SEO-friendly frontend and Supabase for a secure, scalable backend. It addresses pain points such as outdated content, low learner engagement (costing $7.8 trillion globally), and administrative complexity through AI-driven personalization, intuitive UX, and robust analytics. The development follows a 3-month MVP for core functionality and a 6-month full version for advanced features, ensuring rapid market validation and enterprise-grade capabilities.
2. Market Analysis
Market Trends and Competitor Landscape
The LMS market demands scalable, mobile-friendly, and AI-enhanced solutions (Research.com). Competitors like Cornerstone OnDemand, Docebo, and TalentLMS offer course management but lack advanced AI integration and intuitive admin interfaces. ZenithLearn AI differentiates with AI-generated learning paths, real-time collaboration, and optimized performance.
Pain Points and Unmet Needs

Content Relevance: 35% of organizations report irrelevant or outdated content (MapleLMS).
Engagement: Only 21% of employees are engaged, impacting productivity.
Administrative Complexity: Role delegation and user management are cumbersome.
Analytics: Limited insights hinder ROI measurement.
Accessibility: Inconsistent mobile and offline support.

Compliance Requirements

SCORM: Content interoperability.
xAPI: Tracking diverse learning experiences.
GDPR: Data protection and user rights.
SOC2: Security and confidentiality.
WCAG 2.1 AA: Accessibility standards.

Pricing Models
Tiered subscriptions ($89-$179/month for 100 users) or custom enterprise plans (Research.com).
3. Feature Specifications
The admin app features a sidebar with Dashboard, Learning Paths, Learners, Content Library, Reports, Competencies, and Settings, plus a top navbar with tenant logo, profile, notifications, and calendar.
MVP Features (3 Months)



Feature
Description
Acceptance Criteria
Justification



Dashboard: Overview
Modular widgets for KPIs (active learners, completion rates, time spent).
Loads <500ms; draggable widgets saved in user_preferences; real-time updates via Supabase Realtime.
Instant insights for analytics gaps.


Dashboard: Schedule Snapshot
Calendar view of upcoming sessions/reminders.
Displays monthly events; push notifications via Firebase Cloud Messaging (FCM); offline caching.
Simplifies schedule management.


Learning Paths: AI Creation
Wizard for manual/AI-generated paths (OpenAI API).
Generates 3-5 modules in <3s; drag-and-drop editing; supports PDF, video, quiz.
Speeds content creation.


Learning Paths: Assignment
Assign paths to learners/groups via dropdown/CSV.
Supports 1,000 assignments; confirmation modal; stored in learner_assignments.
Streamlines user management.


Learners: List View
Searchable, sortable table (name, email, progress).
Filters persist in localStorage; loads <500ms for 1,000 learners; profile drill-down.
Simplifies cohort monitoring.


Content Library: Upload
Upload PDF, video, PPT, quiz; tenant-organized.
Uploads <100MB in <5s; stored in Supabase Storage; RLS enforced.
Addresses outdated content.


Reports: Completion
Bar chart for completion rates.
Exportable as CSV/PDF; loads <500ms; WCAG-compliant.
Provides ROI insights.


Settings: Profile
Edit admin profile (name, avatar, notifications).
Updates users table; avatar upload; input validation.
Enhances personalization.


Security: Auth & RBAC
Supabase Auth with RLS.
JWT-based login; role-based access (authorize function); MFA.
Ensures compliance.


Full Version Features (6 Months)



Feature
Description
Acceptance Criteria
Justification



Dashboard: Predictive Analytics
AI-driven engagement risk forecasts.
>80% accuracy; drill-down to profiles; weekly updates.
Proactively boosts engagement.


Dashboard: Custom Layout
Add/remove/resize widgets; role-based defaults.
Syncs across sessions; supports 15 widgets; undo option.
Enhances usability.


Learning Paths: Adaptive Editor
Adjusts paths based on performance (e.g., skip if quiz >90%).
Real-time adaptation; admin override; audit log in path_changes.
Increases personalization.


Learning Paths: Q&A
Moderated Q&A and ratings.
Comments moderated <24h; ratings on path cards; notifications.
Improves content quality.


Learning Paths: Templates
Save/reuse path templates.
Supports 50 templates; shareable within tenant.
Speeds recurring training.


Learners: Bulk Actions
Suspend, reassign, remind in bulk.
Processes 1,000 learners <3s; audit log.
Reduces admin time.


Learners: Journey Timeline
Visualize progress, interactions.
Displays milestones, scores; zoomable; offline support.
Deepens engagement insights.


Content Library: Smart Tagging
AI tags content for search.
Tags in <2s; editable; autocomplete search.
Simplifies organization.


Content Library: Version Control
Track/revert content changes.
Supports 10 versions; diff view; RLS-secured.
Ensures integrity.


Reports: Custom Builder
Drag-and-drop report creation.
Supports 15 metrics; PDF/CSV export; shareable templates.
Flexible analytics.


Reports: Engagement Heatmap
Visualize activity by time/day.
Weekly/monthly views; tap-to-filter; offline caching.
Identifies patterns.


Competencies: Mapping Wizard
Map competencies to roles/departments.
AI suggestions; tree view; exportable report.
Aligns training with goals.


Competencies: Gap Analysis
Identify skill gaps via radar charts.
Highlights gaps; recommends paths; real-time.
Drives targeted training.


Settings: Custom Roles
Define granular permissions.
Supports 100 permissions; preview; audit log.
Flexible access control.


Settings: Branding
Customize logo, colors, fonts.
Applies <500ms; custom fonts; preview mode.
Enhances tenant identity.


Collaboration: Admin Chat
Real-time admin collaboration.
Messages <500ms; threaded replies; RLS-secured.
Improves coordination.


Collaboration: Ticket System
Track support tickets with Kanban board.
Assignable tickets; SLA tracking (<24h); notifications.
Streamlines support.


Integrations
Sync with HR systems, calendars.
Workday, Google Calendar sync; secure APIs.
Enhances compatibility.


Gamification
Badges, points, leaderboards.
Awards on completion; displays rankings; updates profiles.
Boosts motivation.


Multilingual Support
Multi-language interface.
5 languages; auto-detects locale; translatable UI.
Global accessibility.


4. Technical Architecture
Frontend

Framework: Next.js 14 with App Router for server-side rendering (SSR), static site generation (SSG), and client-side rendering (CSR).
State Management: Zustand for lightweight, scalable state management; React Query for data fetching and caching.
UI Library: Tailwind CSS for responsive, utility-first styling; Shadcn/UI for accessible components (buttons, modals, tables).
Navigation: Next.js App Router with dynamic routes (e.g., /dashboard, /learning-paths/[id]).
Components:
react-chartjs-2 for charts (bar, pie, radar).
@dnd-kit/core for drag-and-drop (path editor, dashboard widgets).
@tanstack/react-table for sortable, filterable learner tables.
react-markdown for rich-text descriptions.
@headlessui/react for accessible modals, dropdowns.
react-big-calendar for schedules.
tiptap for rich-text editing in Q&A.


SEO: Next.js metadata API for optimized page titles, descriptions.
Performance: Image optimization with Next.js Image; lazy loading; code splitting.
Accessibility: ARIA attributes; axe-core for automated WCAG checks.

Backend

Platform: Supabase with PostgreSQL, Auth, Realtime, and Storage.
Multi-Tenancy: RLS with tenant_id column (tenant_id = auth.jwt()->>'tenant_id').
RBAC: roles and role_permissions tables; authorize function (Supabase Docs).
Storage: Supabase Storage for content (tenant_id/content/{item_id}).
Edge Functions:
generate_learning_path: OpenAI API for path creation.
predict_engagement: TensorFlow.js for risk forecasting.
tag_content: AI-driven content tagging.
process_csv: Bulk user/path assignments.


Caching: Redis for AI responses, frequent queries.

AI Integration

Service: OpenAI API for path generation, content tagging, chat.
Implementation: Edge Functions handle API calls; cache responses in Redis.
Data Privacy: Anonymize inputs; store minimal PII.

5. Database Schema



Table
Columns
Description



tenants
id (int, PK), name (varchar), created_at (timestamptz)
Tenant metadata.


users
id (uuid, PK), tenant_id (int, FK), email, role_id (bigint, FK)
Admin details.


roles
id (bigint, PK), tenant_id, name, permissions (text[])
Role definitions.


user_roles
id (bigint, PK), user_id, role_id
Maps users to roles.


learning_paths
id (bigint, PK), tenant_id, title, description, is_live (boolean)
Learning paths.


modules
id (bigint, PK), path_id, title, order (int)
Path modules.


lessons
id (bigint, PK), module_id, type, content_url, order
Lesson content.


learner_assignments
learner_id (uuid, FK), path_id, assigned_at
Path assignments.


progress
id (bigint, PK), learner_id, lesson_id, status, score
Learner progress.


competencies
id (bigint, PK), tenant_id, name, description
Skill definitions.


competency_mappings
id (bigint, PK), competency_id, path_id
Competency mappings.


messages
id (bigint, PK), tenant_id, sender_id, content, timestamp
Admin chat.


tickets
id (bigint, PK), tenant_id, title, status, priority
Support tickets.


user_preferences
id (bigint, PK), user_id, dashboard_layout (jsonb)
Custom layouts.


path_reviews
id (bigint, PK), path_id, rating, comment
Q&A and ratings.


path_templates
id (bigint, PK), tenant_id, title, structure_json
Reusable templates.


RLS Policies:

Example: CREATE POLICY "Admin access" ON learning_paths FOR ALL TO authenticated USING (auth.tenant_id() = tenant_id AND authorize('manage_learning_path'));

6. API Specification

Supabase REST: CRUD endpoints (/rest/v1/{table}).
Supabase Realtime: Subscriptions for chat, notifications (channels.{table}).
Edge Functions:
generate_learning_path: Inputs (topic, level); returns JSON.
predict_engagement: Inputs (user_id, path_id); returns risk score.
tag_content: Inputs (content_id); returns tags.
process_csv: Inputs (csv_file); inserts users/assignments.



7. Security and Compliance

Authentication: Supabase Auth with JWT, SSO (OAuth), MFA.
Authorization: RLS with authorize function.
Data Protection: AES-256 encryption, TLS (Supabase Security).
Compliance:
GDPR: Consent, data export/deletion.
SOC2: Supabase SOC2 Type 2; secure practices.
SCORM: WebView with SCORM API bridge.
WCAG 2.1 AA: ARIA, high-contrast UI.



8. Performance and Scalability

Frontend:
SSR/SSG for fast loads; React Query caching.
Lazy loading with next/dynamic.
Target <500ms page loads for 1,000 items.


Backend:
Supabase scales PostgreSQL; indexes on tenant_id, user_id.
Redis for caching AI responses.


Targets: 10,000 concurrent users; sub-500ms API responses.

9. AI Implementation Strategy

Path Generation: Edge Function calls OpenAI; stores in learning_paths.
Predictive Analytics: TensorFlow.js in Edge Function; uses progress data.
Content Tagging: OpenAI for tags; stored in content_tags.

10. UX/UI Design

Navigation: Sidebar with collapsible sections; top navbar with logo, profile, notifications.
Visual Design: Tailwind CSS; tenant-customizable themes; animations via framer-motion.
Components: Reusable Shadcn/UI components; charts; drag-and-drop.
Responsive Design: Mobile-first; supports desktop, tablet.
Accessibility: ARIA labels; keyboard navigation; axe-core checks.

11. Testing Strategy

Unit Testing: Jest for components, hooks, Edge Functions.
Integration Testing: Testing Library for API flows.
E2E Testing: Playwright for user journeys.
Security Testing: Penetration tests; GDPR/SOC2 audits.
Performance Testing: Simulate 1,000 users; ensure <500ms responses.

12. Deployment and DevOps

CI/CD: Vercel for Next.js; GitHub Actions for Supabase.
Hosting: Vercel for frontend; Supabase cloud for backend (EU region for GDPR).
Monitoring: Vercel Analytics; Supabase dashboard; Sentry for errors.

13. Risk Assessment



Risk
Mitigation



AI Costs
Rate limiting, Redis caching.


Data Breaches
RLS, encryption, audits.


UI Complexity
Usability testing with 20 admins.


Scalability
Query optimization, load testing.


14. Implementation Timelines
MVP (3 Months)

Month 1: Setup Next.js, Supabase, auth, sidebar, dashboard.
Month 2: Path creation, learner management, reports, AI integration.
Month 3: Testing, accessibility, deployment.

Full Version (6 Months)

Months 4-5: Adaptive paths, custom roles, collaboration, integrations.
Month 6: Gamification, multilingual, analytics; launch.

15. Success Metrics

Usability: 90% task completion <5 clicks.
Performance: <500ms loads; 10,000 users.
Engagement: 80% admin retention.
Compliance: 100% GDPR, SOC2, WCAG adherence.

This PRD delivers a detailed, optimized ZenithLearn AI Admin App for Next.js and Supabase, ready for development with a sleek, scalable design.
