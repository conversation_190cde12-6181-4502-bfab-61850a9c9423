# ZenithLearn AI - Courses API Documentation

## 📋 Table of Contents
1. [API Overview](#api-overview)
2. [Authentication](#authentication)
3. [Course Endpoints](#course-endpoints)
4. [Enrollment Endpoints](#enrollment-endpoints)
5. [Progress Endpoints](#progress-endpoints)
6. [Search & Filtering](#search--filtering)
7. [Real-time Features](#real-time-features)
8. [Error Handling](#error-handling)
9. [Rate Limiting](#rate-limiting)
10. [Examples](#examples)

## 🌐 API Overview

The ZenithLearn AI Courses API is built on Supabase and provides RESTful endpoints for course management, enrollment, and progress tracking. All endpoints support multi-tenant architecture with Row-Level Security (RLS).

### Base URL
```
Production: https://your-project.supabase.co/rest/v1
Development: http://localhost:54321/rest/v1
```

### Content Type
```
Content-Type: application/json
Accept: application/json
```

## 🔐 Authentication

### Headers Required
```http
Authorization: Bearer <jwt_token>
apikey: <supabase_anon_key>
```

### User Context
All API calls automatically include user context through JWT token:
- `user_id`: Authenticated user ID
- `tenant_id`: Organization/tenant ID
- `role`: User role (learner, instructor, admin)

## 📚 Course Endpoints

### Get All Courses
```http
GET /courses
```

**Query Parameters:**
- `select`: Fields to return (default: `*`)
- `limit`: Number of records (default: 20, max: 100)
- `offset`: Pagination offset
- `order`: Sort order (e.g., `created_at.desc`)

**Filters:**
- `category.eq.Programming`: Filter by category
- `level.eq.beginner`: Filter by difficulty level
- `is_published.eq.true`: Only published courses
- `tags.cs.{JavaScript,React}`: Contains tags

**Example Request:**
```http
GET /courses?select=*&is_published=eq.true&category=eq.Programming&limit=12&order=created_at.desc
```

**Response:**
```json
[
  {
    "id": "uuid",
    "title": "React Fundamentals",
    "description": "Learn React from scratch",
    "thumbnail_url": "https://...",
    "instructor_id": "uuid",
    "tenant_id": "uuid",
    "category": "Programming",
    "level": "beginner",
    "duration_hours": 20,
    "price": 99.99,
    "is_published": true,
    "tags": ["React", "JavaScript", "Frontend"],
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
]
```

### Get Course by ID
```http
GET /courses?id=eq.{course_id}&select=*,modules(*,lessons(*))
```

**Response:**
```json
{
  "id": "uuid",
  "title": "React Fundamentals",
  "description": "Complete React course",
  "modules": [
    {
      "id": "uuid",
      "title": "Introduction to React",
      "order_index": 1,
      "lessons": [
        {
          "id": "uuid",
          "title": "What is React?",
          "content_type": "video",
          "duration_minutes": 15
        }
      ]
    }
  ]
}
```

### Search Courses
```http
GET /courses?title=ilike.*{search_term}*&is_published=eq.true
```

**Advanced Search with Full-Text:**
```http
POST /rpc/search_courses
Content-Type: application/json

{
  "search_term": "javascript react",
  "filters": {
    "category": "Programming",
    "level": "beginner",
    "tags": ["React"]
  },
  "limit": 20,
  "offset": 0
}
```

## 🎓 Enrollment Endpoints

### Get User Enrollments
```http
GET /enrollments?learner_id=eq.{user_id}&select=*,course:courses(*)
```

**Response:**
```json
[
  {
    "id": "uuid",
    "learner_id": "uuid",
    "course_id": "uuid",
    "enrolled_at": "2024-01-01T00:00:00Z",
    "completed_at": null,
    "progress_percentage": 45,
    "last_accessed_at": "2024-01-15T10:30:00Z",
    "status": "active",
    "course": {
      "id": "uuid",
      "title": "React Fundamentals",
      "thumbnail_url": "https://..."
    }
  }
]
```

### Enroll in Course
```http
POST /enrollments
Content-Type: application/json

{
  "course_id": "uuid",
  "status": "active"
}
```

**Response:**
```json
{
  "id": "uuid",
  "learner_id": "uuid",
  "course_id": "uuid",
  "enrolled_at": "2024-01-01T00:00:00Z",
  "progress_percentage": 0,
  "status": "active"
}
```

### Update Enrollment Status
```http
PATCH /enrollments?id=eq.{enrollment_id}
Content-Type: application/json

{
  "status": "paused",
  "last_accessed_at": "2024-01-15T10:30:00Z"
}
```

### Unenroll from Course
```http
PATCH /enrollments?id=eq.{enrollment_id}
Content-Type: application/json

{
  "status": "dropped"
}
```

## 📊 Progress Endpoints

### Get Learning Progress
```http
GET /progress?enrollment_id=eq.{enrollment_id}&select=*,lesson:lessons(*)
```

**Response:**
```json
[
  {
    "id": "uuid",
    "enrollment_id": "uuid",
    "lesson_id": "uuid",
    "status": "completed",
    "completion_percentage": 100,
    "time_spent_minutes": 25,
    "last_accessed_at": "2024-01-15T10:30:00Z",
    "score": 95,
    "attempts": 1,
    "lesson": {
      "id": "uuid",
      "title": "Introduction to Components",
      "content_type": "video"
    }
  }
]
```

### Update Lesson Progress
```http
POST /progress
Content-Type: application/json

{
  "enrollment_id": "uuid",
  "lesson_id": "uuid",
  "status": "completed",
  "completion_percentage": 100,
  "time_spent_minutes": 25,
  "score": 95
}
```

**Upsert Progress (Update or Insert):**
```http
POST /progress
Content-Type: application/json
Prefer: resolution=merge-duplicates

{
  "enrollment_id": "uuid",
  "lesson_id": "uuid",
  "completion_percentage": 75,
  "time_spent_minutes": 15
}
```

### Get Course Progress Summary
```http
POST /rpc/get_course_progress
Content-Type: application/json

{
  "p_enrollment_id": "uuid"
}
```

**Response:**
```json
{
  "total_lessons": 25,
  "completed_lessons": 12,
  "progress_percentage": 48,
  "total_time_minutes": 180,
  "average_score": 87.5,
  "next_lesson": {
    "id": "uuid",
    "title": "State Management",
    "module_title": "React Hooks"
  }
}
```

## 🔍 Search & Filtering

### Advanced Course Search
```http
POST /rpc/search_courses_advanced
Content-Type: application/json

{
  "search_query": "react hooks useState",
  "filters": {
    "categories": ["Programming", "Web Development"],
    "levels": ["intermediate", "advanced"],
    "min_rating": 4.0,
    "max_duration": 30,
    "tags": ["React", "Hooks"],
    "instructor_ids": ["uuid1", "uuid2"]
  },
  "sort_by": "rating",
  "sort_order": "desc",
  "limit": 20,
  "offset": 0
}
```

### Get Popular Courses
```http
POST /rpc/get_popular_courses
Content-Type: application/json

{
  "time_period": "month",
  "category": "Programming",
  "limit": 10
}
```

### Get Recommended Courses
```http
POST /rpc/get_course_recommendations
Content-Type: application/json

{
  "learner_id": "uuid",
  "limit": 6,
  "algorithm": "collaborative_filtering"
}
```

## ⚡ Real-time Features

### Subscribe to Course Updates
```javascript
const channel = supabase
  .channel('course-updates')
  .on(
    'postgres_changes',
    {
      event: '*',
      schema: 'public',
      table: 'courses',
      filter: `tenant_id=eq.${tenantId}`,
    },
    (payload) => {
      console.log('Course updated:', payload);
    }
  )
  .subscribe();
```

### Subscribe to Enrollment Changes
```javascript
const channel = supabase
  .channel('enrollment-updates')
  .on(
    'postgres_changes',
    {
      event: '*',
      schema: 'public',
      table: 'enrollments',
      filter: `learner_id=eq.${userId}`,
    },
    (payload) => {
      console.log('Enrollment updated:', payload);
    }
  )
  .subscribe();
```

### Subscribe to Progress Updates
```javascript
const channel = supabase
  .channel('progress-updates')
  .on(
    'postgres_changes',
    {
      event: 'UPDATE',
      schema: 'public',
      table: 'progress',
    },
    (payload) => {
      // Update UI with new progress
      updateProgressUI(payload.new);
    }
  )
  .subscribe();
```

## ❌ Error Handling

### Standard Error Response
```json
{
  "error": {
    "code": "PGRST116",
    "message": "The result contains 0 rows",
    "details": "Results contain 0 rows, application/vnd.pgrst.object+json requires 1 row",
    "hint": null
  }
}
```

### Common Error Codes
- `PGRST116`: No rows returned when expecting one
- `PGRST301`: Row-level security violation
- `23505`: Unique constraint violation
- `23503`: Foreign key constraint violation
- `42501`: Insufficient privileges

### Error Handling Best Practices
```typescript
try {
  const { data, error } = await supabase
    .from('courses')
    .select('*')
    .eq('id', courseId)
    .single();

  if (error) {
    switch (error.code) {
      case 'PGRST116':
        throw new Error('Course not found');
      case 'PGRST301':
        throw new Error('Access denied');
      default:
        throw new Error(`Database error: ${error.message}`);
    }
  }

  return data;
} catch (error) {
  console.error('Course fetch error:', error);
  throw error;
}
```

## 🚦 Rate Limiting

### Default Limits
- **Anonymous requests**: 100 requests per hour
- **Authenticated requests**: 1000 requests per hour
- **Bulk operations**: 10 requests per minute

### Rate Limit Headers
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

### Handling Rate Limits
```typescript
const makeAPICall = async (url: string, options: RequestInit) => {
  const response = await fetch(url, options);
  
  if (response.status === 429) {
    const resetTime = response.headers.get('X-RateLimit-Reset');
    const waitTime = parseInt(resetTime!) * 1000 - Date.now();
    
    await new Promise(resolve => setTimeout(resolve, waitTime));
    return makeAPICall(url, options); // Retry
  }
  
  return response;
};
```

This API documentation provides comprehensive coverage of all course-related endpoints and features in the ZenithLearn AI platform.
