'use client'

import { useState, useCallback } from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  IconButton,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Paper,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  useTheme,
} from '@mui/material'
import {
  DragIndicator as DragIcon,
  Add as AddIcon,
  Remove as RemoveIcon,
  Save as SaveIcon,
  Preview as PreviewIcon,
  Download as DownloadIcon,
  Close as CloseIcon,
  <PERSON><PERSON>hart as Bar<PERSON>hartIcon,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Show<PERSON>hart as LineChartIcon,
  Table<PERSON>hart as TableIcon,
} from '@mui/icons-material'
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core'
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import {
  useSortable,
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { motion, AnimatePresence } from 'framer-motion'

interface MetricItem {
  id: string
  name: string
  type: 'number' | 'percentage' | 'duration' | 'count'
  category: string
  description: string
}

interface FilterItem {
  id: string
  field: string
  operator: string
  value: string
  type: 'date' | 'text' | 'number' | 'select'
}

interface VisualizationItem {
  id: string
  type: 'bar' | 'line' | 'pie' | 'table'
  title: string
  metrics: string[]
  config: Record<string, any>
}

interface ReportConfig {
  name: string
  description: string
  metrics: MetricItem[]
  filters: FilterItem[]
  visualizations: VisualizationItem[]
}

// Sortable metric item component
function SortableMetricItem({ metric, onRemove }: { metric: MetricItem; onRemove: (id: string) => void }) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: metric.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  }

  return (
    <Card
      ref={setNodeRef}
      style={style}
      sx={{
        mb: 1,
        cursor: isDragging ? 'grabbing' : 'grab',
        '&:hover': {
          boxShadow: 2,
        },
      }}
    >
      <CardContent sx={{ py: 1.5, '&:last-child': { pb: 1.5 } }}>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box display="flex" alignItems="center" flex={1}>
            <IconButton
              size="small"
              {...attributes}
              {...listeners}
              sx={{ mr: 1, cursor: 'grab' }}
            >
              <DragIcon />
            </IconButton>
            <Box>
              <Typography variant="body2" fontWeight="medium">
                {metric.name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {metric.category} • {metric.type}
              </Typography>
            </Box>
          </Box>
          <IconButton
            size="small"
            onClick={() => onRemove(metric.id)}
            color="error"
          >
            <RemoveIcon />
          </IconButton>
        </Box>
      </CardContent>
    </Card>
  )
}

export default function ReportBuilder() {
  const theme = useTheme()
  const [reportConfig, setReportConfig] = useState<ReportConfig>({
    name: '',
    description: '',
    metrics: [],
    filters: [],
    visualizations: [],
  })
  const [previewOpen, setPreviewOpen] = useState(false)
  const [saveDialogOpen, setSaveDialogOpen] = useState(false)

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  // Available metrics to choose from
  const availableMetrics: MetricItem[] = [
    {
      id: 'completion_rate',
      name: 'Completion Rate',
      type: 'percentage',
      category: 'Progress',
      description: 'Percentage of learners who completed the course',
    },
    {
      id: 'avg_score',
      name: 'Average Score',
      type: 'number',
      category: 'Performance',
      description: 'Average quiz/assessment scores',
    },
    {
      id: 'time_spent',
      name: 'Time Spent',
      type: 'duration',
      category: 'Engagement',
      description: 'Total time spent on learning activities',
    },
    {
      id: 'active_learners',
      name: 'Active Learners',
      type: 'count',
      category: 'Engagement',
      description: 'Number of active learners in the period',
    },
    {
      id: 'course_enrollments',
      name: 'Course Enrollments',
      type: 'count',
      category: 'Progress',
      description: 'Number of new course enrollments',
    },
    {
      id: 'skill_mastery',
      name: 'Skill Mastery Rate',
      type: 'percentage',
      category: 'Competency',
      description: 'Percentage of skills marked as mastered',
    },
  ]

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event

    if (over && active.id !== over.id) {
      setReportConfig(prev => ({
        ...prev,
        metrics: arrayMove(
          prev.metrics,
          prev.metrics.findIndex(item => item.id === active.id),
          prev.metrics.findIndex(item => item.id === over.id)
        ),
      }))
    }
  }, [])

  const addMetric = (metric: MetricItem) => {
    if (!reportConfig.metrics.find(m => m.id === metric.id)) {
      setReportConfig(prev => ({
        ...prev,
        metrics: [...prev.metrics, metric],
      }))
    }
  }

  const removeMetric = (metricId: string) => {
    setReportConfig(prev => ({
      ...prev,
      metrics: prev.metrics.filter(m => m.id !== metricId),
    }))
  }

  const addVisualization = (type: 'bar' | 'line' | 'pie' | 'table') => {
    const newViz: VisualizationItem = {
      id: `viz_${Date.now()}`,
      type,
      title: `${type.charAt(0).toUpperCase() + type.slice(1)} Chart`,
      metrics: [],
      config: {},
    }
    setReportConfig(prev => ({
      ...prev,
      visualizations: [...prev.visualizations, newViz],
    }))
  }

  const getVisualizationIcon = (type: string) => {
    switch (type) {
      case 'bar':
        return <BarChartIcon />
      case 'line':
        return <LineChartIcon />
      case 'pie':
        return <PieChartIcon />
      case 'table':
        return <TableIcon />
      default:
        return <BarChartIcon />
    }
  }

  return (
    <Box>
      <Grid container spacing={3}>
        {/* Report Configuration */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Report Configuration
              </Typography>
              
              <TextField
                fullWidth
                label="Report Name"
                value={reportConfig.name}
                onChange={(e) => setReportConfig(prev => ({ ...prev, name: e.target.value }))}
                margin="normal"
              />
              
              <TextField
                fullWidth
                label="Description"
                value={reportConfig.description}
                onChange={(e) => setReportConfig(prev => ({ ...prev, description: e.target.value }))}
                multiline
                rows={3}
                margin="normal"
              />

              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                Available Metrics
              </Typography>
              
              <Box sx={{ maxHeight: 300, overflowY: 'auto' }}>
                {availableMetrics.map((metric) => (
                  <Card
                    key={metric.id}
                    sx={{
                      mb: 1,
                      cursor: 'pointer',
                      '&:hover': {
                        backgroundColor: 'action.hover',
                      },
                    }}
                    onClick={() => addMetric(metric)}
                  >
                    <CardContent sx={{ py: 1, '&:last-child': { pb: 1 } }}>
                      <Box display="flex" alignItems="center" justifyContent="space-between">
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {metric.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {metric.category}
                          </Typography>
                        </Box>
                        <IconButton size="small" color="primary">
                          <AddIcon />
                        </IconButton>
                      </Box>
                    </CardContent>
                  </Card>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Report Builder */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h6" fontWeight="bold">
                  Report Builder
                </Typography>
                <Box display="flex" gap={1}>
                  <Button
                    variant="outlined"
                    startIcon={<PreviewIcon />}
                    onClick={() => setPreviewOpen(true)}
                    disabled={reportConfig.metrics.length === 0}
                  >
                    Preview
                  </Button>
                  <Button
                    variant="contained"
                    startIcon={<SaveIcon />}
                    onClick={() => setSaveDialogOpen(true)}
                    disabled={!reportConfig.name || reportConfig.metrics.length === 0}
                  >
                    Save Report
                  </Button>
                </Box>
              </Box>

              {/* Selected Metrics */}
              <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                Selected Metrics ({reportConfig.metrics.length})
              </Typography>
              
              <Paper sx={{ p: 2, mb: 3, minHeight: 200, backgroundColor: 'grey.50' }}>
                {reportConfig.metrics.length === 0 ? (
                  <Box
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                    height={150}
                    color="text.secondary"
                  >
                    <Typography>
                      Drag metrics from the left panel to build your report
                    </Typography>
                  </Box>
                ) : (
                  <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEnd}
                  >
                    <SortableContext
                      items={reportConfig.metrics.map(m => m.id)}
                      strategy={verticalListSortingStrategy}
                    >
                      {reportConfig.metrics.map((metric) => (
                        <SortableMetricItem
                          key={metric.id}
                          metric={metric}
                          onRemove={removeMetric}
                        />
                      ))}
                    </SortableContext>
                  </DndContext>
                )}
              </Paper>

              {/* Visualizations */}
              <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                Visualizations
              </Typography>
              
              <Box display="flex" gap={1} mb={2}>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<BarChartIcon />}
                  onClick={() => addVisualization('bar')}
                >
                  Bar Chart
                </Button>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<LineChartIcon />}
                  onClick={() => addVisualization('line')}
                >
                  Line Chart
                </Button>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<PieChartIcon />}
                  onClick={() => addVisualization('pie')}
                >
                  Pie Chart
                </Button>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<TableIcon />}
                  onClick={() => addVisualization('table')}
                >
                  Table
                </Button>
              </Box>

              <Grid container spacing={2}>
                {reportConfig.visualizations.map((viz) => (
                  <Grid item xs={12} sm={6} key={viz.id}>
                    <Card variant="outlined">
                      <CardContent>
                        <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                          <Box display="flex" alignItems="center">
                            {getVisualizationIcon(viz.type)}
                            <Typography variant="body2" fontWeight="medium" ml={1}>
                              {viz.title}
                            </Typography>
                          </Box>
                          <IconButton
                            size="small"
                            onClick={() => setReportConfig(prev => ({
                              ...prev,
                              visualizations: prev.visualizations.filter(v => v.id !== viz.id),
                            }))}
                          >
                            <CloseIcon />
                          </IconButton>
                        </Box>
                        <Typography variant="caption" color="text.secondary">
                          {viz.type.charAt(0).toUpperCase() + viz.type.slice(1)} visualization
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Save Dialog */}
      <Dialog open={saveDialogOpen} onClose={() => setSaveDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Save Report Template</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Save this report configuration as a template for future use.
          </Typography>
          <TextField
            fullWidth
            label="Template Name"
            value={reportConfig.name}
            onChange={(e) => setReportConfig(prev => ({ ...prev, name: e.target.value }))}
            margin="normal"
          />
          <TextField
            fullWidth
            label="Description"
            value={reportConfig.description}
            onChange={(e) => setReportConfig(prev => ({ ...prev, description: e.target.value }))}
            multiline
            rows={3}
            margin="normal"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSaveDialogOpen(false)}>Cancel</Button>
          <Button variant="contained" onClick={() => setSaveDialogOpen(false)}>
            Save Template
          </Button>
        </DialogActions>
      </Dialog>

      {/* Preview Dialog */}
      <Dialog open={previewOpen} onClose={() => setPreviewOpen(false)} maxWidth="lg" fullWidth>
        <DialogTitle>Report Preview</DialogTitle>
        <DialogContent>
          <Typography variant="h6" gutterBottom>
            {reportConfig.name || 'Untitled Report'}
          </Typography>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            {reportConfig.description || 'No description provided'}
          </Typography>
          <Divider sx={{ my: 2 }} />
          <Typography variant="subtitle1" gutterBottom>
            Metrics ({reportConfig.metrics.length})
          </Typography>
          <Box display="flex" flexWrap="wrap" gap={1} mb={2}>
            {reportConfig.metrics.map((metric) => (
              <Chip key={metric.id} label={metric.name} variant="outlined" />
            ))}
          </Box>
          <Typography variant="subtitle1" gutterBottom>
            Visualizations ({reportConfig.visualizations.length})
          </Typography>
          <Box display="flex" flexWrap="wrap" gap={1}>
            {reportConfig.visualizations.map((viz) => (
              <Chip
                key={viz.id}
                icon={getVisualizationIcon(viz.type)}
                label={viz.title}
                variant="outlined"
              />
            ))}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewOpen(false)}>Close</Button>
          <Button variant="contained" startIcon={<DownloadIcon />}>
            Generate Report
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}
