'use client'

import { useState, useEffect } from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  IconButton,
  Tabs,
  Tab,
  Chip,
  useTheme,
  Skeleton,
  Alert,
  Snackbar,
} from '@mui/material'
import {
  Assessment as ReportsIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
  Dashboard as DashboardIcon,
  Build as BuildIcon,
  Schedule as ScheduleIcon,
  Share as ShareIcon,
  Download as DownloadIcon,
  TrendingUp as TrendingUpIcon,
  Psychology as AIIcon,
} from '@mui/icons-material'
import { motion, AnimatePresence } from 'framer-motion'
import { useQuery } from '@tanstack/react-query'

import { supabase } from '@/lib/supabase'
import { useAuthStore } from '@/lib/store'
import ReportsDashboard from '@/components/reports/ReportsDashboard'
import ReportBuilder from '@/components/reports/ReportBuilder'
import ReportTemplates from '@/components/reports/ReportTemplates'
import AIInsights from '@/components/reports/AIInsights'
import ScheduledReports from '@/components/reports/ScheduledReports'
import SharedReports from '@/components/reports/SharedReports'

interface TabPanelProps {
  children?: React.ReactNode
  index: number
  value: number
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`reports-tabpanel-${index}`}
      aria-labelledby={`reports-tab-${index}`}
      {...other}
    >
      {value === index && (
        <AnimatePresence mode="wait">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Box sx={{ py: 3 }}>
              {children}
            </Box>
          </motion.div>
        </AnimatePresence>
      )}
    </div>
  )
}

function a11yProps(index: number) {
  return {
    id: `reports-tab-${index}`,
    'aria-controls': `reports-tabpanel-${index}`,
  }
}

interface ReportsStats {
  totalReports: number
  activeReports: number
  scheduledReports: number
  sharedReports: number
  totalViews: number
  avgGenerationTime: number
  popularTemplates: Array<{
    id: number
    name: string
    usage: number
  }>
}

export default function ReportsPage() {
  const theme = useTheme()
  const { user } = useAuthStore()
  const [currentTab, setCurrentTab] = useState(0)
  const [snackbar, setSnackbar] = useState<{
    open: boolean
    message: string
    severity: 'success' | 'error' | 'warning' | 'info'
  }>({
    open: false,
    message: '',
    severity: 'info'
  })

  // Fetch reports statistics
  const { data: reportsStats, isLoading, error, refetch } = useQuery<ReportsStats>({
    queryKey: ['reports-stats', user?.tenant_id],
    queryFn: async () => {
      if (!user?.tenant_id) throw new Error('No tenant ID')

      // For MVP, return mock data - in production this would call Supabase
      // const { data, error } = await supabase.functions.invoke('get-reports-analytics', {
      //   body: { tenant_id: user.tenant_id }
      // })
      
      // Mock data for demonstration
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call
      
      return {
        totalReports: 47,
        activeReports: 23,
        scheduledReports: 8,
        sharedReports: 15,
        totalViews: 1247,
        avgGenerationTime: 2.3,
        popularTemplates: [
          { id: 1, name: 'Learner Progress Report', usage: 89 },
          { id: 2, name: 'Completion Analytics', usage: 76 },
          { id: 3, name: 'Engagement Trends', usage: 64 },
        ]
      }
    },
    enabled: !!user?.tenant_id,
    refetchInterval: 30000, // Refresh every 30 seconds
  })

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue)
  }

  const handleRefresh = () => {
    refetch()
    setSnackbar({
      open: true,
      message: 'Reports data refreshed successfully',
      severity: 'success'
    })
  }

  const handleSnackbarClose = () => {
    setSnackbar(prev => ({ ...prev, open: false }))
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          Failed to load reports data. Please try again.
        </Alert>
        <Button variant="contained" onClick={handleRefresh} startIcon={<RefreshIcon />}>
          Retry
        </Button>
      </Box>
    )
  }

  return (
    <Box sx={{ width: '100%' }}>
      {/* Header */}
      <Box mb={3}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box>
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              Reports & Analytics
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Generate insights, build custom reports, and track performance metrics
            </Typography>
          </Box>
          <Box display="flex" gap={1}>
            <IconButton
              onClick={handleRefresh}
              disabled={isLoading}
              aria-label="refresh data"
            >
              <RefreshIcon />
            </IconButton>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setCurrentTab(1)}
              sx={{ ml: 1 }}
            >
              New Report
            </Button>
          </Box>
        </Box>

        {/* Quick Stats */}
        <Grid container spacing={2} mb={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                {isLoading ? (
                  <Skeleton variant="text" height={60} />
                ) : (
                  <>
                    <Typography variant="h4" color="primary" fontWeight="bold">
                      {reportsStats?.totalReports || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Reports
                    </Typography>
                  </>
                )}
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                {isLoading ? (
                  <Skeleton variant="text" height={60} />
                ) : (
                  <>
                    <Typography variant="h4" color="success.main" fontWeight="bold">
                      {reportsStats?.activeReports || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Active Reports
                    </Typography>
                  </>
                )}
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                {isLoading ? (
                  <Skeleton variant="text" height={60} />
                ) : (
                  <>
                    <Typography variant="h4" color="warning.main" fontWeight="bold">
                      {reportsStats?.scheduledReports || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Scheduled
                    </Typography>
                  </>
                )}
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                {isLoading ? (
                  <Skeleton variant="text" height={60} />
                ) : (
                  <>
                    <Typography variant="h4" color="info.main" fontWeight="bold">
                      {reportsStats?.totalViews || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Views
                    </Typography>
                  </>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>

      {/* Navigation Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs
          value={currentTab}
          onChange={handleTabChange}
          aria-label="reports navigation tabs"
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab
            icon={<DashboardIcon />}
            label="Dashboard"
            {...a11yProps(0)}
          />
          <Tab
            icon={<BuildIcon />}
            label="Report Builder"
            {...a11yProps(1)}
          />
          <Tab
            icon={<ReportsIcon />}
            label="Templates"
            {...a11yProps(2)}
          />
          <Tab
            icon={<AIIcon />}
            label="AI Insights"
            {...a11yProps(3)}
          />
          <Tab
            icon={<ScheduleIcon />}
            label="Scheduled"
            {...a11yProps(4)}
          />
          <Tab
            icon={<ShareIcon />}
            label="Shared"
            {...a11yProps(5)}
          />
        </Tabs>
      </Box>

      {/* Tab Panels */}
      <TabPanel value={currentTab} index={0}>
        <ReportsDashboard stats={reportsStats} isLoading={isLoading} />
      </TabPanel>

      <TabPanel value={currentTab} index={1}>
        <ReportBuilder />
      </TabPanel>

      <TabPanel value={currentTab} index={2}>
        <ReportTemplates />
      </TabPanel>

      <TabPanel value={currentTab} index={3}>
        <AIInsights />
      </TabPanel>

      <TabPanel value={currentTab} index={4}>
        <ScheduledReports />
      </TabPanel>

      <TabPanel value={currentTab} index={5}>
        <SharedReports />
      </TabPanel>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  )
}
