import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { useAuthStore } from '@/lib/store'
import { FeatureFlag } from '@/lib/types/settings'

// Helper function to get current user context
const getCurrentUserContext = () => {
  const { user, tenant } = useAuthStore.getState()

  // Development fallback - remove in production
  if (!user || !tenant) {
    console.warn('No authenticated user found, using development fallback')
    return {
      user: {
        id: '5cd09c93-eb00-470f-a605-c6d0d057bdd6',
        email: '<EMAIL>',
        tenant_id: 3,
        role_id: 1
      },
      tenant: {
        id: 3,
        name: 'Development Tenant'
      }
    }
  }

  // Ensure tenant ID is a number
  const tenantId = typeof tenant.id === 'string' ? parseInt(tenant.id, 10) : tenant.id

  if (isNaN(tenantId)) {
    throw new Error('Invalid tenant ID: must be a valid integer')
  }

  return {
    user: {
      ...user,
      tenant_id: tenantId
    },
    tenant: {
      ...tenant,
      id: tenantId
    }
  }
}

// GET /api/settings/feature-flags
export async function GET(request: NextRequest) {
  try {
    const { tenant } = getCurrentUserContext()

    const { data, error } = await supabaseAdmin
      .from('tenant_features')
      .select('*')
      .eq('tenant_id', tenant.id)

    if (error) {
      console.error('Error fetching feature flags:', error)
      throw error
    }

    return NextResponse.json({
      success: true,
      data: data || []
    })

  } catch (error) {
    console.error('Failed to get feature flags:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 })
  }
}
