'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON>,
  CardContent,
  Typography,
  Box,
  IconButton,
  Slider,
  Chip,
  Avatar,
  useTheme,
  LinearProgress,
  Button,
} from '@mui/material'
import {
  Mood as MoodIcon,
  Psychology as BrainIcon,
  Favorite as HeartIcon,
  Bolt as EnergyIcon,
  Visibility as FocusIcon,
} from '@mui/icons-material'
import { motion, AnimatePresence } from 'framer-motion'

interface MoodTrackerWidgetProps {
  currentMood: string | null
  onMoodChange: (mood: string) => void
}

const moodOptions = [
  { emoji: '😊', label: 'Happy', color: '#4CAF50', theme: 'light' },
  { emoji: '😴', label: 'Tired', color: '#9C27B0', theme: 'dark' },
  { emoji: '🔥', label: 'Energetic', color: '#FF5722', theme: 'energetic' },
  { emoji: '🧘', label: 'Calm', color: '#2196F3', theme: 'calm' },
  { emoji: '🤔', label: 'Focused', color: '#FF9800', theme: 'focused' },
  { emoji: '💪', label: 'Motivated', color: '#E91E63', theme: 'motivated' },
  { emoji: '🎯', label: 'Determined', color: '#795548', theme: 'determined' },
  { emoji: '✨', label: 'Inspired', color: '#9C27B0', theme: 'inspired' },
]

const wellnessTips = {
  '😊': ['Keep up the positive energy!', 'Share your happiness with your team'],
  '😴': ['Take short breaks', 'Consider a power nap', 'Stay hydrated'],
  '🔥': ['Channel this energy into creative tasks', 'Great time for brainstorming'],
  '🧘': ['Perfect mindset for strategic planning', 'Good time for deep work'],
  '🤔': ['Ideal for analytical tasks', 'Review complex reports now'],
  '💪': ['Tackle challenging projects', 'Set ambitious goals today'],
  '🎯': ['Focus on priority tasks', 'Great for decision making'],
  '✨': ['Perfect for innovation', 'Share ideas with your team'],
}

export default function MoodTrackerWidget({ currentMood, onMoodChange }: MoodTrackerWidgetProps) {
  const theme = useTheme()
  const [energy, setEnergy] = useState(75)
  const [focus, setFocus] = useState(80)
  const [showMoodSelector, setShowMoodSelector] = useState(false)
  const [moodHistory, setMoodHistory] = useState<Array<{mood: string, timestamp: Date}>>([])

  useEffect(() => {
    // Load mood history from localStorage or API
    const savedHistory = localStorage.getItem('moodHistory')
    if (savedHistory) {
      setMoodHistory(JSON.parse(savedHistory))
    }
  }, [])

  const handleMoodSelect = (mood: string) => {
    onMoodChange(mood)
    setShowMoodSelector(false)
    
    // Add to history
    const newEntry = { mood, timestamp: new Date() }
    const updatedHistory = [...moodHistory, newEntry].slice(-7) // Keep last 7 entries
    setMoodHistory(updatedHistory)
    localStorage.setItem('moodHistory', JSON.stringify(updatedHistory))

    // Adjust energy and focus based on mood
    const moodData = moodOptions.find(m => m.emoji === mood)
    if (moodData) {
      switch (mood) {
        case '😴':
          setEnergy(30)
          setFocus(40)
          break
        case '🔥':
          setEnergy(95)
          setFocus(85)
          break
        case '🧘':
          setEnergy(60)
          setFocus(95)
          break
        case '🤔':
          setEnergy(70)
          setFocus(90)
          break
        case '💪':
          setEnergy(90)
          setFocus(80)
          break
        default:
          setEnergy(75)
          setFocus(75)
      }
    }
  }

  const getCurrentMoodData = () => {
    return moodOptions.find(m => m.emoji === currentMood)
  }

  const getWellnessTip = () => {
    if (!currentMood) return null
    const tips = wellnessTips[currentMood as keyof typeof wellnessTips]
    return tips ? tips[Math.floor(Math.random() * tips.length)] : null
  }

  const moodData = getCurrentMoodData()
  const wellnessTip = getWellnessTip()

  return (
    <Card sx={{
      height: '100%',
      position: 'relative',
      overflow: 'visible',
      zIndex: showMoodSelector ? 1000 : 1,
      '&::before': showMoodSelector ? {
        content: '""',
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0,0,0,0.1)',
        zIndex: -1,
        pointerEvents: 'none'
      } : {}
    }}>
      <CardContent sx={{ position: 'relative', height: '100%', display: 'flex', flexDirection: 'column' }}>
        <Box display="flex" alignItems="center" gap={2} mb={2}>
          <Avatar sx={{ bgcolor: 'secondary.main', width: 32, height: 32 }}>
            <MoodIcon fontSize="small" />
          </Avatar>
          <Typography variant="h6" fontWeight="bold">
            Mood & Wellness
          </Typography>
        </Box>

        {/* Current Mood Display */}
        <Box textAlign="center" mb={3}>
          <motion.div
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
          >
            <IconButton
              onClick={() => setShowMoodSelector(!showMoodSelector)}
              sx={{
                fontSize: '3rem',
                width: 80,
                height: 80,
                bgcolor: moodData?.color || theme.palette.grey[200],
                color: 'white',
                mb: 1,
                '&:hover': {
                  bgcolor: moodData?.color || theme.palette.grey[300],
                  transform: 'scale(1.05)',
                }
              }}
            >
              {currentMood || '😊'}
            </IconButton>
          </motion.div>
          
          <Typography variant="body2" color="text.secondary">
            {moodData?.label || 'Select your mood'}
          </Typography>
          
          {wellnessTip && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <Chip
                label={wellnessTip}
                size="small"
                sx={{ 
                  mt: 1, 
                  bgcolor: moodData?.color + '20',
                  color: moodData?.color,
                  maxWidth: '100%',
                  '& .MuiChip-label': {
                    whiteSpace: 'normal',
                    fontSize: '0.7rem'
                  }
                }}
              />
            </motion.div>
          )}
        </Box>

        {/* Mood Selector */}
        <AnimatePresence>
          {showMoodSelector && (
            <>
              {/* Backdrop */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                onClick={() => setShowMoodSelector(false)}
                style={{
                  position: 'fixed',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  zIndex: 999,
                  backgroundColor: 'rgba(0,0,0,0.2)',
                }}
              />

              {/* Mood Selector Modal */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8, y: -20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.8, y: -20 }}
                style={{
                  position: 'fixed',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  zIndex: 1000,
                  background: theme.palette.background.paper,
                  borderRadius: 16,
                  padding: 20,
                  boxShadow: theme.shadows[16],
                  border: `1px solid ${theme.palette.divider}`,
                  minWidth: 280,
                  maxWidth: '90vw',
                }}
                onClick={(e) => e.stopPropagation()}
              >
                <Typography variant="h6" fontWeight="bold" mb={2} textAlign="center">
                  How are you feeling today?
                </Typography>

                <Box
                  display="grid"
                  gridTemplateColumns="repeat(4, 1fr)"
                  gap={2}
                  mb={2}
                >
                  {moodOptions.map((mood) => (
                    <motion.div
                      key={mood.emoji}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Box
                        onClick={() => handleMoodSelect(mood.emoji)}
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          p: 2,
                          borderRadius: 2,
                          cursor: 'pointer',
                          bgcolor: mood.color + '10',
                          border: currentMood === mood.emoji ? `2px solid ${mood.color}` : '2px solid transparent',
                          transition: 'all 0.2s ease',
                          '&:hover': {
                            bgcolor: mood.color + '20',
                            transform: 'translateY(-2px)',
                          }
                        }}
                      >
                        <Typography sx={{ fontSize: '2rem', mb: 0.5 }}>
                          {mood.emoji}
                        </Typography>
                        <Typography variant="caption" fontWeight="medium" textAlign="center">
                          {mood.label}
                        </Typography>
                      </Box>
                    </motion.div>
                  ))}
                </Box>

                <Box textAlign="center">
                  <Button
                    variant="outlined"
                    onClick={() => setShowMoodSelector(false)}
                    size="small"
                  >
                    Close
                  </Button>
                </Box>
              </motion.div>
            </>
          )}
        </AnimatePresence>

        {/* Energy & Focus Meters */}
        <Box mb={2}>
          <Box display="flex" alignItems="center" gap={1} mb={1}>
            <EnergyIcon color="warning" fontSize="small" />
            <Typography variant="body2" fontWeight="medium">
              Energy
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {energy}%
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={energy}
            sx={{
              height: 6,
              borderRadius: 3,
              bgcolor: 'rgba(255, 152, 0, 0.2)',
              '& .MuiLinearProgress-bar': {
                borderRadius: 3,
                bgcolor: '#FF9800',
              }
            }}
          />
        </Box>

        <Box mb={2}>
          <Box display="flex" alignItems="center" gap={1} mb={1}>
            <FocusIcon color="info" fontSize="small" />
            <Typography variant="body2" fontWeight="medium">
              Focus
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {focus}%
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={focus}
            sx={{
              height: 6,
              borderRadius: 3,
              bgcolor: 'rgba(33, 150, 243, 0.2)',
              '& .MuiLinearProgress-bar': {
                borderRadius: 3,
                bgcolor: '#2196F3',
              }
            }}
          />
        </Box>

        {/* Mood History */}
        {moodHistory.length > 0 && (
          <Box>
            <Typography variant="body2" fontWeight="medium" mb={1}>
              Recent Moods
            </Typography>
            <Box display="flex" gap={0.5} flexWrap="wrap">
              {moodHistory.slice(-5).map((entry, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Chip
                    label={entry.mood}
                    size="small"
                    sx={{
                      fontSize: '0.8rem',
                      height: 24,
                      '& .MuiChip-label': { px: 1 }
                    }}
                    title={entry.timestamp.toLocaleDateString()}
                  />
                </motion.div>
              ))}
            </Box>
          </Box>
        )}
      </CardContent>
    </Card>
  )
}
