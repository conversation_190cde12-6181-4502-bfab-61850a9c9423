'use client'

import { useState } from 'react'
import {
  Card,
  CardContent,
  Typography,
  Box,
  Tabs,
  Tab,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Tooltip,
  Skeleton,
} from '@mui/material'
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  Refresh as RefreshIcon,
  Timeline as TimelineIcon,
} from '@mui/icons-material'
import { Line } from 'react-chartjs-2'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  Filler,
} from 'chart.js'
import { motion } from 'framer-motion'
import { useQuery } from '@tanstack/react-query'

import { supabase } from '@/lib/supabase'
import { useAuthStore } from '@/lib/store'

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  ChartTooltip,
  Legend,
  Filler
)

interface CompetencyTrend {
  competency_id: number
  competency_name: string
  category: string
  current_period: number
  previous_period: number
  growth_rate: number
  trend_direction: 'up' | 'down' | 'flat'
  learners_count: number
}

interface TrendData {
  labels: string[]
  datasets: Array<{
    label: string
    data: number[]
    borderColor: string
    backgroundColor: string
    fill: boolean
  }>
}

export default function CompetencyTrends() {
  const { user } = useAuthStore()
  const [activeTab, setActiveTab] = useState(0)
  const [timeRange, setTimeRange] = useState('30d')

  // Fetch competency trends data
  const { data: trends = [], isLoading, refetch } = useQuery({
    queryKey: ['competency-trends', user?.tenant_id, timeRange],
    queryFn: async (): Promise<CompetencyTrend[]> => {
      if (!user?.tenant_id) return []

      // Mock data for now - in real implementation, this would come from analytics
      const mockTrends: CompetencyTrend[] = [
        {
          competency_id: 1,
          competency_name: 'AI/Machine Learning',
          category: 'Technical Skills',
          current_period: 145,
          previous_period: 98,
          growth_rate: 47.96,
          trend_direction: 'up',
          learners_count: 145,
        },
        {
          competency_id: 2,
          competency_name: 'Data Analysis',
          category: 'Technical Skills',
          current_period: 132,
          previous_period: 108,
          growth_rate: 22.22,
          trend_direction: 'up',
          learners_count: 132,
        },
        {
          competency_id: 3,
          competency_name: 'Cloud Computing',
          category: 'Technical Skills',
          current_period: 89,
          previous_period: 95,
          growth_rate: -6.32,
          trend_direction: 'down',
          learners_count: 89,
        },
        {
          competency_id: 4,
          competency_name: 'Leadership',
          category: 'Soft Skills',
          current_period: 76,
          previous_period: 74,
          growth_rate: 2.70,
          trend_direction: 'flat',
          learners_count: 76,
        },
        {
          competency_id: 5,
          competency_name: 'Project Management',
          category: 'Management',
          current_period: 65,
          previous_period: 58,
          growth_rate: 12.07,
          trend_direction: 'up',
          learners_count: 65,
        },
      ]

      return mockTrends
    },
    enabled: !!user?.tenant_id,
    refetchInterval: 300000, // Refresh every 5 minutes
  })

  // Generate chart data
  const chartData: TrendData = {
    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
    datasets: trends.slice(0, 5).map((trend, index) => {
      const colors = [
        { border: '#1976d2', bg: 'rgba(25, 118, 210, 0.1)' },
        { border: '#2e7d32', bg: 'rgba(46, 125, 50, 0.1)' },
        { border: '#ed6c02', bg: 'rgba(237, 108, 2, 0.1)' },
        { border: '#9c27b0', bg: 'rgba(156, 39, 176, 0.1)' },
        { border: '#d32f2f', bg: 'rgba(211, 47, 47, 0.1)' },
      ]
      
      const color = colors[index % colors.length]
      
      // Generate mock weekly data
      const baseValue = trend.previous_period
      const growth = (trend.current_period - trend.previous_period) / 4
      
      return {
        label: trend.competency_name,
        data: [
          baseValue,
          baseValue + growth,
          baseValue + growth * 2,
          baseValue + growth * 3,
          trend.current_period,
        ],
        borderColor: color.border,
        backgroundColor: color.bg,
        fill: true,
      }
    }),
  }

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
        },
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: 'Time Period',
        },
      },
      y: {
        display: true,
        title: {
          display: true,
          text: 'Number of Learners',
        },
        beginAtZero: true,
      },
    },
    interaction: {
      mode: 'nearest' as const,
      axis: 'x' as const,
      intersect: false,
    },
  }

  const getTrendIcon = (direction: string, growth: number) => {
    if (direction === 'up') return <TrendingUpIcon color="success" />
    if (direction === 'down') return <TrendingDownIcon color="error" />
    return <TrendingFlatIcon color="action" />
  }

  const getTrendColor = (direction: string) => {
    if (direction === 'up') return 'success'
    if (direction === 'down') return 'error'
    return 'default'
  }

  const formatGrowthRate = (rate: number) => {
    const sign = rate > 0 ? '+' : ''
    return `${sign}${rate.toFixed(1)}%`
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Competency Trends
          </Typography>
          <Skeleton variant="rectangular" height={300} />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6">
            Competency Trends
          </Typography>
          <Box display="flex" alignItems="center" gap={1}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Time Range</InputLabel>
              <Select
                value={timeRange}
                label="Time Range"
                onChange={(e) => setTimeRange(e.target.value)}
              >
                <MenuItem value="7d">Last 7 days</MenuItem>
                <MenuItem value="30d">Last 30 days</MenuItem>
                <MenuItem value="90d">Last 90 days</MenuItem>
                <MenuItem value="1y">Last year</MenuItem>
              </Select>
            </FormControl>
            <Tooltip title="Refresh Data">
              <IconButton onClick={() => refetch()} size="small">
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        <Tabs
          value={activeTab}
          onChange={(_, newValue) => setActiveTab(newValue)}
          sx={{ mb: 2 }}
        >
          <Tab label="Chart View" />
          <Tab label="List View" />
        </Tabs>

        {activeTab === 0 ? (
          <Box sx={{ height: 300, mb: 2 }}>
            <Line data={chartData} options={chartOptions} />
          </Box>
        ) : (
          <List disablePadding>
            {trends.map((trend, index) => (
              <motion.div
                key={trend.competency_id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <ListItem
                  divider={index < trends.length - 1}
                  sx={{
                    px: 0,
                    '&:hover': {
                      backgroundColor: 'action.hover',
                      borderRadius: 1,
                    },
                  }}
                >
                  <ListItemIcon>
                    {getTrendIcon(trend.trend_direction, trend.growth_rate)}
                  </ListItemIcon>
                  
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center" gap={1}>
                        <Typography variant="subtitle2">
                          {trend.competency_name}
                        </Typography>
                        <Chip
                          label={trend.category}
                          size="small"
                          variant="outlined"
                          color="primary"
                        />
                      </Box>
                    }
                    secondary={
                      <Box display="flex" alignItems="center" gap={2} mt={0.5}>
                        <Typography variant="body2" color="textSecondary">
                          {trend.learners_count} learners
                        </Typography>
                        <Chip
                          label={formatGrowthRate(trend.growth_rate)}
                          size="small"
                          color={getTrendColor(trend.trend_direction) as any}
                          variant="filled"
                        />
                      </Box>
                    }
                  />
                </ListItem>
              </motion.div>
            ))}
          </List>
        )}

        {trends.length === 0 && (
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            py={4}
          >
            <TimelineIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="body1" color="textSecondary" textAlign="center">
              No trend data available yet.
              <br />
              Trends will appear as learners engage with competencies.
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  )
}
