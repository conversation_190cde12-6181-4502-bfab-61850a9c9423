'use client'

import React from 'react'
import {
  Box,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Autocomplete,
  Slider,
  FormControlLabel,
  Switch,
  Grid,
  Card,
  CardContent,
  Button,
  Divider
} from '@mui/material'
import { Add as AddIcon } from '@mui/icons-material'

import { PathCreationForm, WizardStep } from '@/lib/types/learning-paths'
import PathBuilder from './PathBuilder'
import PathAssignments from './PathAssignments'
import PathPreview from './PathPreview'
import AIPathGenerator from './AIPathGenerator'

interface PathCreationWizardProps {
  step: WizardStep
  pathData: Partial<PathCreationForm>
  modules: any[]
  assignments: any[]
  onDataChange: (data: Partial<PathCreationForm>) => void
  onModulesChange: (modules: any[]) => void
  onAssignmentsChange: (assignments: any[]) => void
}

const PathCreationWizard: React.FC<PathCreationWizardProps> = ({
  step,
  pathData,
  modules,
  assignments,
  onDataChange,
  onModulesChange,
  onAssignmentsChange
}) => {
  const categories = [
    'Technology',
    'Leadership',
    'Sales',
    'Marketing',
    'HR',
    'Finance',
    'Operations',
    'Compliance',
    'Soft Skills'
  ]

  const availableTags = [
    'AI/ML',
    'Data Science',
    'Web Development',
    'Mobile Development',
    'Cloud Computing',
    'Cybersecurity',
    'Project Management',
    'Communication',
    'Team Building',
    'Customer Service',
    'Analytics',
    'Strategy',
    'Innovation',
    'Digital Transformation'
  ]

  const handleObjectiveAdd = () => {
    const objectives = pathData.objectives || []
    onDataChange({ objectives: [...objectives, ''] })
  }

  const handleObjectiveChange = (index: number, value: string) => {
    const objectives = [...(pathData.objectives || [])]
    objectives[index] = value
    onDataChange({ objectives })
  }

  const handleObjectiveRemove = (index: number) => {
    const objectives = [...(pathData.objectives || [])]
    objectives.splice(index, 1)
    onDataChange({ objectives })
  }

  const renderBasicInfo = () => (
    <Box>
      <Typography variant="h5" fontWeight="bold" gutterBottom>
        Basic Information
      </Typography>
      <Typography variant="body1" color="text.secondary" mb={4}>
        Start by defining the core details of your learning path. This information will help learners understand what they'll achieve.
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Learning Path Title"
            value={pathData.title || ''}
            onChange={(e) => onDataChange({ title: e.target.value })}
            placeholder="e.g., Introduction to Data Science"
            required
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            fullWidth
            multiline
            rows={4}
            label="Description"
            value={pathData.description || ''}
            onChange={(e) => onDataChange({ description: e.target.value })}
            placeholder="Describe what learners will gain from this path..."
            required
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControl fullWidth required>
            <InputLabel>Category</InputLabel>
            <Select
              value={pathData.category || ''}
              label="Category"
              onChange={(e) => onDataChange({ category: e.target.value })}
            >
              {categories.map((category) => (
                <MenuItem key={category} value={category}>
                  {category}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel>Difficulty Level</InputLabel>
            <Select
              value={pathData.difficulty || 'beginner'}
              label="Difficulty Level"
              onChange={(e) => onDataChange({ difficulty: e.target.value as any })}
            >
              <MenuItem value="beginner">Beginner</MenuItem>
              <MenuItem value="intermediate">Intermediate</MenuItem>
              <MenuItem value="advanced">Advanced</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <Typography gutterBottom>Duration (weeks)</Typography>
          <Slider
            value={pathData.duration || 4}
            onChange={(_, value) => onDataChange({ duration: value as number })}
            min={1}
            max={52}
            marks={[
              { value: 1, label: '1 week' },
              { value: 4, label: '1 month' },
              { value: 12, label: '3 months' },
              { value: 26, label: '6 months' },
              { value: 52, label: '1 year' }
            ]}
            valueLabelDisplay="on"
            valueLabelFormat={(value) => `${value} week${value !== 1 ? 's' : ''}`}
          />
        </Grid>

        <Grid item xs={12}>
          <Autocomplete
            multiple
            options={availableTags}
            value={pathData.tags || []}
            onChange={(_, newValue) => onDataChange({ tags: newValue })}
            renderTags={(value, getTagProps) =>
              value.map((option, index) => (
                <Chip
                  variant="outlined"
                  label={option}
                  {...getTagProps({ index })}
                  key={option}
                />
              ))
            }
            renderInput={(params) => (
              <TextField
                {...params}
                label="Tags"
                placeholder="Add relevant tags..."
              />
            )}
          />
        </Grid>

        <Grid item xs={12}>
          <Box>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">Learning Objectives</Typography>
              <Button
                startIcon={<AddIcon />}
                onClick={handleObjectiveAdd}
                size="small"
              >
                Add Objective
              </Button>
            </Box>
            
            {(pathData.objectives || []).map((objective, index) => (
              <Box key={index} display="flex" gap={1} mb={2}>
                <TextField
                  fullWidth
                  label={`Objective ${index + 1}`}
                  value={objective}
                  onChange={(e) => handleObjectiveChange(index, e.target.value)}
                  placeholder="What will learners be able to do?"
                />
                <Button
                  onClick={() => handleObjectiveRemove(index)}
                  color="error"
                  sx={{ minWidth: 'auto', px: 1 }}
                >
                  ×
                </Button>
              </Box>
            ))}
            
            {(!pathData.objectives || pathData.objectives.length === 0) && (
              <Typography variant="body2" color="text.secondary" style={{ fontStyle: 'italic' }}>
                Click "Add Objective" to define what learners will achieve
              </Typography>
            )}
          </Box>
        </Grid>

        <Grid item xs={12}>
          <FormControlLabel
            control={
              <Switch
                checked={pathData.is_template || false}
                onChange={(e) => onDataChange({ is_template: e.target.checked })}
              />
            }
            label="Save as template for future use"
          />
        </Grid>
      </Grid>

      <Divider sx={{ my: 4 }} />

      <AIPathGenerator
        onGenerate={(suggestion) => {
          onDataChange({
            title: suggestion.title,
            description: suggestion.description,
            difficulty: suggestion.difficulty,
            tags: suggestion.skills
          })
          // Convert AI suggestion to modules format
          const generatedModules = suggestion.modules.map((module, index) => ({
            id: `temp-${index}`,
            title: module.title,
            description: '',
            order: index,
            is_optional: false,
            estimated_duration: module.lessons.reduce((total, lesson) => total + lesson.estimated_duration, 0),
            lessons: module.lessons.map((lesson, lessonIndex) => ({
              id: `temp-${index}-${lessonIndex}`,
              title: lesson.title,
              description: lesson.description,
              type: lesson.type,
              order: lessonIndex,
              is_optional: false,
              estimated_duration: lesson.estimated_duration,
              content: {},
              dependencies: [],
              interactions: []
            })),
            dependencies: []
          }))
          onModulesChange(generatedModules)
        }}
      />
    </Box>
  )

  const renderStructure = () => (
    <Box>
      <Typography variant="h5" fontWeight="bold" gutterBottom>
        Structure & Modules
      </Typography>
      <Typography variant="body1" color="text.secondary" mb={4}>
        Design your learning path structure by creating modules and lessons. Use drag-and-drop to organize content.
      </Typography>

      <PathBuilder
        modules={modules}
        onChange={onModulesChange}
        pathData={pathData}
      />
    </Box>
  )

  const renderAssignments = () => (
    <Box>
      <Typography variant="h5" fontWeight="bold" gutterBottom>
        Assignments & Access
      </Typography>
      <Typography variant="body1" color="text.secondary" mb={4}>
        Configure who can access this learning path and set up assignment rules.
      </Typography>

      <PathAssignments
        assignments={assignments}
        onChange={onAssignmentsChange}
        pathData={pathData}
      />
    </Box>
  )

  const renderReview = () => (
    <Box>
      <Typography variant="h5" fontWeight="bold" gutterBottom>
        Review & Publish
      </Typography>
      <Typography variant="body1" color="text.secondary" mb={4}>
        Review your learning path before publishing. You can always save as draft and continue editing later.
      </Typography>

      <PathPreview
        pathData={pathData}
        modules={modules}
        assignments={assignments}
      />
    </Box>
  )

  switch (step.component) {
    case 'BasicInfo':
      return renderBasicInfo()
    case 'Structure':
      return renderStructure()
    case 'Assignments':
      return renderAssignments()
    case 'Review':
      return renderReview()
    default:
      return <Typography>Unknown step</Typography>
  }
}

export default PathCreationWizard
