// Test script to verify user creation with Auth Admin API
// Run with: node test-user-creation.js

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://ebugbzdstyztfvkhpqbs.supabase.co'
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVidWdiemRzdHl6dGZ2a2hwcWJzIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTU2MDczMCwiZXhwIjoyMDY1MTM2NzMwfQ.lVkWAUL6ID5_ziqggFfac3XyQqGcWnjKh9ONo_f1c9Q'

const supabaseAdmin = createClient(supabaseUrl, serviceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Generate a temporary password
function generateTemporaryPassword() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
  let password = ''
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return password
}

async function testUserCreation() {
  console.log('🧪 Testing User Creation with Auth Admin API...\n')

  const testEmail = `test-${Date.now()}@example.com`
  const testName = 'Test User'
  const tempPassword = generateTemporaryPassword()

  try {
    console.log('1. Creating auth user...')
    console.log('   Email:', testEmail)
    console.log('   Password:', tempPassword)

    // Step 1: Create user in Supabase Auth
    const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email: testEmail,
      password: tempPassword,
      email_confirm: true,
      user_metadata: {
        full_name: testName
      }
    })

    if (authError) {
      console.log('❌ Auth user creation failed:', authError.message)
      return
    }

    if (!authUser.user) {
      console.log('❌ No user returned from auth creation')
      return
    }

    console.log('✅ Auth user created successfully')
    console.log('   User ID:', authUser.user.id)
    console.log('   Email:', authUser.user.email)

    // Step 2: Wait for trigger to create profile
    console.log('\n2. Waiting for trigger to create profile...')
    await new Promise(resolve => setTimeout(resolve, 2000))

    // Step 3: Check if profile was created
    console.log('3. Checking profile creation...')
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', authUser.user.id)
      .single()

    if (profileError) {
      console.log('❌ Profile check failed:', profileError.message)
      console.log('   Cleaning up auth user...')
      await supabaseAdmin.auth.admin.deleteUser(authUser.user.id)
      return
    }

    console.log('✅ Profile created successfully')
    console.log('   Profile ID:', profile.id)
    console.log('   Tenant ID:', profile.tenant_id)
    console.log('   Role ID:', profile.role_id)

    // Step 4: Update profile with additional data
    console.log('\n4. Updating profile with additional data...')
    const { data: updatedProfile, error: updateError } = await supabaseAdmin
      .from('users')
      .update({
        role_id: 2, // Learner role
        department: 'Test Department',
        position: 'Test Position',
        phone: '1234567890',
        location: 'Test Location',
        status: 'active'
      })
      .eq('id', authUser.user.id)
      .select()
      .single()

    if (updateError) {
      console.log('❌ Profile update failed:', updateError.message)
      return
    }

    console.log('✅ Profile updated successfully')
    console.log('   Department:', updatedProfile.department)
    console.log('   Position:', updatedProfile.position)
    console.log('   Status:', updatedProfile.status)

    console.log('\n🎉 User creation test completed successfully!')
    console.log('   Auth User ID:', authUser.user.id)
    console.log('   Email:', authUser.user.email)
    console.log('   Profile created and updated')

    // Clean up test user
    console.log('\n5. Cleaning up test user...')
    await supabaseAdmin.auth.admin.deleteUser(authUser.user.id)
    console.log('✅ Test user cleaned up')

  } catch (error) {
    console.error('❌ Test failed with error:', error.message)
  }
}

// Run the test
testUserCreation()
