# ZenithLearn AI - Learner My Courses User Guide

## 📚 Welcome to Your Learning Journey

This guide will help you navigate and make the most of the My Courses page in ZenithLearn AI. Whether you're a new learner or looking to optimize your learning experience, this guide covers everything you need to know.

## 🚀 Getting Started

### Accessing Your Courses
1. **Login** to your ZenithLearn AI account
2. **Navigate** to the learner dashboard
3. **Click** on "My Courses" in the sidebar navigation
4. **Explore** your personalized course collection

### First-Time Setup
When you first access the My Courses page:
- **Set your preferences** by clicking the settings icon (⚙️)
- **Choose your view mode**: Grid or List view
- **Configure notifications** for course updates and deadlines
- **Explore available courses** in the "Explore" tab

## 🎯 Course Management Features

### 📋 Course Tabs Overview

#### **Active Courses Tab**
- View all your currently enrolled courses
- See progress indicators for each course
- Quick access to resume learning
- Pin important courses for easy access

#### **Explore Tab**
- Discover new courses available in your organization
- Browse by category, level, and tags
- Preview courses before enrolling
- Get personalized recommendations

#### **Recommendations Tab**
- AI-powered course suggestions based on your learning history
- Skill gap analysis and suggested learning paths
- Trending courses in your field
- Courses recommended by peers and instructors

#### **Completed Tab**
- View all successfully completed courses
- Access certificates and achievements
- Review course materials for reference
- Share your accomplishments

#### **Paused Tab**
- Manage temporarily paused courses
- Resume learning when ready
- Understand why courses were paused
- Set reminders to continue learning

### 🔍 Smart Search & Filtering

#### **Intelligent Search**
- **Type keywords** in the search bar to find courses
- **Use autocomplete** suggestions for faster searching
- **Access search history** by clicking in the search field
- **Try popular searches** for course discovery

#### **Advanced Filtering**
1. **Click the "Filters" button** to expand advanced options
2. **Select categories** like Programming, Design, Business
3. **Choose difficulty levels**: Beginner, Intermediate, Advanced
4. **Filter by status**: Active, Completed, Paused
5. **Add tags** for specific technologies or skills
6. **Sort results** by relevance, date, or progress

#### **Quick Filters**
- Use the dropdown menus for instant filtering
- **Category filter**: Narrow down by subject area
- **Status filter**: Focus on specific course states
- **View toggle**: Switch between grid and list views

### 📊 Course Cards & Information

#### **Course Card Elements**
- **Thumbnail**: Visual representation of the course
- **Title & Description**: Course name and brief overview
- **Instructor**: Course creator with profile link
- **Progress Bar**: Visual completion percentage
- **Duration**: Total course length in hours
- **Rating**: Average student rating with review count
- **Level Badge**: Difficulty indicator
- **Category Tag**: Subject classification
- **Pin Icon**: Quick access marker

#### **Interactive Actions**
- **Resume Button**: Continue from where you left off
- **Pin/Unpin**: Add to favorites for quick access
- **Bookmark**: Save for later review
- **More Actions (⋮)**: Additional course options

### 🎮 Course Actions & Management

#### **Enrolling in New Courses**
1. **Browse** available courses in the Explore tab
2. **Click** on a course card to view details
3. **Preview** course content and curriculum
4. **Check prerequisites** and requirements
5. **Click "Enroll Now"** to join the course
6. **Confirm enrollment** in the dialog

#### **Managing Enrolled Courses**
- **Resume Learning**: Click the Resume button to continue
- **Pause Course**: Temporarily stop progress (via More Actions)
- **Share Course**: Send course link to colleagues
- **Unenroll**: Remove course from your list (permanent action)

#### **Pinning & Organizing**
- **Pin courses** by clicking the pin icon (📌)
- **Pinned courses** appear at the top of your list
- **Drag and drop** to reorder courses (coming soon)
- **Use bookmarks** for courses you want to review later

## ⚙️ Customization & Settings

### 🎨 Display Preferences

#### **View Modes**
- **Grid View**: Card-based layout with rich visuals
- **List View**: Compact horizontal layout for quick scanning
- **Switch views** using the toggle buttons in the filter bar

#### **Courses Per Page**
- **Adjust pagination** from 6 to 24 courses per page
- **Smaller numbers** load faster on slow connections
- **Larger numbers** reduce clicking for browsing

#### **Content Settings**
- **Show/Hide completed courses** in your main view
- **Filter out dropped courses** for cleaner interface
- **Customize course card information** display

### 🔔 Notification Preferences
- **Course deadlines**: Get alerts for upcoming due dates
- **Progress milestones**: Celebrate completion achievements
- **New recommendations**: Discover relevant courses
- **Group activities**: Stay updated on collaborative learning

### 🎯 Learning Preferences
- **Learning format**: Video, text, interactive, or mixed
- **Study schedule**: Set preferred learning times
- **Weekly goals**: Target hours per week
- **Difficulty progression**: Automatic or manual level advancement

## 📈 Progress Tracking & Analytics

### 📊 Understanding Your Progress

#### **Progress Indicators**
- **Percentage bars**: Show completion from 0-100%
- **Module breakdown**: See progress within course sections
- **Time tracking**: Monitor hours spent learning
- **Streak counters**: Track consecutive learning days

#### **Next Lesson Information**
- **Resume point**: Exact lesson where you left off
- **Module context**: Which section you're currently in
- **Estimated time**: How long the next lesson takes
- **Prerequisites**: What you need to know first

#### **Achievement System**
- **Badges**: Earn rewards for milestones
- **Certificates**: Completion credentials
- **Leaderboards**: Compare progress with peers
- **Skill assessments**: Validate your learning

### 📅 Learning Analytics
- **Weekly progress charts**: Visual learning patterns
- **Time distribution**: How you spend learning hours
- **Course completion rates**: Success metrics
- **Skill development tracking**: Competency growth

## 🤝 Collaborative Features

### 👥 Social Learning
- **Study groups**: Join or create learning communities
- **Peer discussions**: Engage in course forums
- **Progress sharing**: Celebrate achievements with others
- **Mentorship**: Connect with experienced learners

### 💬 Communication
- **Course comments**: Ask questions and share insights
- **Direct messaging**: Connect with instructors and peers
- **Group announcements**: Stay updated on important information
- **Feedback system**: Rate and review courses

## 🔧 Troubleshooting & Support

### 🐛 Common Issues

#### **Course Not Loading**
1. **Check internet connection**
2. **Refresh the page** (Ctrl+R or Cmd+R)
3. **Clear browser cache** if problems persist
4. **Try a different browser** to isolate issues

#### **Progress Not Syncing**
1. **Ensure you're logged in** to the correct account
2. **Check for browser sync** across devices
3. **Wait a few minutes** for real-time updates
4. **Contact support** if issues continue

#### **Search Not Working**
1. **Try different keywords** or phrases
2. **Check spelling** and use simpler terms
3. **Clear search filters** that might be too restrictive
4. **Use category browsing** as an alternative

### 📞 Getting Help

#### **Self-Service Options**
- **Help tooltips**: Hover over icons for quick explanations
- **Keyboard shortcuts**: Press '?' for shortcut list
- **FAQ section**: Common questions and answers
- **Video tutorials**: Step-by-step guidance

#### **Contact Support**
- **In-app chat**: Click the help icon for instant support
- **Email support**: <EMAIL>
- **Knowledge base**: Comprehensive documentation
- **Community forums**: Peer-to-peer assistance

## 🎯 Best Practices & Tips

### 📚 Effective Learning Strategies

#### **Course Selection**
- **Start with fundamentals** before advanced topics
- **Mix different formats** (video, text, interactive)
- **Consider prerequisites** and skill requirements
- **Read reviews** from other learners

#### **Progress Management**
- **Set realistic goals** for weekly learning time
- **Take regular breaks** to avoid burnout
- **Review completed lessons** to reinforce learning
- **Practice skills** outside of course materials

#### **Organization Tips**
- **Pin active courses** you're currently working on
- **Use bookmarks** for courses you plan to take later
- **Regularly review** your course list and remove outdated items
- **Set up notifications** for important deadlines

### 🚀 Maximizing Your Learning Experience
- **Engage with content** actively rather than passively watching
- **Take notes** and create personal summaries
- **Join study groups** for collaborative learning
- **Apply skills** in real projects when possible
- **Seek feedback** from instructors and peers

## 🔮 What's Coming Next

### 🆕 Upcoming Features
- **AI-powered study plans**: Personalized learning schedules
- **Advanced analytics**: Detailed learning insights
- **Mobile app**: Learn on-the-go with full synchronization
- **Offline mode**: Download courses for offline access
- **Virtual reality**: Immersive learning experiences

### 🎉 Stay Updated
- **Follow release notes** for new feature announcements
- **Join beta programs** to test upcoming features
- **Provide feedback** to help shape future development
- **Connect with the community** for tips and best practices

---

**🎓 Happy Learning with ZenithLearn AI!**

Remember, learning is a journey, not a destination. Use these tools and features to create a personalized, effective, and enjoyable learning experience that fits your goals and schedule.

For additional support or questions, don't hesitate to reach out to our support team or explore our comprehensive help documentation.
