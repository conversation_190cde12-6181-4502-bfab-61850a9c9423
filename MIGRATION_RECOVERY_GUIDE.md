# Migration Recovery Guide for ZenithLearn AI

## 🚨 Foreign Key Constraint Error Fix

If you're getting the error: `insert or update on table "roles" violates foreign key constraint "roles_tenant_id_fkey"`, follow this step-by-step recovery guide.

## Step 1: Diagnose the Current State

First, run the diagnostic script to understand what's in your database:

### Using Supabase Dashboard:
1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor**
3. Copy and paste the contents of `000_diagnostic.sql`
4. Click **Run**

### Using Supabase CLI:
```bash
supabase db reset --debug
```

## Step 2: Identify the Problem

The diagnostic will show you:
- ✅ Which tables exist
- 📊 How many records are in each table
- 🔗 Foreign key relationships
- 🚫 Any orphaned records

**Common scenarios:**

### Scenario A: Tables exist but no tenant data
```
tenants_count: 0
roles_count: 0
```
**Solution**: Run the fixed seed data migration

### Scenario B: Partial data exists
```
tenants_count: 1
roles_count: 0
```
**Solution**: Run the fixed seed data migration (it will skip existing data)

### Scenario C: Corrupted state
```
tenants_count: 0
roles_count: 2
```
**Solution**: Reset and start fresh

## Step 3: Choose Your Recovery Method

### Method 1: Use the Fixed Migration (Recommended)

The new `003_seed_data_fixed.sql` file includes:
- ✅ Transaction management
- ✅ Existence checking
- ✅ Proper dependency verification
- ✅ Better error handling

**Run the fixed migration:**

```sql
-- In Supabase SQL Editor, run:
\i 003_seed_data_fixed.sql
```

Or copy and paste the entire contents of `003_seed_data_fixed.sql` into the SQL Editor.

### Method 2: Manual Step-by-Step Recovery

If the fixed migration doesn't work, do this manually:

#### Step 2.1: Create the tenant first
```sql
-- Check if tenant exists
SELECT * FROM tenants WHERE slug = 'demo-org';

-- If no results, create it:
INSERT INTO tenants (name, slug, settings) VALUES 
('Demo Organization', 'demo-org', '{"theme": "light", "features": ["ai_chat", "gamification", "reports"]}');

-- Verify it was created and note the ID:
SELECT id, name, slug FROM tenants WHERE slug = 'demo-org';
```

#### Step 2.2: Create roles using the tenant ID
```sql
-- Replace '1' with the actual tenant ID from above
INSERT INTO roles (tenant_id, name, permissions, is_default) VALUES 
(1, 'Super Admin', ARRAY['admin'], false),
(1, 'Admin', ARRAY['view_dashboard', 'view_learning_paths', 'create_learning_paths'], false),
(1, 'Instructor', ARRAY['view_dashboard', 'view_learning_paths'], false),
(1, 'Learner', ARRAY['view_dashboard'], true);
```

#### Step 2.3: Continue with other data
```sql
-- Insert competencies
INSERT INTO competencies (tenant_id, name, description, category, level_definitions) VALUES 
(1, 'JavaScript Programming', 'Proficiency in JavaScript programming language', 'Technical', '{"1": "Basic syntax and concepts"}');

-- Continue with learning paths, etc.
```

### Method 3: Complete Reset (Nuclear Option)

If everything is broken, start fresh:

#### Using Supabase CLI:
```bash
supabase db reset
supabase db push
```

#### Using Dashboard:
1. Go to **Settings** → **Database**
2. Scroll down to **Reset Database**
3. Type your project name to confirm
4. Click **Reset Database**
5. Re-run all migrations in order:
   - `001_initial_schema.sql`
   - `002_rls_policies.sql`
   - `003_seed_data_fixed.sql`

## Step 4: Verify Success

After running any recovery method, verify it worked:

```sql
-- Check that everything is created
SELECT 
    (SELECT COUNT(*) FROM tenants) as tenants,
    (SELECT COUNT(*) FROM roles) as roles,
    (SELECT COUNT(*) FROM competencies) as competencies,
    (SELECT COUNT(*) FROM learning_paths) as learning_paths;

-- Should return something like:
-- tenants: 1, roles: 4, competencies: 4, learning_paths: 4
```

## Step 5: Test the Application

1. Update your `.env.local` with the correct Supabase credentials
2. Restart your Next.js development server:
   ```bash
   npm run dev
   ```
3. Visit `http://localhost:3000/demo` to see if it loads without errors

## Common Issues and Solutions

### Issue: "relation does not exist"
**Cause**: Schema migration didn't run
**Solution**: Run `001_initial_schema.sql` first

### Issue: "permission denied"
**Cause**: Using wrong API key
**Solution**: Make sure you're using the service role key for migrations

### Issue: "duplicate key value violates unique constraint"
**Cause**: Trying to insert data that already exists
**Solution**: Use the fixed migration which checks for existing data

### Issue: "function does not exist"
**Cause**: RLS policies reference functions that don't exist
**Solution**: Run migrations in correct order (schema → RLS → seed data)

## Prevention for Future

To avoid this issue in the future:

1. **Always run migrations in order**:
   - Schema first (`001_initial_schema.sql`)
   - RLS policies second (`002_rls_policies.sql`)
   - Seed data last (`003_seed_data_fixed.sql`)

2. **Use transactions** for complex operations

3. **Check existence** before inserting data

4. **Use the diagnostic script** to verify state before making changes

5. **Backup before major changes**:
   ```bash
   supabase db dump > backup.sql
   ```

## Getting Help

If you're still having issues:

1. Run the diagnostic script and share the output
2. Check the Supabase logs in your dashboard
3. Look for specific error messages in the SQL Editor
4. Join the [Supabase Discord](https://discord.supabase.com) for community help

## Success Indicators

You'll know the recovery worked when:

- ✅ Diagnostic script shows all tables with data
- ✅ No foreign key constraint errors
- ✅ Application connects without errors
- ✅ Demo page loads with data
- ✅ No console errors in browser

The database should now be ready for ZenithLearn AI development!
