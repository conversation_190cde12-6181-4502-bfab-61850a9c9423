'use client'

import { useState } from 'react'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  TextField,
  Avatar,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Divider,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
} from '@mui/material'
import {
  Send as SendIcon,
  Announcement as AnnouncementIcon,
  Chat as ChatIcon,
  Email as EmailIcon,
  Sms as SmsIcon,
  NotificationsActive as NotificationIcon,
  AttachFile as AttachIcon,
  MoreVert as MoreVertIcon,
} from '@mui/icons-material'
import { Editor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'

import { Group } from '@/lib/types/groups'

interface GroupCommunicationProps {
  group: Group
  onRefresh: () => void
}

interface SendMessageDialogProps {
  open: boolean
  onClose: () => void
  onSend: (message: any) => void
  loading?: boolean
}

function SendMessageDialog({ open, onClose, onSend, loading }: SendMessageDialogProps) {
  const [messageType, setMessageType] = useState<'announcement' | 'chat'>('announcement')
  const [subject, setSubject] = useState('')
  const [message, setMessage] = useState('')
  const [channels, setChannels] = useState<string[]>(['in_app'])
  const [priority, setPriority] = useState<'normal' | 'high' | 'urgent'>('normal')

  const handleChannelChange = (channel: string, checked: boolean) => {
    if (checked) {
      setChannels([...channels, channel])
    } else {
      setChannels(channels.filter(c => c !== channel))
    }
  }

  const handleSend = () => {
    const messageData = {
      message_type: messageType,
      subject: messageType === 'announcement' ? subject : undefined,
      message,
      channels,
      metadata: {
        priority,
      },
    }
    onSend(messageData)
    
    // Reset form
    setSubject('')
    setMessage('')
    setChannels(['in_app'])
    setPriority('normal')
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Send Message to Group</DialogTitle>
      <DialogContent>
        <Box sx={{ space: 3, mt: 2 }}>
          <FormControl fullWidth sx={{ mb: 3 }}>
            <InputLabel>Message Type</InputLabel>
            <Select
              value={messageType}
              label="Message Type"
              onChange={(e) => setMessageType(e.target.value as any)}
            >
              <MenuItem value="announcement">Announcement</MenuItem>
              <MenuItem value="chat">Chat Message</MenuItem>
            </Select>
          </FormControl>

          {messageType === 'announcement' && (
            <TextField
              fullWidth
              label="Subject"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              sx={{ mb: 3 }}
            />
          )}

          <TextField
            fullWidth
            label="Message"
            multiline
            rows={6}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Type your message here..."
            sx={{ mb: 3 }}
          />

          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              Delivery Channels
            </Typography>
            <FormControlLabel
              control={
                <Checkbox
                  checked={channels.includes('in_app')}
                  onChange={(e) => handleChannelChange('in_app', e.target.checked)}
                />
              }
              label="In-App Notification"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={channels.includes('email')}
                  onChange={(e) => handleChannelChange('email', e.target.checked)}
                />
              }
              label="Email"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={channels.includes('sms')}
                  onChange={(e) => handleChannelChange('sms', e.target.checked)}
                />
              }
              label="SMS"
            />
          </Box>

          <FormControl fullWidth>
            <InputLabel>Priority</InputLabel>
            <Select
              value={priority}
              label="Priority"
              onChange={(e) => setPriority(e.target.value as any)}
            >
              <MenuItem value="normal">Normal</MenuItem>
              <MenuItem value="high">High</MenuItem>
              <MenuItem value="urgent">Urgent</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={handleSend}
          variant="contained"
          disabled={!message.trim() || channels.length === 0 || loading}
          startIcon={<SendIcon />}
        >
          {loading ? 'Sending...' : 'Send Message'}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default function GroupCommunication({ group, onRefresh }: GroupCommunicationProps) {
  const [sendDialogOpen, setSendDialogOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [messages, setMessages] = useState([
    {
      id: 1,
      sender_name: 'Admin',
      sender_avatar: null,
      message: 'Welcome to the group! Please complete your assigned learning paths by the end of the month.',
      message_type: 'announcement',
      channels: ['in_app', 'email'],
      sent_at: '2024-01-15T10:00:00Z',
      metadata: { priority: 'high' },
    },
    {
      id: 2,
      sender_name: 'John Doe',
      sender_avatar: null,
      message: 'Great progress everyone! Keep up the good work.',
      message_type: 'chat',
      channels: ['in_app'],
      sent_at: '2024-01-14T15:30:00Z',
      metadata: { priority: 'normal' },
    },
  ])

  const handleSendMessage = async (messageData: any) => {
    try {
      setLoading(true)
      // TODO: Call GroupsService.sendGroupMessage
      console.log('Sending message:', messageData)
      
      // Mock adding message to list
      const newMessage = {
        id: Date.now(),
        sender_name: 'Current User',
        sender_avatar: null,
        ...messageData,
        sent_at: new Date().toISOString(),
      }
      setMessages([newMessage, ...messages])
      setSendDialogOpen(false)
    } catch (error) {
      console.error('Failed to send message:', error)
    } finally {
      setLoading(false)
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'error'
      case 'high':
        return 'warning'
      default:
        return 'default'
    }
  }

  const getMessageTypeIcon = (type: string) => {
    switch (type) {
      case 'announcement':
        return <AnnouncementIcon />
      case 'chat':
        return <ChatIcon />
      default:
        return <ChatIcon />
    }
  }

  const getChannelIcons = (channels: string[]) => {
    return channels.map((channel) => {
      switch (channel) {
        case 'email':
          return <EmailIcon key={channel} fontSize="small" />
        case 'sms':
          return <SmsIcon key={channel} fontSize="small" />
        case 'in_app':
          return <NotificationIcon key={channel} fontSize="small" />
        default:
          return null
      }
    })
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">Group Communication</Typography>
        <Button
          variant="contained"
          startIcon={<SendIcon />}
          onClick={() => setSendDialogOpen(true)}
        >
          Send Message
        </Button>
      </Box>

      {/* Communication Settings */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Communication Settings
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Chip
              icon={<ChatIcon />}
              label={group.settings?.communication?.chat_enabled ? 'Chat Enabled' : 'Chat Disabled'}
              color={group.settings?.communication?.chat_enabled ? 'success' : 'default'}
              variant="outlined"
            />
            <Chip
              icon={<EmailIcon />}
              label={group.settings?.notifications?.email_enabled ? 'Email Enabled' : 'Email Disabled'}
              color={group.settings?.notifications?.email_enabled ? 'success' : 'default'}
              variant="outlined"
            />
            <Chip
              icon={<SmsIcon />}
              label={group.settings?.notifications?.sms_enabled ? 'SMS Enabled' : 'SMS Disabled'}
              color={group.settings?.notifications?.sms_enabled ? 'success' : 'default'}
              variant="outlined"
            />
          </Box>
        </CardContent>
      </Card>

      {/* Messages List */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Recent Messages ({messages.length})
          </Typography>
          
          {messages.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 8 }}>
              <ChatIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No messages yet
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Start communicating with your group members
              </Typography>
              <Button
                variant="contained"
                startIcon={<SendIcon />}
                onClick={() => setSendDialogOpen(true)}
              >
                Send First Message
              </Button>
            </Box>
          ) : (
            <List>
              {messages.map((message, index) => (
                <Box key={message.id}>
                  <ListItem alignItems="flex-start">
                    <ListItemAvatar>
                      <Avatar src={message.sender_avatar || undefined} alt={message.sender_name}>
                        {message.sender_name[0]}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                          <Typography variant="subtitle2" fontWeight="medium">
                            {message.sender_name}
                          </Typography>
                          <Chip
                            icon={getMessageTypeIcon(message.message_type)}
                            label={message.message_type}
                            size="small"
                            variant="outlined"
                          />
                          {message.metadata?.priority !== 'normal' && (
                            <Chip
                              label={message.metadata.priority}
                              size="small"
                              color={getPriorityColor(message.metadata.priority) as any}
                              variant="outlined"
                            />
                          )}
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" sx={{ mb: 1 }}>
                            {message.message}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="caption" color="text.secondary">
                              {new Date(message.sent_at).toLocaleString()}
                            </Typography>
                            <Box sx={{ display: 'flex', gap: 0.5 }}>
                              {getChannelIcons(message.channels)}
                            </Box>
                          </Box>
                        </Box>
                      }
                    />
                    <ListItemSecondaryAction>
                      <IconButton edge="end">
                        <MoreVertIcon />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                  {index < messages.length - 1 && <Divider variant="inset" component="li" />}
                </Box>
              ))}
            </List>
          )}
        </CardContent>
      </Card>

      {/* Send Message Dialog */}
      <SendMessageDialog
        open={sendDialogOpen}
        onClose={() => setSendDialogOpen(false)}
        onSend={handleSendMessage}
        loading={loading}
      />
    </Box>
  )
}
