'use client'

import React, { useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControlLabel,
  Checkbox,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material'
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  People as PeopleIcon,
  Security as SecurityIcon,
  School as SchoolIcon,
  Assessment as AssessmentIcon
} from '@mui/icons-material'
import { motion } from 'framer-motion'

const mockRoles = [
  {
    id: '1',
    name: 'Super Admin',
    description: 'Full system access',
    userCount: 2,
    permissions: ['all'],
    isDefault: false
  },
  {
    id: '2',
    name: 'Admin',
    description: 'Administrative access',
    userCount: 5,
    permissions: ['user_management', 'content_management', 'reports'],
    isDefault: true
  },
  {
    id: '3',
    name: 'Instructor',
    description: 'Can create and manage courses',
    userCount: 15,
    permissions: ['content_create', 'learner_view', 'progress_view'],
    isDefault: false
  },
  {
    id: '4',
    name: 'Learner',
    description: 'Basic learner access',
    userCount: 150,
    permissions: ['course_access', 'progress_view'],
    isDefault: true
  }
]

const availablePermissions = [
  { key: 'user_management', name: 'User Management', category: 'Administration', icon: <PeopleIcon /> },
  { key: 'content_management', name: 'Content Management', category: 'Content', icon: <SchoolIcon /> },
  { key: 'content_create', name: 'Content Creation', category: 'Content', icon: <SchoolIcon /> },
  { key: 'reports', name: 'Reports & Analytics', category: 'Analytics', icon: <AssessmentIcon /> },
  { key: 'security_settings', name: 'Security Settings', category: 'Administration', icon: <SecurityIcon /> },
  { key: 'learner_view', name: 'View Learners', category: 'Users', icon: <PeopleIcon /> },
  { key: 'progress_view', name: 'View Progress', category: 'Analytics', icon: <AssessmentIcon /> },
  { key: 'course_access', name: 'Course Access', category: 'Learning', icon: <SchoolIcon /> }
]

export default function RoleManagement() {
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [editingRole, setEditingRole] = useState<any>(null)
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([])

  const handleCreateRole = () => {
    setEditingRole(null)
    setSelectedPermissions([])
    setCreateDialogOpen(true)
  }

  const handleEditRole = (role: any) => {
    setEditingRole(role)
    setSelectedPermissions(role.permissions)
    setCreateDialogOpen(true)
  }

  const handleCloseDialog = () => {
    setCreateDialogOpen(false)
    setEditingRole(null)
    setSelectedPermissions([])
  }

  const handlePermissionToggle = (permission: string) => {
    setSelectedPermissions(prev => 
      prev.includes(permission)
        ? prev.filter(p => p !== permission)
        : [...prev, permission]
    )
  }

  const groupedPermissions = availablePermissions.reduce((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = []
    }
    acc[permission.category].push(permission)
    return acc
  }, {} as Record<string, typeof availablePermissions>)

  return (
    <Grid container spacing={3}>
      {/* Header */}
      <Grid item xs={12}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6" fontWeight="bold">
            Role Management
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateRole}
          >
            Create Role
          </Button>
        </Box>
      </Grid>

      {/* Roles Table */}
      <Grid item xs={12}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card>
            <CardContent>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Role Name</TableCell>
                      <TableCell>Description</TableCell>
                      <TableCell>Users</TableCell>
                      <TableCell>Permissions</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {mockRoles.map((role) => (
                      <TableRow key={role.id}>
                        <TableCell>
                          <Typography fontWeight="medium">
                            {role.name}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" color="text.secondary">
                            {role.description}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={`${role.userCount} users`}
                            size="small"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {role.permissions.length} permissions
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={role.isDefault ? 'Default' : 'Custom'}
                            color={role.isDefault ? 'primary' : 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <IconButton 
                            size="small"
                            onClick={() => handleEditRole(role)}
                          >
                            <EditIcon />
                          </IconButton>
                          {!role.isDefault && (
                            <IconButton size="small" color="error">
                              <DeleteIcon />
                            </IconButton>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </motion.div>
      </Grid>

      {/* Create/Edit Role Dialog */}
      <Dialog 
        open={createDialogOpen} 
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {editingRole ? 'Edit Role' : 'Create New Role'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Role Name"
                defaultValue={editingRole?.name || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={<Checkbox defaultChecked={editingRole?.isDefault || false} />}
                label="Default role for new users"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={2}
                defaultValue={editingRole?.description || ''}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Permissions
              </Typography>
              {Object.entries(groupedPermissions).map(([category, permissions]) => (
                <Box key={category} mb={2}>
                  <Typography variant="subtitle2" color="primary" gutterBottom>
                    {category}
                  </Typography>
                  <List dense>
                    {permissions.map((permission) => (
                      <ListItem key={permission.key} dense>
                        <ListItemIcon>
                          {permission.icon}
                        </ListItemIcon>
                        <ListItemText primary={permission.name} />
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={selectedPermissions.includes(permission.key)}
                              onChange={() => handlePermissionToggle(permission.key)}
                            />
                          }
                          label=""
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              ))}
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            Cancel
          </Button>
          <Button variant="contained" onClick={handleCloseDialog}>
            {editingRole ? 'Update Role' : 'Create Role'}
          </Button>
        </DialogActions>
      </Dialog>
    </Grid>
  )
}
