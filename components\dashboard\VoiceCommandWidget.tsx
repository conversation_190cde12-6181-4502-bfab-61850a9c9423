'use client'

import { useState, useEffect, useRef } from 'react'
import {
  Card,
  CardContent,
  Typography,
  Box,
  IconButton,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  useTheme,
  Tooltip,
  LinearProgress,
} from '@mui/material'
import {
  Mic as Mic<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON> as <PERSON>c<PERSON>ff<PERSON><PERSON>,
  RecordVoiceOver as VoiceIcon,
  VolumeUp as SpeakIcon,
  Check as CheckIcon,
  Close as ErrorIcon,
  Hearing as ListeningIcon,
} from '@mui/icons-material'
import { motion, AnimatePresence } from 'framer-motion'

interface VoiceCommandWidgetProps {
  isActive: boolean
  onToggle: (active: boolean) => void
  onCommand: (command: string) => void
}

interface VoiceCommand {
  id: string
  command: string
  action: string
  timestamp: Date
  success: boolean
}

const voiceCommands = [
  { phrase: "Show analytics", action: "Navigate to reports", example: "Show me the analytics dashboard" },
  { phrase: "Add user", action: "Open user creation", example: "Add a new user" },
  { phrase: "Create course", action: "Start course creation", example: "Create a new learning path" },
  { phrase: "Show learners", action: "Navigate to learners", example: "Show me all learners" },
  { phrase: "Open settings", action: "Navigate to settings", example: "Open the settings page" },
  { phrase: "Refresh data", action: "Refresh dashboard", example: "Refresh the dashboard data" },
]

export default function VoiceCommandWidget({ isActive, onToggle, onCommand }: VoiceCommandWidgetProps) {
  const theme = useTheme()
  const [isListening, setIsListening] = useState(false)
  const [transcript, setTranscript] = useState('')
  const [confidence, setConfidence] = useState(0)
  const [recentCommands, setRecentCommands] = useState<VoiceCommand[]>([])
  const [recognition, setRecognition] = useState<any>(null)
  const [isSupported, setIsSupported] = useState(false)

  useEffect(() => {
    // Check if Web Speech API is supported
    if (typeof window !== 'undefined' && 'webkitSpeechRecognition' in window) {
      setIsSupported(true)
      const SpeechRecognition = (window as any).webkitSpeechRecognition
      const recognitionInstance = new SpeechRecognition()
      
      recognitionInstance.continuous = false
      recognitionInstance.interimResults = true
      recognitionInstance.lang = 'en-US'

      recognitionInstance.onstart = () => {
        setIsListening(true)
        setTranscript('')
        setConfidence(0)
      }

      recognitionInstance.onresult = (event: any) => {
        let finalTranscript = ''
        let interimTranscript = ''

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript
          const confidence = event.results[i][0].confidence

          if (event.results[i].isFinal) {
            finalTranscript += transcript
            setConfidence(confidence)
          } else {
            interimTranscript += transcript
          }
        }

        setTranscript(finalTranscript || interimTranscript)

        if (finalTranscript) {
          processVoiceCommand(finalTranscript, confidence)
        }
      }

      recognitionInstance.onerror = (event: any) => {
        console.error('Speech recognition error:', event.error)
        setIsListening(false)
        addCommandToHistory('', 'Error: ' + event.error, false)
      }

      recognitionInstance.onend = () => {
        setIsListening(false)
      }

      setRecognition(recognitionInstance)
    }
  }, [])

  const processVoiceCommand = (command: string, confidence: number) => {
    const lowerCommand = command.toLowerCase()
    let matchedCommand = null
    let success = false

    // Find matching command
    for (const cmd of voiceCommands) {
      if (lowerCommand.includes(cmd.phrase.toLowerCase())) {
        matchedCommand = cmd
        success = true
        onCommand(command)
        break
      }
    }

    addCommandToHistory(
      command,
      matchedCommand ? matchedCommand.action : 'Command not recognized',
      success
    )

    // Provide audio feedback
    if (success) {
      speak('Command executed successfully')
    } else {
      speak('Sorry, I didn\'t understand that command')
    }
  }

  const addCommandToHistory = (command: string, action: string, success: boolean) => {
    const newCommand: VoiceCommand = {
      id: Date.now().toString(),
      command: command || transcript,
      action,
      timestamp: new Date(),
      success,
    }

    setRecentCommands(prev => [newCommand, ...prev.slice(0, 4)])
  }

  const speak = (text: string) => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text)
      utterance.rate = 0.8
      utterance.pitch = 1
      utterance.volume = 0.7
      speechSynthesis.speak(utterance)
    }
  }

  const startListening = () => {
    if (recognition && !isListening) {
      recognition.start()
    }
  }

  const stopListening = () => {
    if (recognition && isListening) {
      recognition.stop()
    }
  }

  const handleToggle = () => {
    const newState = !isActive
    onToggle(newState)
    
    if (!newState && isListening) {
      stopListening()
    }
  }

  if (!isSupported) {
    return (
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Box display="flex" alignItems="center" gap={2} mb={2}>
            <Avatar sx={{ bgcolor: 'grey.400', width: 32, height: 32 }}>
              <VoiceIcon fontSize="small" />
            </Avatar>
            <Typography variant="h6" fontWeight="bold">
              Voice Commands
            </Typography>
          </Box>
          <Typography variant="body2" color="text.secondary" textAlign="center">
            Voice commands are not supported in this browser
          </Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card sx={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      <CardContent sx={{
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
        <Box display="flex" alignItems="center" gap={2} mb={2}>
          <Avatar sx={{ bgcolor: isActive ? 'primary.main' : 'grey.400', width: 32, height: 32 }}>
            <VoiceIcon fontSize="small" />
          </Avatar>
          <Typography variant="h6" fontWeight="bold">
            Voice Commands
          </Typography>
          <Chip 
            label={isActive ? 'Active' : 'Inactive'} 
            color={isActive ? 'success' : 'default'}
            size="small"
          />
        </Box>

        {/* Voice Control */}
        <Box textAlign="center" mb={3}>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <IconButton
              onClick={isListening ? stopListening : startListening}
              disabled={!isActive}
              sx={{
                width: 64,
                height: 64,
                bgcolor: isListening ? 'error.main' : (isActive ? 'primary.main' : 'grey.400'),
                color: 'white',
                mb: 1,
                '&:hover': {
                  bgcolor: isListening ? 'error.dark' : (isActive ? 'primary.dark' : 'grey.500'),
                },
                '&:disabled': {
                  bgcolor: 'grey.300',
                  color: 'grey.500',
                }
              }}
            >
              <motion.div
                animate={isListening ? { scale: [1, 1.2, 1] } : {}}
                transition={{ duration: 1, repeat: isListening ? Infinity : 0 }}
              >
                {isListening ? <MicIcon /> : <MicOffIcon />}
              </motion.div>
            </IconButton>
          </motion.div>

          <Typography variant="body2" color="text.secondary" mb={1}>
            {isListening ? 'Listening...' : 'Click to start voice command'}
          </Typography>

          {isListening && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              <LinearProgress 
                sx={{ 
                  mb: 1,
                  '& .MuiLinearProgress-bar': {
                    background: 'linear-gradient(45deg, #FE6B8B 30%, #FF8E53 90%)',
                  }
                }}
              />
            </motion.div>
          )}

          {transcript && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <Typography variant="body2" sx={{ 
                bgcolor: 'background.default', 
                p: 1, 
                borderRadius: 1,
                border: 1,
                borderColor: 'divider'
              }}>
                "{transcript}"
              </Typography>
              {confidence > 0 && (
                <Typography variant="caption" color="text.secondary">
                  Confidence: {Math.round(confidence * 100)}%
                </Typography>
              )}
            </motion.div>
          )}
        </Box>

        {/* Toggle Switch */}
        <Box display="flex" justifyContent="center" mb={2}>
          <Tooltip title={isActive ? 'Disable voice commands' : 'Enable voice commands'}>
            <IconButton onClick={handleToggle} color={isActive ? 'primary' : 'default'}>
              {isActive ? <ListeningIcon /> : <MicOffIcon />}
            </IconButton>
          </Tooltip>
        </Box>

        {/* Recent Commands */}
        {recentCommands.length > 0 && (
          <Box>
            <Typography variant="body2" fontWeight="medium" mb={1}>
              Recent Commands
            </Typography>
            <List dense>
              {recentCommands.map((cmd) => (
                <motion.div
                  key={cmd.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <ListItem sx={{ px: 0, py: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: 32 }}>
                      {cmd.success ? (
                        <CheckIcon color="success" fontSize="small" />
                      ) : (
                        <ErrorIcon color="error" fontSize="small" />
                      )}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography variant="body2" noWrap>
                          {cmd.command || 'Voice input'}
                        </Typography>
                      }
                      secondary={
                        <Typography variant="caption" color="text.secondary" noWrap>
                          {cmd.action}
                        </Typography>
                      }
                    />
                  </ListItem>
                </motion.div>
              ))}
            </List>
          </Box>
        )}

        {/* Available Commands */}
        {recentCommands.length === 0 && isActive && (
          <Box>
            <Typography variant="body2" fontWeight="medium" mb={1}>
              Try saying:
            </Typography>
            <Box display="flex" flexDirection="column" gap={0.5}>
              {voiceCommands.slice(0, 3).map((cmd, index) => (
                <Chip
                  key={index}
                  label={`"${cmd.phrase}"`}
                  size="small"
                  variant="outlined"
                  sx={{ 
                    justifyContent: 'flex-start',
                    '& .MuiChip-label': { fontSize: '0.7rem' }
                  }}
                />
              ))}
            </Box>
          </Box>
        )}
      </CardContent>
    </Card>
  )
}
