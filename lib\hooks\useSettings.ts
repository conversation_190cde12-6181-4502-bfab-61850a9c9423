import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { settingsApi } from '@/lib/api/settings'
import {
  GeneralSettings,
  BrandingSettings,
  SecuritySettings,
  Role,
  Permission,
  FeatureFlag,
  SettingsAuditLog,
  SettingsResponse
} from '@/lib/types/settings'

// Error handler for settings API calls
const handleSettingsError = (error: any, operation: string) => {
  console.error(`Settings ${operation} error:`, error)

  if (error?.message?.includes('Invalid tenant ID')) {
    toast.error('Authentication error: Please log in again')
    return
  }

  if (error?.code === '22P02') {
    toast.error('Data format error: Please refresh and try again')
    return
  }

  if (error?.message?.includes('JWT')) {
    toast.error('Session expired: Please log in again')
    return
  }

  toast.error(error?.message || `Failed to ${operation}`)
}

// General Settings Hooks
export const useGeneralSettings = () => {
  return useQuery({
    queryKey: ['settings', 'general'],
    queryFn: settingsApi.getGeneralSettings,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error?.message?.includes('Invalid tenant ID') || error?.message?.includes('JWT')) {
        return false
      }
      return failureCount < 2
    }
  })
}

export const useUpdateGeneralSettings = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (settings: Partial<GeneralSettings>) =>
      settingsApi.updateGeneralSettings(settings),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['settings', 'general'] })
      toast.success(response.message || 'General settings updated successfully')
    },
    onError: (error: any) => {
      handleSettingsError(error, 'update general settings')
    },
  })
}

// Branding Settings Hooks
export const useBrandingSettings = () => {
  return useQuery({
    queryKey: ['settings', 'branding'],
    queryFn: settingsApi.getBrandingSettings,
    staleTime: 5 * 60 * 1000,
    retry: (failureCount, error) => {
      if (error?.message?.includes('Invalid tenant ID') || error?.message?.includes('JWT')) {
        return false
      }
      return failureCount < 2
    }
  })
}

export const useUpdateBrandingSettings = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (settings: Partial<BrandingSettings>) => 
      settingsApi.updateBrandingSettings(settings),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['settings', 'branding'] })
      toast.success(response.message || 'Branding settings updated successfully')
    },
    onError: (error: any) => {
      handleSettingsError(error, 'update branding settings')
    },
  })
}

// Security Settings Hooks
export const useSecuritySettings = () => {
  return useQuery({
    queryKey: ['settings', 'security'],
    queryFn: settingsApi.getSecuritySettings,
    staleTime: 5 * 60 * 1000,
  })
}

export const useUpdateSecuritySettings = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (settings: Partial<SecuritySettings>) => 
      settingsApi.updateSecuritySettings(settings),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['settings', 'security'] })
      toast.success(response.message || 'Security settings updated successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update security settings')
    },
  })
}

// Role Management Hooks
export const useRoles = () => {
  return useQuery({
    queryKey: ['settings', 'roles'],
    queryFn: settingsApi.getRoles,
    staleTime: 5 * 60 * 1000,
    retry: (failureCount, error) => {
      if (error?.message?.includes('Invalid tenant ID') || error?.message?.includes('JWT')) {
        return false
      }
      return failureCount < 2
    }
  })
}

export const usePermissions = () => {
  return useQuery({
    queryKey: ['settings', 'permissions'],
    queryFn: settingsApi.getPermissions,
    staleTime: 10 * 60 * 1000, // 10 minutes - permissions change rarely
  })
}

export const useCreateRole = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (roleData: Omit<Role, 'id' | 'created_at' | 'updated_at' | 'user_count'>) => 
      settingsApi.createRole(roleData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['settings', 'roles'] })
      toast.success('Role created successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create role')
    },
  })
}

// Feature Flags Hooks
export const useFeatureFlags = () => {
  return useQuery({
    queryKey: ['settings', 'feature-flags'],
    queryFn: settingsApi.getFeatureFlags,
    staleTime: 5 * 60 * 1000,
  })
}

export const useUpdateFeatureFlag = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ key, enabled, config }: { key: string; enabled: boolean; config?: Record<string, any> }) => 
      settingsApi.updateFeatureFlag(key, enabled, config),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['settings', 'feature-flags'] })
      toast.success(response.message || 'Feature flag updated successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update feature flag')
    },
  })
}

// Audit Logs Hook
export const useAuditLogs = (limit = 50, offset = 0) => {
  return useQuery({
    queryKey: ['settings', 'audit-logs', limit, offset],
    queryFn: () => settingsApi.getAuditLogs(limit, offset),
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

// File Upload Hook
export const useUploadLogo = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (file: File) => settingsApi.uploadLogo(file),
    onSuccess: (logoUrl) => {
      queryClient.invalidateQueries({ queryKey: ['settings', 'branding'] })
      toast.success('Logo uploaded successfully')
      return logoUrl
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to upload logo')
    },
  })
}

// Combined Settings Hook for efficiency
export const useAllSettings = () => {
  const generalQuery = useGeneralSettings()
  const brandingQuery = useBrandingSettings()
  const securityQuery = useSecuritySettings()
  const rolesQuery = useRoles()
  const featureFlagsQuery = useFeatureFlags()

  return {
    general: generalQuery,
    branding: brandingQuery,
    security: securityQuery,
    roles: rolesQuery,
    featureFlags: featureFlagsQuery,
    isLoading: generalQuery.isLoading || brandingQuery.isLoading || securityQuery.isLoading,
    isError: generalQuery.isError || brandingQuery.isError || securityQuery.isError,
    error: generalQuery.error || brandingQuery.error || securityQuery.error
  }
}

// Settings validation hook
export const useValidateSettings = () => {
  return useMutation({
    mutationFn: async (settings: any) => {
      // Implement validation logic here
      // This could call a Supabase Edge Function for complex validation
      return { valid: true, errors: [] }
    },
    onError: (error: any) => {
      toast.error(error.message || 'Settings validation failed')
    },
  })
}

// Bulk settings update hook
export const useBulkUpdateSettings = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (updates: {
      general?: Partial<GeneralSettings>
      branding?: Partial<BrandingSettings>
      security?: Partial<SecuritySettings>
    }) => {
      const results = []
      
      if (updates.general) {
        results.push(await settingsApi.updateGeneralSettings(updates.general))
      }
      if (updates.branding) {
        results.push(await settingsApi.updateBrandingSettings(updates.branding))
      }
      if (updates.security) {
        results.push(await settingsApi.updateSecuritySettings(updates.security))
      }
      
      return results
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['settings'] })
      toast.success('All settings updated successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update settings')
    },
  })
}

// Real-time settings subscription hook
export const useSettingsSubscription = () => {
  const queryClient = useQueryClient()
  
  // This would set up a Supabase Realtime subscription
  // For now, we'll return a placeholder
  return {
    subscribe: () => {
      // Set up real-time subscription to tenant_settings table
      console.log('Setting up real-time subscription for settings')
    },
    unsubscribe: () => {
      console.log('Unsubscribing from settings updates')
    }
  }
}
