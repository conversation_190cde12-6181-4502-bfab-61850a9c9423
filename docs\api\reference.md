# API Reference Documentation

This document provides comprehensive API documentation for the Groups and Batches feature, including all endpoints, request/response formats, and usage examples.

## 🌐 Base URL

```
https://your-supabase-project.supabase.co/rest/v1/
```

## 🔐 Authentication

All API requests require authentication using Supabase JWT tokens:

```javascript
headers: {
  'Authorization': `Bearer ${supabaseToken}`,
  'apikey': 'your-supabase-anon-key',
  'Content-Type': 'application/json'
}
```

## 📋 Groups API

### Get Groups
Retrieve groups with filtering and pagination.

**Endpoint:** `GET /groups`

**Query Parameters:**
- `select` (string): Columns to select
- `tenant_id` (integer): Filter by tenant ID
- `status` (string): Filter by status (active, inactive, archived, draft)
- `limit` (integer): Number of results to return
- `offset` (integer): Number of results to skip

**Example Request:**
```javascript
const { data, error } = await supabase
  .from('groups')
  .select(`
    *,
    group_members(count),
    group_assignments(count),
    group_progress(completion_rate)
  `)
  .eq('tenant_id', tenantId)
  .eq('status', 'active')
  .order('created_at', { ascending: false })
  .limit(25)
```

**Example Response:**
```json
{
  "data": [
    {
      "id": 1,
      "tenant_id": 1,
      "name": "Engineering Team Q1 2024",
      "description": "Frontend and backend engineers learning new technologies",
      "status": "active",
      "tags": ["Engineering", "Q1", "Technology"],
      "parent_group_id": null,
      "settings": {
        "notifications": {
          "email_enabled": true,
          "in_app_enabled": true
        }
      },
      "created_by": "uuid-here",
      "created_at": "2024-01-15T10:00:00Z",
      "updated_at": "2024-01-15T10:00:00Z",
      "group_members": [{"count": 5}],
      "group_assignments": [{"count": 2}],
      "group_progress": [{"completion_rate": 65.5}]
    }
  ],
  "error": null
}
```

### Get Single Group
Retrieve detailed information about a specific group.

**Endpoint:** `GET /groups?id=eq.{groupId}`

**Example Request:**
```javascript
const { data, error } = await supabase
  .from('groups')
  .select(`
    *,
    group_members(*,
      users(full_name, email, avatar_url)
    ),
    group_assignments(*,
      learning_paths(title, description, estimated_duration)
    ),
    group_progress(*),
    children:groups!parent_group_id(*)
  `)
  .eq('id', groupId)
  .single()
```

### Create Group
Create a new group.

**Endpoint:** `POST /groups`

**Request Body:**
```json
{
  "tenant_id": 1,
  "name": "New Learning Group",
  "description": "Description of the group",
  "status": "active",
  "tags": ["tag1", "tag2"],
  "parent_group_id": null,
  "settings": {
    "notifications": {
      "email_enabled": true,
      "sms_enabled": false,
      "in_app_enabled": true
    }
  },
  "created_by": "user-uuid"
}
```

**Example Request:**
```javascript
const { data, error } = await supabase
  .from('groups')
  .insert([groupData])
  .select()
  .single()
```

### Update Group
Update an existing group.

**Endpoint:** `PATCH /groups?id=eq.{groupId}`

**Request Body:**
```json
{
  "name": "Updated Group Name",
  "description": "Updated description",
  "status": "inactive",
  "tags": ["updated", "tags"]
}
```

**Example Request:**
```javascript
const { data, error } = await supabase
  .from('groups')
  .update(updates)
  .eq('id', groupId)
  .select()
  .single()
```

### Delete Group
Delete a group and all related data.

**Endpoint:** `DELETE /groups?id=eq.{groupId}`

**Example Request:**
```javascript
const { error } = await supabase
  .from('groups')
  .delete()
  .eq('id', groupId)
```

## 👥 Group Members API

### Get Group Members
Retrieve members of a specific group.

**Endpoint:** `GET /group_members?group_id=eq.{groupId}`

**Example Request:**
```javascript
const { data, error } = await supabase
  .from('group_members')
  .select(`
    *,
    users(full_name, email, avatar_url, department)
  `)
  .eq('group_id', groupId)
```

### Add Group Member
Add a single member to a group.

**Endpoint:** `POST /group_members`

**Request Body:**
```json
{
  "group_id": 1,
  "learner_id": "user-uuid",
  "role": "member",
  "added_by": "admin-uuid"
}
```

### Add Multiple Members
Add multiple members to a group.

**Endpoint:** `POST /group_members`

**Request Body:**
```json
[
  {
    "group_id": 1,
    "learner_id": "user-uuid-1",
    "role": "member",
    "added_by": "admin-uuid"
  },
  {
    "group_id": 1,
    "learner_id": "user-uuid-2",
    "role": "moderator",
    "added_by": "admin-uuid"
  }
]
```

### Update Member Role
Update a member's role in a group.

**Endpoint:** `PATCH /group_members?id=eq.{memberId}`

**Request Body:**
```json
{
  "role": "moderator"
}
```

### Remove Group Member
Remove a member from a group.

**Endpoint:** `DELETE /group_members?id=eq.{memberId}`

## 📚 Group Assignments API

### Get Group Assignments
Retrieve learning path assignments for a group.

**Endpoint:** `GET /group_assignments?group_id=eq.{groupId}`

**Example Request:**
```javascript
const { data, error } = await supabase
  .from('group_assignments')
  .select(`
    *,
    learning_paths(title, description, estimated_duration, difficulty_level)
  `)
  .eq('group_id', groupId)
```

### Assign Learning Path
Assign a learning path to a group.

**Endpoint:** `POST /group_assignments`

**Request Body:**
```json
{
  "group_id": 1,
  "path_id": 5,
  "assigned_by": "admin-uuid",
  "start_date": "2024-02-01T00:00:00Z",
  "due_date": "2024-03-01T00:00:00Z",
  "is_mandatory": true,
  "settings": {
    "auto_assign_new_members": true,
    "grace_period_days": 7
  }
}
```

### Bulk Assign Paths
Assign multiple paths to multiple groups.

**Endpoint:** `POST /rpc/bulk_assign_paths`

**Request Body:**
```json
{
  "group_ids": [1, 2, 3],
  "path_ids": [5, 6],
  "settings": {
    "is_mandatory": true,
    "due_date": "2024-03-01T00:00:00Z"
  }
}
```

## 📊 Group Progress API

### Get Group Progress
Retrieve progress statistics for a group.

**Endpoint:** `GET /group_progress?group_id=eq.{groupId}`

**Example Request:**
```javascript
const { data, error } = await supabase
  .from('group_progress')
  .select(`
    *,
    learning_paths(title)
  `)
  .eq('group_id', groupId)
```

**Example Response:**
```json
{
  "data": [
    {
      "id": 1,
      "group_id": 1,
      "path_id": 5,
      "total_members": 10,
      "completed_members": 3,
      "in_progress_members": 5,
      "not_started_members": 2,
      "average_score": 78.5,
      "completion_rate": 30.0,
      "updated_at": "2024-01-15T15:30:00Z",
      "learning_paths": {
        "title": "JavaScript Fundamentals"
      }
    }
  ]
}
```

### Refresh Group Progress
Manually refresh progress calculations.

**Endpoint:** `POST /rpc/refresh_group_progress`

**Request Body:**
```json
{
  "group_id": 1
}
```

## 💬 Group Messages API

### Get Group Messages
Retrieve messages for a group.

**Endpoint:** `GET /group_messages?group_id=eq.{groupId}`

**Query Parameters:**
- `limit` (integer): Number of messages to return
- `order` (string): Sort order (default: sent_at.desc)

**Example Request:**
```javascript
const { data, error } = await supabase
  .from('group_messages')
  .select(`
    *,
    users(full_name, avatar_url)
  `)
  .eq('group_id', groupId)
  .order('sent_at', { ascending: false })
  .limit(50)
```

### Send Group Message
Send a message to a group.

**Endpoint:** `POST /group_messages`

**Request Body:**
```json
{
  "group_id": 1,
  "sender_id": "user-uuid",
  "message": "Welcome to the group!",
  "message_type": "announcement",
  "channels": ["in_app", "email"],
  "metadata": {
    "priority": "high",
    "read_receipts": true
  }
}
```

## 🎯 Group Milestones API

### Get Group Milestones
Retrieve milestones for a group.

**Endpoint:** `GET /group_milestones?group_id=eq.{groupId}`

**Example Request:**
```javascript
const { data, error } = await supabase
  .from('group_milestones')
  .select('*')
  .eq('group_id', groupId)
  .order('due_date', { ascending: true })
```

### Create Milestone
Create a new milestone for a group.

**Endpoint:** `POST /group_milestones`

**Request Body:**
```json
{
  "group_id": 1,
  "title": "Complete JavaScript Fundamentals",
  "description": "All members should complete the course with 80% score",
  "due_date": "2024-02-15T00:00:00Z",
  "completion_criteria": {
    "completion_rate": 80,
    "minimum_score": 80
  },
  "created_by": "admin-uuid"
}
```

## 📋 Assignment Templates API

### Get Templates
Retrieve assignment templates for a tenant.

**Endpoint:** `GET /assignment_templates?tenant_id=eq.{tenantId}`

### Create Template
Create a new assignment template.

**Endpoint:** `POST /assignment_templates`

**Request Body:**
```json
{
  "tenant_id": 1,
  "name": "Standard Onboarding Template",
  "description": "Default template for new employee onboarding",
  "template_data": {
    "paths": [
      {
        "path_id": 1,
        "is_mandatory": true,
        "due_date_offset_days": 30
      }
    ],
    "default_settings": {
      "notifications": {
        "email_enabled": true
      }
    }
  },
  "created_by": "admin-uuid"
}
```

## 🔧 RPC Functions

### Refresh Group Progress
```javascript
const { data, error } = await supabase.rpc('refresh_group_progress', {
  group_id: 1 // Optional: refresh specific group, or null for all
})
```

### Bulk Operations
```javascript
// Bulk assign paths
const { data, error } = await supabase.rpc('bulk_assign_paths', {
  group_ids: [1, 2, 3],
  path_ids: [5, 6],
  settings: { is_mandatory: true }
})

// Bulk update member roles
const { data, error } = await supabase.rpc('bulk_update_member_roles', {
  member_ids: [1, 2, 3],
  new_role: 'moderator'
})
```

## 🔄 Real-time Subscriptions

### Subscribe to Group Changes
```javascript
const groupsSubscription = supabase
  .channel('groups-changes')
  .on('postgres_changes', 
    { 
      event: '*', 
      schema: 'public', 
      table: 'groups',
      filter: `tenant_id=eq.${tenantId}`
    }, 
    (payload) => {
      console.log('Group changed:', payload)
    }
  )
  .subscribe()
```

### Subscribe to Progress Updates
```javascript
const progressSubscription = supabase
  .channel('progress-updates')
  .on('postgres_changes', 
    { 
      event: 'UPDATE', 
      schema: 'public', 
      table: 'group_progress',
      filter: `group_id=eq.${groupId}`
    }, 
    (payload) => {
      console.log('Progress updated:', payload)
    }
  )
  .subscribe()
```

### Subscribe to Group Messages
```javascript
const messagesSubscription = supabase
  .channel('group-messages')
  .on('postgres_changes', 
    { 
      event: 'INSERT', 
      schema: 'public', 
      table: 'group_messages',
      filter: `group_id=eq.${groupId}`
    }, 
    (payload) => {
      console.log('New message:', payload)
    }
  )
  .subscribe()
```

## ❌ Error Handling

### Common Error Responses

**400 Bad Request:**
```json
{
  "error": {
    "message": "Invalid input data",
    "details": "Field 'name' is required",
    "hint": "Check the request body format"
  }
}
```

**403 Forbidden:**
```json
{
  "error": {
    "message": "Insufficient permissions",
    "details": "User cannot access this tenant's data"
  }
}
```

**404 Not Found:**
```json
{
  "error": {
    "message": "Resource not found",
    "details": "Group with ID 123 does not exist"
  }
}
```

### Error Handling Best Practices

```javascript
try {
  const { data, error } = await supabase
    .from('groups')
    .select('*')
    .eq('id', groupId)
    .single()
  
  if (error) {
    throw error
  }
  
  return data
} catch (error) {
  console.error('API Error:', error.message)
  // Handle specific error types
  if (error.code === 'PGRST116') {
    // No rows returned
    throw new Error('Group not found')
  }
  throw error
}
```

This API reference provides comprehensive documentation for integrating with the Groups and Batches feature. For more examples and advanced usage patterns, see the [Examples Documentation](../examples/basic-usage.md).
