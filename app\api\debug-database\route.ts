import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Debugging database state...')

    // Check tenants
    const { data: tenants, error: tenantsError } = await supabaseAdmin
      .from('tenants')
      .select('id, name')
      .order('id')

    if (tenantsError) {
      console.error('Error fetching tenants:', tenantsError)
    }

    // Check roles for tenant_id = 3
    const { data: roles, error: rolesError } = await supabaseAdmin
      .from('roles')
      .select('id, name, tenant_id, is_default')
      .eq('tenant_id', 3)
      .order('id')

    if (rolesError) {
      console.error('Error fetching roles:', rolesError)
    }

    // Check if there's a default role for tenant_id = 3
    const { data: defaultRole, error: defaultRoleError } = await supabaseAdmin
      .from('roles')
      .select('id, name, tenant_id, is_default')
      .eq('tenant_id', 3)
      .eq('is_default', true)
      .single()

    if (defaultRoleError && defaultRoleError.code !== 'PGRST116') {
      console.error('Error fetching default role:', defaultRoleError)
    }

    // Check current trigger function
    const { data: triggerFunction, error: triggerError } = await supabaseAdmin
      .rpc('get_function_definition', { function_name: 'handle_new_user' })
      .single()

    if (triggerError) {
      console.error('Error fetching trigger function:', triggerError)
    }

    // Check existing users count
    const { count: usersCount, error: usersCountError } = await supabaseAdmin
      .from('users')
      .select('*', { count: 'exact', head: true })

    if (usersCountError) {
      console.error('Error counting users:', usersCountError)
    }

    return NextResponse.json({
      success: true,
      data: {
        tenants: tenants || [],
        roles: roles || [],
        defaultRole: defaultRole || null,
        triggerFunction: triggerFunction || null,
        usersCount: usersCount || 0,
        errors: {
          tenants: tenantsError?.message || null,
          roles: rolesError?.message || null,
          defaultRole: defaultRoleError?.message || null,
          trigger: triggerError?.message || null,
          usersCount: usersCountError?.message || null
        }
      }
    })

  } catch (error) {
    console.error('Debug database error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
