'use client'

import React from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Switch,
  FormControlLabel,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Button
} from '@mui/material'
import {
  Analytics as AnalyticsIcon,
  Schedule as ScheduleIcon,
  Download as DownloadIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon
} from '@mui/icons-material'
import { motion } from 'framer-motion'

const scheduledReports = [
  {
    id: '1',
    name: 'Weekly Learning Progress',
    type: 'Progress Report',
    schedule: 'Every Monday at 9:00 AM',
    recipients: ['<EMAIL>', '<EMAIL>'],
    enabled: true,
    lastSent: '2024-01-15T09:00:00Z'
  },
  {
    id: '2',
    name: 'Monthly Completion Summary',
    type: 'Completion Report',
    schedule: 'First day of month at 8:00 AM',
    recipients: ['<EMAIL>'],
    enabled: true,
    lastSent: '2024-01-01T08:00:00Z'
  },
  {
    id: '3',
    name: 'Quarterly Analytics Dashboard',
    type: 'Analytics Report',
    schedule: 'Every quarter',
    recipients: ['<EMAIL>', '<EMAIL>'],
    enabled: false,
    lastSent: null
  }
]

export default function AnalyticsSettings() {
  return (
    <Grid container spacing={3}>
      {/* Data Collection Settings */}
      <Grid item xs={12}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={3}>
                <AnalyticsIcon sx={{ mr: 1 }} />
                <Typography variant="h6" fontWeight="bold">
                  Data Collection & Privacy
                </Typography>
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Collect learning analytics"
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 4 }}>
                    Track user progress, time spent, and completion rates
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Collect usage analytics"
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 4 }}>
                    Track feature usage and platform interactions
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={<Switch />}
                    label="Collect performance analytics"
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 4 }}>
                    Monitor system performance and load times
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={<Switch />}
                    label="Anonymous usage statistics"
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 4 }}>
                    Share anonymized data to improve the platform
                  </Typography>
                </Grid>
              </Grid>

              <Alert severity="info" sx={{ mt: 2 }}>
                All data collection complies with GDPR and other privacy regulations. Users can opt-out of non-essential tracking.
              </Alert>
            </CardContent>
          </Card>
        </motion.div>
      </Grid>

      {/* Data Retention */}
      <Grid item xs={12}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Data Retention Policies
              </Typography>

              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>Analytics Data Retention</InputLabel>
                    <Select defaultValue="2years">
                      <MenuItem value="6months">6 Months</MenuItem>
                      <MenuItem value="1year">1 Year</MenuItem>
                      <MenuItem value="2years">2 Years</MenuItem>
                      <MenuItem value="5years">5 Years</MenuItem>
                      <MenuItem value="indefinite">Indefinite</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>User Activity Logs</InputLabel>
                    <Select defaultValue="1year">
                      <MenuItem value="3months">3 Months</MenuItem>
                      <MenuItem value="6months">6 Months</MenuItem>
                      <MenuItem value="1year">1 Year</MenuItem>
                      <MenuItem value="2years">2 Years</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>Performance Metrics</InputLabel>
                    <Select defaultValue="6months">
                      <MenuItem value="1month">1 Month</MenuItem>
                      <MenuItem value="3months">3 Months</MenuItem>
                      <MenuItem value="6months">6 Months</MenuItem>
                      <MenuItem value="1year">1 Year</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </motion.div>
      </Grid>

      {/* Export Settings */}
      <Grid item xs={12}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Data Export Settings
              </Typography>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Available Export Formats
                  </Typography>
                  <Box display="flex" gap={1} flexWrap="wrap" mb={2}>
                    <Chip label="CSV" color="primary" />
                    <Chip label="Excel" color="primary" />
                    <Chip label="PDF" color="primary" />
                    <Chip label="JSON" variant="outlined" />
                  </Box>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Default Export Format</InputLabel>
                    <Select defaultValue="csv">
                      <MenuItem value="csv">CSV</MenuItem>
                      <MenuItem value="excel">Excel</MenuItem>
                      <MenuItem value="pdf">PDF</MenuItem>
                      <MenuItem value="json">JSON</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Include user personal data in exports (requires admin approval)"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </motion.div>
      </Grid>

      {/* Scheduled Reports */}
      <Grid item xs={12}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Box display="flex" alignItems="center">
                  <ScheduleIcon sx={{ mr: 1 }} />
                  <Typography variant="h6" fontWeight="bold">
                    Scheduled Reports
                  </Typography>
                </Box>
                <Button variant="contained" startIcon={<AddIcon />} size="small">
                  Add Report
                </Button>
              </Box>

              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Report Name</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell>Schedule</TableCell>
                      <TableCell>Recipients</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Last Sent</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {scheduledReports.map((report) => (
                      <TableRow key={report.id}>
                        <TableCell>
                          <Typography fontWeight="medium">
                            {report.name}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip label={report.type} size="small" variant="outlined" />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {report.schedule}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {report.recipients.length} recipient{report.recipients.length !== 1 ? 's' : ''}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={report.enabled ? 'Active' : 'Inactive'}
                            color={report.enabled ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" color="text.secondary">
                            {report.lastSent 
                              ? new Date(report.lastSent).toLocaleDateString()
                              : 'Never'
                            }
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <IconButton size="small">
                            <EditIcon />
                          </IconButton>
                          <IconButton size="small">
                            <DownloadIcon />
                          </IconButton>
                          <IconButton size="small" color="error">
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </motion.div>
      </Grid>

      {/* Custom Metrics */}
      <Grid item xs={12}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Custom Analytics Metrics
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                        Engagement Score
                      </Typography>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Custom metric combining time spent, interactions, and completion rates
                      </Typography>
                      <Box mt={2}>
                        <Chip label="Active" color="success" size="small" />
                        <Chip label="Custom Formula" variant="outlined" size="small" sx={{ ml: 1 }} />
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                        Learning Velocity
                      </Typography>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Rate of learning progress over time periods
                      </Typography>
                      <Box mt={2}>
                        <Chip label="Active" color="success" size="small" />
                        <Chip label="Time-based" variant="outlined" size="small" sx={{ ml: 1 }} />
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              <Alert severity="info" sx={{ mt: 2 }}>
                Custom metrics can be created using our analytics query builder. Contact support for advanced metric configuration.
              </Alert>
            </CardContent>
          </Card>
        </motion.div>
      </Grid>
    </Grid>
  )
}
