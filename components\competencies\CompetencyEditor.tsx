'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Box,
  Typography,
  Switch,
  FormControlLabel,
  Autocomplete,
  Alert,
  Divider,
} from '@mui/material'
import { LoadingButton } from '@mui/lab'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query'
import toast from 'react-hot-toast'

import { supabase } from '@/lib/supabase'
import { useAuthStore } from '@/lib/store'
import RichTextEditor from '@/components/common/RichTextEditor'

const competencySchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name too long'),
  description: z.string().optional(),
  category: z.string().optional(),
  tags: z.array(z.string()).default([]),
  status: z.enum(['active', 'inactive', 'draft']).default('active'),
  is_public: z.boolean().default(true),
  level_definitions: z.record(z.string()).optional(),
})

type CompetencyFormData = z.infer<typeof competencySchema>

interface CompetencyEditorProps {
  open: boolean
  competency?: any
  onClose: () => void
}

const PREDEFINED_CATEGORIES = [
  'Technical Skills',
  'Leadership',
  'Communication',
  'Project Management',
  'Data Analysis',
  'Software Development',
  'Marketing',
  'Sales',
  'Customer Service',
  'Finance',
  'HR',
  'Operations',
  'Strategy',
  'Innovation',
  'Compliance',
]

const PREDEFINED_TAGS = [
  'AI/ML',
  'Data Science',
  'Web Development',
  'Mobile Development',
  'Cloud Computing',
  'Cybersecurity',
  'DevOps',
  'UI/UX',
  'Database',
  'Analytics',
  'Agile',
  'Scrum',
  'Leadership',
  'Communication',
  'Problem Solving',
  'Critical Thinking',
  'Teamwork',
  'Time Management',
  'Presentation',
  'Negotiation',
]

export default function CompetencyEditor({ open, competency, onClose }: CompetencyEditorProps) {
  const { user } = useAuthStore()
  const queryClient = useQueryClient()
  const [description, setDescription] = useState('')

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isValid },
  } = useForm<CompetencyFormData>({
    resolver: zodResolver(competencySchema),
    defaultValues: {
      name: '',
      description: '',
      category: '',
      tags: [],
      status: 'active',
      is_public: true,
      level_definitions: {},
    },
  })

  const watchedTags = watch('tags')

  // Fetch existing categories for autocomplete
  const { data: existingCategories = [] } = useQuery({
    queryKey: ['competency-categories', user?.tenant_id],
    queryFn: async () => {
      if (!user?.tenant_id) return []

      const { data } = await supabase
        .from('competencies')
        .select('category')
        .eq('tenant_id', user.tenant_id)
        .not('category', 'is', null)

      const uniqueCategories = [...new Set(data?.map(item => item.category).filter(Boolean))]
      return [...new Set([...PREDEFINED_CATEGORIES, ...uniqueCategories])]
    },
    enabled: !!user?.tenant_id && open,
  })

  // Create/Update mutation
  const saveMutation = useMutation({
    mutationFn: async (data: CompetencyFormData) => {
      const payload = {
        ...data,
        description,
        tenant_id: user?.tenant_id,
        created_by: user?.id,
        metadata: {
          level_definitions: data.level_definitions || {},
        },
      }

      if (competency?.id) {
        // Update existing competency
        const { error } = await supabase
          .from('competencies')
          .update(payload)
          .eq('id', competency.id)
          .eq('tenant_id', user?.tenant_id)

        if (error) throw error
      } else {
        // Create new competency
        const { error } = await supabase
          .from('competencies')
          .insert([payload])

        if (error) throw error
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['competencies'] })
      queryClient.invalidateQueries({ queryKey: ['competency-stats'] })
      toast.success(competency?.id ? 'Competency updated successfully' : 'Competency created successfully')
      onClose()
    },
    onError: (error) => {
      console.error('Save error:', error)
      toast.error('Failed to save competency')
    },
  })

  // Reset form when dialog opens/closes or competency changes
  useEffect(() => {
    if (open) {
      if (competency) {
        reset({
          name: competency.name || '',
          description: competency.description || '',
          category: competency.category || '',
          tags: competency.tags || [],
          status: competency.status || 'active',
          is_public: competency.is_public ?? true,
          level_definitions: competency.metadata?.level_definitions || {},
        })
        setDescription(competency.description || '')
      } else {
        reset({
          name: '',
          description: '',
          category: '',
          tags: [],
          status: 'active',
          is_public: true,
          level_definitions: {},
        })
        setDescription('')
      }
    }
  }, [open, competency, reset])

  const onSubmit = (data: CompetencyFormData) => {
    saveMutation.mutate(data)
  }

  const handleClose = () => {
    if (!saveMutation.isPending) {
      onClose()
    }
  }

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { minHeight: '70vh' }
      }}
    >
      <DialogTitle>
        {competency?.id ? 'Edit Competency' : 'Create New Competency'}
      </DialogTitle>

      <form onSubmit={handleSubmit(onSubmit)}>
        <DialogContent dividers>
          <Box display="flex" flexDirection="column" gap={3}>
            {/* Basic Information */}
            <Box>
              <Typography variant="h6" gutterBottom>
                Basic Information
              </Typography>
              
              <Box display="flex" flexDirection="column" gap={2}>
                <Controller
                  name="name"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Competency Name"
                      fullWidth
                      required
                      error={!!errors.name}
                      helperText={errors.name?.message}
                      placeholder="e.g., JavaScript Programming, Leadership Skills"
                    />
                  )}
                />

                <Controller
                  name="category"
                  control={control}
                  render={({ field }) => (
                    <Autocomplete
                      {...field}
                      options={existingCategories}
                      freeSolo
                      value={field.value || ''}
                      onChange={(_, value) => field.onChange(value || '')}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Category"
                          placeholder="Select or enter a category"
                        />
                      )}
                    />
                  )}
                />

                <Box>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    Description
                  </Typography>
                  <RichTextEditor
                    content={description}
                    onChange={setDescription}
                    placeholder="Describe this competency, its importance, and what it encompasses..."
                  />
                </Box>
              </Box>
            </Box>

            <Divider />

            {/* Tags and Classification */}
            <Box>
              <Typography variant="h6" gutterBottom>
                Tags and Classification
              </Typography>
              
              <Controller
                name="tags"
                control={control}
                render={({ field }) => (
                  <Autocomplete
                    {...field}
                    multiple
                    options={PREDEFINED_TAGS}
                    freeSolo
                    value={field.value || []}
                    onChange={(_, value) => field.onChange(value)}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip
                          variant="outlined"
                          label={option}
                          {...getTagProps({ index })}
                          key={option}
                        />
                      ))
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Tags"
                        placeholder="Add tags to categorize this competency"
                        helperText="Press Enter to add custom tags"
                      />
                    )}
                  />
                )}
              />
            </Box>

            <Divider />

            {/* Settings */}
            <Box>
              <Typography variant="h6" gutterBottom>
                Settings
              </Typography>
              
              <Box display="flex" flexDirection="column" gap={2}>
                <Controller
                  name="status"
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth>
                      <InputLabel>Status</InputLabel>
                      <Select {...field} label="Status">
                        <MenuItem value="active">Active</MenuItem>
                        <MenuItem value="draft">Draft</MenuItem>
                        <MenuItem value="inactive">Inactive</MenuItem>
                      </Select>
                    </FormControl>
                  )}
                />

                <Controller
                  name="is_public"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={
                        <Switch
                          checked={field.value}
                          onChange={field.onChange}
                        />
                      }
                      label="Make this competency visible to all learners"
                    />
                  )}
                />
              </Box>
            </Box>

            {/* Preview */}
            {watchedTags.length > 0 && (
              <Box>
                <Typography variant="body2" color="textSecondary" gutterBottom>
                  Tags Preview:
                </Typography>
                <Box display="flex" gap={1} flexWrap="wrap">
                  {watchedTags.map((tag, index) => (
                    <Chip
                      key={index}
                      label={tag}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                  ))}
                </Box>
              </Box>
            )}
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 2 }}>
          <Button onClick={handleClose} disabled={saveMutation.isPending}>
            Cancel
          </Button>
          <LoadingButton
            type="submit"
            variant="contained"
            loading={saveMutation.isPending}
            disabled={!isValid}
          >
            {competency?.id ? 'Update' : 'Create'} Competency
          </LoadingButton>
        </DialogActions>
      </form>
    </Dialog>
  )
}
