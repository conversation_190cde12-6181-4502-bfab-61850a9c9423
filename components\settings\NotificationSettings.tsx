'use client'

import React from 'react'
import {
  Box,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Grid,
  Switch,
  FormControlLabel,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material'
import {
  ExpandMore as ExpandMoreIcon,
  Email as EmailIcon,
  Sms as SmsIcon,
  Notifications as PushIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material'
import { motion } from 'framer-motion'

const notificationCategories = [
  {
    title: 'Email Notifications',
    icon: <EmailIcon />,
    description: 'Configure email notification settings',
    settings: [
      { key: 'user_registration', name: 'User Registration', description: 'Welcome emails for new users', enabled: true },
      { key: 'course_completion', name: 'Course Completion', description: 'Congratulations on course completion', enabled: true },
      { key: 'assignment_due', name: 'Assignment Due', description: 'Reminders for upcoming deadlines', enabled: true },
      { key: 'progress_reports', name: 'Progress Reports', description: 'Weekly progress summaries', enabled: false },
      { key: 'system_updates', name: 'System Updates', description: 'Platform updates and maintenance', enabled: true }
    ]
  },
  {
    title: 'Push Notifications',
    icon: <PushIcon />,
    description: 'Browser and mobile push notifications',
    settings: [
      { key: 'new_content', name: 'New Content Available', description: 'Notify when new courses are published', enabled: false },
      { key: 'discussion_replies', name: 'Discussion Replies', description: 'Replies to forum discussions', enabled: true },
      { key: 'achievement_unlocked', name: 'Achievement Unlocked', description: 'Badge and milestone notifications', enabled: true },
      { key: 'live_sessions', name: 'Live Sessions', description: 'Upcoming live training sessions', enabled: false }
    ]
  },
  {
    title: 'SMS Notifications',
    icon: <SmsIcon />,
    description: 'Text message notifications (Premium feature)',
    settings: [
      { key: 'urgent_alerts', name: 'Urgent Alerts', description: 'Critical system alerts only', enabled: false },
      { key: 'exam_reminders', name: 'Exam Reminders', description: 'Important exam notifications', enabled: false },
      { key: 'security_alerts', name: 'Security Alerts', description: 'Login and security notifications', enabled: false }
    ]
  }
]

export default function NotificationSettings() {
  return (
    <Grid container spacing={3}>
      {/* Global Settings */}
      <Grid item xs={12}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Global Notification Settings
              </Typography>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Default Timezone</InputLabel>
                    <Select defaultValue="UTC">
                      <MenuItem value="UTC">UTC</MenuItem>
                      <MenuItem value="America/New_York">Eastern Time</MenuItem>
                      <MenuItem value="America/Los_Angeles">Pacific Time</MenuItem>
                      <MenuItem value="Europe/London">GMT</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Notification Frequency</InputLabel>
                    <Select defaultValue="immediate">
                      <MenuItem value="immediate">Immediate</MenuItem>
                      <MenuItem value="hourly">Hourly Digest</MenuItem>
                      <MenuItem value="daily">Daily Digest</MenuItem>
                      <MenuItem value="weekly">Weekly Digest</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Allow users to customize their notification preferences"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </motion.div>
      </Grid>

      {/* Notification Categories */}
      {notificationCategories.map((category, index) => (
        <Grid item xs={12} key={category.title}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Accordion defaultExpanded={index === 0}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box display="flex" alignItems="center" width="100%">
                  <Box sx={{ mr: 2, color: 'primary.main' }}>
                    {category.icon}
                  </Box>
                  <Box flexGrow={1}>
                    <Typography variant="h6" fontWeight="bold">
                      {category.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {category.description}
                    </Typography>
                  </Box>
                  <Chip 
                    label={`${category.settings.filter(s => s.enabled).length}/${category.settings.length} enabled`}
                    size="small"
                    color="primary"
                    variant="outlined"
                  />
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={2}>
                  {category.settings.map((setting) => (
                    <Grid item xs={12} key={setting.key}>
                      <Card variant="outlined">
                        <CardContent sx={{ py: 2 }}>
                          <Box display="flex" justifyContent="space-between" alignItems="center">
                            <Box flexGrow={1}>
                              <Typography variant="subtitle1" fontWeight="medium">
                                {setting.name}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                {setting.description}
                              </Typography>
                            </Box>
                            <Switch
                              checked={setting.enabled}
                              color="primary"
                            />
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>

                {/* Category-specific settings */}
                {category.title === 'Email Notifications' && (
                  <Box mt={3}>
                    <Typography variant="subtitle2" gutterBottom>
                      Email Configuration
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="From Email Address"
                          defaultValue="<EMAIL>"
                          size="small"
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="From Name"
                          defaultValue="ZenithLearn AI"
                          size="small"
                        />
                      </Grid>
                    </Grid>
                  </Box>
                )}

                {category.title === 'SMS Notifications' && (
                  <Alert severity="info" sx={{ mt: 2 }}>
                    SMS notifications require a premium subscription and additional setup. Contact support for more information.
                  </Alert>
                )}
              </AccordionDetails>
            </Accordion>
          </motion.div>
        </Grid>
      ))}

      {/* Quiet Hours */}
      <Grid item xs={12}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={3}>
                <ScheduleIcon sx={{ mr: 1 }} />
                <Typography variant="h6" fontWeight="bold">
                  Quiet Hours
                </Typography>
              </Box>

              <FormControlLabel
                control={<Switch />}
                label="Enable quiet hours (no notifications during specified times)"
                sx={{ mb: 2 }}
              />

              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Start Time"
                    type="time"
                    defaultValue="22:00"
                    size="small"
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="End Time"
                    type="time"
                    defaultValue="08:00"
                    size="small"
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
              </Grid>

              <Alert severity="info" sx={{ mt: 2 }}>
                Quiet hours apply to all non-urgent notifications. Critical security alerts will still be sent.
              </Alert>
            </CardContent>
          </Card>
        </motion.div>
      </Grid>

      {/* Notification Templates */}
      <Grid item xs={12}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.5 }}
        >
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Email Templates
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                        Welcome Email
                      </Typography>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Sent to new users upon registration
                      </Typography>
                      <Box mt={2}>
                        <Chip label="Active" color="success" size="small" />
                        <Chip label="Customizable" variant="outlined" size="small" sx={{ ml: 1 }} />
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                        Course Completion
                      </Typography>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Congratulations message with certificate
                      </Typography>
                      <Box mt={2}>
                        <Chip label="Active" color="success" size="small" />
                        <Chip label="Customizable" variant="outlined" size="small" sx={{ ml: 1 }} />
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                        Password Reset
                      </Typography>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Security-related password reset instructions
                      </Typography>
                      <Box mt={2}>
                        <Chip label="Active" color="success" size="small" />
                        <Chip label="System Template" variant="outlined" size="small" sx={{ ml: 1 }} />
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                        Weekly Progress Report
                      </Typography>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Summary of learning activities and progress
                      </Typography>
                      <Box mt={2}>
                        <Chip label="Inactive" color="default" size="small" />
                        <Chip label="Customizable" variant="outlined" size="small" sx={{ ml: 1 }} />
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </motion.div>
      </Grid>
    </Grid>
  )
}
