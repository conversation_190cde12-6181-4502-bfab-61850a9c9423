'use client'

import { useState, useRef, useEffect } from 'react'
import {
  Card,
  CardContent,
  Typography,
  Box,
  TextField,
  IconButton,
  Avatar,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  useTheme,
  CircularProgress,
} from '@mui/material'
import {
  Send as SendIcon,
  Psychology as AIIcon,
  Person as PersonIcon,
  Lightbulb as LightbulbIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
} from '@mui/icons-material'
import { motion, AnimatePresence } from 'framer-motion'

interface Message {
  id: string
  type: 'user' | 'ai'
  content: string
  timestamp: Date
  suggestions?: string[]
  insights?: {
    type: 'recommendation' | 'alert' | 'trend' | 'success'
    title: string
    description: string
  }[]
}

const aiSuggestions = [
  "Show me user engagement trends",
  "What courses need attention?",
  "Generate a weekly report",
  "How can I improve completion rates?",
  "Show me top performing learners",
]

const mockAIResponses = {
  "engagement": "Based on your data, user engagement has increased by 15% this month. The top performing courses are JavaScript Fundamentals and React Development. I recommend creating similar content for Python and Data Science.",
  "courses": "3 courses need immediate attention: 'Advanced CSS' has a 45% completion rate, 'Database Design' has high dropout after lesson 3, and 'Project Management' needs updated content.",
  "report": "I'll generate your weekly report now. This week: 247 active users (+12%), 78% average completion rate (+5%), and 15 new course enrollments. Full report will be ready in 2 minutes.",
  "completion": "To improve completion rates, consider: 1) Adding progress checkpoints, 2) Implementing peer learning groups, 3) Sending personalized reminders, 4) Creating bite-sized lessons.",
  "performers": "Top 5 learners this week: Sarah Chen (95% completion), Mike Johnson (12 courses completed), Lisa Wang (highest engagement score), David Brown (peer mentor), Emma Davis (fastest learner).",
}

export default function AIAssistantWidget() {
  const theme = useTheme()
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'ai',
      content: "Hello! I'm ZenithBot, your AI assistant. I can help you analyze learner data, generate insights, and optimize your learning programs. What would you like to know?",
      timestamp: new Date(),
      insights: [
        {
          type: 'recommendation',
          title: 'Course Optimization',
          description: 'Consider adding interactive elements to boost engagement'
        }
      ]
    }
  ])
  const [inputValue, setInputValue] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date(),
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsTyping(true)

    // Simulate AI processing
    setTimeout(() => {
      const aiResponse = generateAIResponse(inputValue)
      setMessages(prev => [...prev, aiResponse])
      setIsTyping(false)
    }, 1500)
  }

  const generateAIResponse = (input: string): Message => {
    const lowerInput = input.toLowerCase()
    let response = "I understand your question. Let me analyze your data and provide insights."
    let insights = []

    if (lowerInput.includes('engagement') || lowerInput.includes('trend')) {
      response = mockAIResponses.engagement
      insights = [
        { type: 'trend', title: 'Engagement Up 15%', description: 'JavaScript courses leading growth' },
        { type: 'recommendation', title: 'Expand Popular Topics', description: 'Create more JS and React content' }
      ]
    } else if (lowerInput.includes('course') || lowerInput.includes('attention')) {
      response = mockAIResponses.courses
      insights = [
        { type: 'alert', title: 'Low Completion Alert', description: 'Advanced CSS needs review' },
        { type: 'recommendation', title: 'Content Update', description: 'Refresh Project Management materials' }
      ]
    } else if (lowerInput.includes('report')) {
      response = mockAIResponses.report
      insights = [
        { type: 'success', title: 'Weekly Growth', description: '12% increase in active users' }
      ]
    } else if (lowerInput.includes('completion')) {
      response = mockAIResponses.completion
      insights = [
        { type: 'recommendation', title: 'Gamification', description: 'Add progress checkpoints and rewards' }
      ]
    } else if (lowerInput.includes('performer') || lowerInput.includes('learner')) {
      response = mockAIResponses.performers
      insights = [
        { type: 'success', title: 'Top Performers', description: '5 learners exceeding expectations' }
      ]
    }

    return {
      id: Date.now().toString(),
      type: 'ai',
      content: response,
      timestamp: new Date(),
      suggestions: aiSuggestions.filter(s => !s.toLowerCase().includes(lowerInput.split(' ')[0])).slice(0, 3),
      insights: insights as any
    }
  }

  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion)
  }

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'recommendation': return <LightbulbIcon color="warning" />
      case 'alert': return <WarningIcon color="error" />
      case 'trend': return <TrendingUpIcon color="info" />
      case 'success': return <CheckIcon color="success" />
      default: return <AIIcon color="primary" />
    }
  }

  return (
    <Card sx={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      <CardContent sx={{
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
        p: 2,
        height: '100%',
        overflow: 'hidden'
      }}>
        <Box display="flex" alignItems="center" gap={2} mb={2}>
          <Avatar sx={{ bgcolor: 'primary.main', width: 32, height: 32 }}>
            <AIIcon fontSize="small" />
          </Avatar>
          <Typography variant="h6" fontWeight="bold">
            ZenithBot Assistant
          </Typography>
          <Chip label="AI" color="primary" size="small" />
        </Box>

        <Box
          sx={{
            flexGrow: 1,
            overflowY: 'auto',
            mb: 2,
            minHeight: 0,
            '&::-webkit-scrollbar': { width: 4 },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: theme.palette.divider,
              borderRadius: 2
            }
          }}
        >
          <List dense>
            <AnimatePresence>
              {messages.map((message) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                    <ListItemAvatar>
                      <Avatar sx={{ 
                        width: 24, 
                        height: 24,
                        bgcolor: message.type === 'ai' ? 'primary.main' : 'secondary.main'
                      }}>
                        {message.type === 'ai' ? <AIIcon fontSize="small" /> : <PersonIcon fontSize="small" />}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Typography variant="body2" sx={{ wordBreak: 'break-word' }}>
                          {message.content}
                        </Typography>
                      }
                      secondary={
                        <Box>
                          <Typography variant="caption" color="text.secondary">
                            {message.timestamp.toLocaleTimeString()}
                          </Typography>
                          
                          {message.insights && message.insights.length > 0 && (
                            <Box mt={1}>
                              {message.insights.map((insight, index) => (
                                <Chip
                                  key={index}
                                  icon={getInsightIcon(insight.type)}
                                  label={insight.title}
                                  size="small"
                                  sx={{ mr: 0.5, mb: 0.5 }}
                                  title={insight.description}
                                />
                              ))}
                            </Box>
                          )}
                          
                          {message.suggestions && (
                            <Box mt={1}>
                              {message.suggestions.map((suggestion, index) => (
                                <Chip
                                  key={index}
                                  label={suggestion}
                                  size="small"
                                  variant="outlined"
                                  clickable
                                  onClick={() => handleSuggestionClick(suggestion)}
                                  sx={{ mr: 0.5, mb: 0.5, fontSize: '0.7rem' }}
                                />
                              ))}
                            </Box>
                          )}
                        </Box>
                      }
                    />
                  </ListItem>
                  {message !== messages[messages.length - 1] && <Divider />}
                </motion.div>
              ))}
            </AnimatePresence>
            
            {isTyping && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
              >
                <ListItem sx={{ px: 0 }}>
                  <ListItemAvatar>
                    <Avatar sx={{ width: 24, height: 24, bgcolor: 'primary.main' }}>
                      <AIIcon fontSize="small" />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center" gap={1}>
                        <CircularProgress size={16} />
                        <Typography variant="body2" color="text.secondary">
                          ZenithBot is thinking...
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
              </motion.div>
            )}
            <div ref={messagesEndRef} />
          </List>
        </Box>

        <Box display="flex" gap={1}>
          <TextField
            fullWidth
            size="small"
            placeholder="Ask me anything about your learners..."
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            disabled={isTyping}
          />
          <IconButton 
            color="primary" 
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isTyping}
          >
            <SendIcon />
          </IconButton>
        </Box>
      </CardContent>
    </Card>
  )
}
