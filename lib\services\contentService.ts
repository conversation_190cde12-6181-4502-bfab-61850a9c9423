import { supabase } from '@/lib/supabase'
import { StorageService } from './storageService'
import type {
  ContentItem,
  ContentFilter,
  ContentUploadRequest,
  ContentUploadResult,
  ContentCategory,
  ContentSearchResult,
  ContentPreview,
  ContentUsageStats,
  ContentValidationResult,
  BulkContentOperation,
  ContentVersion,
  ContentReview,
  SmartFolder,
  ContentType
} from '@/lib/types/content'

export class ContentService {
  private static readonly TABLE_NAME = 'content_library'
  private static readonly CATEGORIES_TABLE = 'content_categories'
  private static readonly REVIEWS_TABLE = 'content_reviews'
  private static readonly VERSIONS_TABLE = 'content_versions'

  // Content CRUD Operations
  static async getContent(filter: ContentFilter = {}): Promise<ContentSearchResult> {
    try {
      let query = supabase
        .from(this.TABLE_NAME)
        .select(`
          *,
          category:content_categories(
            id,
            name,
            description,
            color,
            icon,
            sort_order
          )
        `, { count: 'exact' })

      // Apply filters
      if (filter.search) {
        query = query.or(`title.ilike.%${filter.search}%,description.ilike.%${filter.search}%`)
      }

      if (filter.type && filter.type.length > 0) {
        query = query.in('type', filter.type)
      }

      if (filter.category_id && filter.category_id.length > 0) {
        query = query.in('category_id', filter.category_id)
      }

      if (filter.tags && filter.tags.length > 0) {
        query = query.overlaps('tags', filter.tags)
      }

      if (filter.uploaded_by && filter.uploaded_by.length > 0) {
        query = query.in('uploaded_by', filter.uploaded_by)
      }

      if (filter.is_public !== undefined) {
        query = query.eq('is_public', filter.is_public)
      }

      if (filter.date_range) {
        query = query
          .gte('created_at', filter.date_range.start)
          .lte('created_at', filter.date_range.end)
      }

      if (filter.file_size_range) {
        query = query
          .gte('file_size', filter.file_size_range.min)
          .lte('file_size', filter.file_size_range.max)
      }

      // Apply sorting
      const sortBy = filter.sort_by || 'created_at'
      const sortOrder = filter.sort_order || 'desc'
      query = query.order(sortBy, { ascending: sortOrder === 'asc' })

      // Apply pagination
      const page = filter.page || 1
      const limit = filter.limit || 20
      const offset = (page - 1) * limit
      query = query.range(offset, offset + limit - 1)

      const { data, error, count } = await query

      if (error) throw error

      return {
        items: data || [],
        total_count: count || 0,
        page,
        limit,
        has_more: (count || 0) > offset + limit
      }
    } catch (error) {
      console.error('Error fetching content:', error)
      throw error
    }
  }

  static async getContentById(id: number): Promise<ContentItem | null> {
    try {
      const { data, error } = await supabase
        .from(this.TABLE_NAME)
        .select(`
          *,
          category:content_categories(
            id,
            name,
            description,
            color,
            icon,
            sort_order
          )
        `)
        .eq('id', id)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error fetching content by ID:', error)
      throw error
    }
  }

  static async uploadContent(request: ContentUploadRequest): Promise<ContentUploadResult> {
    try {
      // Upload file to storage
      const uploadResult = await StorageService.uploadFile(request.file)

      // Prepare metadata
      const metadata = {
        original_filename: request.file.name,
        file_extension: request.file.name.split('.').pop(),
        storage_path: uploadResult.path,
        processing_status: 'pending' as const
      }

      // Insert content record
      const { data, error } = await supabase
        .from(this.TABLE_NAME)
        .insert({
          title: request.title,
          description: request.description,
          type: request.type,
          file_url: uploadResult.url,
          file_size: request.file.size,
          mime_type: request.file.type,
          tags: request.tags || [],
          metadata,
          is_public: request.is_public || false
        })
        .select()
        .single()

      if (error) throw error

      // Process AI enhancements if requested
      let aiSuggestions
      if (request.auto_tag || request.auto_categorize) {
        aiSuggestions = await this.generateAISuggestions(data.id, request.file)
      }

      return {
        content_id: data.id,
        file_url: uploadResult.url,
        storage_path: uploadResult.path,
        metadata,
        processing_status: 'completed',
        ai_suggestions: aiSuggestions
      }
    } catch (error) {
      console.error('Error uploading content:', error)
      throw error
    }
  }

  static async updateContent(id: number, updates: Partial<ContentItem>): Promise<ContentItem> {
    try {
      const { data, error } = await supabase
        .from(this.TABLE_NAME)
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error updating content:', error)
      throw error
    }
  }

  static async deleteContent(id: number): Promise<void> {
    try {
      // Get content details first
      const content = await this.getContentById(id)
      if (!content) throw new Error('Content not found')

      // Delete file from storage
      if (content.metadata?.storage_path) {
        await StorageService.deleteFile(content.metadata.storage_path)
      }

      // Delete content record
      const { error } = await supabase
        .from(this.TABLE_NAME)
        .delete()
        .eq('id', id)

      if (error) throw error
    } catch (error) {
      console.error('Error deleting content:', error)
      throw error
    }
  }

  // Category Management
  static async getCategories(): Promise<ContentCategory[]> {
    try {
      const { data, error } = await supabase
        .from(this.CATEGORIES_TABLE)
        .select('*')
        .order('sort_order', { ascending: true })

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching categories:', error)
      throw error
    }
  }

  static async createCategory(category: Omit<ContentCategory, 'id' | 'created_at' | 'updated_at'>): Promise<ContentCategory> {
    try {
      const { data, error } = await supabase
        .from(this.CATEGORIES_TABLE)
        .insert(category)
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error creating category:', error)
      throw error
    }
  }

  // AI-Powered Features
  static async generateAISuggestions(contentId: number, file: File) {
    try {
      // This would call an Edge Function for AI processing
      const { data, error } = await supabase.functions.invoke('generate-content-tags', {
        body: {
          content_id: contentId,
          file_type: file.type,
          file_name: file.name
        }
      })

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error generating AI suggestions:', error)
      return null
    }
  }

  static async generateThumbnail(contentId: number): Promise<string | null> {
    try {
      const { data, error } = await supabase.functions.invoke('generate-thumbnail', {
        body: { content_id: contentId }
      })

      if (error) throw error
      return data?.thumbnail_url || null
    } catch (error) {
      console.error('Error generating thumbnail:', error)
      return null
    }
  }

  // Content Preview
  static async getContentPreview(id: number): Promise<ContentPreview | null> {
    try {
      const content = await this.getContentById(id)
      if (!content) return null

      // Generate preview based on content type
      switch (content.type) {
        case 'pdf':
          return {
            content_id: id,
            preview_type: 'pdf_preview',
            preview_url: content.file_url,
            page_count: content.metadata?.pdf_pages
          }
        case 'video':
          return {
            content_id: id,
            preview_type: 'video_preview',
            preview_url: content.file_url,
            duration: content.duration
          }
        case 'image':
          return {
            content_id: id,
            preview_type: 'thumbnail',
            preview_url: content.file_url
          }
        default:
          return {
            content_id: id,
            preview_type: 'text',
            text_excerpt: content.description || content.title
          }
      }
    } catch (error) {
      console.error('Error getting content preview:', error)
      return null
    }
  }

  // Bulk Operations
  static async bulkOperation(operation: BulkContentOperation): Promise<void> {
    try {
      switch (operation.operation) {
        case 'delete':
          const { error: deleteError } = await supabase
            .from(this.TABLE_NAME)
            .delete()
            .in('id', operation.content_ids)
          if (deleteError) throw deleteError
          break
        case 'update_category':
          const { error: categoryError } = await supabase
            .from(this.TABLE_NAME)
            .update({ category_id: operation.parameters?.category_id })
            .in('id', operation.content_ids)
          if (categoryError) throw categoryError
          break
        case 'update_tags':
          const { error: tagsError } = await supabase
            .from(this.TABLE_NAME)
            .update({ tags: operation.parameters?.tags })
            .in('id', operation.content_ids)
          if (tagsError) throw tagsError
          break
        case 'update_visibility':
          const { error: visibilityError } = await supabase
            .from(this.TABLE_NAME)
            .update({ is_public: operation.parameters?.is_public })
            .in('id', operation.content_ids)
          if (visibilityError) throw visibilityError
          break
      }
    } catch (error) {
      console.error('Error performing bulk operation:', error)
      throw error
    }
  }

  // Usage Analytics
  static async getContentStats(id: number): Promise<ContentUsageStats | null> {
    try {
      // This would typically come from a separate analytics table
      // For now, return mock data structure
      return {
        content_id: id,
        total_views: 0,
        total_downloads: 0,
        unique_viewers: 0,
        engagement_score: 0,
        last_accessed: new Date().toISOString(),
        popular_tags: [],
        usage_by_date: []
      }
    } catch (error) {
      console.error('Error fetching content stats:', error)
      return null
    }
  }

  // Content Validation
  static async validateContent(id: number): Promise<ContentValidationResult> {
    try {
      const { data, error } = await supabase.functions.invoke('validate-content', {
        body: { content_id: id }
      })

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error validating content:', error)
      return {
        content_id: id,
        is_valid: false,
        validation_errors: ['Validation service unavailable'],
        validation_warnings: [],
        compliance_issues: [],
        quality_score: 0,
        recommendations: []
      }
    }
  }
}
