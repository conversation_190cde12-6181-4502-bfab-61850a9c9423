import { z } from 'zod'
import { LearningPath, Module, Lesson, PathCreationForm } from '@/lib/types/learning-paths'

// Validation schemas
export const pathCreationSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  description: z.string().min(1, 'Description is required').max(2000, 'Description must be less than 2000 characters'),
  objectives: z.array(z.string().min(1, 'Objective cannot be empty')).min(1, 'At least one objective is required'),
  duration: z.number().min(1, 'Duration must be at least 1 week').max(52, 'Duration cannot exceed 52 weeks'),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']),
  category: z.string().min(1, 'Category is required'),
  tags: z.array(z.string()).optional(),
  is_template: z.boolean().optional()
})

export const moduleSchema = z.object({
  title: z.string().min(1, 'Module title is required').max(200, 'Title must be less than 200 characters'),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  is_optional: z.boolean().optional(),
  estimated_duration: z.number().min(1, 'Duration must be at least 1 minute').max(10080, 'Duration cannot exceed 1 week')
})

export const lessonSchema = z.object({
  title: z.string().min(1, 'Lesson title is required').max(200, 'Title must be less than 200 characters'),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  type: z.enum(['video', 'pdf', 'quiz', 'link', 'simulation', 'project', 'discussion', 'scorm']),
  is_optional: z.boolean().optional(),
  estimated_duration: z.number().min(1, 'Duration must be at least 1 minute').max(480, 'Duration cannot exceed 8 hours')
})

// Validation functions
export const validatePathCreation = (data: Partial<PathCreationForm>) => {
  try {
    pathCreationSchema.parse(data)
    return { isValid: true, errors: [] }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message
        }))
      }
    }
    return { isValid: false, errors: [{ field: 'general', message: 'Validation failed' }] }
  }
}

export const validateModule = (data: any) => {
  try {
    moduleSchema.parse(data)
    return { isValid: true, errors: [] }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message
        }))
      }
    }
    return { isValid: false, errors: [{ field: 'general', message: 'Validation failed' }] }
  }
}

export const validateLesson = (data: any) => {
  try {
    lessonSchema.parse(data)
    return { isValid: true, errors: [] }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message
        }))
      }
    }
    return { isValid: false, errors: [{ field: 'general', message: 'Validation failed' }] }
  }
}

// Business logic validation
export const validatePathStructure = (modules: any[]) => {
  const errors: string[] = []

  if (modules.length === 0) {
    errors.push('At least one module is required')
  }

  modules.forEach((module, moduleIndex) => {
    if (!module.title?.trim()) {
      errors.push(`Module ${moduleIndex + 1} must have a title`)
    }

    if (!module.lessons || module.lessons.length === 0) {
      errors.push(`Module ${moduleIndex + 1} must have at least one lesson`)
    }

    module.lessons?.forEach((lesson: any, lessonIndex: number) => {
      if (!lesson.title?.trim()) {
        errors.push(`Lesson ${lessonIndex + 1} in Module ${moduleIndex + 1} must have a title`)
      }

      if (!lesson.type) {
        errors.push(`Lesson ${lessonIndex + 1} in Module ${moduleIndex + 1} must have a type`)
      }
    })
  })

  return {
    isValid: errors.length === 0,
    errors
  }
}

export const validatePathReadiness = (pathData: any, modules: any[], assignments: any[]) => {
  const errors: string[] = []
  const warnings: string[] = []

  // Required fields
  if (!pathData.title?.trim()) {
    errors.push('Path title is required')
  }

  if (!pathData.description?.trim()) {
    errors.push('Path description is required')
  }

  if (!pathData.category) {
    errors.push('Path category is required')
  }

  if (!pathData.objectives || pathData.objectives.length === 0) {
    warnings.push('Learning objectives help learners understand what they will achieve')
  }

  // Structure validation
  const structureValidation = validatePathStructure(modules)
  errors.push(...structureValidation.errors)

  // Duration validation
  const totalDuration = modules.reduce((total, module) => 
    total + (module.lessons?.reduce((lessonTotal: number, lesson: any) => 
      lessonTotal + (lesson.estimated_duration || 0), 0) || 0), 0)

  const estimatedWeeks = Math.ceil(totalDuration / (60 * 40)) // Assuming 40 hours per week
  
  if (pathData.duration && estimatedWeeks > pathData.duration * 2) {
    warnings.push(`Content duration (${Math.round(totalDuration / 60)}h) seems high for ${pathData.duration} week(s)`)
  }

  // Assignment validation
  if (assignments.length === 0) {
    warnings.push('No assignments configured - learners will need to be assigned manually')
  }

  return {
    isValid: errors.length === 0,
    canPublish: errors.length === 0,
    errors,
    warnings
  }
}

// Content validation helpers
export const validateVideoUrl = (url: string) => {
  const videoUrlPattern = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be|vimeo\.com|wistia\.com)/
  return videoUrlPattern.test(url)
}

export const validateExternalUrl = (url: string) => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

export const validateQuizData = (quizData: any) => {
  const errors: string[] = []

  if (!quizData.questions || quizData.questions.length === 0) {
    errors.push('Quiz must have at least one question')
  }

  if (quizData.passing_score < 0 || quizData.passing_score > 100) {
    errors.push('Passing score must be between 0 and 100')
  }

  quizData.questions?.forEach((question: any, index: number) => {
    if (!question.question?.trim()) {
      errors.push(`Question ${index + 1} must have text`)
    }

    if (question.type === 'multiple_choice' && (!question.options || question.options.length < 2)) {
      errors.push(`Question ${index + 1} must have at least 2 options`)
    }

    if (!question.correct_answer) {
      errors.push(`Question ${index + 1} must have a correct answer`)
    }

    if (question.points <= 0) {
      errors.push(`Question ${index + 1} must have positive points`)
    }
  })

  return {
    isValid: errors.length === 0,
    errors
  }
}

// File validation
export const validateFileUpload = (file: File, type: 'pdf' | 'video' | 'scorm') => {
  const errors: string[] = []
  const maxSizes = {
    pdf: 100 * 1024 * 1024, // 100MB
    video: 500 * 1024 * 1024, // 500MB
    scorm: 200 * 1024 * 1024 // 200MB
  }

  const allowedTypes = {
    pdf: ['application/pdf'],
    video: ['video/mp4', 'video/webm', 'video/ogg'],
    scorm: ['application/zip', 'application/x-zip-compressed']
  }

  if (file.size > maxSizes[type]) {
    errors.push(`File size exceeds ${maxSizes[type] / (1024 * 1024)}MB limit`)
  }

  if (!allowedTypes[type].includes(file.type)) {
    errors.push(`Invalid file type. Expected: ${allowedTypes[type].join(', ')}`)
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// Accessibility validation
export const validateAccessibility = (pathData: any, modules: any[]) => {
  const warnings: string[] = []

  // Check for alt text on images (if any)
  // Check for video captions
  // Check for readable content structure

  modules.forEach((module, moduleIndex) => {
    module.lessons?.forEach((lesson: any, lessonIndex: number) => {
      if (lesson.type === 'video' && !lesson.content?.video_transcript) {
        warnings.push(`Video lesson "${lesson.title}" should include a transcript for accessibility`)
      }

      if (lesson.type === 'pdf' && !lesson.description) {
        warnings.push(`PDF lesson "${lesson.title}" should include a description for screen readers`)
      }
    })
  })

  return {
    warnings
  }
}

export default {
  validatePathCreation,
  validateModule,
  validateLesson,
  validatePathStructure,
  validatePathReadiness,
  validateVideoUrl,
  validateExternalUrl,
  validateQuizData,
  validateFileUpload,
  validateAccessibility
}
