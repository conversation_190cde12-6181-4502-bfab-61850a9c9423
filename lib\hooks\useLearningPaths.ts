import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { 
  learningPathsApi, 
  modulesApi, 
  lessonsApi, 
  assignmentsApi, 
  templatesApi,
  aiApi 
} from '@/lib/api/learning-paths'
import {
  LearningP<PERSON>,
  Module,
  Lesson,
  LearnerAssignment,
  Assignment,
  PathFilters,
  PathSortOptions
} from '@/lib/types/learning-paths'

// Learning Paths Hooks
export const useLearningPaths = (
  filters?: Partial<PathFilters>,
  sort?: PathSortOptions,
  page = 1,
  limit = 20
) => {
  return useQuery({
    queryKey: ['learning-paths', filters, sort, page, limit],
    queryFn: () => learningPathsApi.getPaths(filters, sort, page, limit),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export const useLearningPath = (id: string) => {
  return useQuery({
    queryKey: ['learning-path', id],
    queryFn: () => learningPathsApi.getPath(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export const useCreateLearningPath = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: learningPathsApi.createPath,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['learning-paths'] })
      toast.success('Learning path created successfully!')
      return data
    },
    onError: (error: any) => {
      console.error('Create learning path error:', error)

      // Provide more specific error messages
      let errorMessage = 'Failed to create learning path'

      if (error.message?.includes('authentication')) {
        errorMessage = 'Authentication required. Please log in and try again.'
      } else if (error.message?.includes('permission')) {
        errorMessage = 'You do not have permission to create learning paths.'
      } else if (error.message?.includes('validation')) {
        errorMessage = 'Please check all required fields and try again.'
      } else if (error.message) {
        errorMessage = error.message
      }

      toast.error(errorMessage)
    },
  })
}

export const useUpdateLearningPath = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<LearningPath> }) =>
      learningPathsApi.updatePath(id, updates),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['learning-paths'] })
      queryClient.invalidateQueries({ queryKey: ['learning-path', variables.id] })
      toast.success('Learning path updated successfully!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update learning path')
    },
  })
}

export const useDeleteLearningPath = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: learningPathsApi.deletePath,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['learning-paths'] })
      toast.success('Learning path deleted successfully!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete learning path')
    },
  })
}

export const usePublishLearningPath = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: learningPathsApi.publishPath,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['learning-paths'] })
      queryClient.invalidateQueries({ queryKey: ['learning-path', data.id.toString()] })
      toast.success('Learning path published successfully!')
    },
    onError: (error: any) => {
      console.error('Publish error:', error)
      toast.error(error.message || 'Failed to publish learning path')
    },
  })
}

export const useDuplicateLearningPath = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, newTitle }: { id: string; newTitle: string }) =>
      learningPathsApi.duplicatePath(id, newTitle),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['learning-paths'] })
      toast.success('Learning path duplicated successfully!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to duplicate learning path')
    },
  })
}

// Modules Hooks
export const useCreateModule = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ pathId, moduleData }: { pathId: string; moduleData: Omit<Module, 'id' | 'created_at' | 'updated_at'> }) =>
      modulesApi.createModule(pathId, moduleData),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['learning-path', variables.pathId] })
      toast.success('Module created successfully!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create module')
    },
  })
}

export const useUpdateModule = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, updates, pathId }: { id: string; updates: Partial<Module>; pathId: string }) =>
      modulesApi.updateModule(id, updates),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['learning-path', variables.pathId] })
      toast.success('Module updated successfully!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update module')
    },
  })
}

export const useDeleteModule = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, pathId }: { id: string; pathId: string }) =>
      modulesApi.deleteModule(id),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['learning-path', variables.pathId] })
      toast.success('Module deleted successfully!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete module')
    },
  })
}

// Lessons Hooks
export const useCreateLesson = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ moduleId, lessonData, pathId }: { 
      moduleId: string; 
      lessonData: Omit<Lesson, 'id' | 'created_at' | 'updated_at'>; 
      pathId: string 
    }) =>
      lessonsApi.createLesson(moduleId, lessonData),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['learning-path', variables.pathId] })
      toast.success('Lesson created successfully!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create lesson')
    },
  })
}

export const useUpdateLesson = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, updates, pathId }: { id: string; updates: Partial<Lesson>; pathId: string }) =>
      lessonsApi.updateLesson(id, updates),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['learning-path', variables.pathId] })
      toast.success('Lesson updated successfully!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update lesson')
    },
  })
}

export const useDeleteLesson = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, pathId }: { id: string; pathId: string }) =>
      lessonsApi.deleteLesson(id),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['learning-path', variables.pathId] })
      toast.success('Lesson deleted successfully!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete lesson')
    },
  })
}

// Assignments Hooks
export const usePathAssignments = (pathId: string) => {
  return useQuery({
    queryKey: ['path-assignments', pathId],
    queryFn: () => assignmentsApi.getPathAssignments(pathId),
    enabled: !!pathId,
  })
}

export const useCreateAssignment = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: assignmentsApi.createAssignment,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['path-assignments', data.path_id.toString()] })
      toast.success('Assignment created successfully!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create assignment')
    },
  })
}

export const useCreateBulkAssignments = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: assignmentsApi.createBulkAssignments,
    onSuccess: (data) => {
      if (data.length > 0) {
        queryClient.invalidateQueries({ queryKey: ['path-assignments', data[0].path_id.toString()] })
      }
      toast.success(`${data.length} assignments created successfully!`)
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create assignments')
    },
  })
}

// Templates Hooks
export const usePathTemplates = () => {
  return useQuery({
    queryKey: ['path-templates'],
    queryFn: templatesApi.getTemplates,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export const useCreateTemplate = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ pathId, templateData }: { 
      pathId: string; 
      templateData: any 
    }) =>
      templatesApi.createTemplate(pathId, templateData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['path-templates'] })
      toast.success('Template created successfully!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create template')
    },
  })
}

// AI Hooks
export const useGeneratePathSuggestions = () => {
  return useMutation({
    mutationFn: ({ prompt, options }: { prompt: string; options: any }) =>
      aiApi.generatePathSuggestions(prompt, options),
    onError: (error: any) => {
      toast.error(error.message || 'Failed to generate path suggestions')
    },
  })
}

export const useEnhanceLesson = () => {
  return useMutation({
    mutationFn: ({ lessonId, content }: { lessonId: string; content: string }) =>
      aiApi.enhanceLesson(lessonId, content),
    onSuccess: () => {
      toast.success('Lesson enhanced successfully!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to enhance lesson')
    },
  })
}

export const useGenerateQuiz = () => {
  return useMutation({
    mutationFn: ({ content, questionCount }: { content: string; questionCount?: number }) =>
      aiApi.generateQuiz(content, questionCount),
    onSuccess: () => {
      toast.success('Quiz generated successfully!')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to generate quiz')
    },
  })
}
