'use client'

import React, { useState, useEffect, useCallback } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Grid,
  Avatar,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  Chip,
  Paper,
  Divider
} from '@mui/material'
import {
  CloudUpload as UploadIcon,
  Palette as PaletteIcon,
  Preview as PreviewIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Info as InfoIcon
} from '@mui/icons-material'
import { useDropzone } from 'react-dropzone'
import { useForm, Controller } from 'react-hook-form'
import { motion } from 'framer-motion'

import { useBrandingSettings, useUpdateBrandingSettings, useUploadLogo } from '@/lib/hooks/useSettings'
import { useSettingsStore } from '@/lib/store'
import { BrandingSettings as BrandingSettingsType } from '@/lib/types/settings'

// Font options
const fontOptions = [
  { value: 'Roboto', label: 'Roboto (Default)' },
  { value: 'Inter', label: 'Inter' },
  { value: 'Open Sans', label: 'Open Sans' },
  { value: 'Lato', label: 'Lato' },
  { value: 'Montserrat', label: 'Montserrat' },
  { value: 'Poppins', label: 'Poppins' },
  { value: 'Source Sans Pro', label: 'Source Sans Pro' },
  { value: 'Nunito', label: 'Nunito' },
  { value: 'Raleway', label: 'Raleway' },
  { value: 'Ubuntu', label: 'Ubuntu' }
]

// Theme presets
const themePresets = [
  { value: 'default', label: 'Default', primary: '#1976d2', secondary: '#dc004e' },
  { value: 'corporate', label: 'Corporate Blue', primary: '#0d47a1', secondary: '#1565c0' },
  { value: 'modern', label: 'Modern Purple', primary: '#7b1fa2', secondary: '#ab47bc' },
  { value: 'nature', label: 'Nature Green', primary: '#2e7d32', secondary: '#66bb6a' },
  { value: 'sunset', label: 'Sunset Orange', primary: '#f57c00', secondary: '#ff9800' },
  { value: 'ocean', label: 'Ocean Teal', primary: '#00695c', secondary: '#26a69a' },
  { value: 'minimal', label: 'Minimal Gray', primary: '#424242', secondary: '#757575' }
]

interface ColorPickerProps {
  label: string
  value: string
  onChange: (color: string) => void
}

function ColorPicker({ label, value, onChange }: ColorPickerProps) {
  return (
    <Box>
      <Typography variant="body2" gutterBottom>
        {label}
      </Typography>
      <Box display="flex" alignItems="center" gap={1}>
        <Box
          sx={{
            width: 40,
            height: 40,
            backgroundColor: value,
            border: '2px solid',
            borderColor: 'divider',
            borderRadius: 1,
            cursor: 'pointer',
            position: 'relative'
          }}
        >
          <input
            type="color"
            value={value}
            onChange={(e) => onChange(e.target.value)}
            style={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              opacity: 0,
              cursor: 'pointer'
            }}
          />
        </Box>
        <TextField
          value={value}
          onChange={(e) => onChange(e.target.value)}
          size="small"
          sx={{ width: 100 }}
        />
      </Box>
    </Box>
  )
}

export default function BrandingSettings() {
  const { setUnsavedChanges } = useSettingsStore()
  const { data: settings, isLoading } = useBrandingSettings()
  const updateSettings = useUpdateBrandingSettings()
  const uploadLogo = useUploadLogo()
  
  const [previewMode, setPreviewMode] = useState(false)
  const [logoPreview, setLogoPreview] = useState<string | null>(null)
  
  const { control, handleSubmit, watch, reset, setValue, formState: { isDirty } } = useForm<BrandingSettingsType>({
    defaultValues: settings || {
      primary_color: '#1976d2',
      secondary_color: '#dc004e',
      font_family: 'Roboto',
      theme_name: 'default',
      logo_url: '',
      custom_css: '',
      email_templates: []
    }
  })

  // Update form when settings data loads
  useEffect(() => {
    if (settings) {
      reset(settings)
      setLogoPreview((settings as BrandingSettingsType).logo_url || null)
    }
  }, [settings, reset])

  // Track unsaved changes
  useEffect(() => {
    setUnsavedChanges(isDirty)
  }, [isDirty, setUnsavedChanges])

  const watchedValues = watch()

  // Logo upload handling
  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (file) {
      try {
        const logoUrl = await uploadLogo.mutateAsync(file)
        setValue('logo_url', logoUrl, { shouldDirty: true })
        setLogoPreview(logoUrl)
      } catch (error) {
        console.error('Failed to upload logo:', error)
      }
    }
  }, [uploadLogo, setValue])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.svg', '.webp']
    },
    maxSize: 5 * 1024 * 1024, // 5MB
    multiple: false
  })

  const onSubmit = async (data: BrandingSettingsType) => {
    try {
      await updateSettings.mutateAsync(data)
      reset(data)
    } catch (error) {
      console.error('Failed to save branding settings:', error)
    }
  }

  const applyThemePreset = (preset: typeof themePresets[0]) => {
    setValue('primary_color', preset.primary, { shouldDirty: true })
    setValue('secondary_color', preset.secondary, { shouldDirty: true })
    setValue('theme_name', preset.value, { shouldDirty: true })
  }

  const removeLogo = () => {
    setValue('logo_url', '', { shouldDirty: true })
    setLogoPreview(null)
  }

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" py={4}>
        <CircularProgress />
      </Box>
    )
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Grid container spacing={3}>
        {/* Logo Upload */}
        <Grid item xs={12}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" mb={3}>
                  <Typography variant="h6" fontWeight="bold">
                    Logo & Brand Assets
                  </Typography>
                  <Tooltip title="Upload your organization's logo">
                    <IconButton size="small" sx={{ ml: 1 }}>
                      <InfoIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>

                <Grid container spacing={3} alignItems="center">
                  <Grid item xs={12} md={6}>
                    <Paper
                      {...getRootProps()}
                      sx={{
                        p: 3,
                        border: '2px dashed',
                        borderColor: isDragActive ? 'primary.main' : 'divider',
                        backgroundColor: isDragActive ? 'action.hover' : 'background.default',
                        cursor: 'pointer',
                        textAlign: 'center',
                        transition: 'all 0.2s ease'
                      }}
                    >
                      <input {...getInputProps()} />
                      <UploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                      <Typography variant="h6" gutterBottom>
                        {isDragActive ? 'Drop logo here' : 'Upload Logo'}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Drag & drop or click to select (PNG, JPG, SVG - Max 5MB)
                      </Typography>
                      {uploadLogo.isPending && (
                        <Box mt={2}>
                          <CircularProgress size={24} />
                        </Box>
                      )}
                    </Paper>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Box textAlign="center">
                      <Typography variant="subtitle2" gutterBottom>
                        Current Logo
                      </Typography>
                      {logoPreview ? (
                        <Box>
                          <Avatar
                            src={logoPreview}
                            sx={{ 
                              width: 120, 
                              height: 120, 
                              mx: 'auto', 
                              mb: 2,
                              border: '2px solid',
                              borderColor: 'divider'
                            }}
                            variant="rounded"
                          />
                          <Button
                            variant="outlined"
                            color="error"
                            size="small"
                            startIcon={<DeleteIcon />}
                            onClick={removeLogo}
                          >
                            Remove Logo
                          </Button>
                        </Box>
                      ) : (
                        <Box
                          sx={{
                            width: 120,
                            height: 120,
                            mx: 'auto',
                            border: '2px dashed',
                            borderColor: 'divider',
                            borderRadius: 2,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                        >
                          <Typography variant="body2" color="text.secondary">
                            No logo
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* Color Scheme */}
        <Grid item xs={12}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" mb={3}>
                  <Typography variant="h6" fontWeight="bold">
                    Color Scheme
                  </Typography>
                  <Chip 
                    label={watchedValues.theme_name || 'Custom'}
                    size="small"
                    sx={{ ml: 2 }}
                  />
                </Box>

                {/* Theme Presets */}
                <Box mb={3}>
                  <Typography variant="subtitle2" gutterBottom>
                    Quick Presets
                  </Typography>
                  <Box display="flex" gap={1} flexWrap="wrap">
                    {themePresets.map((preset) => (
                      <Button
                        key={preset.value}
                        variant={watchedValues.theme_name === preset.value ? 'contained' : 'outlined'}
                        size="small"
                        onClick={() => applyThemePreset(preset)}
                        startIcon={
                          <Box
                            sx={{
                              width: 16,
                              height: 16,
                              backgroundColor: preset.primary,
                              borderRadius: '50%'
                            }}
                          />
                        }
                      >
                        {preset.label}
                      </Button>
                    ))}
                  </Box>
                </Box>

                <Divider sx={{ my: 3 }} />

                {/* Custom Colors */}
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Controller
                      name="primary_color"
                      control={control}
                      render={({ field }) => (
                        <ColorPicker
                          label="Primary Color"
                          value={field.value}
                          onChange={field.onChange}
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Controller
                      name="secondary_color"
                      control={control}
                      render={({ field }) => (
                        <ColorPicker
                          label="Secondary Color"
                          value={field.value}
                          onChange={field.onChange}
                        />
                      )}
                    />
                  </Grid>
                </Grid>

                {/* Color Preview */}
                <Box mt={3}>
                  <Typography variant="subtitle2" gutterBottom>
                    Preview
                  </Typography>
                  <Box display="flex" gap={2}>
                    <Button 
                      variant="contained" 
                      sx={{ backgroundColor: watchedValues.primary_color }}
                    >
                      Primary Button
                    </Button>
                    <Button 
                      variant="contained" 
                      sx={{ backgroundColor: watchedValues.secondary_color }}
                    >
                      Secondary Button
                    </Button>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* Typography */}
        <Grid item xs={12}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <Card>
              <CardContent>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  Typography
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Controller
                      name="font_family"
                      control={control}
                      render={({ field }) => (
                        <FormControl fullWidth>
                          <InputLabel>Font Family</InputLabel>
                          <Select {...field} label="Font Family">
                            {fontOptions.map((font) => (
                              <MenuItem key={font.value} value={font.value}>
                                <span style={{ fontFamily: font.value }}>
                                  {font.label}
                                </span>
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Box>
                      <Typography variant="subtitle2" gutterBottom>
                        Font Preview
                      </Typography>
                      <Paper sx={{ p: 2, backgroundColor: 'background.default' }}>
                        <Typography 
                          variant="h6" 
                          sx={{ fontFamily: watchedValues.font_family }}
                          gutterBottom
                        >
                          Heading Text
                        </Typography>
                        <Typography 
                          variant="body1" 
                          sx={{ fontFamily: watchedValues.font_family }}
                        >
                          This is how your body text will appear with the selected font family.
                        </Typography>
                      </Paper>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* Save Button */}
        <Grid item xs={12}>
          <Box display="flex" justifyContent="flex-end" gap={2}>
            <Button
              variant="outlined"
              onClick={() => reset()}
              disabled={!isDirty}
            >
              Reset Changes
            </Button>
            <Button
              type="submit"
              variant="contained"
              disabled={!isDirty || updateSettings.isPending}
              startIcon={updateSettings.isPending ? <CircularProgress size={20} /> : undefined}
            >
              Save Branding Settings
            </Button>
          </Box>
        </Grid>
      </Grid>
    </form>
  )
}
