-- Simple seed data for ZenithLearn AI
-- Use this if the main seed file has issues

-- Insert default tenant
INSERT INTO tenants (name, slug, settings) VALUES 
('Demo Organization', 'demo-org', '{"theme": "light", "features": ["ai_chat", "gamification", "reports"]}');

-- Insert default roles with explicit array casting
INSERT INTO roles (tenant_id, name, permissions, is_default) VALUES 
(1, 'Super Admin', ARRAY['admin']::TEXT[], false),
(1, 'Admin', ARRAY['view_dashboard', 'view_learning_paths', 'create_learning_paths', 'edit_learning_paths', 'delete_learning_paths', 'assign_learning_paths', 'view_learners', 'create_learners', 'edit_learners', 'view_content', 'upload_content', 'edit_content', 'view_reports', 'export_reports', 'view_settings', 'edit_settings']::TEXT[], false),
(1, 'Instructor', ARRAY['view_dashboard', 'view_learning_paths', 'create_learning_paths', 'edit_learning_paths', 'assign_learning_paths', 'view_learners', 'view_content', 'upload_content', 'view_reports']::TEXT[], false),
(1, 'Learner', ARRAY['view_dashboard']::TEXT[], true);

-- Insert default competencies
INSERT INTO competencies (tenant_id, name, description, category, level_definitions) VALUES 
(1, 'JavaScript Programming', 'Proficiency in JavaScript programming language', 'Technical', '{"1": "Basic syntax and concepts", "2": "Functions and objects", "3": "Advanced concepts and frameworks", "4": "Expert level with optimization", "5": "Thought leader and mentor"}'),
(1, 'React Development', 'Building user interfaces with React', 'Technical', '{"1": "Basic components", "2": "State and props", "3": "Hooks and context", "4": "Performance optimization", "5": "Architecture and patterns"}'),
(1, 'Project Management', 'Managing projects and teams effectively', 'Soft Skills', '{"1": "Basic planning", "2": "Team coordination", "3": "Risk management", "4": "Strategic planning", "5": "Organizational leadership"}'),
(1, 'Communication', 'Effective verbal and written communication', 'Soft Skills', '{"1": "Basic communication", "2": "Clear presentation", "3": "Persuasive communication", "4": "Leadership communication", "5": "Inspirational speaking"}');

-- Insert sample learning paths with explicit array casting
INSERT INTO learning_paths (tenant_id, title, description, objectives, prerequisites, difficulty_level, estimated_duration, is_live, is_featured, category, tags, created_by) VALUES 
(1, 'JavaScript Fundamentals', 'Learn the basics of JavaScript programming', 
 ARRAY['Understand variables and data types', 'Master functions and scope', 'Work with objects and arrays']::TEXT[], 
 ARRAY['Basic computer literacy']::TEXT[], 
 'beginner', 480, true, true, 'Programming', 
 ARRAY['javascript', 'programming', 'web-development']::TEXT[], NULL),
(1, 'React Development Bootcamp', 'Comprehensive React.js development course', 
 ARRAY['Build React components', 'Manage state effectively', 'Create full applications']::TEXT[], 
 ARRAY['JavaScript Fundamentals']::TEXT[], 
 'intermediate', 720, true, true, 'Programming', 
 ARRAY['react', 'javascript', 'frontend']::TEXT[], NULL),
(1, 'Project Management Essentials', 'Core project management skills and methodologies', 
 ARRAY['Plan and execute projects', 'Manage teams effectively', 'Handle project risks']::TEXT[], 
 ARRAY[]::TEXT[], 
 'beginner', 360, true, true, 'Management', 
 ARRAY['project-management', 'leadership', 'soft-skills']::TEXT[], NULL);

-- Insert modules for JavaScript Fundamentals
INSERT INTO modules (path_id, title, description, order_index) VALUES 
(1, 'Introduction to JavaScript', 'Getting started with JavaScript basics', 1),
(1, 'Variables and Data Types', 'Understanding JavaScript data types and variables', 2),
(1, 'Functions and Scope', 'Working with functions and understanding scope', 3);

-- Insert sample lessons
INSERT INTO lessons (module_id, title, description, type, content_text, order_index, estimated_duration) VALUES 
(1, 'What is JavaScript?', 'Introduction to JavaScript and its uses', 'text', 'JavaScript is a versatile programming language primarily used for web development.', 1, 15),
(1, 'Setting up Development Environment', 'How to set up your coding environment', 'video', NULL, 2, 20),
(2, 'Understanding Variables', 'Learn about var, let, and const', 'text', 'Variables are containers for storing data values in JavaScript.', 1, 20);

-- Insert sample content library items with explicit array casting
INSERT INTO content_library (tenant_id, title, description, type, file_url, tags, uploaded_by, is_public) VALUES 
(1, 'JavaScript Cheat Sheet', 'Quick reference for JavaScript syntax', 'pdf', '/content/javascript-cheat-sheet.pdf', ARRAY['javascript', 'reference']::TEXT[], NULL, true),
(1, 'React Component Patterns', 'Common patterns for React components', 'video', '/content/react-patterns.mp4', ARRAY['react', 'patterns']::TEXT[], NULL, true);

-- Insert path competencies mapping
INSERT INTO path_competencies (path_id, competency_id, level_gained) VALUES 
(1, 1, 2), -- JavaScript Fundamentals -> JavaScript Programming (Level 2)
(2, 1, 1), -- React Bootcamp -> JavaScript Programming (Level 1 additional)
(3, 3, 2); -- Project Management -> Project Management (Level 2)

-- Create a function to handle new user registration
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert user into users table with default role
    INSERT INTO users (id, tenant_id, role_id, email, full_name)
    VALUES (
        NEW.id,
        1, -- Default tenant for demo
        (SELECT id FROM roles WHERE tenant_id = 1 AND is_default = true LIMIT 1),
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1))
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user registration
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();
