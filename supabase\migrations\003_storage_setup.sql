-- Create storage buckets for content management
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  (
    'content',
    'content',
    true,
    104857600, -- 100MB limit
    ARRAY[
      -- Video types
      'video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/mkv',
      -- Document types
      'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain', 'text/rtf',
      -- Presentation types
      'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/vnd.oasis.opendocument.presentation',
      -- Image types
      'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml', 'image/bmp',
      -- Audio types
      'audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/mp3', 'audio/aac',
      -- Other allowed types
      'application/json', 'text/csv', 'application/zip', 'application/x-zip-compressed'
    ]
  )
ON CONFLICT (id) DO UPDATE SET
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Create avatars bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  (
    'avatars',
    'avatars',
    true,
    5242880, -- 5MB limit for avatars
    ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
  )
ON CONFLICT (id) DO NOTHING;

-- Function to extract tenant_id from storage path
CREATE OR REPLACE FUNCTION get_tenant_id_from_path(path text)
RETURNS INTEGER AS $$
BEGIN
  -- Extract tenant_id from path like 'tenant_3/content/videos/...'
  RETURN (regexp_match(path, '^tenant_(\d+)/'))[1]::INTEGER;
EXCEPTION
  WHEN OTHERS THEN
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user belongs to tenant from path
CREATE OR REPLACE FUNCTION user_can_access_tenant_content(path text)
RETURNS BOOLEAN AS $$
DECLARE
  path_tenant_id INTEGER;
  user_tenant_id INTEGER;
BEGIN
  -- Get tenant_id from path
  path_tenant_id := get_tenant_id_from_path(path);
  
  -- Get user's tenant_id
  SELECT tenant_id INTO user_tenant_id
  FROM users 
  WHERE id = auth.uid();
  
  -- Check if they match
  RETURN path_tenant_id = user_tenant_id;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- RLS Policies for content bucket

-- Policy for SELECT (viewing/downloading files)
CREATE POLICY "Users can view files in their tenant" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'content' AND 
    user_can_access_tenant_content(name)
  );

-- Policy for INSERT (uploading files)
CREATE POLICY "Users can upload files to their tenant" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'content' AND 
    user_can_access_tenant_content(name) AND
    -- Ensure the path follows the correct pattern: tenant_{id}/content/{type}/{user_id}/
    name ~ '^tenant_\d+/content/(videos|documents|presentations|images|audio|other)/[a-f0-9-]+/\d+_[^/]+$'
  );

-- Policy for UPDATE (updating file metadata)
CREATE POLICY "Users can update files in their tenant" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'content' AND 
    user_can_access_tenant_content(name)
  );

-- Policy for DELETE (deleting files)
CREATE POLICY "Users can delete files in their tenant" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'content' AND 
    user_can_access_tenant_content(name)
  );

-- RLS Policies for avatars bucket

-- Policy for SELECT (viewing avatars)
CREATE POLICY "Users can view avatars" ON storage.objects
  FOR SELECT USING (bucket_id = 'avatars');

-- Policy for INSERT (uploading avatars)
CREATE POLICY "Users can upload their own avatar" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'avatars' AND 
    (storage.foldername(name))[1] = auth.uid()::text
  );

-- Policy for UPDATE (updating avatar metadata)
CREATE POLICY "Users can update their own avatar" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'avatars' AND 
    (storage.foldername(name))[1] = auth.uid()::text
  );

-- Policy for DELETE (deleting avatars)
CREATE POLICY "Users can delete their own avatar" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'avatars' AND 
    (storage.foldername(name))[1] = auth.uid()::text
  );

-- Enable RLS on storage.objects
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_storage_objects_bucket_tenant 
ON storage.objects (bucket_id, (get_tenant_id_from_path(name)));

CREATE INDEX IF NOT EXISTS idx_storage_objects_name_pattern 
ON storage.objects (bucket_id, name) 
WHERE bucket_id = 'content';

-- Create a function to clean up orphaned files
CREATE OR REPLACE FUNCTION cleanup_orphaned_content_files()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER := 0;
  file_record RECORD;
BEGIN
  -- Find files that are not referenced in any lesson's content_metadata
  FOR file_record IN
    SELECT o.name, o.id
    FROM storage.objects o
    WHERE o.bucket_id = 'content'
    AND NOT EXISTS (
      SELECT 1 
      FROM lessons l 
      WHERE l.content_metadata::text LIKE '%' || o.name || '%'
      OR l.content_url = o.name
    )
    AND o.created_at < NOW() - INTERVAL '24 hours' -- Only delete files older than 24 hours
  LOOP
    -- Delete the orphaned file
    DELETE FROM storage.objects WHERE id = file_record.id;
    deleted_count := deleted_count + 1;
  END LOOP;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get storage usage by tenant
CREATE OR REPLACE FUNCTION get_tenant_storage_usage(tenant_id_param INTEGER)
RETURNS TABLE (
  content_type TEXT,
  file_count BIGINT,
  total_size BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    CASE 
      WHEN o.name ~ 'tenant_\d+/content/videos/' THEN 'videos'
      WHEN o.name ~ 'tenant_\d+/content/documents/' THEN 'documents'
      WHEN o.name ~ 'tenant_\d+/content/presentations/' THEN 'presentations'
      WHEN o.name ~ 'tenant_\d+/content/images/' THEN 'images'
      WHEN o.name ~ 'tenant_\d+/content/audio/' THEN 'audio'
      ELSE 'other'
    END as content_type,
    COUNT(*) as file_count,
    COALESCE(SUM(o.metadata->>'size')::BIGINT, 0) as total_size
  FROM storage.objects o
  WHERE o.bucket_id = 'content'
  AND get_tenant_id_from_path(o.name) = tenant_id_param
  GROUP BY content_type
  ORDER BY total_size DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION get_tenant_id_from_path(text) TO authenticated;
GRANT EXECUTE ON FUNCTION user_can_access_tenant_content(text) TO authenticated;
GRANT EXECUTE ON FUNCTION get_tenant_storage_usage(INTEGER) TO authenticated;

-- Only allow admins to run cleanup
GRANT EXECUTE ON FUNCTION cleanup_orphaned_content_files() TO service_role;

-- Create a view for easy storage monitoring
CREATE OR REPLACE VIEW storage_usage_summary AS
SELECT 
  t.id as tenant_id,
  t.name as tenant_name,
  COALESCE(SUM(CASE WHEN o.metadata->>'size' IS NOT NULL THEN (o.metadata->>'size')::BIGINT ELSE 0 END), 0) as total_storage_bytes,
  COUNT(o.id) as total_files,
  COUNT(CASE WHEN o.name ~ 'videos/' THEN 1 END) as video_files,
  COUNT(CASE WHEN o.name ~ 'documents/' THEN 1 END) as document_files,
  COUNT(CASE WHEN o.name ~ 'presentations/' THEN 1 END) as presentation_files,
  COUNT(CASE WHEN o.name ~ 'images/' THEN 1 END) as image_files
FROM tenants t
LEFT JOIN storage.objects o ON (
  o.bucket_id = 'content' AND 
  get_tenant_id_from_path(o.name) = t.id
)
GROUP BY t.id, t.name
ORDER BY total_storage_bytes DESC;

-- Grant access to the view
GRANT SELECT ON storage_usage_summary TO authenticated;
