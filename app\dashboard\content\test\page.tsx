'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Button,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip
} from '@mui/material'
import { supabase } from '@/lib/supabase'

interface TestResult {
  test: string
  status: 'success' | 'error' | 'pending'
  message: string
  data?: any
}

export default function ContentLibraryTestPage() {
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const addTestResult = (result: TestResult) => {
    setTestResults(prev => [...prev, result])
  }

  const runTests = async () => {
    setIsRunning(true)
    setTestResults([])

    // Test 1: Basic connection
    try {
      addTestResult({ test: 'Database Connection', status: 'pending', message: 'Testing...' })
      const { data, error } = await supabase.from('content_library').select('count').limit(1)
      if (error) throw error
      addTestResult({ 
        test: 'Database Connection', 
        status: 'success', 
        message: 'Successfully connected to database' 
      })
    } catch (error) {
      addTestResult({ 
        test: 'Database Connection', 
        status: 'error', 
        message: `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
      })
    }

    // Test 2: Content Categories Table
    try {
      addTestResult({ test: 'Content Categories Table', status: 'pending', message: 'Testing...' })
      const { data, error } = await supabase.from('content_categories').select('*').limit(5)
      if (error) throw error
      addTestResult({ 
        test: 'Content Categories Table', 
        status: 'success', 
        message: `Found ${data?.length || 0} categories`,
        data: data
      })
    } catch (error) {
      addTestResult({ 
        test: 'Content Categories Table', 
        status: 'error', 
        message: `Categories query failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
      })
    }

    // Test 3: Content Library with Categories Relationship
    try {
      addTestResult({ test: 'Content-Category Relationship', status: 'pending', message: 'Testing...' })
      const { data, error } = await supabase
        .from('content_library')
        .select(`
          id,
          title,
          type,
          category_id,
          category:content_categories(
            id,
            name,
            color
          )
        `)
        .limit(5)
      
      if (error) throw error
      addTestResult({ 
        test: 'Content-Category Relationship', 
        status: 'success', 
        message: `Successfully fetched ${data?.length || 0} content items with categories`,
        data: data
      })
    } catch (error) {
      addTestResult({ 
        test: 'Content-Category Relationship', 
        status: 'error', 
        message: `Relationship query failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
      })
    }

    // Test 4: Content Service
    try {
      addTestResult({ test: 'Content Service', status: 'pending', message: 'Testing...' })
      const { ContentService } = await import('@/lib/services/contentService')
      const result = await ContentService.getContent({ limit: 3 })
      addTestResult({ 
        test: 'Content Service', 
        status: 'success', 
        message: `Service returned ${result.items.length} items`,
        data: result
      })
    } catch (error) {
      addTestResult({ 
        test: 'Content Service', 
        status: 'error', 
        message: `Service failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
      })
    }

    setIsRunning(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'success'
      case 'error': return 'error'
      case 'pending': return 'warning'
      default: return 'default'
    }
  }

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Content Library Database Test
      </Typography>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        This page tests the database schema and relationships for the Content Library feature.
      </Alert>

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">
              Test Results
            </Typography>
            <Button 
              variant="contained" 
              onClick={runTests}
              disabled={isRunning}
              startIcon={isRunning ? <CircularProgress size={20} /> : null}
            >
              {isRunning ? 'Running Tests...' : 'Run Tests'}
            </Button>
          </Box>

          {testResults.length > 0 && (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Test</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Message</TableCell>
                    <TableCell>Data</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {testResults.map((result, index) => (
                    <TableRow key={index}>
                      <TableCell>{result.test}</TableCell>
                      <TableCell>
                        <Chip 
                          label={result.status} 
                          color={getStatusColor(result.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{result.message}</TableCell>
                      <TableCell>
                        {result.data && (
                          <details>
                            <summary style={{ cursor: 'pointer' }}>View Data</summary>
                            <pre style={{ fontSize: '12px', marginTop: '8px' }}>
                              {JSON.stringify(result.data, null, 2)}
                            </pre>
                          </details>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Expected Schema
          </Typography>
          <Typography variant="body2" color="text.secondary">
            The following tables and relationships should exist:
          </Typography>
          <Box component="ul" sx={{ mt: 1 }}>
            <li><strong>content_library</strong> - Main content table with category_id foreign key</li>
            <li><strong>content_categories</strong> - Categories table with hierarchical support</li>
            <li><strong>content_tags</strong> - Tags management table</li>
            <li><strong>content_tag_assignments</strong> - Many-to-many content-tag relationships</li>
            <li><strong>content_versions</strong> - Version control for content</li>
            <li><strong>content_reviews</strong> - Approval workflow</li>
            <li><strong>content_usage_stats</strong> - Analytics and tracking</li>
            <li><strong>smart_folders</strong> - Dynamic content organization</li>
          </Box>
        </CardContent>
      </Card>
    </Box>
  )
}
