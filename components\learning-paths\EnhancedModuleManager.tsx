'use client'

import React, { useState, useCallback, useRef } from 'react'
import {
  Box,
  Typo<PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  CardActions,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Chip,
  Tooltip,
  Fab,
  Grid,
  Paper,
  Divider,
  Alert,
  Checkbox,
  FormControlLabel,
  LinearProgress,
} from '@mui/material'
import {
  Add as AddIcon,
  MoreVert as MoreIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  DragIndicator as DragIcon,
  ContentCopy as CopyIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  VideoLibrary as VideoIcon,
  Quiz as QuizIcon,
  MenuBook as ReadingIcon,
  Assignment as ProjectIcon,
  Science as InteractiveIcon,
  Schedule as TimeIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
} from '@mui/icons-material'
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core'
import { arrayMove, SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { motion, AnimatePresence } from 'framer-motion'

import { Module, ModuleTemplate, ModuleWithOperations } from '@/lib/types/learning-paths'

interface EnhancedModuleManagerProps {
  modules: Module[]
  onChange: (modules: Module[]) => void
  learningPathId: string
}

// Module Templates for Quick Creation
const moduleTemplates: ModuleTemplate[] = [
  {
    id: 'video',
    name: 'Video Module',
    description: 'Video-based learning content',
    icon: 'video',
    color: '#1976d2',
    category: 'video',
    defaultLessons: [
      { title: 'Introduction Video', type: 'video', description: 'Overview of the topic', estimated_duration: 15 },
      { title: 'Main Content', type: 'video', description: 'Detailed explanation', estimated_duration: 30 },
      { title: 'Summary', type: 'video', description: 'Key takeaways', estimated_duration: 10 },
    ]
  },
  {
    id: 'interactive',
    name: 'Interactive Module',
    description: 'Hands-on learning experience',
    icon: 'interactive',
    color: '#388e3c',
    category: 'interactive',
    defaultLessons: [
      { title: 'Interactive Demo', type: 'interactive', description: 'Try it yourself', estimated_duration: 20 },
      { title: 'Practice Exercise', type: 'interactive', description: 'Apply your knowledge', estimated_duration: 25 },
    ]
  },
  {
    id: 'assessment',
    name: 'Quiz Module',
    description: 'Knowledge assessment',
    icon: 'quiz',
    color: '#f57c00',
    category: 'assessment',
    defaultLessons: [
      { title: 'Knowledge Check', type: 'quiz', description: 'Test your understanding', estimated_duration: 15 },
      { title: 'Final Assessment', type: 'quiz', description: 'Comprehensive evaluation', estimated_duration: 30 },
    ]
  },
  {
    id: 'reading',
    name: 'Reading Module',
    description: 'Text-based learning materials',
    icon: 'reading',
    color: '#7b1fa2',
    category: 'reading',
    defaultLessons: [
      { title: 'Reading Material', type: 'text', description: 'Study the content', estimated_duration: 20 },
      { title: 'Key Concepts', type: 'text', description: 'Important points', estimated_duration: 10 },
    ]
  },
  {
    id: 'project',
    name: 'Project Module',
    description: 'Practical project work',
    icon: 'project',
    color: '#d32f2f',
    category: 'project',
    defaultLessons: [
      { title: 'Project Brief', type: 'text', description: 'Project requirements', estimated_duration: 10 },
      { title: 'Implementation', type: 'assignment', description: 'Build your project', estimated_duration: 120 },
      { title: 'Review & Feedback', type: 'assignment', description: 'Submit for review', estimated_duration: 30 },
    ]
  }
]

const getModuleIcon = (category: string) => {
  switch (category) {
    case 'video': return <VideoIcon />
    case 'interactive': return <InteractiveIcon />
    case 'assessment': return <QuizIcon />
    case 'reading': return <ReadingIcon />
    case 'project': return <ProjectIcon />
    default: return <PlayIcon />
  }
}

const getModuleColor = (category: string) => {
  const template = moduleTemplates.find(t => t.category === category)
  return template?.color || '#1976d2'
}

// Sortable Module Item Component
interface SortableModuleItemProps {
  module: ModuleWithOperations
  index: number
  onEdit: (module: Module) => void
  onDelete: (moduleId: number) => void
  onDuplicate: (module: Module) => void
  onToggleStatus: (moduleId: number) => void
  isSelected: boolean
  onSelect: (moduleId: number, selected: boolean) => void
}

function SortableModuleItem({ 
  module, 
  index, 
  onEdit, 
  onDelete, 
  onDuplicate, 
  onToggleStatus,
  isSelected,
  onSelect
}: SortableModuleItemProps) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: module.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  }

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
  }

  const estimatedTime = module.lessons?.reduce((total, lesson) => total + (lesson.estimated_duration || 0), 0) || 0

  return (
    <motion.div
      ref={setNodeRef}
      style={style}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.2 }}
    >
      <Card 
        sx={{ 
          mb: 2, 
          border: isSelected ? 2 : 1,
          borderColor: isSelected ? 'primary.main' : 'divider',
          '&:hover': { 
            boxShadow: 3,
            transform: 'translateY(-2px)',
          },
          transition: 'all 0.2s ease',
        }}
      >
        <CardContent>
          <Box display="flex" alignItems="center" gap={2}>
            <Checkbox
              checked={isSelected}
              onChange={(e) => onSelect(module.id, e.target.checked)}
              size="small"
            />
            
            <Box 
              {...attributes} 
              {...listeners}
              sx={{ 
                cursor: 'grab',
                '&:active': { cursor: 'grabbing' },
                display: 'flex',
                alignItems: 'center',
                color: 'text.secondary'
              }}
            >
              <DragIcon />
            </Box>

            <Box 
              sx={{ 
                width: 40, 
                height: 40, 
                borderRadius: 1, 
                bgcolor: getModuleColor(module.category || 'video') + '20',
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                color: getModuleColor(module.category || 'video')
              }}
            >
              {getModuleIcon(module.category || 'video')}
            </Box>

            <Box flexGrow={1}>
              <Box display="flex" alignItems="center" gap={1} mb={1}>
                <Typography variant="h6" fontWeight="bold">
                  {module.title}
                </Typography>
                {module.hasUnsavedChanges && (
                  <Chip label="Unsaved" color="warning" size="small" />
                )}
                {module.is_optional && (
                  <Chip label="Optional" variant="outlined" size="small" />
                )}
              </Box>
              
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                {module.description}
              </Typography>
              
              <Box display="flex" alignItems="center" gap={2}>
                <Box display="flex" alignItems="center" gap={0.5}>
                  <TimeIcon fontSize="small" color="action" />
                  <Typography variant="caption">
                    {estimatedTime} min
                  </Typography>
                </Box>
                
                <Typography variant="caption" color="text.secondary">
                  {module.lessons?.length || 0} lessons
                </Typography>
                
                {module.validationErrors && module.validationErrors.length > 0 && (
                  <Tooltip title={module.validationErrors.join(', ')}>
                    <WarningIcon color="error" fontSize="small" />
                  </Tooltip>
                )}
              </Box>
            </Box>

            <IconButton onClick={handleMenuClick}>
              <MoreIcon />
            </IconButton>
          </Box>
        </CardContent>

        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={() => { onEdit(module); handleMenuClose() }}>
            <EditIcon sx={{ mr: 1 }} fontSize="small" />
            Edit Module
          </MenuItem>
          <MenuItem onClick={() => { onDuplicate(module); handleMenuClose() }}>
            <CopyIcon sx={{ mr: 1 }} fontSize="small" />
            Duplicate
          </MenuItem>
          <MenuItem onClick={() => { onToggleStatus(module.id); handleMenuClose() }}>
            {module.is_optional ? <CheckIcon sx={{ mr: 1 }} fontSize="small" /> : <PauseIcon sx={{ mr: 1 }} fontSize="small" />}
            {module.is_optional ? 'Make Required' : 'Make Optional'}
          </MenuItem>
          <Divider />
          <MenuItem onClick={() => { onDelete(module.id); handleMenuClose() }} sx={{ color: 'error.main' }}>
            <DeleteIcon sx={{ mr: 1 }} fontSize="small" />
            Delete
          </MenuItem>
        </Menu>
      </Card>
    </motion.div>
  )
}

export default function EnhancedModuleManager({ modules, onChange, learningPathId }: EnhancedModuleManagerProps) {
  const [showTemplateDialog, setShowTemplateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [editingModule, setEditingModule] = useState<Module | null>(null)
  const [deletingModuleId, setDeletingModuleId] = useState<number | null>(null)
  const [selectedModules, setSelectedModules] = useState<Set<number>>(new Set())
  const [bulkActionAnchor, setBulkActionAnchor] = useState<null | HTMLElement>(null)

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  // Convert modules to enhanced modules with operations
  const enhancedModules: ModuleWithOperations[] = modules.map(module => ({
    ...module,
    isEditing: false,
    isDuplicate: false,
    hasUnsavedChanges: false,
    validationErrors: [],
    estimatedCompletionTime: module.lessons?.reduce((total, lesson) => total + (lesson.estimated_duration || 0), 0) || 0
  }))

  const handleDragEnd = useCallback((event: any) => {
    const { active, over } = event

    if (active.id !== over.id) {
      const oldIndex = modules.findIndex(module => module.id === active.id)
      const newIndex = modules.findIndex(module => module.id === over.id)
      
      const newModules = arrayMove(modules, oldIndex, newIndex).map((module, index) => ({
        ...module,
        order_index: index
      }))
      
      onChange(newModules)
    }
  }, [modules, onChange])

  const handleCreateFromTemplate = (template: ModuleTemplate) => {
    const newModule: Module = {
      id: Date.now(), // Temporary ID
      learning_path_id: parseInt(learningPathId),
      title: template.name,
      description: template.description,
      order_index: modules.length,
      is_optional: false,
      estimated_duration: template.defaultLessons.reduce((total, lesson) => total + lesson.estimated_duration, 0),
      category: template.category,
      lessons: template.defaultLessons.map((lesson, index) => ({
        id: Date.now() + index + 1,
        module_id: Date.now(),
        title: lesson.title,
        description: lesson.description,
        type: lesson.type as any,
        order_index: index,
        estimated_duration: lesson.estimated_duration,
        is_optional: false,
        content_url: '',
        metadata: {}
      })),
      metadata: {
        template_id: template.id,
        created_from_template: true
      }
    }

    onChange([...modules, newModule])
    setShowTemplateDialog(false)
  }

  const handleEditModule = (module: Module) => {
    setEditingModule(module)
    setShowEditDialog(true)
  }

  const handleDeleteModule = (moduleId: number) => {
    setDeletingModuleId(moduleId)
    setShowDeleteDialog(true)
  }

  const confirmDelete = () => {
    if (deletingModuleId) {
      const newModules = modules.filter(m => m.id !== deletingModuleId)
      onChange(newModules)
      setShowDeleteDialog(false)
      setDeletingModuleId(null)
    }
  }

  const handleDuplicateModule = (module: Module) => {
    const duplicatedModule: Module = {
      ...module,
      id: Date.now(),
      title: `${module.title} (Copy)`,
      order_index: modules.length,
      lessons: module.lessons?.map(lesson => ({
        ...lesson,
        id: Date.now() + Math.random(),
        module_id: Date.now()
      }))
    }

    onChange([...modules, duplicatedModule])
  }

  const handleToggleModuleStatus = (moduleId: number) => {
    const newModules = modules.map(module =>
      module.id === moduleId
        ? { ...module, is_optional: !module.is_optional }
        : module
    )
    onChange(newModules)
  }

  const handleSelectModule = (moduleId: number, selected: boolean) => {
    const newSelected = new Set(selectedModules)
    if (selected) {
      newSelected.add(moduleId)
    } else {
      newSelected.delete(moduleId)
    }
    setSelectedModules(newSelected)
  }

  const handleSelectAll = () => {
    if (selectedModules.size === modules.length) {
      setSelectedModules(new Set())
    } else {
      setSelectedModules(new Set(modules.map(m => m.id)))
    }
  }

  const handleBulkDelete = () => {
    const newModules = modules.filter(m => !selectedModules.has(m.id))
    onChange(newModules)
    setSelectedModules(new Set())
    setBulkActionAnchor(null)
  }

  const handleBulkDuplicate = () => {
    const modulesToDuplicate = modules.filter(m => selectedModules.has(m.id))
    const duplicatedModules = modulesToDuplicate.map(module => ({
      ...module,
      id: Date.now() + Math.random(),
      title: `${module.title} (Copy)`,
      order_index: modules.length + modulesToDuplicate.indexOf(module),
      lessons: module.lessons?.map(lesson => ({
        ...lesson,
        id: Date.now() + Math.random(),
        module_id: Date.now() + Math.random()
      }))
    }))

    onChange([...modules, ...duplicatedModules])
    setSelectedModules(new Set())
    setBulkActionAnchor(null)
  }

  return (
    <Box>
      {/* Header with Actions */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h5" fontWeight="bold" gutterBottom>
            Modules & Content
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Create and manage learning modules with drag-and-drop ordering
          </Typography>
        </Box>

        <Box display="flex" gap={1}>
          {selectedModules.size > 0 && (
            <>
              <Button
                variant="outlined"
                onClick={(e) => setBulkActionAnchor(e.currentTarget)}
                startIcon={<MoreIcon />}
              >
                Bulk Actions ({selectedModules.size})
              </Button>
              
              <Menu
                anchorEl={bulkActionAnchor}
                open={Boolean(bulkActionAnchor)}
                onClose={() => setBulkActionAnchor(null)}
              >
                <MenuItem onClick={handleBulkDuplicate}>
                  <CopyIcon sx={{ mr: 1 }} fontSize="small" />
                  Duplicate Selected
                </MenuItem>
                <MenuItem onClick={handleBulkDelete} sx={{ color: 'error.main' }}>
                  <DeleteIcon sx={{ mr: 1 }} fontSize="small" />
                  Delete Selected
                </MenuItem>
              </Menu>
            </>
          )}

          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setShowTemplateDialog(true)}
          >
            Add Module
          </Button>
        </Box>
      </Box>

      {/* Module Statistics */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={3}>
            <Box textAlign="center">
              <Typography variant="h4" fontWeight="bold" color="primary">
                {modules.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Modules
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={3}>
            <Box textAlign="center">
              <Typography variant="h4" fontWeight="bold" color="success.main">
                {modules.reduce((total, module) => total + (module.lessons?.length || 0), 0)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Lessons
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={3}>
            <Box textAlign="center">
              <Typography variant="h4" fontWeight="bold" color="warning.main">
                {Math.round(modules.reduce((total, module) => total + (module.estimated_duration || 0), 0) / 60)}h
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Estimated Duration
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={3}>
            <Box textAlign="center">
              <Typography variant="h4" fontWeight="bold" color="info.main">
                {modules.filter(m => m.is_optional).length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Optional Modules
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Select All Checkbox */}
      {modules.length > 0 && (
        <Box mb={2}>
          <FormControlLabel
            control={
              <Checkbox
                checked={selectedModules.size === modules.length}
                indeterminate={selectedModules.size > 0 && selectedModules.size < modules.length}
                onChange={handleSelectAll}
              />
            }
            label={`Select All (${selectedModules.size}/${modules.length})`}
          />
        </Box>
      )}

      {/* Modules List */}
      {modules.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No modules yet
          </Typography>
          <Typography variant="body2" color="text.secondary" mb={3}>
            Create your first module to start building your learning path
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setShowTemplateDialog(true)}
          >
            Add Your First Module
          </Button>
        </Paper>
      ) : (
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext items={modules.map(m => m.id)} strategy={verticalListSortingStrategy}>
            <AnimatePresence>
              {enhancedModules.map((module, index) => (
                <SortableModuleItem
                  key={module.id}
                  module={module}
                  index={index}
                  onEdit={handleEditModule}
                  onDelete={handleDeleteModule}
                  onDuplicate={handleDuplicateModule}
                  onToggleStatus={handleToggleModuleStatus}
                  isSelected={selectedModules.has(module.id)}
                  onSelect={handleSelectModule}
                />
              ))}
            </AnimatePresence>
          </SortableContext>
        </DndContext>
      )}

      {/* Module Template Selection Dialog */}
      <Dialog
        open={showTemplateDialog}
        onClose={() => setShowTemplateDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Choose Module Template</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" mb={3}>
            Select a template to quickly create a new module with pre-configured lessons
          </Typography>
          
          <Grid container spacing={2}>
            {moduleTemplates.map((template) => (
              <Grid item xs={12} sm={6} key={template.id}>
                <Card 
                  sx={{ 
                    cursor: 'pointer',
                    '&:hover': { 
                      boxShadow: 3,
                      transform: 'translateY(-2px)',
                    },
                    transition: 'all 0.2s ease',
                  }}
                  onClick={() => handleCreateFromTemplate(template)}
                >
                  <CardContent>
                    <Box display="flex" alignItems="center" gap={2} mb={2}>
                      <Box 
                        sx={{ 
                          width: 48, 
                          height: 48, 
                          borderRadius: 1, 
                          bgcolor: template.color + '20',
                          display: 'flex', 
                          alignItems: 'center', 
                          justifyContent: 'center',
                          color: template.color
                        }}
                      >
                        {getModuleIcon(template.category)}
                      </Box>
                      <Box>
                        <Typography variant="h6" fontWeight="bold">
                          {template.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {template.description}
                        </Typography>
                      </Box>
                    </Box>
                    
                    <Typography variant="caption" color="text.secondary">
                      Includes {template.defaultLessons.length} default lessons
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowTemplateDialog(false)}>
            Cancel
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
      >
        <DialogTitle>Delete Module</DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            This action cannot be undone. All lessons within this module will also be deleted.
          </Alert>
          <Typography>
            Are you sure you want to delete this module?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowDeleteDialog(false)}>
            Cancel
          </Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}
