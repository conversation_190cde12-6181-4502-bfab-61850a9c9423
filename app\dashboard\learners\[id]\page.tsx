'use client'

import React, { useState } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Avatar,
  Chip,
  Button,
  IconButton,
  Divider,
  LinearProgress,
  Alert,
  Skeleton,
  useTheme
} from '@mui/material'
import {
  ArrowBack as BackIcon,
  Edit as EditIcon,
  Message as MessageIcon,
  Assignment as AssignIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  Business as BusinessIcon
} from '@mui/icons-material'
import { motion } from 'framer-motion'

import { useLearner } from '@/lib/hooks/useLearners'
import LearnerModal from '@/components/learners/LearnerModal'
import AssignPathModal from '@/components/learners/AssignPathModal'
import MessageModal from '@/components/learners/MessageModal'

export default function LearnerProfilePage() {
  const params = useParams()
  const router = useRouter()
  const theme = useTheme()
  const learnerId = params.id as string

  const [showEditModal, setShowEditModal] = useState(false)
  const [showAssignModal, setShowAssignModal] = useState(false)
  const [showMessageModal, setShowMessageModal] = useState(false)

  const { data: learner, isLoading, error } = useLearner(learnerId)

  if (isLoading) {
    return (
      <Box>
        <Box display="flex" alignItems="center" gap={2} mb={3}>
          <Skeleton variant="circular" width={40} height={40} />
          <Skeleton variant="text" width={200} height={32} />
        </Box>
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Skeleton variant="circular" width={80} height={80} sx={{ mx: 'auto', mb: 2 }} />
                <Skeleton variant="text" height={24} />
                <Skeleton variant="text" height={20} />
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Skeleton variant="text" width={150} height={24} />
                <Skeleton variant="rectangular" height={200} sx={{ mt: 2 }} />
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    )
  }

  if (error || !learner) {
    return (
      <Box>
        <Alert severity="error">
          Failed to load learner profile. Please try again.
        </Alert>
      </Box>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success'
      case 'inactive':
        return 'default'
      case 'suspended':
        return 'error'
      default:
        return 'default'
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Box>
        {/* Header */}
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
          <Box display="flex" alignItems="center" gap={2}>
            <IconButton onClick={() => router.back()}>
              <BackIcon />
            </IconButton>
            <Box>
              <Typography variant="h4" fontWeight="bold">
                {learner.full_name}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Learner Profile
              </Typography>
            </Box>
          </Box>

          <Box display="flex" gap={1}>
            <Button
              variant="outlined"
              startIcon={<MessageIcon />}
              onClick={() => setShowMessageModal(true)}
            >
              Message
            </Button>
            <Button
              variant="outlined"
              startIcon={<AssignIcon />}
              onClick={() => setShowAssignModal(true)}
            >
              Assign Path
            </Button>
            <Button
              variant="contained"
              startIcon={<EditIcon />}
              onClick={() => setShowEditModal(true)}
            >
              Edit Profile
            </Button>
          </Box>
        </Box>

        <Grid container spacing={3}>
          {/* Profile Card */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Avatar
                  src={learner.avatar_url}
                  sx={{ width: 80, height: 80, mx: 'auto', mb: 2 }}
                >
                  {learner.full_name.charAt(0).toUpperCase()}
                </Avatar>
                
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  {learner.full_name}
                </Typography>
                
                <Chip
                  label={learner.status}
                  color={getStatusColor(learner.status) as any}
                  sx={{ mb: 2 }}
                />

                <Divider sx={{ my: 2 }} />

                <Box textAlign="left">
                  <Box display="flex" alignItems="center" gap={1} mb={1}>
                    <EmailIcon fontSize="small" color="action" />
                    <Typography variant="body2">{learner.email}</Typography>
                  </Box>
                  
                  {learner.phone && (
                    <Box display="flex" alignItems="center" gap={1} mb={1}>
                      <PhoneIcon fontSize="small" color="action" />
                      <Typography variant="body2">{learner.phone}</Typography>
                    </Box>
                  )}
                  
                  {learner.department && (
                    <Box display="flex" alignItems="center" gap={1} mb={1}>
                      <BusinessIcon fontSize="small" color="action" />
                      <Typography variant="body2">{learner.department}</Typography>
                    </Box>
                  )}
                  
                  {learner.location && (
                    <Box display="flex" alignItems="center" gap={1} mb={1}>
                      <LocationIcon fontSize="small" color="action" />
                      <Typography variant="body2">{learner.location}</Typography>
                    </Box>
                  )}
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Progress & Stats */}
          <Grid item xs={12} md={8}>
            <Grid container spacing={3}>
              {/* Progress Overview */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Learning Progress
                    </Typography>
                    
                    <Grid container spacing={3}>
                      <Grid item xs={12} sm={6} md={3}>
                        <Box textAlign="center">
                          <Typography variant="h4" color="primary" fontWeight="bold">
                            {learner.completion_rate || 0}%
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Completion Rate
                          </Typography>
                        </Box>
                      </Grid>
                      
                      <Grid item xs={12} sm={6} md={3}>
                        <Box textAlign="center">
                          <Typography variant="h4" color="success.main" fontWeight="bold">
                            {learner.completed_paths || 0}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Completed Paths
                          </Typography>
                        </Box>
                      </Grid>
                      
                      <Grid item xs={12} sm={6} md={3}>
                        <Box textAlign="center">
                          <Typography variant="h4" color="info.main" fontWeight="bold">
                            {learner.active_paths || 0}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Active Paths
                          </Typography>
                        </Box>
                      </Grid>
                      
                      <Grid item xs={12} sm={6} md={3}>
                        <Box textAlign="center">
                          <Typography variant="h4" color="warning.main" fontWeight="bold">
                            {learner.overdue_assignments || 0}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Overdue
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>

                    <Box mt={3}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Overall Progress
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={learner.completion_rate || 0}
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              {/* Recent Activity */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Recent Activity
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Activity tracking will be implemented in the full version.
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Grid>
        </Grid>

        {/* Modals */}
        <LearnerModal
          open={showEditModal}
          onClose={() => setShowEditModal(false)}
          learner={learner}
        />

        <AssignPathModal
          open={showAssignModal}
          onClose={() => setShowAssignModal(false)}
          learnerIds={[learner.id]}
        />

        <MessageModal
          open={showMessageModal}
          onClose={() => setShowMessageModal(false)}
          recipientIds={[learner.id]}
        />
      </Box>
    </motion.div>
  )
}
