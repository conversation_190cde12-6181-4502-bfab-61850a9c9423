'use client'

import React from 'react'
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Alert,
  Paper,
  LinearProgress
} from '@mui/material'
import {
  CheckCircle as CheckIcon,
  Schedule as ScheduleIcon,
  People as PeopleIcon,
  School as SchoolIcon,
  PlayArrow as PlayIcon,
  Quiz as QuizIcon,
  PictureAsPdf as PdfIcon,
  Link as LinkIcon,
  Code as CodeIcon,
  Assignment as AssignmentIcon
} from '@mui/icons-material'

import { PathCreationForm } from '@/lib/types/learning-paths'

interface PathPreviewProps {
  pathData: Partial<PathCreationForm>
  modules: any[]
  assignments: any[]
}

const PathPreview: React.FC<PathPreviewProps> = ({
  pathData,
  modules,
  assignments
}) => {
  const getLessonIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <PlayIcon />
      case 'pdf':
        return <PdfIcon />
      case 'quiz':
        return <QuizIcon />
      case 'link':
        return <LinkIcon />
      case 'simulation':
        return <CodeIcon />
      default:
        return <PlayIcon />
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'success'
      case 'intermediate':
        return 'warning'
      case 'advanced':
        return 'error'
      default:
        return 'default'
    }
  }

  const totalLessons = modules.reduce((total, module) => 
    total + (module.lessons?.length || 0), 0)
  
  const totalDuration = modules.reduce((total, module) => 
    total + (module.lessons?.reduce((lessonTotal: number, lesson: any) => 
      lessonTotal + (lesson.estimated_duration || 0), 0) || 0), 0)

  const estimatedHours = Math.round(totalDuration / 60 * 10) / 10

  const completionChecklist = [
    { label: 'Basic Information', completed: !!(pathData.title && pathData.description && pathData.category) },
    { label: 'Learning Objectives', completed: !!(pathData.objectives && pathData.objectives.length > 0) },
    { label: 'Modules & Lessons', completed: modules.length > 0 && totalLessons > 0 },
    { label: 'Duration Set', completed: !!(pathData.duration && pathData.duration > 0) },
    { label: 'Difficulty Level', completed: !!pathData.difficulty },
    { label: 'Category Selected', completed: !!pathData.category }
  ]

  const completedItems = completionChecklist.filter(item => item.completed).length
  const completionPercentage = (completedItems / completionChecklist.length) * 100

  return (
    <Box>
      <Grid container spacing={3}>
        {/* Completion Status */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Learning Path Completion Status
              </Typography>
              <Box mb={2}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="body2" color="text.secondary">
                    Progress
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {completedItems}/{completionChecklist.length} completed
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={completionPercentage}
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
              
              <List dense>
                {completionChecklist.map((item, index) => (
                  <ListItem key={index} sx={{ py: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      <CheckIcon 
                        color={item.completed ? 'success' : 'disabled'} 
                        fontSize="small"
                      />
                    </ListItemIcon>
                    <ListItemText 
                      primary={item.label}
                      sx={{ 
                        '& .MuiListItemText-primary': {
                          color: item.completed ? 'text.primary' : 'text.secondary',
                          textDecoration: item.completed ? 'none' : 'none'
                        }
                      }}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Path Overview */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h5" fontWeight="bold" gutterBottom>
                {pathData.title || 'Untitled Learning Path'}
              </Typography>
              
              <Typography variant="body1" color="text.secondary" paragraph>
                {pathData.description || 'No description provided'}
              </Typography>

              <Box display="flex" gap={1} mb={3} flexWrap="wrap">
                {pathData.difficulty && (
                  <Chip 
                    label={pathData.difficulty} 
                    color={getDifficultyColor(pathData.difficulty) as any}
                    size="small"
                  />
                )}
                {pathData.category && (
                  <Chip 
                    label={pathData.category} 
                    variant="outlined" 
                    size="small"
                  />
                )}
                {pathData.is_template && (
                  <Chip 
                    label="Template" 
                    color="secondary" 
                    variant="outlined" 
                    size="small"
                  />
                )}
              </Box>

              {pathData.tags && pathData.tags.length > 0 && (
                <Box mb={3}>
                  <Typography variant="subtitle2" gutterBottom>
                    Tags:
                  </Typography>
                  <Box display="flex" gap={0.5} flexWrap="wrap">
                    {pathData.tags.map((tag, index) => (
                      <Chip
                        key={index}
                        label={tag}
                        size="small"
                        variant="outlined"
                        sx={{ fontSize: '0.75rem' }}
                      />
                    ))}
                  </Box>
                </Box>
              )}

              {pathData.objectives && pathData.objectives.length > 0 && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Learning Objectives:
                  </Typography>
                  <List dense>
                    {pathData.objectives.map((objective, index) => (
                      <ListItem key={index} sx={{ py: 0.5, pl: 0 }}>
                        <ListItemIcon sx={{ minWidth: 24 }}>
                          <SchoolIcon fontSize="small" color="primary" />
                        </ListItemIcon>
                        <ListItemText primary={objective} />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Stats */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Stats
              </Typography>
              
              <Box display="flex" flexDirection="column" gap={2}>
                <Box display="flex" alignItems="center" gap={1}>
                  <ScheduleIcon color="action" fontSize="small" />
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      Duration
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {pathData.duration || 0} weeks ({estimatedHours}h estimated)
                    </Typography>
                  </Box>
                </Box>

                <Box display="flex" alignItems="center" gap={1}>
                  <SchoolIcon color="action" fontSize="small" />
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      Content
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {modules.length} modules, {totalLessons} lessons
                    </Typography>
                  </Box>
                </Box>

                <Box display="flex" alignItems="center" gap={1}>
                  <AssignmentIcon color="action" fontSize="small" />
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      Assignments
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {assignments.length} assignment rule{assignments.length !== 1 ? 's' : ''}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Modules Structure */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Learning Path Structure
              </Typography>
              
              {modules.length > 0 ? (
                modules.map((module, index) => (
                  <Box key={module.id} mb={3}>
                    <Paper variant="outlined" sx={{ p: 2 }}>
                      <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                        Module {index + 1}: {module.title || 'Untitled Module'}
                      </Typography>
                      
                      {module.description && (
                        <Typography variant="body2" color="text.secondary" mb={2}>
                          {module.description}
                        </Typography>
                      )}

                      {module.lessons && module.lessons.length > 0 ? (
                        <List dense>
                          {module.lessons.map((lesson: any, lessonIndex: number) => (
                            <ListItem key={lesson.id} sx={{ py: 0.5, pl: 2 }}>
                              <ListItemIcon sx={{ minWidth: 32 }}>
                                {getLessonIcon(lesson.type)}
                              </ListItemIcon>
                              <ListItemText
                                primary={
                                  <Box display="flex" alignItems="center" gap={1}>
                                    <Typography variant="body2">
                                      {lesson.title || 'Untitled Lesson'}
                                    </Typography>
                                    <Chip 
                                      label={lesson.type} 
                                      size="small" 
                                      variant="outlined"
                                      sx={{ fontSize: '0.7rem', height: 20 }}
                                    />
                                    {lesson.is_optional && (
                                      <Chip 
                                        label="Optional" 
                                        size="small" 
                                        color="secondary"
                                        variant="outlined"
                                        sx={{ fontSize: '0.7rem', height: 20 }}
                                      />
                                    )}
                                  </Box>
                                }
                                secondary={`${lesson.estimated_duration || 0} minutes`}
                              />
                            </ListItem>
                          ))}
                        </List>
                      ) : (
                        <Typography variant="body2" color="text.secondary" style={{ fontStyle: 'italic' }}>
                          No lessons added to this module yet
                        </Typography>
                      )}
                    </Paper>
                  </Box>
                ))
              ) : (
                <Alert severity="warning">
                  No modules have been created yet. Add modules and lessons to complete your learning path.
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Assignments Summary */}
        {assignments.length > 0 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Assignment Summary
                </Typography>
                
                {assignments.map((assignment, index) => (
                  <Box key={assignment.id} mb={2}>
                    <Typography variant="body2" fontWeight="medium">
                      Assignment {index + 1}:
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Type: {assignment.type} • 
                      {assignment.due_date 
                        ? ` Due: ${new Date(assignment.due_date).toLocaleDateString()}`
                        : ' No due date'
                      }
                      {assignment.auto_assign && ' • Auto-assign enabled'}
                    </Typography>
                  </Box>
                ))}
              </CardContent>
            </Card>
          </Grid>
        )}

        {/* Readiness Check */}
        <Grid item xs={12}>
          {completionPercentage === 100 ? (
            <Alert severity="success">
              <Typography variant="body2">
                <strong>Ready to Publish!</strong> Your learning path is complete and ready to be published. 
                Learners will be able to access it immediately after publishing.
              </Typography>
            </Alert>
          ) : (
            <Alert severity="warning">
              <Typography variant="body2">
                <strong>Almost Ready!</strong> Complete the remaining items in the checklist above 
                before publishing your learning path.
              </Typography>
            </Alert>
          )}
        </Grid>
      </Grid>
    </Box>
  )
}

export default PathPreview
