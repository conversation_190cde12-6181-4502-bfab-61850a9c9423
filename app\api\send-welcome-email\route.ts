import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

// POST /api/send-welcome-email
export async function POST(request: NextRequest) {
  try {
    const { user_id } = await request.json()

    if (!user_id) {
      return NextResponse.json({
        error: 'User ID is required'
      }, { status: 400 })
    }

    // Get user data
    const { data: userData, error: userError } = await supabaseAdmin
      .from('users')
      .select(`
        id,
        email,
        full_name,
        tenants (
          id,
          name
        )
      `)
      .eq('id', user_id)
      .single()

    if (userError || !userData) {
      return NextResponse.json({
        error: 'User not found'
      }, { status: 404 })
    }

    // For now, just log that we would send an email
    // In a real implementation, you would integrate with an email service
    console.log(`Would send welcome email to ${userData.email} for ${userData.full_name}`)
    
    // You could integrate with services like:
    // - Resend
    // - SendGrid
    // - AWS SES
    // - Supabase Edge Functions for email

    return NextResponse.json({
      success: true,
      message: 'Welcome email queued for sending'
    })

  } catch (error) {
    console.error('Send welcome email error:', error)
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
