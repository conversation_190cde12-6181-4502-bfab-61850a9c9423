-- Enhanced Competencies Schema for ZenithLearn AI
-- This migration adds advanced competency management features

-- Create competency_frameworks table for custom frameworks
CREATE TABLE competency_frameworks (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    structure_json JSONB DEFAULT '{}',
    is_default BOOLEAN DEFAULT FALSE,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, name)
);

-- Create framework_competencies table to link frameworks with competencies
CREATE TABLE framework_competencies (
    id SERIAL PRIMARY KEY,
    framework_id INTEGER REFERENCES competency_frameworks(id) ON DELETE CASCADE,
    competency_id INTEGER REFERENCES competencies(id) ON DELETE CASCADE,
    parent_id INTEGER REFERENCES framework_competencies(id),
    order_index INTEGER DEFAULT 0,
    level INTEGER DEFAULT 1,
    UNIQUE(framework_id, competency_id)
);

-- Create competency_categories table for better organization
CREATE TABLE competency_categories (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#1976d2', -- hex color
    icon VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, name)
);

-- Create competency_tags table for flexible tagging
CREATE TABLE competency_tags (
    id SERIAL PRIMARY KEY,
    competency_id INTEGER REFERENCES competencies(id) ON DELETE CASCADE,
    tag VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(competency_id, tag)
);

-- Create competency_versions table for change tracking
CREATE TABLE competency_versions (
    id SERIAL PRIMARY KEY,
    competency_id INTEGER REFERENCES competencies(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    version_json JSONB NOT NULL,
    change_summary TEXT,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create competency_audit table for detailed audit logging
CREATE TABLE competency_audit (
    id SERIAL PRIMARY KEY,
    competency_id INTEGER REFERENCES competencies(id) ON DELETE CASCADE,
    action VARCHAR(50) NOT NULL, -- 'created', 'updated', 'deleted', 'mapped', 'unmapped'
    admin_id UUID REFERENCES users(id),
    changes JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create skill_gaps table for AI-powered gap analysis
CREATE TABLE skill_gaps (
    id SERIAL PRIMARY KEY,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    competency_id INTEGER REFERENCES competencies(id) ON DELETE CASCADE,
    gap_score DECIMAL(3,2) CHECK (gap_score >= 0 AND gap_score <= 1),
    suggested_path_id INTEGER REFERENCES learning_paths(id),
    confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    detected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(20) DEFAULT 'open' CHECK (status IN ('open', 'addressed', 'dismissed')),
    UNIQUE(learner_id, competency_id)
);

-- Create competency_badges table for gamification
CREATE TABLE competency_badges (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    competency_id INTEGER REFERENCES competencies(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    criteria_json JSONB NOT NULL,
    badge_image_url TEXT,
    points_value INTEGER DEFAULT 0,
    rarity VARCHAR(20) DEFAULT 'common' CHECK (rarity IN ('common', 'rare', 'epic', 'legendary')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create learner_badges table to track earned badges
CREATE TABLE learner_badges (
    id SERIAL PRIMARY KEY,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    badge_id INTEGER REFERENCES competency_badges(id) ON DELETE CASCADE,
    earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    evidence JSONB DEFAULT '{}',
    UNIQUE(learner_id, badge_id)
);

-- Create mapping_suggestions table for AI-powered mapping
CREATE TABLE mapping_suggestions (
    id SERIAL PRIMARY KEY,
    competency_id INTEGER REFERENCES competencies(id) ON DELETE CASCADE,
    path_id INTEGER REFERENCES learning_paths(id) ON DELETE CASCADE,
    confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    reasoning TEXT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected')),
    suggested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reviewed_by UUID REFERENCES users(id),
    reviewed_at TIMESTAMP WITH TIME ZONE
);

-- Create skill_journeys table for learner progression visualization
CREATE TABLE skill_journeys (
    id SERIAL PRIMARY KEY,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    journey_json JSONB NOT NULL,
    current_step INTEGER DEFAULT 0,
    total_steps INTEGER NOT NULL,
    estimated_completion_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create learning_plans table for AI-tailored learning paths
CREATE TABLE learning_plans (
    id SERIAL PRIMARY KEY,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    plan_json JSONB NOT NULL,
    assigned_by UUID REFERENCES users(id),
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'paused', 'cancelled')),
    completion_percentage DECIMAL(5,2) DEFAULT 0.00,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create competency_reviews table for approval workflow
CREATE TABLE competency_reviews (
    id SERIAL PRIMARY KEY,
    competency_id INTEGER REFERENCES competencies(id) ON DELETE CASCADE,
    reviewer_id UUID REFERENCES users(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'needs_changes')),
    comments TEXT,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create external_competencies table for third-party integrations
CREATE TABLE external_competencies (
    id SERIAL PRIMARY KEY,
    competency_id INTEGER REFERENCES competencies(id) ON DELETE CASCADE,
    external_id VARCHAR(255) NOT NULL,
    source VARCHAR(100) NOT NULL, -- 'linkedin', 'coursera', 'sap', etc.
    sync_data JSONB DEFAULT '{}',
    last_synced_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(competency_id, external_id, source)
);

-- Create competency_goals table for gamification
CREATE TABLE competency_goals (
    id SERIAL PRIMARY KEY,
    competency_id INTEGER REFERENCES competencies(id) ON DELETE CASCADE,
    goal_type VARCHAR(50) NOT NULL, -- 'mastery', 'completion', 'time_based'
    reward_type VARCHAR(50) NOT NULL, -- 'points', 'badge', 'certificate'
    reward_value INTEGER DEFAULT 0,
    criteria_json JSONB NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create learner_points table for gamification scoring
CREATE TABLE learner_points (
    id SERIAL PRIMARY KEY,
    learner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    competency_id INTEGER REFERENCES competencies(id) ON DELETE CASCADE,
    points INTEGER NOT NULL,
    reason VARCHAR(255),
    awarded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    awarded_by UUID REFERENCES users(id)
);

-- Create report_templates table for custom reporting
CREATE TABLE report_templates (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    admin_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    metrics_json JSONB NOT NULL,
    filters_json JSONB DEFAULT '{}',
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance optimization
CREATE INDEX idx_competencies_tenant_id ON competencies(tenant_id);
CREATE INDEX idx_competencies_category ON competencies(category);
CREATE INDEX idx_competencies_status ON competencies(status);
CREATE INDEX idx_competencies_created_by ON competencies(created_by);
CREATE INDEX idx_competency_frameworks_tenant_id ON competency_frameworks(tenant_id);
CREATE INDEX idx_framework_competencies_framework_id ON framework_competencies(framework_id);
CREATE INDEX idx_framework_competencies_competency_id ON framework_competencies(competency_id);
CREATE INDEX idx_competency_categories_tenant_id ON competency_categories(tenant_id);
CREATE INDEX idx_competency_tags_competency_id ON competency_tags(competency_id);
CREATE INDEX idx_competency_versions_competency_id ON competency_versions(competency_id);
CREATE INDEX idx_competency_audit_competency_id ON competency_audit(competency_id);
CREATE INDEX idx_competency_audit_admin_id ON competency_audit(admin_id);
CREATE INDEX idx_competency_audit_created_at ON competency_audit(created_at);
CREATE INDEX idx_skill_gaps_learner_id ON skill_gaps(learner_id);
CREATE INDEX idx_skill_gaps_competency_id ON skill_gaps(competency_id);
CREATE INDEX idx_skill_gaps_status ON skill_gaps(status);
CREATE INDEX idx_competency_badges_tenant_id ON competency_badges(tenant_id);
CREATE INDEX idx_competency_badges_competency_id ON competency_badges(competency_id);
CREATE INDEX idx_learner_badges_learner_id ON learner_badges(learner_id);
CREATE INDEX idx_learner_badges_badge_id ON learner_badges(badge_id);
CREATE INDEX idx_mapping_suggestions_competency_id ON mapping_suggestions(competency_id);
CREATE INDEX idx_mapping_suggestions_path_id ON mapping_suggestions(path_id);
CREATE INDEX idx_mapping_suggestions_status ON mapping_suggestions(status);
CREATE INDEX idx_skill_journeys_learner_id ON skill_journeys(learner_id);
CREATE INDEX idx_learning_plans_learner_id ON learning_plans(learner_id);
CREATE INDEX idx_learning_plans_assigned_by ON learning_plans(assigned_by);
CREATE INDEX idx_learning_plans_status ON learning_plans(status);
CREATE INDEX idx_competency_reviews_competency_id ON competency_reviews(competency_id);
CREATE INDEX idx_competency_reviews_reviewer_id ON competency_reviews(reviewer_id);
CREATE INDEX idx_competency_reviews_status ON competency_reviews(status);
CREATE INDEX idx_external_competencies_competency_id ON external_competencies(competency_id);
CREATE INDEX idx_external_competencies_source ON external_competencies(source);
CREATE INDEX idx_competency_goals_competency_id ON competency_goals(competency_id);
CREATE INDEX idx_learner_points_learner_id ON learner_points(learner_id);
CREATE INDEX idx_learner_points_competency_id ON learner_points(competency_id);
CREATE INDEX idx_report_templates_tenant_id ON report_templates(tenant_id);
CREATE INDEX idx_report_templates_admin_id ON report_templates(admin_id);

-- Add updated_at triggers for tables that need them
CREATE TRIGGER update_competencies_updated_at BEFORE UPDATE ON competencies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_competency_frameworks_updated_at BEFORE UPDATE ON competency_frameworks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_skill_journeys_updated_at BEFORE UPDATE ON skill_journeys FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_learning_plans_updated_at BEFORE UPDATE ON learning_plans FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_report_templates_updated_at BEFORE UPDATE ON report_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
