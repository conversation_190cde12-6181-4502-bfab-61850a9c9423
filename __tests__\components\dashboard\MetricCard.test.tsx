import { render, screen } from '@testing-library/react'
import { ThemeProvider } from '@mui/material/styles'
import { People as PeopleIcon } from '@mui/icons-material'
import '@testing-library/jest-dom'

import MetricCard from '@/components/dashboard/MetricCard'
import { theme } from '@/lib/theme'

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={theme}>
      {component}
    </ThemeProvider>
  )
}

describe('MetricCard', () => {
  it('renders metric card with title and value', () => {
    renderWithTheme(
      <MetricCard
        title="Active Learners"
        value={1247}
        icon={<PeopleIcon />}
        color="primary"
      />
    )

    expect(screen.getByText('Active Learners')).toBeInTheDocument()
    expect(screen.getByText('1,247')).toBeInTheDocument()
  })

  it('displays positive change correctly', () => {
    renderWithTheme(
      <MetricCard
        title="Active Learners"
        value={1247}
        change={12.5}
        icon={<PeopleIcon />}
        color="primary"
      />
    )

    expect(screen.getByText('+12.5%')).toBeInTheDocument()
    expect(screen.getByText('vs last month')).toBeInTheDocument()
  })

  it('displays negative change correctly', () => {
    renderWithTheme(
      <MetricCard
        title="Active Learners"
        value={1247}
        change={-5.2}
        icon={<PeopleIcon />}
        color="primary"
      />
    )

    expect(screen.getByText('-5.2%')).toBeInTheDocument()
  })

  it('shows loading skeleton when isLoading is true', () => {
    renderWithTheme(
      <MetricCard
        title="Active Learners"
        value={1247}
        icon={<PeopleIcon />}
        color="primary"
        isLoading={true}
      />
    )

    // Should not show the actual content when loading
    expect(screen.queryByText('Active Learners')).not.toBeInTheDocument()
    expect(screen.queryByText('1,247')).not.toBeInTheDocument()
  })

  it('handles string values correctly', () => {
    renderWithTheme(
      <MetricCard
        title="Completion Rate"
        value="78.3%"
        icon={<PeopleIcon />}
        color="success"
      />
    )

    expect(screen.getByText('78.3%')).toBeInTheDocument()
  })
})
