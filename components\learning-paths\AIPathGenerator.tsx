'use client'

import React, { useState } from 'react'
import {
  Box,
  Typography,
  TextField,
  Button,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Grid,
  Alert,
  CircularProgress,
  Collapse,
  IconButton,
  Chip
} from '@mui/material'
import {
  AutoAwesome as AIIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material'
import { motion, AnimatePresence } from 'framer-motion'

import { useGeneratePathSuggestions } from '@/lib/hooks/useLearningPaths'
import { AIPathSuggestion, LessonType } from '@/lib/types/learning-paths'

interface AIPathGeneratorProps {
  onGenerate: (suggestion: AIPathSuggestion) => void
}

const AIPathGenerator: React.FC<AIPathGeneratorProps> = ({ onGenerate }) => {
  const [isExpanded, setIsExpanded] = useState(false)
  const [prompt, setPrompt] = useState('')
  const [options, setOptions] = useState({
    difficulty: 'beginner',
    duration: 4,
    category: 'Technology'
  })
  const [lastSuggestion, setLastSuggestion] = useState<AIPathSuggestion | null>(null)

  const generateMutation = useGeneratePathSuggestions()

  const handleGenerate = async () => {
    if (!prompt.trim()) return

    try {
      const suggestion = await generateMutation.mutateAsync({ prompt, options })
      setLastSuggestion(suggestion)
    } catch (error) {
      console.error('Failed to generate path:', error)

      // Fallback: Generate a mock learning path structure
      console.log('Using fallback AI generation...')
      const fallbackSuggestion = generateFallbackPath(prompt, options)
      setLastSuggestion(fallbackSuggestion)
    }
  }

  // Fallback function to generate a basic learning path structure
  const generateFallbackPath = (prompt: string, options: any): AIPathSuggestion => {
    const topics = prompt.toLowerCase()
    let category = 'General'
    let modules: AIPathSuggestion['modules'] = []

    // Simple keyword-based path generation
    if (topics.includes('data science') || topics.includes('python') || topics.includes('machine learning')) {
      category = 'Technology'
      modules = [
        {
          title: 'Introduction and Setup',
          lessons: [
            { title: 'Course Overview', type: 'video' as LessonType, description: 'Introduction to the learning path', estimated_duration: 15 },
            { title: 'Environment Setup', type: 'pdf' as LessonType, description: 'Setting up your development environment', estimated_duration: 30 },
            { title: 'Knowledge Check', type: 'quiz' as LessonType, description: 'Test your understanding', estimated_duration: 10 }
          ]
        },
        {
          title: 'Core Concepts',
          lessons: [
            { title: 'Fundamental Principles', type: 'video' as LessonType, description: 'Learn the basic concepts', estimated_duration: 45 },
            { title: 'Hands-on Practice', type: 'project' as LessonType, description: 'Apply what you learned', estimated_duration: 60 },
            { title: 'Assessment', type: 'quiz' as LessonType, description: 'Evaluate your progress', estimated_duration: 20 }
          ]
        },
        {
          title: 'Advanced Topics',
          lessons: [
            { title: 'Advanced Techniques', type: 'video' as LessonType, description: 'Explore advanced concepts', estimated_duration: 50 },
            { title: 'Real-world Application', type: 'project' as LessonType, description: 'Build a complete project', estimated_duration: 90 },
            { title: 'Final Assessment', type: 'quiz' as LessonType, description: 'Comprehensive evaluation', estimated_duration: 30 }
          ]
        }
      ]
    } else if (topics.includes('leadership') || topics.includes('management')) {
      category = 'Leadership'
      modules = [
        {
          title: 'Leadership Fundamentals',
          lessons: [
            { title: 'What Makes a Great Leader', type: 'video' as LessonType, description: 'Understanding leadership principles', estimated_duration: 25 },
            { title: 'Leadership Styles', type: 'pdf' as LessonType, description: 'Different approaches to leadership', estimated_duration: 20 },
            { title: 'Self-Assessment', type: 'quiz' as LessonType, description: 'Identify your leadership style', estimated_duration: 15 }
          ]
        },
        {
          title: 'Team Management',
          lessons: [
            { title: 'Building High-Performance Teams', type: 'video' as LessonType, description: 'Creating effective teams', estimated_duration: 35 },
            { title: 'Communication Strategies', type: 'video' as LessonType, description: 'Effective team communication', estimated_duration: 30 },
            { title: 'Conflict Resolution', type: 'simulation' as LessonType, description: 'Practice resolving team conflicts', estimated_duration: 40 }
          ]
        }
      ]
    } else {
      // Generic structure
      modules = [
        {
          title: 'Getting Started',
          lessons: [
            { title: 'Introduction', type: 'video' as LessonType, description: 'Course introduction and objectives', estimated_duration: 20 },
            { title: 'Prerequisites', type: 'pdf' as LessonType, description: 'What you need to know before starting', estimated_duration: 15 },
            { title: 'Quick Check', type: 'quiz' as LessonType, description: 'Verify your readiness', estimated_duration: 10 }
          ]
        },
        {
          title: 'Main Content',
          lessons: [
            { title: 'Core Concepts', type: 'video' as LessonType, description: 'Learn the main concepts', estimated_duration: 40 },
            { title: 'Practice Exercise', type: 'project' as LessonType, description: 'Apply your knowledge', estimated_duration: 60 },
            { title: 'Knowledge Check', type: 'quiz' as LessonType, description: 'Test your understanding', estimated_duration: 15 }
          ]
        }
      ]
    }

    return {
      title: `${prompt} - Learning Path`,
      description: `A comprehensive learning path covering ${prompt.toLowerCase()}. This AI-generated path provides structured learning with practical exercises and assessments.`,
      difficulty: options.difficulty || 'beginner',
      estimated_duration: options.duration || 4,
      skills: [prompt.split(' ')[0], 'Problem Solving', 'Critical Thinking'],
      modules
    }
  }

  const handleApplySuggestion = () => {
    if (lastSuggestion) {
      onGenerate(lastSuggestion)
      setIsExpanded(false)
    }
  }

  const handleRegenerateWithChanges = () => {
    if (prompt.trim()) {
      handleGenerate()
    }
  }

  const categories = [
    'Technology',
    'Leadership',
    'Sales',
    'Marketing',
    'HR',
    'Finance',
    'Operations',
    'Compliance',
    'Soft Skills'
  ]

  return (
    <Card sx={{ border: '2px dashed', borderColor: 'primary.main', bgcolor: 'primary.50' }}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box display="flex" alignItems="center" gap={1}>
            <AIIcon color="primary" />
            <Typography variant="h6" fontWeight="bold" color="primary">
              AI Path Generator
            </Typography>
          </Box>
          <IconButton
            onClick={() => setIsExpanded(!isExpanded)}
            color="primary"
          >
            {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>

        <Typography variant="body2" color="text.secondary" mb={2}>
          Let AI help you create a comprehensive learning path structure based on your requirements
        </Typography>

        <Collapse in={isExpanded}>
          <Box mt={2}>
            <Grid container spacing={3}>
              {/* AI Prompt */}
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Describe your learning path"
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder="e.g., Create a comprehensive data science course for beginners covering Python, statistics, and machine learning..."
                  helperText="Be specific about topics, target audience, and learning outcomes"
                />
              </Grid>

              {/* Options */}
              <Grid item xs={12} md={4}>
                <FormControl fullWidth>
                  <InputLabel>Difficulty Level</InputLabel>
                  <Select
                    value={options.difficulty}
                    label="Difficulty Level"
                    onChange={(e) => setOptions(prev => ({ ...prev, difficulty: e.target.value }))}
                  >
                    <MenuItem value="beginner">Beginner</MenuItem>
                    <MenuItem value="intermediate">Intermediate</MenuItem>
                    <MenuItem value="advanced">Advanced</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={4}>
                <FormControl fullWidth>
                  <InputLabel>Category</InputLabel>
                  <Select
                    value={options.category}
                    label="Category"
                    onChange={(e) => setOptions(prev => ({ ...prev, category: e.target.value }))}
                  >
                    {categories.map((category) => (
                      <MenuItem key={category} value={category}>
                        {category}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={4}>
                <Typography gutterBottom>Duration (weeks)</Typography>
                <Slider
                  value={options.duration}
                  onChange={(_, value) => setOptions(prev => ({ ...prev, duration: value as number }))}
                  min={1}
                  max={26}
                  marks={[
                    { value: 1, label: '1w' },
                    { value: 4, label: '1m' },
                    { value: 12, label: '3m' },
                    { value: 26, label: '6m' }
                  ]}
                  valueLabelDisplay="on"
                  valueLabelFormat={(value) => `${value}w`}
                />
              </Grid>

              {/* Generate Button */}
              <Grid item xs={12}>
                <Button
                  variant="contained"
                  startIcon={generateMutation.isPending ? <CircularProgress size={20} /> : <AIIcon />}
                  onClick={handleGenerate}
                  disabled={!prompt.trim() || generateMutation.isPending}
                  fullWidth
                  size="large"
                >
                  {generateMutation.isPending ? 'Generating...' : 'Generate Learning Path'}
                </Button>
              </Grid>
            </Grid>

            {/* AI Suggestion Display */}
            <AnimatePresence>
              {lastSuggestion && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <Box mt={3}>
                    <Alert severity="success" sx={{ mb: 2 }}>
                      AI has generated a learning path suggestion! Review it below and apply if it looks good.
                    </Alert>

                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="h6" fontWeight="bold" gutterBottom>
                          {lastSuggestion.title}
                        </Typography>
                        
                        <Typography variant="body2" color="text.secondary" mb={2}>
                          {lastSuggestion.description}
                        </Typography>

                        <Box display="flex" gap={1} mb={3}>
                          <Chip 
                            label={lastSuggestion.difficulty} 
                            color="primary" 
                            size="small" 
                          />
                          <Chip 
                            label={`${lastSuggestion.estimated_duration} weeks`} 
                            variant="outlined" 
                            size="small" 
                          />
                          <Chip 
                            label={`${lastSuggestion.modules.length} modules`} 
                            variant="outlined" 
                            size="small" 
                          />
                        </Box>

                        {/* Skills */}
                        {lastSuggestion.skills && lastSuggestion.skills.length > 0 && (
                          <Box mb={3}>
                            <Typography variant="subtitle2" gutterBottom>
                              Skills Covered:
                            </Typography>
                            <Box display="flex" gap={0.5} flexWrap="wrap">
                              {lastSuggestion.skills.map((skill, index) => (
                                <Chip
                                  key={index}
                                  label={skill}
                                  size="small"
                                  variant="outlined"
                                />
                              ))}
                            </Box>
                          </Box>
                        )}

                        {/* Modules Preview */}
                        <Typography variant="subtitle2" gutterBottom>
                          Modules Structure:
                        </Typography>
                        <Box>
                          {lastSuggestion.modules.map((module, index) => (
                            <Box key={index} mb={2}>
                              <Typography variant="body2" fontWeight="bold">
                                {index + 1}. {module.title}
                              </Typography>
                              <Box ml={2}>
                                {module.lessons.map((lesson, lessonIndex) => (
                                  <Typography 
                                    key={lessonIndex} 
                                    variant="caption" 
                                    color="text.secondary"
                                    display="block"
                                  >
                                    • {lesson.title} ({lesson.type}, {lesson.estimated_duration}min)
                                  </Typography>
                                ))}
                              </Box>
                            </Box>
                          ))}
                        </Box>

                        <Box display="flex" gap={2} mt={3}>
                          <Button
                            variant="contained"
                            onClick={handleApplySuggestion}
                            fullWidth
                          >
                            Apply This Structure
                          </Button>
                          <Button
                            variant="outlined"
                            startIcon={<RefreshIcon />}
                            onClick={handleRegenerateWithChanges}
                            disabled={generateMutation.isPending}
                          >
                            Regenerate
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Box>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Error Display */}
            {generateMutation.isError && (
              <Alert severity="error" sx={{ mt: 2 }}>
                Failed to generate learning path. Please try again with a different prompt.
              </Alert>
            )}
          </Box>
        </Collapse>
      </CardContent>
    </Card>
  )
}

export default AIPathGenerator
