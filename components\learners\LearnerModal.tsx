'use client'

import React, { useState, useEffect } from 'react'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Avatar,
  IconButton,
  Typography,
  Switch,
  FormControlLabel,
  Divider,
  Alert
} from '@mui/material'
import {
  PhotoCamera as PhotoIcon,
  Close as CloseIcon
} from '@mui/icons-material'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'

import { <PERSON>rner, CreateLearnerData, UpdateLearnerData } from '@/lib/types/learners'
import { useCreateLearner, useUpdateLearner } from '@/lib/hooks/useLearners'

const learnerSchema = z.object({
  email: z.string().email('Invalid email address'),
  full_name: z.string().min(2, 'Name must be at least 2 characters'),
  role_id: z.number().min(1, 'Please select a role'),
  department: z.string().optional(),
  position: z.string().optional(),
  phone: z.string().optional(),
  location: z.string().optional(),
  send_welcome_email: z.boolean().optional()
})

type LearnerFormData = z.infer<typeof learnerSchema>

interface LearnerModalProps {
  open: boolean
  onClose: () => void
  learner?: Learner | null
}

const LearnerModal: React.FC<LearnerModalProps> = ({
  open,
  onClose,
  learner
}) => {
  const [avatarFile, setAvatarFile] = useState<File | null>(null)
  const [avatarPreview, setAvatarPreview] = useState<string>('')

  const isEditing = !!learner
  const createMutation = useCreateLearner()
  const updateMutation = useUpdateLearner()

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting }
  } = useForm<LearnerFormData>({
    resolver: zodResolver(learnerSchema),
    defaultValues: {
      email: '',
      full_name: '',
      role_id: 2, // Default to learner role
      department: '',
      position: '',
      phone: '',
      location: '',
      send_welcome_email: true
    }
  })

  useEffect(() => {
    if (learner) {
      reset({
        email: learner.email,
        full_name: learner.full_name,
        role_id: learner.role_id,
        department: learner.department || '',
        position: learner.position || '',
        phone: learner.phone || '',
        location: learner.location || '',
        send_welcome_email: false
      })
      setAvatarPreview(learner.avatar_url || '')
    } else {
      reset({
        email: '',
        full_name: '',
        role_id: 2,
        department: '',
        position: '',
        phone: '',
        location: '',
        send_welcome_email: true
      })
      setAvatarPreview('')
    }
    setAvatarFile(null)
  }, [learner, reset])

  const handleAvatarChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setAvatarFile(file)
      const reader = new FileReader()
      reader.onload = (e) => {
        setAvatarPreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const onSubmit = async (data: LearnerFormData) => {
    try {
      if (isEditing && learner) {
        const updateData: UpdateLearnerData = {
          ...data,
          // Handle avatar upload if needed
          avatar_url: avatarFile ? undefined : learner.avatar_url // TODO: Implement avatar upload
        }
        await updateMutation.mutateAsync({
          learnerId: learner.id,
          updates: updateData
        })
      } else {
        const createData: CreateLearnerData = {
          ...data,
          // Handle avatar upload if needed
          // TODO: Implement avatar upload
        }
        await createMutation.mutateAsync(createData)
      }
      onClose()
    } catch (error) {
      console.error('Error saving learner:', error)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  const roleOptions = [
    { value: 2, label: 'Learner' },
    { value: 3, label: 'Manager' },
    { value: 4, label: 'Instructor' }
  ]

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 }
      }}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6" fontWeight="bold">
            {isEditing ? 'Edit Learner' : 'Add New Learner'}
          </Typography>
          <IconButton onClick={handleClose} disabled={isSubmitting}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <form onSubmit={handleSubmit(onSubmit)}>
        <DialogContent dividers>
          <Grid container spacing={3}>
            {/* Avatar Section */}
            <Grid item xs={12}>
              <Box display="flex" alignItems="center" gap={3}>
                <Box position="relative">
                  <Avatar
                    src={avatarPreview}
                    sx={{ width: 80, height: 80 }}
                  >
                    {!avatarPreview && <PhotoIcon />}
                  </Avatar>
                  <IconButton
                    component="label"
                    sx={{
                      position: 'absolute',
                      bottom: -8,
                      right: -8,
                      backgroundColor: 'primary.main',
                      color: 'white',
                      '&:hover': {
                        backgroundColor: 'primary.dark'
                      }
                    }}
                    size="small"
                  >
                    <PhotoIcon fontSize="small" />
                    <input
                      type="file"
                      hidden
                      accept="image/*"
                      onChange={handleAvatarChange}
                    />
                  </IconButton>
                </Box>
                <Box>
                  <Typography variant="subtitle1" fontWeight="medium">
                    Profile Picture
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Upload a profile picture for the learner
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Divider />
            </Grid>

            {/* Basic Information */}
            <Grid item xs={12} md={6}>
              <Controller
                name="full_name"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Full Name"
                    error={!!errors.full_name}
                    helperText={errors.full_name?.message}
                    required
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Controller
                name="email"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Email Address"
                    type="email"
                    error={!!errors.email}
                    helperText={errors.email?.message}
                    required
                    disabled={isEditing} // Don't allow email changes
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Controller
                name="role_id"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.role_id}>
                    <InputLabel>Role</InputLabel>
                    <Select
                      {...field}
                      label="Role"
                    >
                      {roleOptions.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Controller
                name="department"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Department"
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Controller
                name="position"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Position"
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Controller
                name="phone"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Phone Number"
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <Controller
                name="location"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Location"
                  />
                )}
              />
            </Grid>

            {!isEditing && (
              <Grid item xs={12}>
                <Controller
                  name="send_welcome_email"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={
                        <Switch
                          checked={field.value}
                          onChange={field.onChange}
                        />
                      }
                      label="Send welcome email to learner"
                    />
                  )}
                />
              </Grid>
            )}

            {(createMutation.error || updateMutation.error) && (
              <Grid item xs={12}>
                <Alert severity="error">
                  {(createMutation.error as any)?.message || 
                   (updateMutation.error as any)?.message || 
                   'An error occurred while saving the learner'}
                </Alert>
              </Grid>
            )}
          </Grid>
        </DialogContent>

        <DialogActions sx={{ p: 3 }}>
          <Button
            onClick={handleClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Saving...' : isEditing ? 'Update Learner' : 'Create Learner'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  )
}

export default LearnerModal
