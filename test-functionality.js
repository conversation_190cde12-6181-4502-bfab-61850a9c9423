// Quick test script to verify Learning Path functionality
// Run with: node test-functionality.js

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://ebugbzdstyztfvkhpqbs.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVidWdiemRzdHl6dGZ2a2hwcWJzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1NjA3MzAsImV4cCI6MjA2NTEzNjczMH0.aMHZiuBjytE86YQfbFRQOx4nlKwKgPkvXnBfz0lBS2E'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testBasicFunctionality() {
  console.log('🧪 Testing Basic Learning Path Functionality...\n')

  try {
    // Test 1: Fetch existing learning paths
    console.log('1️⃣ Testing: Fetch learning paths')
    const { data: paths, error: fetchError } = await supabase
      .from('learning_paths')
      .select('*')
      .limit(5)

    if (fetchError) {
      console.error('❌ Error fetching paths:', fetchError)
      return false
    }

    console.log(`✅ Successfully fetched ${paths.length} learning paths`)
    if (paths.length > 0) {
      console.log(`   - Sample: "${paths[0].title}" (${paths[0].difficulty_level})`)
    }
    console.log()

    // Test 2: Test publish functionality on an existing draft
    const draftPath = paths.find(p => !p.is_live)
    if (draftPath) {
      console.log('2️⃣ Testing: Publish functionality')
      console.log(`   - Publishing path: "${draftPath.title}"`)
      
      const { data: publishedPath, error: publishError } = await supabase
        .from('learning_paths')
        .update({ 
          is_live: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', draftPath.id)
        .select()
        .single()

      if (publishError) {
        console.error('❌ Error publishing path:', publishError)
        return false
      }

      console.log(`✅ Successfully published: "${publishedPath.title}"`)
      
      // Revert back to draft for future tests
      await supabase
        .from('learning_paths')
        .update({ is_live: false })
        .eq('id', draftPath.id)
      
      console.log('   - Reverted back to draft for future tests')
      console.log()
    } else {
      console.log('2️⃣ Skipping publish test - no draft paths found')
      console.log()
    }

    // Test 3: Test path creation
    console.log('3️⃣ Testing: Path creation')
    const testPathData = {
      title: 'Test Path - Quick Functionality Check',
      description: 'A test learning path to verify creation functionality',
      objectives: ['Test objective 1', 'Test objective 2'],
      prerequisites: ['Basic knowledge'],
      difficulty_level: 'beginner',
      estimated_duration: 2,
      category: 'Testing',
      tags: ['test', 'functionality'],
      is_live: false,
      is_featured: false,
      tenant_id: 3,
      created_by: '5cd09c93-eb00-470f-a605-c6d0d057bdd6'
    }

    const { data: newPath, error: createError } = await supabase
      .from('learning_paths')
      .insert(testPathData)
      .select()
      .single()

    if (createError) {
      console.error('❌ Error creating path:', createError)
      return false
    }

    console.log(`✅ Successfully created test path: "${newPath.title}" (ID: ${newPath.id})`)
    console.log()

    // Test 4: Test module creation
    console.log('4️⃣ Testing: Module creation')
    const testModuleData = {
      path_id: newPath.id,
      title: 'Test Module',
      description: 'A test module for functionality verification',
      order_index: 0,
      is_optional: false,
      unlock_conditions: {}
    }

    const { data: newModule, error: moduleError } = await supabase
      .from('modules')
      .insert(testModuleData)
      .select()
      .single()

    if (moduleError) {
      console.error('❌ Error creating module:', moduleError)
      return false
    }

    console.log(`✅ Successfully created test module: "${newModule.title}" (ID: ${newModule.id})`)
    console.log()

    // Test 5: Test lesson creation
    console.log('5️⃣ Testing: Lesson creation')
    const testLessonData = {
      module_id: newModule.id,
      title: 'Test Lesson',
      description: 'A test lesson for functionality verification',
      type: 'video',
      content_url: 'https://example.com/test-video',
      content_metadata: { duration: 600 },
      order_index: 0,
      estimated_duration: 10,
      is_mandatory: true,
      max_attempts: 3
    }

    const { data: newLesson, error: lessonError } = await supabase
      .from('lessons')
      .insert(testLessonData)
      .select()
      .single()

    if (lessonError) {
      console.error('❌ Error creating lesson:', lessonError)
      return false
    }

    console.log(`✅ Successfully created test lesson: "${newLesson.title}" (ID: ${newLesson.id})`)
    console.log()

    // Test 6: Test assignment creation
    console.log('6️⃣ Testing: Assignment creation')
    const testAssignmentData = {
      path_id: newPath.id,
      learner_id: '5cd09c93-eb00-470f-a605-c6d0d057bdd6',
      assigned_by: '5cd09c93-eb00-470f-a605-c6d0d057bdd6',
      due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      status: 'assigned',
      completion_percentage: 0
    }

    const { data: newAssignment, error: assignmentError } = await supabase
      .from('learner_assignments')
      .insert(testAssignmentData)
      .select()
      .single()

    if (assignmentError) {
      console.error('❌ Error creating assignment:', assignmentError)
      return false
    }

    console.log(`✅ Successfully created test assignment (ID: ${newAssignment.id})`)
    console.log()

    // Test 7: Test complete path fetch with relationships
    console.log('7️⃣ Testing: Complete path fetch with relationships')
    const { data: completePath, error: completeError } = await supabase
      .from('learning_paths')
      .select(`
        *,
        modules:modules(
          *,
          lessons:lessons(*)
        ),
        learner_assignments:learner_assignments!learner_assignments_path_id_fkey(*)
      `)
      .eq('id', newPath.id)
      .single()

    if (completeError) {
      console.error('❌ Error fetching complete path:', completeError)
      return false
    }

    console.log(`✅ Successfully fetched complete path with relationships`)
    console.log(`   - Modules: ${completePath.modules.length}`)
    console.log(`   - Lessons: ${completePath.modules.reduce((total, module) => total + module.lessons.length, 0)}`)
    console.log(`   - Assignments: ${completePath.learner_assignments.length}`)
    console.log()

    // Cleanup: Delete test data
    console.log('8️⃣ Cleaning up test data...')
    await supabase.from('lessons').delete().eq('module_id', newModule.id)
    await supabase.from('modules').delete().eq('path_id', newPath.id)
    await supabase.from('learner_assignments').delete().eq('path_id', newPath.id)
    await supabase.from('learning_paths').delete().eq('id', newPath.id)
    console.log('✅ Test data cleaned up')
    console.log()

    console.log('🎉 All functionality tests passed!')
    console.log('✅ Database operations are working correctly')
    console.log('✅ CRUD operations are functional')
    console.log('✅ Relationships are properly maintained')
    console.log('✅ Ready for frontend testing')

    return true

  } catch (error) {
    console.error('💥 Unexpected error during testing:', error)
    return false
  }
}

// Run the test
testBasicFunctionality().then(success => {
  if (success) {
    console.log('\n🚀 You can now test the frontend functionality:')
    console.log('   1. Create new learning paths')
    console.log('   2. Use AI path generation (with fallback)')
    console.log('   3. Publish learning paths')
    console.log('   4. All features should work end-to-end')
  } else {
    console.log('\n❌ Some tests failed. Please check the errors above.')
  }
  process.exit(success ? 0 : 1)
})
