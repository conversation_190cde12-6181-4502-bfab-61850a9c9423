import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface EnhanceLessonRequest {
  lessonId: string
  content: string
  type?: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { lessonId, content, type }: EnhanceLessonRequest = await req.json()

    if (!content || content.trim().length === 0) {
      return new Response(
        JSON.stringify({ error: 'Content is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get OpenAI API key from environment
    const openaiApiKey = Deno.env.get('OPENAI_API_KEY')
    if (!openaiApiKey) {
      return new Response(
        JSON.stringify({ error: 'AI service not configured' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const systemPrompt = `You are an expert instructional designer. Analyze the provided lesson content and suggest improvements to make it more engaging, clear, and effective for learners.

Return a JSON object with the following structure:
{
  "suggestions": [
    {
      "type": "improvement_type",
      "description": "What to improve",
      "suggestion": "Specific suggestion",
      "priority": "high|medium|low"
    }
  ],
  "enhanced_content": {
    "title": "Improved title if needed",
    "description": "Enhanced description",
    "learning_objectives": ["objective1", "objective2"],
    "key_points": ["point1", "point2"],
    "activities": ["activity1", "activity2"]
  },
  "readability_score": number_between_1_and_10,
  "engagement_tips": ["tip1", "tip2"]
}`

    const userPrompt = `Please analyze and enhance this lesson content:

Lesson Type: ${type || 'general'}
Content: ${content}

Focus on:
1. Clarity and readability
2. Learning objectives alignment
3. Engagement and interactivity
4. Structure and flow
5. Accessibility considerations`

    // Call OpenAI API
    const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.7,
        max_tokens: 1500,
      }),
    })

    if (!openaiResponse.ok) {
      const errorText = await openaiResponse.text()
      console.error('OpenAI API error:', errorText)
      return new Response(
        JSON.stringify({ error: 'Failed to enhance lesson content' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const openaiData = await openaiResponse.json()
    const aiResponse = openaiData.choices[0]?.message?.content

    if (!aiResponse) {
      return new Response(
        JSON.stringify({ error: 'No response from AI service' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Parse the AI response
    let enhancement
    try {
      enhancement = JSON.parse(aiResponse)
    } catch (parseError) {
      // Try to extract JSON from markdown
      const jsonMatch = aiResponse.match(/```json\n([\s\S]*?)\n```/)
      if (jsonMatch) {
        try {
          enhancement = JSON.parse(jsonMatch[1])
        } catch (secondParseError) {
          return new Response(
            JSON.stringify({ error: 'Invalid response format from AI service' }),
            { 
              status: 500, 
              headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
            }
          )
        }
      } else {
        return new Response(
          JSON.stringify({ error: 'Invalid response format from AI service' }),
          { 
            status: 500, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }
    }

    // Ensure response has required structure
    const sanitizedEnhancement = {
      suggestions: enhancement.suggestions || [],
      enhanced_content: enhancement.enhanced_content || {},
      readability_score: enhancement.readability_score || 5,
      engagement_tips: enhancement.engagement_tips || []
    }

    return new Response(
      JSON.stringify(sanitizedEnhancement),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in ai-enhance-lesson function:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
