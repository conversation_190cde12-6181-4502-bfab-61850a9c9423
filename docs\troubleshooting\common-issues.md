# Common Issues and Troubleshooting

This guide helps you diagnose and resolve common issues when implementing and using the Groups and Batches feature.

## 🚨 Database Issues

### Issue: Groups table not found
**Error Message:** `relation "groups" does not exist`

**Cause:** Database migrations haven't been run or failed to complete.

**Solution:**
```bash
# Check if tables exist
psql -h your-supabase-host -U postgres -d postgres -c "SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND tablename LIKE 'group%';"

# Run migrations in order
psql -h your-supabase-host -U postgres -d postgres -f docs/database/migrations/001_create_groups_table.sql
# Continue with other migration files...

# Or use Supabase CLI
supabase db push
```

### Issue: RLS policies blocking access
**Error Message:** `new row violates row-level security policy`

**Cause:** Row Level Security policies are preventing data access.

**Solution:**
```sql
-- Check if RLS is enabled
SELECT tablename, rowsecurity FROM pg_tables WHERE tablename = 'groups';

-- Check existing policies
SELECT * FROM pg_policies WHERE tablename = 'groups';

-- Verify user has proper tenant_id
SELECT id, tenant_id FROM users WHERE id = auth.uid();

-- Test policy manually
SELECT * FROM groups WHERE tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid());
```

### Issue: Foreign key constraint violations
**Error Message:** `violates foreign key constraint`

**Cause:** Referenced records don't exist or have been deleted.

**Solution:**
```sql
-- Check if referenced tenant exists
SELECT id FROM tenants WHERE id = 1;

-- Check if referenced user exists
SELECT id FROM users WHERE id = 'user-uuid';

-- Check if referenced learning path exists
SELECT id FROM learning_paths WHERE id = 1;

-- Find orphaned records
SELECT * FROM group_members gm 
LEFT JOIN users u ON gm.learner_id = u.id 
WHERE u.id IS NULL;
```

## 🔧 Component Issues

### Issue: Components not rendering
**Error Message:** `Cannot resolve module '@/components/groups/GroupsDashboard'`

**Cause:** Component files not copied or incorrect import paths.

**Solution:**
```bash
# Verify component files exist
ls -la components/groups/

# Check import paths in your files
grep -r "components/groups" app/

# Update import paths if needed
# From: import GroupsDashboard from '@/components/groups/GroupsDashboard'
# To: import GroupsDashboard from '../components/groups/GroupsDashboard'
```

### Issue: TypeScript errors
**Error Message:** `Property 'groups' does not exist on type`

**Cause:** Type definitions not imported or outdated.

**Solution:**
```typescript
// Ensure types are imported
import { Group, GroupMember } from '@/lib/types/groups'

// Update Supabase types
supabase gen types typescript --local > lib/types/supabase.ts

// Check type definitions match database schema
interface Group {
  id: number
  tenant_id: number
  name: string
  // ... other fields
}
```

### Issue: Material-UI theme conflicts
**Error Message:** `useTheme must be used within a ThemeProvider`

**Cause:** Components not wrapped in ThemeProvider or theme conflicts.

**Solution:**
```tsx
// Wrap your app with ThemeProvider
import { ThemeProvider, createTheme } from '@mui/material/styles'

const theme = createTheme({
  // Your theme configuration
})

function App() {
  return (
    <ThemeProvider theme={theme}>
      <YourComponents />
    </ThemeProvider>
  )
}

// Or check if theme is already provided
import { useTheme } from '@mui/material/styles'

function YourComponent() {
  const theme = useTheme()
  console.log('Theme:', theme) // Should not be undefined
}
```

## 📡 API Issues

### Issue: Supabase connection errors
**Error Message:** `Failed to fetch` or `Network error`

**Cause:** Incorrect Supabase configuration or network issues.

**Solution:**
```typescript
// Check environment variables
console.log('Supabase URL:', process.env.NEXT_PUBLIC_SUPABASE_URL)
console.log('Supabase Key:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY)

// Test connection
import { supabase } from '@/lib/supabase'

async function testConnection() {
  try {
    const { data, error } = await supabase.from('groups').select('count').limit(1)
    if (error) throw error
    console.log('Connection successful')
  } catch (error) {
    console.error('Connection failed:', error)
  }
}
```

### Issue: Authentication errors
**Error Message:** `JWT expired` or `Invalid JWT`

**Cause:** User session expired or authentication not properly configured.

**Solution:**
```typescript
// Check user session
const { data: { user }, error } = await supabase.auth.getUser()
if (error || !user) {
  // Redirect to login
  router.push('/login')
}

// Refresh session
const { data, error } = await supabase.auth.refreshSession()
if (error) {
  console.error('Failed to refresh session:', error)
}

// Set up session refresh
useEffect(() => {
  const { data: { subscription } } = supabase.auth.onAuthStateChange(
    (event, session) => {
      if (event === 'SIGNED_OUT' || !session) {
        router.push('/login')
      }
    }
  )
  
  return () => subscription.unsubscribe()
}, [])
```

## 🔄 Real-time Issues

### Issue: Real-time updates not working
**Error Message:** No error, but updates don't appear in real-time

**Cause:** Realtime not enabled or subscription not properly configured.

**Solution:**
```sql
-- Check if realtime is enabled for tables
SELECT * FROM pg_publication_tables WHERE pubname = 'supabase_realtime';

-- Enable realtime for missing tables
ALTER PUBLICATION supabase_realtime ADD TABLE groups;
ALTER PUBLICATION supabase_realtime ADD TABLE group_members;
```

```typescript
// Check subscription setup
const subscription = supabase
  .channel('groups-changes')
  .on('postgres_changes', 
    { 
      event: '*', 
      schema: 'public', 
      table: 'groups',
      filter: `tenant_id=eq.${tenantId}` // Make sure filter is correct
    }, 
    (payload) => {
      console.log('Realtime update:', payload) // Add logging
    }
  )
  .subscribe((status) => {
    console.log('Subscription status:', status) // Should be 'SUBSCRIBED'
  })

// Clean up subscription
return () => {
  subscription.unsubscribe()
}
```

### Issue: Too many real-time connections
**Error Message:** `Maximum number of channels reached`

**Cause:** Subscriptions not properly cleaned up.

**Solution:**
```typescript
// Use a single subscription per component
useEffect(() => {
  const subscription = supabase
    .channel(`groups-${groupId}`) // Unique channel name
    .on('postgres_changes', { /* config */ }, handler)
    .subscribe()

  return () => {
    subscription.unsubscribe() // Always clean up
  }
}, [groupId]) // Depend on relevant variables only

// Avoid creating subscriptions in render
// ❌ Wrong
function Component() {
  const subscription = supabase.channel('test').subscribe()
  return <div>Content</div>
}

// ✅ Correct
function Component() {
  useEffect(() => {
    const subscription = supabase.channel('test').subscribe()
    return () => subscription.unsubscribe()
  }, [])
  return <div>Content</div>
}
```

## 🎨 UI/UX Issues

### Issue: Charts not rendering
**Error Message:** `Chart.js not registered` or charts appear blank

**Cause:** Chart.js components not properly registered.

**Solution:**
```typescript
// Register Chart.js components
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  BarElement,
} from 'chart.js'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  BarElement
)

// Then use charts
import { Line, Doughnut, Bar } from 'react-chartjs-2'
```

### Issue: Drag and drop not working
**Error Message:** No error, but drag and drop doesn't function

**Cause:** @dnd-kit not properly configured or missing context providers.

**Solution:**
```tsx
import { DndContext, closestCenter } from '@dnd-kit/core'
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable'

function DragDropComponent() {
  return (
    <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
      <SortableContext items={items} strategy={verticalListSortingStrategy}>
        {items.map(item => (
          <SortableItem key={item.id} id={item.id} />
        ))}
      </SortableContext>
    </DndContext>
  )
}
```

### Issue: Responsive design problems
**Error Message:** Layout breaks on mobile devices

**Cause:** Incorrect breakpoint usage or missing responsive props.

**Solution:**
```tsx
// Use Material-UI breakpoints correctly
<Grid container spacing={{ xs: 2, md: 3 }}>
  <Grid item xs={12} sm={6} md={4}>
    <GroupCard />
  </Grid>
</Grid>

// Use responsive typography
<Typography 
  variant="h4" 
  sx={{
    fontSize: { xs: '1.5rem', sm: '2rem', md: '2.5rem' }
  }}
>
  Title
</Typography>

// Test on different screen sizes
// Use browser dev tools or
npm install -g responsive-viewer
```

## 🔒 Permission Issues

### Issue: Users can't create groups
**Error Message:** `Insufficient permissions` or `Access denied`

**Cause:** User role doesn't have required permissions.

**Solution:**
```typescript
// Check user permissions
const userPermissions = await getUserPermissions(userId)
console.log('User permissions:', userPermissions)

// Update permission check
const canCreateGroups = userPermissions.includes('create_groups')
if (!canCreateGroups) {
  throw new Error('User does not have permission to create groups')
}

// Update RLS policy if needed
CREATE POLICY "Users with create_groups permission can create groups" ON groups
  FOR INSERT WITH CHECK (
    tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid())
    AND EXISTS (
      SELECT 1 FROM user_permissions up
      WHERE up.user_id = auth.uid()
      AND up.permission = 'create_groups'
    )
  );
```

### Issue: Members can't see group content
**Error Message:** `No data returned` or empty group lists

**Cause:** User not properly added to group or missing member permissions.

**Solution:**
```sql
-- Check if user is group member
SELECT * FROM group_members 
WHERE learner_id = 'user-uuid' AND group_id = 1;

-- Check group member policies
SELECT * FROM pg_policies WHERE tablename = 'group_members';

-- Add user to group if missing
INSERT INTO group_members (group_id, learner_id, role, added_by)
VALUES (1, 'user-uuid', 'member', 'admin-uuid');
```

## 📊 Performance Issues

### Issue: Slow loading times
**Error Message:** No error, but pages load slowly

**Cause:** Inefficient queries or missing indexes.

**Solution:**
```sql
-- Check query performance
EXPLAIN ANALYZE SELECT * FROM groups WHERE tenant_id = 1;

-- Add missing indexes
CREATE INDEX IF NOT EXISTS idx_groups_tenant_status ON groups(tenant_id, status);
CREATE INDEX IF NOT EXISTS idx_group_members_group_learner ON group_members(group_id, learner_id);

-- Optimize queries
-- Instead of loading all data:
SELECT * FROM groups;

-- Load only needed fields:
SELECT id, name, status, member_count FROM groups WHERE tenant_id = 1 LIMIT 25;
```

### Issue: Memory leaks in React components
**Error Message:** `Warning: Can't perform a React state update on an unmounted component`

**Cause:** Async operations continuing after component unmount.

**Solution:**
```typescript
// Use cleanup in useEffect
useEffect(() => {
  let isMounted = true
  
  async function loadData() {
    try {
      const data = await fetchGroups()
      if (isMounted) {
        setGroups(data)
      }
    } catch (error) {
      if (isMounted) {
        setError(error)
      }
    }
  }
  
  loadData()
  
  return () => {
    isMounted = false
  }
}, [])

// Use AbortController for fetch requests
useEffect(() => {
  const controller = new AbortController()
  
  fetch('/api/groups', { signal: controller.signal })
    .then(response => response.json())
    .then(data => setGroups(data))
    .catch(error => {
      if (error.name !== 'AbortError') {
        setError(error)
      }
    })
  
  return () => {
    controller.abort()
  }
}, [])
```

## 🧪 Testing Issues

### Issue: Tests failing due to missing mocks
**Error Message:** `Cannot read property 'from' of undefined`

**Cause:** Supabase client not properly mocked in tests.

**Solution:**
```typescript
// Create test utils
// __tests__/utils/supabase-mock.ts
export const mockSupabase = {
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      eq: jest.fn(() => ({
        single: jest.fn(() => Promise.resolve({ data: mockData, error: null }))
      }))
    })),
    insert: jest.fn(() => Promise.resolve({ data: mockData, error: null })),
    update: jest.fn(() => Promise.resolve({ data: mockData, error: null })),
    delete: jest.fn(() => Promise.resolve({ error: null }))
  }))
}

// Mock in test file
jest.mock('@/lib/supabase', () => ({
  supabase: mockSupabase
}))
```

## 🆘 Getting Help

### Debug Information to Collect

When reporting issues, include:

1. **Environment Information:**
```bash
node --version
npm --version
next --version
```

2. **Database Schema:**
```sql
\d groups
\d group_members
SELECT * FROM pg_policies WHERE tablename LIKE 'group%';
```

3. **Browser Console Errors:**
- Open browser dev tools (F12)
- Check Console tab for JavaScript errors
- Check Network tab for failed API requests

4. **Component Props and State:**
```typescript
console.log('Props:', props)
console.log('State:', state)
console.log('Store state:', useGroupsStore.getState())
```

### Support Channels

- 📚 **Documentation**: Check other docs files first
- 🐛 **Bug Reports**: Create GitHub issue with reproduction steps
- 💬 **Community**: Join Discord for community support
- 📧 **Enterprise Support**: Contact <EMAIL>

### Quick Diagnostic Script

```typescript
// Add this to your page to diagnose common issues
async function runDiagnostics() {
  console.log('=== ZenithLearn Groups Diagnostics ===')
  
  // Check environment
  console.log('Environment:', {
    supabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
    supabaseKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    nodeEnv: process.env.NODE_ENV
  })
  
  // Check authentication
  const { data: { user } } = await supabase.auth.getUser()
  console.log('User authenticated:', !!user)
  
  // Check database connection
  try {
    const { data, error } = await supabase.from('groups').select('count').limit(1)
    console.log('Database connection:', error ? 'Failed' : 'Success')
    if (error) console.error('DB Error:', error)
  } catch (error) {
    console.error('Connection error:', error)
  }
  
  // Check permissions
  try {
    const { data, error } = await supabase.from('groups').select('id').limit(1)
    console.log('Groups access:', error ? 'Denied' : 'Allowed')
    if (error) console.error('Access error:', error)
  } catch (error) {
    console.error('Permission error:', error)
  }
  
  console.log('=== End Diagnostics ===')
}

// Run diagnostics
runDiagnostics()
```

This troubleshooting guide covers the most common issues you might encounter. If you're still having problems after trying these solutions, please reach out for support with the diagnostic information.
