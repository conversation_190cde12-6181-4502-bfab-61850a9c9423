import { NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

// GET /api/settings/permissions
export async function GET() {
  try {
    const { data, error } = await supabaseAdmin
      .from('permissions')
      .select('*')
      .order('category, name')

    if (error) {
      console.error('Error fetching permissions:', error)
      throw error
    }

    return NextResponse.json({
      success: true,
      data: data || []
    })

  } catch (error) {
    console.error('Failed to get permissions:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 })
  }
}
