-- Diagnostic script for ZenithLearn AI database
-- Run this to check the current state of your database

-- Check if tables exist
SELECT 
    'Tables Status' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'tenants' AND table_schema = 'public') 
        THEN '✓ tenants table exists'
        ELSE '✗ tenants table missing'
    END as tenants_status,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'roles' AND table_schema = 'public') 
        THEN '✓ roles table exists'
        ELSE '✗ roles table missing'
    END as roles_status,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users' AND table_schema = 'public') 
        THEN '✓ users table exists'
        ELSE '✗ users table missing'
    END as users_status;

-- Check table row counts
SELECT 
    'Row Counts' as check_type,
    (SELECT COUNT(*) FROM tenants) as tenants_count,
    (SELECT COUNT(*) FROM roles) as roles_count,
    (SELECT COUNT(*) FROM competencies) as competencies_count,
    (SELECT COUNT(*) FROM learning_paths) as learning_paths_count,
    (SELECT COUNT(*) FROM modules) as modules_count,
    (SELECT COUNT(*) FROM lessons) as lessons_count;

-- Check specific tenant data
SELECT 
    'Tenant Details' as check_type,
    id,
    name,
    slug,
    created_at
FROM tenants 
ORDER BY id;

-- Check roles for each tenant
SELECT 
    'Roles Details' as check_type,
    r.id,
    r.tenant_id,
    r.name,
    r.is_default,
    t.name as tenant_name
FROM roles r
LEFT JOIN tenants t ON r.tenant_id = t.id
ORDER BY r.tenant_id, r.id;

-- Check for foreign key constraints
SELECT 
    'Foreign Key Constraints' as check_type,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'public'
    AND tc.table_name IN ('roles', 'users', 'learning_paths', 'modules', 'lessons')
ORDER BY tc.table_name, kcu.column_name;

-- Check for any orphaned records
SELECT 
    'Orphaned Records Check' as check_type,
    'roles' as table_name,
    COUNT(*) as orphaned_count
FROM roles r
WHERE NOT EXISTS (SELECT 1 FROM tenants t WHERE t.id = r.tenant_id)

UNION ALL

SELECT 
    'Orphaned Records Check' as check_type,
    'learning_paths' as table_name,
    COUNT(*) as orphaned_count
FROM learning_paths lp
WHERE NOT EXISTS (SELECT 1 FROM tenants t WHERE t.id = lp.tenant_id);

-- Check migration history (if using Supabase CLI)
SELECT 
    'Migration History' as check_type,
    version,
    name,
    executed_at
FROM supabase_migrations.schema_migrations
ORDER BY executed_at DESC
LIMIT 10;

-- Check RLS status
SELECT 
    'RLS Status' as check_type,
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public'
    AND tablename IN ('tenants', 'roles', 'users', 'learning_paths')
ORDER BY tablename;

-- Check current database user and permissions
SELECT 
    'Database User Info' as check_type,
    current_user as current_db_user,
    session_user as session_user,
    current_database() as current_database;

-- Final summary
SELECT 
    'Summary' as check_type,
    CASE 
        WHEN (SELECT COUNT(*) FROM tenants) > 0 
        THEN 'Database has tenant data'
        ELSE 'Database is empty - needs seed data'
    END as data_status,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'tenants' AND table_schema = 'public')
        THEN 'Schema exists'
        ELSE 'Schema missing - run initial migration'
    END as schema_status;
